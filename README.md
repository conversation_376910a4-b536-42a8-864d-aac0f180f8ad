# New Spine Search

Responsável por realizar buscas no Elasticsearch e DynamoDB, trazendo funcionalidades de autocomplete, busca por homônimos ou  por dados de pessoas e empresas.

O projeto serverless consiste em uma API Gateway com dois endpoints distintos, cada um associado a uma função Lambda específica.

# Configurando e Testando Local

## Requisitos do Sistema

- Node.js v18.x
- NPM ≥ v7.24.0. ou Yarn ≥ v1.22.0
typescript
## Configurando o Projeto

### Passo 1: Gerar o arquivo .env a partir dos arquivos de exemplo

- Para simular o ambiente de QA:

```
cp .env.qa .env
```

ou

```
cp .env.local .env
```

- Para simular o ambiente de PROD:

```
cp .env.prod .env
```

### Passo 2: Instalar as dependências

- Com NPM:

```
npm install
```

- Ou com Yarn:

```
yarn install
```

## Realizando Testes Locais

Use a VPN correspondente ao ambiente que deseja simular;

Para testar as funções, na raiz do projeto execute o seguinte comando:

- Se estiver usando NPM:

npx sls invoke local -f {nomeDaFunção} --path ./tests/{nomeDoArquivo}.json 

- Se estiver usando Yarn:

yarn sls invoke local -f {nomeDaFunção} --path ./tests/{nomeDoArquivo}.json 

Substitua o {nomeDaFunção} e o {nomeDoArquivo} pelos valores reais, por exemplo:

```
npx sls invoke local -f search --path ./tests/search_cnpj.json
```
```
npx sls invoke local -f autocomplete --path ./tests/autocomplete_pf.json
```

Segue a lista das funcionalidades e seus respectivos arquivos de teste:

| Função | Descrição | Arquivo |
| --- | --- | --- |
| autocomplete | Pessoa Física | autocomplete_pf.json |
| autocomplete | Pessoa Jurídica - Formato em String | autocomplete_pj_string.json |
| autocomplete | Pessoa Jurídica - Formato em Json | autocomplete_pj_json.json |
| search | Homônimos | search_homonimos.json |
| search | Pessoa Jurídica - Busca por CNPJ | search_cnpj.json |
| search | Pessoa Jurídica - Busca por Nome | search_nome_pj.json |
| search | Pessoa Física - Busca por CPF | search_cpf.json |
| search | Pessoa Física - Busca por Nome | search_nome_pf.json |
| search | Cidades das Empresas Salvas| search_cities.json |

Crie ou edite um arquivo de teste para testar com outros filtros.

Consulte a [documentação do comando](https://www.serverless.com/framework/docs/providers/aws/cli-reference/invoke-local/) para mais informações.

# Estrutura Interna

Ele foi gerado usando o modelo [aws-nodejs-typescript](https://github.com/fawaz-alesayi/lambda-typescript-http-api-template) da estrutura [Serverless](https://www.serverless.com/).

```
.
├── src
│   ├── functions             # Pasta de configuração e código-fonte das Lambdas
│   │   ├── autocomplete
│   │   │   ├── handler.ts    # Código-fonte da lambda autocomplete
│   │   │   ├── index.ts      # Configuração do serverless p/ a lambda autocomplete
│   │   ├── search
│   │   │   ├── handler.ts    # Código-fonte da lambda search
│   │   │   ├── index.ts      # Configuração do serverless p/ a lambda search
│   │   │
│   │   └── index.ts         # Importação/exportação das configurações de lambda
│   │
│   └── libs                     # Código compartilhado da Lambda
│       └── apiGateway.ts        # Helpers específicos para o API Gateway
│       └── handler-resolver.ts  # Helpers para as configurações de lambda
│       └── lambda.ts            # Middleware da lambda
│   │
│   └── repositories          # Pasta de manipulação e acesso ao banco de dados
│       └── spinePj.ts        # Consultas na tabela spine_pj do dynamo
│   │
│   └── spider                # Pasta de consultas ao Elasticsearch
│       └── CNPJSpider.ts     # Consultas ao Elasticsearch - índice: spine_pj
│       └── CPFSpider.ts      # Consultas ao Elasticsearch - índice: spine_pf
│       └── CitySpider.ts      # Consultas ao Elasticsearch - índice: spine_pf
│   │
│   └── types                    # Pasta para definição de tipos personalizados
│       └── multiPropertyObj.ts  # Define um tipo de objeto
│   │
│   └── utils                          # Pasta de helpers para o código-fonte das lambdas
│       └── errorHandler.ts            # Exceção personalizada
│       └── UFCodes.ts                 # UFs e números para obtenção de CPFs
│       └── Utils.ts                   # Helpers gerais (validações, formatações e etc)
│       └── ElasticSearchHelper.ts     # Helper para consultar o Elasticsearch
│       └── SearchResponseFormatter.ts # Formata respostas do endpoint search
│
├── tests                     # Pasta com arquivos com entradas p/ testes
├── package.json
├── serverless.ts               # Arquivo de serviço serverless
├── tsconfig.json               # Configuração do compilador typeScript
├── tsconfig.paths.json         # Caminhos do typeScript

```

# Documentação Completa
[Documentação No Drive](https://drive.google.com/drive/folders/1Jp_OMns5lRJHzyq2ob1eX-mrevCtLcX4?usp=drive_link)