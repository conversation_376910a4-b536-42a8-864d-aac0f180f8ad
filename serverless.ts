import type { AWS } from '@serverless/typescript';

import autocomplete from '@functions/autocomplete';
import search from '@functions/search';

import { getVPC } from 'serverless-includes/vpc';
import { config } from "dotenv";

config({path: __dirname + '/.env'})

const serverlessConfiguration: AWS = {
    service: 'new-spine-search4',
    frameworkVersion: '3',
    plugins: ['serverless-esbuild', 'serverless-offline'],
    useDotenv: true,
    provider: {
        name: 'aws',
        region: 'us-east-1',
        vpc: getVPC(process.env.APP_ENV),
        runtime: 'nodejs18.x',
        environment: {
            AWS_NODEJS_CONNECTION_REUSE_ENABLED: '1',
            NODE_OPTIONS: '--enable-source-maps --stack-trace-limit=1000',
            ELASTIC_HOST: "${env:ELASTIC_HOST}",
            AWS_REGION_STRING: "${env:AWS_REGION_STRING}",
            APP_ENV: "${env:APP_ENV}",
        },
        iamRoleStatements: [
            {
                Effect: 'Allow',
                Action: [
                    "dynamodb:GetItem",
                    "dynamodb:BatchGetItem",
                    "dynamodb:Scan",
                    "dynamodb:Query",
                    "dynamodb:ConditionCheckItem"
                ],
                Resource: [
                    'arn:aws:dynamodb:us-east-1:342743391564:table/spine_pj',
                    'arn:aws:dynamodb:us-east-1:857717895630:table/spine_pj'
                ]
            }
        ]
    },

    // import the function via paths
    functions: { autocomplete, search },
    package: { individually: true },
    custom: {
        prune:{
            automatic: true,
            number: 3
        },
        esbuild: {
            bundle: true,
            minify: false,
            sourcemap: true,
            exclude: ['aws-sdk'],
            target: 'node18',
            define: { 'require.resolve': undefined },
            platform: 'node',
            concurrency: 10,
        },
    },
};

module.exports = serverlessConfiguration;
