<?php

namespace App\Factories;

use App\Sources\BigDataCorpBasicData;
use App\Sources\BoavistaParticipacaoEmpresas;
use App\Sources\BoaVistaParticipacaoEmpresasScore;
use App\Sources\BoaVistaQsaScore;
use App\Sources\NewQuod;
use App\Sources\QsaBoaVista;
use App\Sources\ReceitaFederalPf;
use App\Sources\ReceitaFederalPj;
use App\Sources\ReceitaFederalSimplesNacional;
use App\Sources\Source;
use App\Sources\SpcQsa;
use App\Sources\TargetDataCpf;
use Exception;

/**
 * Classe construtora de classes de processamento das fontes
 */
class SourceFactory
{
    const SOURCES_MAP = [
        'ReceitaFederalPf' => ReceitaFederalPf::class,
        'ReceitaFederalPj' => ReceitaFederalPj::class,
        'QsaBoaVista' => QsaBoaVista::class,
        'BoavistaParticipacaoEmpresas' => BoavistaParticipacaoEmpresas::class,
        'BoaVistaParticipacaoEmpresasScore' => BoaVistaParticipacaoEmpresasScore::class,
        'BoaVistaQsaScore' => BoaVistaQsaScore::class,
        'ReceitaFederalSimplesNacional' => ReceitaFederalSimplesNacional::class,
        'BigDataCorpBasicData' => BigDataCorpBasicData::class,
        'TargetDataCpf' => TargetDataCpf::class,
        'SpcQsa' => SpcQsa::class,
        'NewQuod' => NewQuod::class
    ];

    public function create(string $source, array $data): Source
    {
        throw_if(
            empty(self::SOURCES_MAP[$source]),
            Exception::class,
            "Fonte {$source} não implementada"
        );

        $class = self::SOURCES_MAP[$source];

        return new $class($data);
    }
}