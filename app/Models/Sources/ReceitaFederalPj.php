<?php

namespace App\Models\Sources;

class ReceitaFederalPj extends Sources
{
    public function find($cnpj)
    {
        $params = [
            'retry' => 1,
            'source' => 'ReceitaFederalPjGeneric',
            'param' => [
                'documento'  => $cnpj
            ],
        ];

        $data_string = json_encode($params);

        $ch = curl_init(\config('env.URL_LAMBDA'));
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data_string)
        ]);

        $result = curl_exec($ch);

        $result = json_decode($result, true);

        return $result['data'] ?? [];
    }
}