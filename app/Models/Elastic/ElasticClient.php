<?php

namespace App\Models\Elastic;

use Elasticsearch\Client;
use Elasticsearch\ClientBuilder;
use Elasticsearch\Common\Exceptions\Missing404Exception;
use Exception;

class ElasticClient
{
    public Client $client;
    public $es_index;
    public $es_type;

    public function __construct()
    {
        $host = [
            \config('env.ES_HOST'),
        ];

        // Instancia o client do ElastSearch
        $this->client = ClientBuilder::create()->setHosts($host)->build();
    }

    public function exists($params)
    {
        return $this->client->exists($params);
    }

    public function get($params)
    {
        try {
            $result = $this->client->get($params);
        } catch (Missing404Exception $e) {
            $erro = json_decode($e->getMessage());
            if (!$erro->found) {
                return false;
            }
        }

        return $result;
    }

    public function insert($id, $message)
    {
        if (empty($message['capital_social']) && $this->es_index === 'spine_pj') {
            $message['capital_social'] = (float) 0.0;
        }

        $params = [
            'index' => $this->es_index,
            'type' => $this->es_type,
            'id' => $id,
            'body' => $message,
        ];

        try {
            return $this->client->index($params);
        } catch (Exception $e) {
            throw $e;
        }
    }

    public function update($id, $message)
    {
        $message = array_filter($message, function ($value, $key) {
            return $value !== ' ' || ($key === 'dominio' && $value === " ");
        }, ARRAY_FILTER_USE_BOTH);

        $message = array_filter($message, function ($value) {
            if ($value === 0) {
                return true;
            }

            return !empty($value);
        });

        if (empty($message['capital_social']) && $this->es_index === 'spine_pj') {
            $message['capital_social'] = (float) 0.0;
        }

        $params = [
            'index' => $this->es_index,
            'type' => $this->es_type,
            'id' => $id,
            'body' => [
                'doc' => $message,
            ],
            'retry_on_conflict' => 3
        ];

        try {
            return $this->client->update($params);
        } catch (Exception $e) {
            throw $e;
        }
    }

    public function updateWithWitheSpaces($id, $message)
    {
        $params = [
            'index' => $this->es_index,
            'type' => $this->es_type,
            'id' => $id,
            'body' => [
                'doc' => $message,
            ],
        ];

        try {
            return $this->client->update($params);
        } catch (Exception $e) {
            throw $e;
        }
    }
}
