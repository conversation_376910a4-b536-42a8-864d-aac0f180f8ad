<?php

namespace App\Models\Dynamo;

use BaoPham\DynamoDb\DynamoDbModel;

class SpinePJ extends DynamoDbModel
{
    protected $table = 'spine_pj';

    protected $fillable = [
        "bairro",
        "cep",
        "cidade",
        "cnae",
        "cnae_segmento",
        "cnpj",
        "data_abertura",
        "data_situacao",
        "faixa_funcionarios",
        "faturamento_anual_estimado",
        "ibge",
        "latitude",
        "logr_nome",
        "logr_numero",
        "logr_tipo",
        "logradouro",
        "longitude",
        "matriz",
        "motivo_situacao",
        "natureza_juridica",
        "nome_fantasia",
        "porte",
        "razao_social",
        "setor",
        "situacao_cadastral",
        "tipo_cnae",
        "uf",
        "porte",
        "numero_filiais",
    ];

    protected $primaryKey = 'cnpj';

    public $timestamps = false;

    public function __construct()
    {
        $this->table = \config('env.DYNAMO_PREFIX_TABLE') . $this->table;

        parent::__construct();
    }
}
