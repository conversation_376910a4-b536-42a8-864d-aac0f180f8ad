<?php

namespace App\Models\Dynamo;

use Aws\DynamoDb\Marshaler;
use BaoPham\DynamoDb\DynamoDbModel;
use BaoPham\DynamoDb\Facades\DynamoDb;

class SpinePJQsa extends DynamoDbModel
{
    protected $table = 'spine_pj_qsa';

    protected $fillable = [
        "ano",
        "ano_cnpj",
        "cnpj",
        "documento_socio",
        "participacao",
        "qualificacao",
        "tipo_socio",
        "data_entrada",
        "data_alt",
        "data_cad",
        "nome",
    ];

    protected $primaryKey = 'ano_cnpj';

    public $timestamps = false;

    protected $compositeKey = ['ano', 'cnpj'];

    protected $dynamoDbIndexKeys = [
        'cnpj-ano-index' => [
            'hash' => 'cnpj',
            'range' => 'ano'
        ],
        'documento_socio-ano-index' => [
            'hash' => 'documento_socio',
            'range' => 'ano'
        ],
    ];

    public function __construct()
    {
        $this->table = \config('env.DYNAMO_PREFIX_TABLE') . $this->table;

        parent::__construct();
    }

    /**
     * Busca o qsa a partir do ano e cnpj
     */
    public function getQsa($year, $cnpj)
    {
        $marshaler = new Marshaler();

        $iterator = $this->queryQsaByYearCnpj($year, $cnpj);

        $data = [];
        foreach($iterator['Items'] as $item){
            $data[] = $marshaler->unmarshalItem($item);
        }

        return $data;
    }

    /**
     * Deleta o qsa a partir do ano e cnpj
     */
    public function deleteQsa($year, $cnpj)
    {
        $marshaler = new Marshaler();

        $iterator = $this->queryQsaByYearCnpj($year, $cnpj);

        $data = [];
        foreach ($iterator['Items'] as $item) {

            $data = $marshaler->unmarshalItem($item);

            DynamoDb::table($this->table)
                ->setKey(
                    DynamoDb::marshalItem([
                        'ano_cnpj' => "{$year}_{$cnpj}", 
                        'documento_socio' => $data['documento_socio']
                    ])
                )->prepare()->deleteItem();
        }
    }

    /**
     * Busca o qsa a partir do ano e documento
     */
    public function getCompaniesByDoc($year, $document)
    {
        $marshaler = new Marshaler();

        $iterator = $this->queryQsaByYearDoc($year, $document);

        $data = [];
        foreach ($iterator['Items'] as $item) {
            $item = $marshaler->unmarshalItem($item);
            $data[$item['cnpj']] = $item;
        }

        return $data;
    }

    /**
     * Deleta o qsa a partir do ano e documento
     */
    public function deleteCompaniesByDoc($year, $document)
    {
        $marshaler = new Marshaler();

        $iterator = $this->queryQsaByYearDoc($year, $document);

        $data = [];
        foreach ($iterator['Items'] as $item) {

            $data = $marshaler->unmarshalItem($item);

            DynamoDb::table($this->table)
                ->setKey(
                    DynamoDb::marshalItem([
                        'ano_cnpj' => $data['ano_cnpj'], 
                        'documento_socio' => $data['documento_socio']
                    ])
                )->prepare()->deleteItem();
        }
    }

    /**
     * Query por ano e cnpj
     */
    private function queryQsaByYearCnpj($year, $cnpj)
    {
        return DynamoDb::table($this->table)
            ->setIndexName('cnpj-ano-index')
            ->setKeyConditionExpression('ano = :ano and cnpj = :cnpj')
            ->setExpressionAttributeValue(':cnpj' , ['S' =>  (string) $cnpj])
            ->setExpressionAttributeValue(':ano' , ['S' => (string) $year])
            ->prepare()
            ->query();
    }

    /**
     * Query por ano e documento
     */
    private function queryQsaByYearDoc($year, $document)
    {
        return DynamoDb::table($this->table)
            ->setIndexName('documento_socio-ano-index')
            ->setKeyConditionExpression('ano = :ano and documento_socio = :documento')
            ->setExpressionAttributeValue(':documento' , ['S' =>  (string) $document])
            ->setExpressionAttributeValue(':ano' , ['S' => (string) $year])
            ->prepare()
            ->query();
    }
}
