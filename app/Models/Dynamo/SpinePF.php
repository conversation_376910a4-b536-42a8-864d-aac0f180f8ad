<?php

namespace App\Models\Dynamo;

use BaoPham\DynamoDb\DynamoDbModel;

class SpinePF extends DynamoDbModel
{
    protected $table = 'spine_pf';

    protected $fillable = [
        'classe_social',
        'cpf',
        'data_nascimento',
        'data_situacao',
        'hash_rf',
        'hora_situacao',
        'mae',
        'nome',
        'orgao_emissor',
        'renda_estimada',
        'rg',
        'sexo',
        'situacao',
    ];

    protected $primaryKey = 'cpf';

    public $timestamps = false;

    public function __construct()
    {
        $this->table = \config('env.DYNAMO_PREFIX_TABLE') . $this->table;

        parent::__construct();
    }
}
