<?php

namespace App\Util;

use DateTime;
use DateTimeZone;
use Log;

class Util
{
    public static function formatDate($date, $in, $out)
    {
        $date = trim($date);

        $tmp = ['DD' => '', 'YYYY' => '', 'MM' => '', 'HH' => '', 'II' => '', 'SS' => ''];

        // if (strlen($date) != strlen($in)) throw new \Exception("Formato de entrada inválido", 1);
        if (strlen($date) != strlen($in)) {
            return '';
        }

        for ($k = 0; $k < strlen($in); $k++) {
            if (!in_array($in[$k], ['Y', 'D', 'M', 'H', 'I', 'S'])) {
                continue;
            }

            switch ($in[$k]) {
                case 'Y':
                    $tmp['YYYY'] .= $date[$k];
                    break;
                case 'D':
                    $tmp['DD'] .= $date[$k];
                    break;
                case 'M':
                    $tmp['MM'] .= $date[$k];
                    break;
                case 'H':
                    $tmp['HH'] .= $date[$k];
                    break;
                case 'I':
                    $tmp['II'] .= $date[$k];
                    break;
                case 'S':
                    $tmp['SS'] .= $date[$k];
                    break;
            }
        }

        $res = strtr($out, $tmp);

        return $res;
    }

    public static function getHomonimo($name)
    {
        $name = urlencode(str_replace(['\\', '/'], '', strtoupper($name)));

        // Procura por um homonimo PF
        $result = file_get_contents(\config('env.URL_SPINE') . "?field=individuos&homonimos=1&query={$name}");
        $result = json_decode($result);

        // Procura por um homonimo PJ
        if (isset($result->suggestions[0]) && empty($result->suggestions[0])) {
            $result = file_get_contents(\config('env.URL_SPINE') . "?field=empresa&homonimos=1&query={$name}");
            $result = json_decode($result);
        }

        // Se tiver vazio ou mais de 1, returna vazio
        if ((isset($result->suggestions[0]) && empty($result->suggestions[0])) || count($result->suggestions) > 1) {
            return false;
        }

        $result = $result->suggestions[0];

        if (isset($result->cpf) || isset($result->cnpj)) {
            return $result;
        }
    }

    public static function getNameByDoc($doc)
    {
        if (strlen($doc) <= 11) {
            $dy_model = new \App\Models\Dynamo\SpinePF();
            $result = $dy_model::find($doc);
        }

        if (strlen($doc) > 11) {
            $dy_model = new \App\Models\Dynamo\SpinePJ();
            $result = $dy_model::find($doc);
        }

        if (!$result) {
            return [];
        }

        return $result->toArray();
    }

    public static function removeSpecial($string)
    {
        return preg_replace(
            ["/(á|à|ã|â|ä)/", "/(Á|À|Ã|Â|Ä)/", "/(é|è|ê|ë)/", "/(É|È|Ê|Ë)/", "/(í|ì|î|ï)/", "/(Í|Ì|Î|Ï)/", "/(ó|ò|õ|ô|ö)/", "/(Ó|Ò|Õ|Ô|Ö)/", "/(ú|ù|û|ü)/", "/(Ú|Ù|Û|Ü)/", "/(ñ)/", "/(Ñ)/"],
            explode(" ", "a A e E i I o O u U n N"),
            $string
        );
    }

    public static function nameString($name)
    {
        $string = str_replace([',', '-', '.', ' ', '/'], '', $name);

        $string = self::removeSpecial($string);

        $string = strtoupper($string);

        return $string;
    }

    public static function logInfo($message, $file_path = null)
    {
        Log::build([
            'driver' => 'single',
            'path' => $file_path ?? storage_path('logs/laravel.log'),
        ])->info($message);
    }

    public static function logError($message, $file_path = null)
    {
        Log::build([
            'driver' => 'single',
            'path' => $file_path ?? storage_path('logs/laravel.log'),
        ])->error($message);
    }

    public static function unMask($documento)
    {
        return preg_replace('/[^0-9]/', '', $documento);
    }

    /**
     * Check if a string is a valid date(time)
     *
     * DateTime::createFromFormat requires PHP >= 5.3
     *
     * @param string $str_dt
     * @param string $str_dateformat
     * @param string $str_timezone (If timezone is invalid, php will throw an exception)
     * @return bool
     */
    public static function isValidDateTimeString($str_dt, $str_dateformat, $str_timezone = null)
    {
        $date = DateTime::createFromFormat($str_dateformat, $str_dt, new DateTimeZone($str_timezone));
        return $date && DateTime::getLastErrors()["warning_count"] == 0 && DateTime::getLastErrors()["error_count"] == 0;
    }
}
