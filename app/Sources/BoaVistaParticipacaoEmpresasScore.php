<?php

namespace App\Sources;

use App\Util\Util;
use Exception;

/**
 * Class BoaVistaParticipacaoEmpresasScore
 * @package App\Sources
 * <AUTHOR>
 */
class BoaVistaParticipacaoEmpresasScore extends Source
{
    /**
     * Aqui a magica se inicia
     * @throws Exception
     */
    public function process($data)
    {
        $this->data['cpf'] = Util::nameString($this->data['cpf'] ?? '');
        throw_if(
            empty($this->data['cpf']),
            Exception::class,
            'Nenhum CPF informado.'
        );

        $this->getProcess($data, $this->data['cpf']);

        return;
    }

    /**
     * Processa o resultado da boavista e cruza os dados com o qsa da base para gerar o novo qsa
     */
    public function processQsa($data): void
    {
        $participations = $data["participacoes_do_documento_consultado"] ?? null;
        if (!isset($participations)) {
            return;
        }

        foreach ($participations as $participation) {
            if (!empty($participation['numeroDocumentoB'])) {
                $this->searchOnRfIfNotExistsInDynamoOrElastic(
                    Util::nameString($participation['numeroDocumentoB'])
                );
            }
        }
    }
}
