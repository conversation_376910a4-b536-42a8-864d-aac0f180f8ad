<?php

namespace App\Sources;

use App\Factories\SourceFactory;
use App\Jobs\ProcessDoc;
use App\Jobs\ProcessSource;
use App\Models\Dynamo\SpinePJQsa as DyModelQsa;
use Exception;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Arr;

abstract class Source
{
    public function __construct(
        protected array $data,
    ) {
    }

    abstract public function process($data);

    /**
     * @param mixed $data
     * @param array $document
     * @return void
     * @throws Exception
     */
    public function getProcess($data, $document)
    {
        try {
            $this->searchOnRfIfNotExistsInDynamoOrElastic($document);

            $this->processQsa($data);
            return true;
        } catch (Exception $e) {
            if (config('env.APP_ENV') == 'local') {
                throw $e;
            }
            Storage::append(
                'log/error.log',
                self::class . "Erro interno ao processar {$document}: " . $e->getMessage() . PHP_EOL
            );
        }
    }

    protected function searchOnRfIfNotExistsInDynamoOrElastic($document)
    {
        if (!$this->getEs($document) && !$this->getDynamo($document)) {
            $this->searchRF($document);
        }
    }

    /**
     * Verifica se já existe o registro no Dynamo
     */
    public function getDynamo($id)
    {
        $dyType = new DynamoType();
        $dyModel = $dyType->get($id);

        return !empty($dyModel::find($id));
    }

    /**
     * Verifica se já existe o registro no ElasticSearch
     */
    public function getEs($id)
    {

        $esModel = new ElasticType();
        $esModel = $esModel->get($id);

        $params = [
            'index' => $esModel->es_index,
            'type' => $esModel->es_type,
            'id' => $id,
        ];

        return $esModel->exists($params);
    }

    /**
     * Faz chamada na receita para atualizar os dados (Lembrando que toda chamada na receita atualiza a SPINE)
     */
    public function searchRF($id)
    {
        try {
            $source = 'ReceitaFederalPf';

            if (strlen($id) > 11) {
                $source = 'ReceitaFederalPj';
            }
    
            $procSource = new ProcessSource($id, $source);
            $procSource->onQueue(\config('env.DYNAMO_PREFIX_TABLE') . 'spine_consultas');
    
            if ($source == 'ReceitaFederalPf') {
                dispatch($procSource);
                return;
            }

            // Atualizar o Receita PJ de forma síncrona para não entrar em conflito com o QSA de outra fonte
            // Exemplo: BoaVistaQsaScore
            $data = $procSource->handle();

            $procDoc = new ProcessDoc($source, $data);
            $procDoc->handle(app(SourceFactory::class));
        } catch (\Exception $e) {
            Storage::append('log/error.log', self::class . " ---> " . $e->getMessage());
        }
    }

    /**
     * Deleta o qsa
     */
    public function updateQsa($documento, $ano, $data)
    {
        $dyModel = new DyModelQsa();

        $dyModel->deleteQsa($ano, $documento);

        $this->insertQsa($data);
    }

    public function saveQsa($documento, array $params)
    {
        try {
            DyModelQsa::create($params);
        } catch (Exception $e) {
            throw $e;
        }
    }

    protected function fill(&$model, $fields, $data, $old = null)
    {
        $object = is_object($model);

        foreach ($fields as $field => $model_field) {
            if (!Arr::has($data, $field) && $field !== 'dominio') {
                if ($old) {
                    if ($object) {
                        $model->$model_field = Arr::get($old, $model_field, '');

                        continue;
                    }

                    $model[$model_field] = Arr::get($old, $model_field, '');
                }

                continue;
            }

            if ($object) {
                $model->$model_field = Arr::get($data, $field, '');

                continue;
            }

            $model[$model_field] = Arr::get($data, $field, '');
        }
    }

    protected function keyHas($keys, array $array)
    {
        if (is_array($keys)) {
            foreach ($keys as $key) {
                if (in_array($key, array_keys($array))) {
                    return true;
                }
            }

            return false;
        }
    }
}
