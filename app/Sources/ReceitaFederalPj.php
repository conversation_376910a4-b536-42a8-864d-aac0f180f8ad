<?php

namespace App\Sources;

use App\Exceptions\ReceitaFederalException;
use App\Models\Dynamo\SpinePJ as DyModel;
use App\Models\Dynamo\SpinePJQsa as DyModelQsa;
use App\Models\Elastic\SpinePJ as EsModel;
use App\Services\GeolocationService;
use App\Util\Util;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

/**
 * Job que insere ou atualiza os dados de empresas no Dynamo e no ElasticSearch
 * Éder Zadravec
 */
class ReceitaFederalPj extends Source
{
    /**
     * Aqui a magica se inicia
     */
    public function process($data): bool
    {
        try {
            if (empty($data['cnpj'])) {
                throw new ReceitaFederalException('O cnpj está vazio');
            }

            $data['cnpj'] = Util::unMask($data['cnpj']);

            $dynamo = $this->getDynamo($data['cnpj']);
            if (!$dynamo || !is_numeric($dynamo->latitude)) {
                $data = $this->fillDataWithLatLon($data);
            }

            if (isset($data['cod_atividade'])) {
                $data['cod_atividade2'] = $data['cod_atividade'];
            }

            if ($dynamo) {
                $this->updateDynamo($data, $dynamo);
            } else {
                $this->insertDynamo($data);
            }

            $old = $this->getEs($data['cnpj']);

            if (!$old || empty($old->location)) {
                $data = $this->fillDataWithLatLon($data);
            }

            if (!$old) {
                $this->insertEs($data);
            }

            if ($old && count($old) > 0) {
                $this->updateEs($data, $old);
            }

            $this->processQsa($data);

            return true;
        } catch (\Exception $e) {
            if (\config('env.APP_ENV') == 'local') {
                throw $e;
            }

            Util::logError("Erro interno ao processar {$data['cnpj']}: " . $e->getMessage() . " {$e->getTraceAsString()} " . $e->getLine(), storage_path('logs/ReceitaFederalPj.log'));

            return false;
        }
    }

    /**
     * Utiliza os dados de endereço da variável $data e seta latitude e longitude
     *
     * @param array $data
     * @return void
     * @throws \Exception
     */
    private function fillDataWithLatLon(array $data)
    {
        $location = (new GeolocationService(
            $data['logradouro'] ?? '',
            $data['numero'] ?? '',
            $data['municipio'] ?? '',
            $data['uf'] ?? '',
            $data['cep'] ?? '',
            $data['uf'] == 'EX' ? '' : 'BRASIL',
            $data['uf'] == 'EX'
        ))->getGeolocation();

        $data['latitude'] = is_numeric($location['lat']) ? $location['lat'] : 0;
        $data['longitude'] = is_numeric($location['lon']) ? $location['lon'] : 0;
        $data['location'] = [
            'lat' => is_numeric($location['lat']) ? $location['lat'] : 0,
            'lon' => is_numeric($location['lon']) ? $location['lon'] : 0
        ];
        return $data;
    }

    /**
     * Verifica se já existe a empresa no Dynamo
     */
    public function getDynamo($id)
    {
        $dy_model = new DyModel();

        $result = $dy_model::find($id);

        return $result;
    }

    /**
     * Insere os da empresa dados no Dynamo
     */
    public function insertDynamo($data): bool
    {
        $dy_model = new DyModel();

        $fields = [
            'cnpj' => 'cnpj',
            'tipo' => 'matriz',
            'data_abertura' => 'data_abertura',
            'nome_empresarial' => 'razao_social',
            'nome_fantasia' => 'nome_fantasia',
            'cod_atividade' => 'cnae',
            'cod_atividade2' => 'cnae_segmento',
            'cod_natureza' => 'natureza_juridica',
            'logr_nome' => 'logr_nome',
            'logr_tipo' => 'logr_tipo',
            'logradouro' => 'logradouro',
            'logr_numero' => 'logr_numero',
            'data_dominio' => 'data_dominio',
            'numero' => 'numero',
            'cep' => 'cep',
            'dominio' => 'dominio',
            'bairro' => 'bairro',
            'municipio' => 'municipio',
            'uf' => 'uf',
            'situacao_cadastral' => 'situacao_cadastral',
            'data_situacao' => 'data_situacao',
            'motivo_situacao' => 'motivo_situacao',
            'capital_social' => 'capital_social',
            'complemento' => 'logr_complemento',
            'catchall' => 'catchall',
            'faturamento_anual_estimado' => 'faturamento_anual_estimado',
            'faixa_funcionarios' => 'faixa_funcionarios',
            'porte' => 'porte',
            'numero_filiais' => 'numero_filiais',
            'simples_nacional' => 'simples_nacional',
            'longitude' => 'longitude',
            'latitude' => 'latitude',
        ];

        // Cast logradouro
        if (!empty($data['logradouro']) && !$this->keyHas(['logr_nome', 'logr_tipo'], $data)) {
            foreach (['logr_nome', 'logr_tipo'] as $field) {
                $data[$field] = $data['logradouro'];
            }
        }

        // Cast das datas
        if ($this->keyHas(['data_situacao', 'data_abertura', 'data_dominio'], $data)) {
            foreach (['data_situacao', 'data_abertura', 'data_dominio'] as $field) {
                if (!empty($data[$field])) {
                    $data[$field] = (int) Util::formatDate($data[$field], 'DD/MM/YYYY', 'YYYYMMDD');
                }
            }
        }

        $casts = [
            'tipo' => 'setMatriz',
            'cod_atividade' => 'setCnae',
            'cod_atividade2' => 'setUnMask',
            'cod_natureza' => 'setUnMask',
            'logr_nome' => 'setLogrNome',
            'logr_tipo' => 'setLogrTipo',
            'cep' => 'setUnMask',
            'uf' => 'setUf',
        ];

        foreach ($casts as $field => $cast) {
            if (Arr::has($data, $field)) {
                $data[$field] = call_user_func([$this, $cast], $data[$field]);
            }
        }

        $this->fill($dy_model, $fields, $data);

        $dy_model->data_cad = (int) date('Ymd');

        try {
            $dy_model->save();
        } catch (\Exception $e) {
            Util::logError("Erro inserir registro no Dynamo {$data['cpf']}: " . json_encode($data), storage_path('logs/ReceitaFederalPj.log'));
        }

        return true;
    }

    protected function setMatriz($value): string
    {
        return (strtoupper($value) == 'MATRIZ') ? "True" : "False";
    }

    protected function setCnae($value)
    {
        return Util::unMask(substr($value, 0, -2));
    }

    protected function setUnMask($value)
    {
        return Util::unMask($value);
    }

    protected function setLogrNome($value)
    {
        return substr($value, strpos($value, ' ') + 1);
    }

    protected function setLogrTipo($value)
    {
        return substr($value, 0, strpos($value, ' '));
    }

    protected function setUf($value): string
    {
        return strtoupper($value);
    }

    /**
     * Altera os da empresa dados no Dynamo
     */
    public function updateDynamo($data, $old): bool
    {
        $dy_model = new DyModel();

        $result = $dy_model::find($data['cnpj']);

        $fields = [
            'tipo' => 'matriz',
            'data_abertura' => 'data_abertura',
            'nome_empresarial' => 'razao_social',
            'nome_fantasia' => 'nome_fantasia',
            'cod_atividade' => 'cnae',
            'cod_atividade2' => 'cnae_segmento',
            'cod_natureza' => 'natureza_juridica',
            'logr_nome' => 'logr_nome',
            'logr_tipo' => 'logr_tipo',
            'logradouro' => 'logradouro',
            'logr_numero' => 'logr_numero',
            'dominio' => 'dominio',
            'data_dominio' => 'data_dominio',
            'numero' => 'numero',
            'cep' => 'cep',
            'bairro' => 'bairro',
            'municipio' => 'municipio',
            'uf' => 'uf',
            'situacao_cadastral' => 'situacao_cadastral',
            'data_situacao' => 'data_situacao',
            'motivo_situacao' => 'motivo_situacao',
            'capital_social' => 'capital_social',
            'complemento' => 'logr_complemento',
            'catchall' => 'catchall',
            'faturamento_anual_estimado' => 'faturamento_anual_estimado',
            'faixa_funcionarios' => 'faixa_funcionarios',
            'porte' => 'porte',
            'numero_filiais' => 'numero_filiais',
            'longitude' => 'longitude',
            'latitude' => 'latitude',
            'simples_nacional' => 'simples_nacional'
        ];

        // Cast logradouro
        if (!empty($data['logradouro']) && !$this->keyHas(['logr_nome', 'logr_tipo'], $data)) {
            foreach (['logr_nome', 'logr_tipo'] as $field) {
                Arr::set($data, $field, $data['logradouro']);
            }
        }

        // Cast das datas
        if ($this->keyHas(['data_situacao', 'data_abertura', 'data_dominio'], $data)) {
            foreach (['data_situacao', 'data_abertura', 'data_dominio'] as $field) {
                if (!empty($data[$field])) {
                    $new = (int) Util::formatDate($data[$field], 'DD/MM/YYYY', 'YYYYMMDD');
                    Arr::set($data, $field, $new);
                }
            }
        }

        $casts = [
            'tipo' => 'setMatriz',
            'cod_atividade' => 'setCnae',
            'cod_atividade2' => 'setUnMask',
            'cod_natureza' => 'setUnMask',
            'logr_nome' => 'setLogrNome',
            'logr_tipo' => 'setLogrTipo',
            'cep' => 'setUnMask',
            'uf' => 'setUf',
        ];

        foreach ($casts as $field => $cast) {
            if (empty(Arr::get($data, $field))) {
                $old_field = !empty(Arr::get($fields, $field)) ? Arr::get($fields, $field) : $field;
                Arr::set($data, $field, empty($old->$old_field) ? '' : $old->$old_field);

                continue;
            }

            Arr::set($data, $field, call_user_func([$this, $cast], Arr::get($data, $field)));
        }

        $this->fill($result, $fields, $data, $old);

        $result->data_alt = (int) date('Ymd');

        try {
            $result->save();
        } catch (\Exception $e) {
            Util::logError("Erro atualizar registro no Dynamo {$data['cpf']}: " . json_encode($data), storage_path('logs/ReceitaFederalPj.log'));
        }

        return true;
    }

    /**
     * Verifica se já existe a empresa no ElasticSearch
     */
    public function getEs($id)
    {
        $es_model = new EsModel();

        $params = [
            'index' => $es_model->es_index,
            'type' => $es_model->es_type,
            'id' => $id,
        ];

        $result = $es_model->get($params);

        return Arr::get($result, '_source', false);
    }

    /**
     * Insere os da empresa dados no ElasticSearch
     */
    public function insertEs($data)
    {
        $es_model = new EsModel();

        $message = [];

        $fields = [
            'cnpj' => 'cnpj',
            'tipo' => 'matriz',
            'data_abertura' => 'data_abertura',
            'nome_empresarial' => 'razao_social',
            'nome_fantasia' => 'nome_fantasia',
            'cod_atividade' => 'cnae',
            'cod_natureza' => 'natureza_juridica',
            'logr_nome' => 'logr_nome',
            'logr_tipo' => 'logr_tipo',
            'logradouro' => 'logradouro',
            'dominio' => 'dominio',
            'data_dominio' => 'data_dominio',
            'logr_numero' => 'logr_numero',
            'numero' => 'numero',
            'cep' => 'cep',
            'bairro' => 'bairro',
            'municipio' => 'municipio',
            'uf' => 'uf',
            'situacao_cadastral' => 'situacao_cadastral',
            'data_situacao' => 'data_situacao',
            'motivo_situacao' => 'motivo_situacao',
            'capital_social' => 'capital_social',
            'complemento' => 'logr_complemento',
            'catchall' => 'catchall',
            'faturamento_anual_estimado' => 'faturamento_anual_estimado',
            'faixa_funcionarios' => 'faixa_funcionarios',
            'porte' => 'porte',
            'numero_filiais' => 'numero_filiais',
            'simples_nacional' => 'simples_nacional',
            'location' => 'location',
        ];

        // Cast logradouro
        if (!empty($data['logradouro']) && !$this->keyHas(['logr_nome', 'logr_tipo'], $data)) {
            foreach (['logr_nome', 'logr_tipo'] as $field) {
                $data[$field] = $data['logradouro'];
            }
        }

        // Cast das datas
        if ($this->keyHas(['data_situacao', 'data_abertura', 'data_dominio'], $data)) {
            foreach (['data_situacao', 'data_abertura', 'data_dominio'] as $field) {
                if (!empty($data[$field])) {
                    $data[$field] = (int) Util::formatDate($data[$field], 'DD/MM/YYYY', 'YYYYMMDD');
                }
            }
        }

        $casts = [ 
            'tipo' => 'setMatriz',
            'cod_atividade' => 'setUnMask',
            'cod_natureza' => 'setUnMask',
            'logr_nome' => 'setLogrNome',
            'logr_tipo' => 'setLogrTipo',
            'cep' => 'setUnMask',
            'uf' => 'setUf',
            'capital_social' => 'floatval'
        ];

        foreach ($casts as $field => $cast) {
            if (!isset($data[$field])) {
                continue;
            }

            if (method_exists($this, $cast)) {
                $data[$field] = call_user_func([$this, $cast], $data[$field]);
                continue;
            }

            $data[$field] = call_user_func($cast, $data[$field]);
        }

        if (empty($message['capital_social'])) {
            $message['capital_social'] = (float) 0.0;
        }

        $this->fill($message, $fields, $data);
        $message['data_cad'] = (int) date('Ymd');

        if (count($message) == 0) {
            return true;
        }

        if (!$es_model->insert($data['cnpj'], $message)) {
            Util::logError("Erro inserir registro no ES {$data['cnpj']}: " . json_encode($data), storage_path('logs/ReceitaFederalPj.log'));

            return false;
        }

        return true;
    }

    /**
     * Altera os da empresa dados no ElasticSearch
     */
    public function updateEs($data, $old)
    {
        $es_model = new EsModel();

        $message = [];

        $fields = [
            'data_abertura' => 'data_abertura',
            'nome_empresarial' => 'razao_social',
            'nome_fantasia' => 'nome_fantasia',
            'cod_atividade' => 'cnae',
            'dominio' => 'dominio',
            'data_dominio' => 'data_dominio',
            'cod_natureza' => 'natureza_juridica',
            'logr_nome' => 'logr_nome',
            'logr_tipo' => 'logr_tipo',
            'logradouro' => 'logradouro',
            'logr_numero' => 'logr_numero',
            'numero' => 'numero',
            'cep' => 'cep',
            'bairro' => 'bairro',
            'municipio' => 'municipio',
            'uf' => 'uf',
            'situacao_cadastral' => 'situacao_cadastral',
            'data_situacao' => 'data_situacao',
            'motivo_situacao' => 'motivo_situacao',
            'capital_social' => 'capital_social',
            'complemento' => 'logr_complemento',
            'catchall' => 'catchall',
            'faturamento_anual_estimado' => 'faturamento_anual_estimado',
            'faixa_funcionarios' => 'faixa_funcionarios',
            'porte' => 'porte',
            'numero_filiais' => 'numero_filiais',
            'location' => 'location',
            'simples_nacional' => 'simples_nacional'
        ];

        // Cast logradouro
        if (!empty($data['logradouro']) && !$this->keyHas(['logr_nome', 'logr_tipo'], $data)) {
            foreach (['logr_nome', 'logr_tipo'] as $field) {
                $data[$field] = $data['logradouro'];
            }
        }

        // Cast das datas
        if ($this->keyHas(['data_situacao', 'data_abertura', 'data_dominio'], $data)) {
            foreach (['data_situacao', 'data_abertura', 'data_dominio'] as $field) {
                if (!empty($data[$field])) {
                    $data[$field] = (int) Util::formatDate($data[$field], 'DD/MM/YYYY', 'YYYYMMDD');
                }
            }
        }

        $casts = [
            'tipo' => 'setMatriz',
            'cod_atividade' => 'setUnMask',
            'cod_natureza' => 'setUnMask',
            'logr_nome' => 'setLogrNome',
            'logr_tipo' => 'setLogrTipo',
            'cep' => 'setUnMask',
            'uf' => 'setUf',
            'capital_social' => 'floatval',
        ];

        foreach ($casts as $field => $cast) {
            if (empty($data[$field])) {
                $old_field = !empty($fields[$field]) ? $fields[$field] : $field;
                $data[$field] = empty($old[$old_field]) ? '' : $old[$old_field];

                continue;
            }

            if (method_exists($this, $cast)) {
                $data[$field] = call_user_func([$this, $cast], $data[$field]);
                continue;
            }

            $data[$field] = call_user_func($cast, $data[$field]);
        }

        $this->fill($message, $fields, $data, $old);
        $message['data_alt'] = (int) date('Ymd');

        if (count($message) == 0) {
            return true;
        }

        if (!$es_model->update($data['cnpj'], $message)) {
            Util::logError("Erro atualizar registro no ES {$data['cnpj']}: " . json_encode($data), storage_path('logs/ReceitaFederalPj.log'));

            return false;
        }

        return true;
    }

    /**
     * Processa o resultado da receita e cruza os dados com o qsa da base para gerar o novo qsa
     */
    public function processQsa($data)
    {
        $dy_model = new DyModelQsa();

        // Verifica se vem da fonte da Receita Federal Dossie no Lambda
        if (!empty($data['aQsa']) && is_array($data['aQsa'])) {
            $data['qsa']['corporate_structure'] = $data['aQsa'];
        }

        // Verifica se vem direto da fonte da Receita Federal no Lambda
        if (isset($data['qsa']['corporate_structure']) && is_array($data['qsa']['corporate_structure']) && !empty($data['qsa']['corporate_structure'])) {
            $anoQsa = (string) date('Y'); // Precisa ser string
            $qsaReceita = $data['qsa']['corporate_structure'];
            $data['cnpj'] = Util::nameString($data['cnpj']);
            $qsaAtual = $dy_model->getQsa($anoQsa, $data['cnpj']);
            $qsaNovo = [];
            $qsaTmp = [];

            // Verifica se já tem QSA na base
            if (!empty($qsaAtual)) {
                // Busca o nome dos atuais socios pelo documento socio
                foreach ($qsaAtual as $socio) {
                    // Não pega o socio por nomes sem documento valido
                    if (!ctype_digit($socio['documento_socio'])) {
                        continue;
                    }

                    $result = $dy_model->getCompaniesByDoc($anoQsa, $socio['documento_socio']);

                    if (!isset($result[$data['cnpj']]) || empty($result[$data['cnpj']]['nome'])) {
                        $result = $dy_model->getCompaniesByDoc($anoQsa, str_replace([',', '.'], '', $socio['documento_socio']));
                        if (!isset($result[$data['cnpj']]) || empty($result[$data['cnpj']]['nome'])) {
                            continue;
                        }
                    }

                    $nome = $result[$data['cnpj']]['nome'];

                    $qsaTmp[Util::nameString($nome)] = $socio;
                }

                foreach ($qsaReceita as $socio) {
                    if (!isset($socio['name']) || empty($socio['name'])) {
                        continue;
                    }

                    // Verifica quais dos socios da base se mantiveram na empresa
                    if (isset($qsaTmp[Util::nameString($socio['name'])])) {
                        $socioAtual = $qsaTmp[Util::nameString($socio['name'])];

                        $qsaNovo[] = [
                            'data_alt' => date('Ymd'),
                            'data_cad' => $socioAtual['data_cad'] ?? '',
                            'ano' => $anoQsa,
                            'ano_cnpj' => $anoQsa . '_' . $data['cnpj'],
                            'cnpj' => $data['cnpj'],
                            'nome' => $socio['name'],
                            'documento_socio' => $this->defineDocumentOrName($socio, $socioAtual['documento_socio']),
                            'participacao' => $socioAtual['participacao'] ?? '',
                            'tipo_socio' => $socioAtual['tipo_socio'],
                            'qualificacao' => (isset($socio['qualification']) && !empty($socio['qualification']))
                            ? strtoupper(Util::removeSpecial($socio['qualification']))
                            : $socioAtual['qualificacao'],
                        ];

                        continue;
                        // Insere o novos socios
                    }

                    $homonimo = Util::getHomonimo($socio['name']);

                    if (!$homonimo) {
                        $tipo = 'PF';
                        if (
                            preg_match('/(?:LTD$|LTDA$|\sME$|CORP$|S\/A|S\.A(?:\.|))/i', $socio['name']) ||
                            Str::contains(strtolower(Util::removeSpecial($socio['qualification'])), 'juridica')
                        ) {
                            $tipo = 'PJ';
                        }

                        $qsaNovo[] = [
                            'data_cad' => date('Ymd'),
                            'ano' => $anoQsa,
                            'ano_cnpj' => $anoQsa . '_' . $data['cnpj'],
                            'cnpj' => $data['cnpj'],
                            'nome' => $socio['name'],
                            'documento_socio' => $this->defineDocumentOrName($socio),
                            'participacao' => '',
                            'tipo_socio' => $tipo,
                            'qualificacao' => (isset($socio['qualification']) && !empty($socio['qualification']))
                            ? strtoupper(Util::removeSpecial($socio['qualification']))
                            : '',
                        ];

                        continue;
                    }

                    $document = $homonimo->cnpj ?? ($homonimo->cpf ?? null);

                    $qsaNovo[] = [
                        'data_cad' => date('Ymd'),
                        'ano' => $anoQsa,
                        'ano_cnpj' => $anoQsa . '_' . $data['cnpj'],
                        'cnpj' => $data['cnpj'],
                        'nome' => $socio['name'],
                        'documento_socio' => $this->defineDocumentOrName($socio, $document),
                        'participacao' => '',
                        'tipo_socio' => (isset($homonimo->cnpj)) ? 'PJ' : 'PF',
                        'qualificacao' => (isset($socio['qualification']) && !empty($socio['qualification']))
                        ? strtoupper(Util::removeSpecial($socio['qualification']))
                        : '',
                    ];
                }

                $this->updateQsa($data['cnpj'], $anoQsa, $qsaNovo);

            // Caso não tenha, só inserir
            } else {
                foreach ($qsaReceita as $socio) {
                    $homonimo = Util::getHomonimo($socio['name']);

                    if (!$homonimo) {
                        $tipo = 'PF';
                        if (
                            preg_match('/(?:LTD$|LTDA$|\sME$|CORP$|S\.A(?:\.|))/i', $socio['name']) ||
                            Str::contains(strtolower(Util::removeSpecial($socio['qualification'])), 'juridica')
                        ) {
                            $tipo = 'PJ';
                        }

                        $qsaNovo[] = [
                            'data_cad' => date('Ymd'),
                            'ano' => $anoQsa,
                            'ano_cnpj' => $anoQsa . '_' . $data['cnpj'],
                            'cnpj' => $data['cnpj'],
                            'nome' => $socio['name'],
                            'documento_socio' => $this->defineDocumentOrName($socio),
                            'participacao' => '',
                            'tipo_socio' => $tipo,
                            'qualificacao' => (isset($socio['qualification']) && !empty($socio['qualification']))
                            ? strtoupper(Util::removeSpecial($socio['qualification']))
                            : '',
                        ];

                        continue;
                    }

                    $document = $homonimo->cnpj ?? ($homonimo->cpf ?? null);

                    $qsaNovo[] = [
                        'data_cad' => date('Ymd'),
                        'ano' => $anoQsa,
                        'ano_cnpj' => $anoQsa . '_' . $data['cnpj'],
                        'cnpj' => $data['cnpj'],
                        'nome' => $socio['name'],
                        'documento_socio' => $this->defineDocumentOrName($socio, $document),
                        'participacao' => '',
                        'tipo_socio' => (isset($homonimo->cnpj)) ? 'PJ' : 'PF',
                        'qualificacao' => (isset($socio['qualification']) && !empty($socio['qualification']))
                        ? strtoupper(Util::removeSpecial($socio['qualification']))
                        : '',
                    ];
                }

                $this->insertQsa($qsaNovo);
            }
        }
    }

    /**
     * Define o documento pelo array de socios
     * Não alterar este metodo sem falar antes com o Fábio
     *
     * @param array $partner
     * @param string|int|null $document
     *
     * @return string
     */
    private function defineDocumentOrName(array $partner, $document = null)
    {
        // Documento socio existe e é somente numeros
        if (!empty($partner['documento_socio']) && ctype_digit($partner['documento_socio'])) {
            return Util::nameString($partner['documento_socio']);
        }

        // Quando o documento socio antigo era texto, mas agora tem documento
        if (!empty($document) && ctype_digit($document)) {
            return Util::nameString($document);
        }

        // Quando não tem doc socio e nem homonimo
        return Str::slug($partner['name'], '_');
    }

    /**
     * Deleta o qsa
     */
    public function updateQsa($cnpj, $ano, $data)
    {
        $dyModel = new DyModelQsa();

        // Deleta o qsa atual
        $dyModel->deleteQsa($ano, $cnpj);

        $this->insertQsa($data);
    }

    /**
     * Insere o novo qsa
     */
    public function insertQsa($data)
    {
        // Insere o novo qsa na base
        foreach ($data as $socio) {
            $dyModelQsa = new DyModelQsa();

            $fields = [
                'ano' => 'ano',
                'ano_cnpj' => 'ano_cnpj',
                'cnpj' => 'cnpj',
                'nome' => 'nome',
                'documento_socio' => 'documento_socio',
                'participacao' => 'participacao',
                'tipo_socio' => 'tipo_socio',
                'qualificacao' => 'qualificacao',
                'data_alt' => 'data_alt',
                'data_cad' => 'data_cad',
            ];

            if ($this->keyHas(['data_alt', 'data_cad'], $socio)) {
                foreach (['data_alt', 'data_cad'] as $field) {
                    if (!empty($socio[$field])) {
                        $socio[$field] = intval($socio[$field]);
                    }
                }
            }

            $this->fill($dyModelQsa, $fields, $socio);

            $dyModelQsa->save();
        }
    }
}
