<?php

namespace App\Sources;

use App\Models\Dynamo\SpinePJQsa;
use App\Util\Util;
use Exception;
use Illuminate\Support\Facades\Storage;

class NewQuod extends Source
{
    /**
     * @throws Exception
     * @throws Exception
     */
    public function process($data)
    {
        if (!isset($this->data['cnpj'])) {
            throw new Exception('O Cnpj está vazio');
        }

        try {
            $this->getProcess($this->data, str_replace(['.', '/', '-'], '', $this->data['cnpj']));
            return true;
        } catch (\Exception $e) {
            if (\config('env.APP_ENV') == 'local') {
                throw $e;
            }
            Util::logError("Erro interno ao processar {$this->data['cnpj']}: " . $e->getMessage() . " {$e->getTraceAsString()} " . $e->getLine());
            return false;
        }
    }

    /**
     * Processa o resultado da quod e cruza os dados com o qsa da base para gerar o novo qsa
     * @throws Exception
     */

    public function processQsa($data): bool
    {
        if (empty($this->data['socios'])) {
            return false;
        }

        $actualQsa = [];
        $year = (string)date('Y');
        $spinePjQSAModel = new SpinePJQsa();

        $newQsa = array_map(
            fn ($partner) => $this->parsePartner($partner, $year),
            $this->data['socios']
        );

        $actualQsa = $spinePjQSAModel->getQsa($year, $this->data['cnpj']);
        foreach ($actualQsa as $socio) {
            $actualQsa[Util::nameString($socio['documento_socio'])] = $socio;
        }
        $this->updateExistingQsa($actualQsa, $newQsa);

        return true;
    }

    private function parsePartner($partner, $year) {
        if (empty($partner['documento'])) {
            Storage::append('log/error.log', self::class . " ---> {$partner['nome']}: O Sócio {$partner['documento']} não possui documento.");
            throw new Exception('Atualização interrompida porque um dos sócios não é válido');
        }

        $cnpj = preg_replace('/\D/', '', $this->data['cnpj']);

        return [
            'data_cad'          => date('Ymd'),
            'ano'               => $year,
            'ano_cnpj'          => "{$year}_{$cnpj}",
            'cnpj'              => $cnpj,
            'nome'              => $partner['nome'],
            'qualificacao'      => $partner['funcao'],
            'tipo_socio'        => $partner['tipoPessoa'],
            'data_entrada'      => $partner['entrada'],
            'documento_socio'   => $partner['documento'] ,
            'participacao'      => floatval($partner['percentual']),
            'data_alt' => date('Ymd')
        ];
    }

    private function updateExistingQsa(array $actualQsa, array $newQsa) {
        foreach ($newQsa as $partner) {
            if (empty($partner['documento_socio'])) {
                continue;
            }

            $year = (string)date('Y');
            $document = Util::nameString($partner['documento_socio']);
            if (!empty($actualQsa[$document])) {
                $actualPartner = $actualQsa[$document];

                $actualPartner['qualificacao'] = $partner['qualificacao'];

                if (!empty($partner['participacao'])) {
                    $actualPartner['participacao'] = floatval($partner['participacao']);
                }

                $actualPartner['data_alt'] = date('Ymd');

                if ($partner['data_entrada']) {
                    $actualPartner['data_entrada'] = Util::formatDate(
                        $partner['data_entrada'],
                        'DD/MM/YYYY',
                        'YYYYMMDD'
                    );
                }

                $this->updateQsa($this->data['cnpj'], $year, $actualPartner);
            }

            if (empty($actualQsa[$document])) {
                $this->saveQsa(
                    $partner['documento_socio'],
                    $partner
                );
            }
        }
    }
}
