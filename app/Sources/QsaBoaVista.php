<?php

namespace App\Sources;

use App\Util\Util;
use Exception;

/**
 * Job que insere ou atualiza os dados de pessoas no Dynamo e no ElasticSearch
 * Éder Zadravec
 */
class QsaBoaVista extends Source
{
    /**
     * Aqui a magica se inicia
     */
    public function process($data)
    {
        $this->data['cnpj'] = Util::nameString($this->data['cnpj'] ?? '');
        throw_if(
            empty($this->data['cnpj']),
            Exception::class,
            'Nenhum CNPJ informado.'
        );

        $this->getProcess($this->data, $this->data['cnpj']);

        return true;
    }

    /**
     * Processa o resultado da boavista e cruza os dados com o qsa da base para gerar o novo qsa
     */
    public function processQsa($data): void
    {
        if (empty($data['socios'])) {
            return;
        }

        foreach ($data['socios'] as $partner) {
            if (!empty($partner['cpf_cnpj'])) {
                $this->searchOnRfIfNotExistsInDynamoOrElastic(
                    Util::nameString($partner['cpf_cnpj'])
                );
            }
        }
    }
}
