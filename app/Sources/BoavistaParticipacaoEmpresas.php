<?php

namespace App\Sources;

use App\Util\Util;
use Exception;

/**
 * Job que insere ou atualiza os dados de pessoas no Dynamo e no ElasticSearch
 * Éder Zadravec
 */
class BoavistaParticipacaoEmpresas extends Source
{
    /**
     * Aqui a magica se inicia
     */
    public function process($data)
    {
        $this->data['cpf'] = Util::nameString($this->data['cpf'] ?? '');
        throw_if(
            empty($this->data['cpf']),
            Exception::class,
            'Nenhum CPF informado.'
        );

        $this->getProcess($this->data, $this->data['cpf']);

        return true;
    }
    /**
     * Processa o resultado da boavista e cruza os dados com o qsa da base para gerar o novo qsa
     */
    public function processQsa($data): void
    {
        if (empty($data['empresas'])) {
            return;
        }

        foreach ($data['empresas'] as $company) {
            if (!empty($company['cnpj'])) {
                $document = Util::nameString($company['cnpj']);
                $this->searchOnRfIfNotExistsInDynamoOrElastic($document);
            }
        }
    }
}
