<?php

namespace App\Sources;

use App\Models\Dynamo\SpinePJQsa as DyModelQsa;
use App\Util\Util;
use Storage;

class SpcQsa extends Source
{
    public function process($data)
    {
        try {
            $data['cnpj'] = str_replace(['.', '/', '-'], '', $data['consumidor']['consumidor-pessoa-juridica']['cnpj']['numero']);
            if (empty($data['cnpj'])) {
                throw new \Exception('O Cnpj está vazio');
            }

            // Verifica se temos na nossa base
            if (!$this->getDynamo($data['cnpj']) || !$this->getEs($data['cnpj'])) {
                // Busca e insere
                $this->searchRF($data['cnpj']);
            }

            $this->processQsa($data);

            return true;
        } catch (\Exception $e) {
            if (\config('env.APP_ENV') == 'local') {
                throw $e;
            }

            $criterio = isset($data['cnpj']) ? $data['cnpj'] : null;
            $this->setLog(__CLASS__ . "Erro interno ao processar {$criterio}: " . $e->getMessage() . PHP_EOL);
            return false;
        }
    }


    public function processQsa($data)
    {
        $dy_model = new DyModelQsa();
        if (isset($data['socio']['detalhe-socio']) && is_array($data['socio']['detalhe-socio']) && !empty($data['socio']['detalhe-socio'])) {
            $ano_qsa = (string) date('Y');
            $qsa_spc = $data['socio']['detalhe-socio'];
            $qsa_atual = $dy_model->getQsa($ano_qsa, $data['cnpj']);
            $qsa_novo = [];
            $qsa_tmp = [];

            if (!empty($qsa_atual)) {

                foreach ($qsa_atual as $socio) {
                    $socio = array_change_key_case($socio, CASE_LOWER);
                    $qsa_tmp[Util::nameString($socio['documento_socio'])] = $socio;
                }
                foreach ($qsa_spc as $socio) {
                    $socio = array_change_key_case($socio, CASE_LOWER);

                    if (empty($socio['documento'])) {
                        continue;
                    }

                    // Verifica quais dos socios da base se mantiveram na empresa
                    if (isset($qsa_tmp[Util::nameString($socio['documento'])])) {
                        $socio_atual = $qsa_tmp[Util::nameString($socio['documento'])];

                        $qsa_novo[] = [
                            'nome' => !empty($socio['razao_social']) ? $socio['razao_social'] : $socio['nome'],
                            'data_alt' => date('Ymd'),
                            'data_cad' => (isset($socio_atual['data_cad'])) ? $socio_atual['data_cad'] : '',
                            'ano' => $ano_qsa,
                            'ano_cnpj' => $ano_qsa . '_' . $data['cnpj'],
                            'cnpj' => $data['cnpj'],
                            'documento' => $socio['documento'],
                            'participacao' => (isset($socio['valor-participacao'])) ? $socio['valor-participacao'] / 100 : $socio_atual['participacao'],
                            'tipo_socio' => $socio_atual['tipo_socio'],
                            'qualificacao' => (isset($socio['funcao']) && !empty($socio['funcao']))
                                ? strtoupper(Util::removeSpecial($socio['funcao']))
                                : $socio_atual['qualificacao'],
                            'data_entrada' => (isset($socio['data_entrada']) && !empty($socio['data_entrada']))
                                ? $socio['data_entrada']
                                : (isset($socio_atual['data_entrada'])
                                    ? $socio_atual['data_entrada']
                                    : ''),
                        ];
                        // Insere o novos socios
                    } else {
                        $data_entrada = \Carbon\Carbon::parse($socio['data-entrada'])->format('Y-m-d');
                        $qsa_novo[] = [
                            'nome' => !empty($socio['razao_social']) ? $socio['razao_social'] : $socio['nome'],
                            'data_cad' => date('Ymd'),
                            'ano' => $ano_qsa,
                            'ano_cnpj' => $ano_qsa . '_' . $data['cnpj'],
                            'cnpj' => $data['cnpj'],
                            'documento' => $socio['documento'],
                            'participacao' => $socio['valor-participacao'] / 100,
                            'tipo_socio' => $this->charAmount($socio['documento']) == 11 ? 'PF' : 'PJ',
                            'qualificacao' => strtoupper(Util::removeSpecial($socio['tipo-relacionamento'])),
                            'data_entrada' => str_replace('-', '', $data_entrada),
                        ];
                    }
                }

                // $this->updateQsa($data['cnpj'], $ano_qsa, $qsa_novo);

                // Caso não tenha, só inserir
            } else {

                foreach ($qsa_spc as $socio) {
                    $socio = array_change_key_case($socio, CASE_LOWER);

                    if (empty($socio['documento'])) {
                        $this->setLog(__CLASS__ . " ---> {$data['cnpj']}: O Sócio {$socio['razao_social']} não possui documento.");
                        throw new \Exception('Atualização interrompida porque um dos sócios não é válido');
                    }

                    $data_entrada = \Carbon\Carbon::parse($socio['data-entrada'])->format('Y-m-d');

                    $qsa_novo[] = [
                        'nome' => !empty($socio['razao_social']) ? $socio['razao_social'] : $socio['nome'],
                        'data_cad' => date('Ymd'),
                        'ano' => $ano_qsa,
                        'ano_cnpj' => $ano_qsa . '_' . $data['cnpj'],
                        'cnpj' => $data['cnpj'],
                        'documento' => $socio['documento'],
                        'participacao' => $socio['valor-participacao'] / 100,
                        'tipo_socio' => $this->charAmount($socio['documento']) == 11 ? 'PF' : 'PJ',
                        'qualificacao' => strtoupper(Util::removeSpecial($socio['tipo-relacionamento'])),
                        'data_entrada' =>  str_replace('-', '', $data_entrada)
                    ];
                }

                if (count($qsa_novo) > 0) {
                    $this->updateQsa($data['cnpj'], $ano_qsa, $qsa_novo);
                }
            }
        }
    }

    private function charAmount($doc)
    {
        $doc = preg_replace("/[^0-9]/", '', $doc);
        return strlen($doc);
    }

    private function setLog($message)
    {
        Storage::append('log/error.log', $message);
    }

    public function updateQsa($documento, $ano, $data)
    {
        $dyModel = new DyModelQsa();

        $dyModel->deleteQsa($ano, $documento);

        $this->insertQsa($data);
    }

    public function insertQsa($data)
    {
        foreach ($data as $socio) {
            $socio = array_change_key_case($socio, CASE_LOWER);

            if (!$this->getEs($socio['documento'])) {

                $this->searchRF($socio['documento']);
            }

            $dy_model_qsa = new DyModelQsa();

            (isset($socio['ano']) && !empty($socio['ano'])) && $dy_model_qsa->ano = $socio['ano'];
            (isset($socio['ano_cnpj']) && !empty($socio['ano_cnpj'])) && $dy_model_qsa->ano_cnpj = $socio['ano_cnpj'];
            (isset($socio['cnpj']) && !empty($socio['cnpj'])) && $dy_model_qsa->cnpj = $socio['cnpj'];
            (isset($socio['documento']) && !empty($socio['documento'])) && $dy_model_qsa->documento_socio = $socio['documento'];
            (isset($socio['participacao']) && !empty($socio['participacao'])) && $dy_model_qsa->participacao = $socio['participacao'];
            (isset($socio['tipo_socio']) && !empty($socio['tipo_socio'])) && $dy_model_qsa->tipo_socio = $socio['tipo_socio'];
            (isset($socio['qualificacao']) && !empty($socio['qualificacao'])) && $dy_model_qsa->qualificacao = $socio['qualificacao'];
            (isset($socio['data_alt']) && !empty($socio['data_alt'])) && $dy_model_qsa->data_alt = (int) $socio['data_alt'];
            (isset($socio['data_cad']) && !empty($socio['data_cad'])) && $dy_model_qsa->data_cad = (int) $socio['data_cad'];
            (isset($socio['data_entrada']) && !empty($socio['data_entrada'])) && $dy_model_qsa->data_entrada = (int) $socio['data_entrada'];
            (isset($socio['nome']) && !empty($socio['nome'])) && $dy_model_qsa->nome = $socio['nome'];

            $dy_model_qsa->save();
        }
    }
}
