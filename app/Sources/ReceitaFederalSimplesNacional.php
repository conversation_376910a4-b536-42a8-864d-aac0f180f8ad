<?php

namespace App\Sources;

use App\Models\Dynamo\SpinePjSimplesNacional;
use App\Models\Elastic\SpinePJ as ElasticSpinePj;
use App\Util\Util;
use Illuminate\Support\Arr;

class ReceitaFederalSimplesNacional extends Source
{
    /**
     * Aqui a magica se inicia
     */
    public function process($data)
    {
        try {
            if (empty($data['cnpj'])) {
                throw new \Exception('O Cnpj está vazio');
            }

            $data['cnpj'] = Util::unMask($data['cnpj']);
            $data['data_consulta'] = date('Ymd');

            $this->saveElastic($data);
            $this->saveDynamo($data);

            return true;
        } catch (\Exception $e) {
            if (\config('env.APP_ENV') == 'local') {
                throw $e;
            }

            Util::logError("Erro interno ao processar {$data['cnpj']}: " . $e->getMessage() . " {$e->getTraceAsString()} " . $e->getLine(), storage_path('logs/ReceitaFederalPj.log'));

            return false;
        }
    }

    private function saveElastic($data)
    {
        $elasticModel = new ElasticSpinePj();

        $elasticData =  $elasticModel->get([
            'index' => $elasticModel->es_index,
            'type' => $elasticModel->es_type,
            'id' => $data['cnpj'],
        ]);

        if (!$elasticData) {
            return '';
        }

        $casts = [
            'data_situacao_simei' => 'formatDate',
            'data_situacao_simples' => 'formatDate',
            'situacao_simei' => 'formatElasticOption',
            'situacao_simples' => 'formatElasticOption'
        ];

        foreach ($casts as $field => $cast) {
            if (Arr::has($data, $field)) {
                $data[$field] = call_user_func([$this, $cast], $data[$field]);
            }
        }

        $fields = [
            'data_situacao_simei' => 'data_mei',
            'data_situacao_simples' => 'data_simples_nacional',
            'situacao_simei' => 'mei',
            'situacao_simples' => 'simples_nacional'
        ];

        $update = [];

        $this->fill($update, $fields, $data);

        if (!$elasticModel->updateWithWitheSpaces($data['cnpj'], $update)) {
            Util::logError("Erro atualizar registro no ES {$data['cnpj']}: " . json_encode($data), storage_path('logs/ReceitaFederalSimplesNacional.log'));

            return false;
        }
    }

    private function saveDynamo($data)
    {
        $dynamoModel = new SpinePjSimplesNacional();

        $dynamoData = $dynamoModel->find($data['cnpj']);

        $casts = [
            'data_situacao_simei' => 'formatDate',
            'data_situacao_simples' => 'formatDate',
            'situacao_simei' => 'formatDynamoOption',
            'situacao_simples' => 'formatDynamoOption'
        ];

        foreach ($casts as $field => $cast) {
            if (Arr::has($data, $field)) {
                $data[$field] = call_user_func([$this, $cast], $data[$field]);
            }
        }

        $fields = [
            'cnpj' => 'cnpj',
            'data_consulta' => 'data_consulta',
            'data_situacao_simei' => 'data_opcao_simei',
            'data_situacao_simples' => 'data_opcao_sn',
            'situacao_simei' => 'status_simei',
            'situacao_simples' => 'status_sn'
        ];

        try {
            if ($dynamoData) {
                $this->fill($dynamoData, $fields, $data, $dynamoData);
                $dynamoData->save();
            } else {
                $this->fill($dynamoModel, $fields, $data);
                $dynamoModel->save();
            }
        } catch (\Exception $e) {
            Util::logError("Erro interno ao processar {$data['cnpj']}: " . $e->getMessage() . " {$e->getTraceAsString()} " . $e->getLine(), storage_path('logs/ReceitaFederalSimplesNacional.log'));
        }
    }

    private function formatDate($date)
    {
        if (!empty($date)) {
            $newDate = \DateTime::createFromFormat('d/m/Y', $date);

            return $newDate->format('Ymd');
        }

        return ' ';
    }

    private function formatDynamoOption($option)
    {
        if ($option == 'NÃO optante') {
            return 'N';
        } elseif ($option == 'Optante') {
            return 'P';
        }

        return ' ';
    }

    private function formatElasticOption($option)
    {
        if (!empty($option)) {
            return strtoupper($option);
        }

        return ' ';
    }
}
