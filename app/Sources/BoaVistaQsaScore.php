<?php

namespace App\Sources;

use App\Util\Util;
use Exception;

/**
 * Class BoaVistaQsaScore
 *
 * @package App\Sources
 * <AUTHOR>
 */
class BoaVistaQsaScore extends Source
{
    /**
     * @throws Exception
     */
    public function process($data)
    {
        $this->data['cnpj'] = Util::nameString($this->data['cnpj'] ?? '');
        throw_if(
            empty($this->data['cnpj']),
            Exception::class,
            'Nenhum CNPJ informado.'
        );

        $this->getProcess($this->data, $this->data['cnpj']);

        return true;
    }

    /**
     * Processa o resultado da boavista e cruza os dados com o qsa da base para gerar o novo qsa
     *
     * @throws Exception
     */
    public function processQsa($data): void
    {
        if (empty($data['socios'])) {
            return;
        }

        foreach ($data['socios'] as $socio) {
            if (!empty($socio['cpf_cnpj'])) {
                $this->searchOnRfIfNotExistsInDynamoOrElastic(
                    Util::nameString($socio['cpf_cnpj'])
                );
            }
        }
    }
}
