<?php

namespace App\Sources;

use App\Exceptions\ReceitaFederalException;
use App\Models\Dynamo\SpinePF as DyModel;
use App\Models\Elastic\SpinePF as EsModel;
use App\Util\Util;

/**
 * Job que insere ou atualiza os dados de pessoas no Dynamo e no ElasticSearch
 * Éder Zadravec
 */
class ReceitaFederalPf extends Source
{
    /**
     * Aqui a magica se inicia
     */
    public function process($data)
    {
        try {
            if (empty($data['cpf'])) {
                throw new ReceitaFederalException('O Cpf está vazio', 1);
            }

            $data['cpf'] = Util::unMask($data['cpf']);

            if ($this->getEs($data['cpf'])) {
                $this->updateEs($data);
            } else {
                $this->insertEs($data);
            }

            return true;
        } catch (\Exception $e) {
            if (\config('env.APP_ENV') == 'local' || \config('env.APP_ENV') == 'testing') {
                throw new ReceitaFederalException($e->getMessage(), $e->getCode(), $e);
            }

            Util::logError("Erro interno ao processar {$data['cpf']}: {$e->getLine()} - {$e->getFile()} - {$e->getMessage()} , DATA: " . json_encode($data), storage_path('logs/ReceitaFederalPf.log'));

            return false;
        }
    }

    /**
     * Verifica se já existe a pessoa no Dynamo
     */
    public function getDynamo($id)
    {
        $dy_model = new DyModel();

        $result = $dy_model::find($id);

        return (count($result) > 0) ? true : false;
    }

    /**
     * Verifica se já existe a pessoa no ElasticSearch
     */
    public function getEs($id)
    {
        $es_model = new EsModel;

        $params = [
            'index' => $es_model->es_index,
            'type' => $es_model->es_type,
            'id' => $id,
        ];

        if (!$result = $es_model->get($params)) {
            return false;
        }

        return true;
    }

    /**
     * Insere os da pessoa dados no ElasticSearch
     */
    public function insertEs($data)
    {
        $es_model = new EsModel;

        $message = [];

        $fields = $this->getFields();

        $data = $this->formatDateAndHour($data);

        $this->fill($message, $fields, $data);

        $message['data_cad'] = (int) date('Ymd');

        if (count($message) == 0) {
            return true;
        }
        $insert = $es_model->insert($data['cpf'], $message);
        
        if (!$insert) {
            Util::logError("Erro inserir registro no ES {$data['cpf']}: " . json_encode($data), storage_path("logs/ReceitaFederalPf.log"));
            return false;
        }

        return true;
    }

    /**
     * Altera os da pessoa dados no ElasticSearch
     */
    public function updateEs($data)
    {
        $es_model = new EsModel;

        $message = [];

        $fields = $this->getFields();

        $data = $this->formatDateAndHour($data);

        $this->fill($message, $fields, $data);

        $message['data_alt'] = (int) date('Ymd');

        if (count($message) == 0) {
            return true;
        }

        if (!$es_model->update($data['cpf'], $message)) {
            Util::logError("Erro atualizar registro no ES {$data['cpf']}: " . json_encode($data), storage_path("logs/ReceitaFederalPf.log"));
            return false;
        }

        return true;
    }

    /**
     * Retorna campos para inserir/atualizar
     */
    private function getFields()
    {
        return [
            'cpf' => 'cpf',
            'nascimento' => 'data_nascimento',
            'dataNascimento' => 'data_nascimento',
            'nome' => 'nome',
            'situacao' => 'situacao',
            'data' => 'data_situacao',
            'hora' => 'hora_situacao',
            'chave' => 'hash_rf',
            'obito' => 'data_obito',
            'digito_verificador' => 'digito_verificador',
            'inscricao' => 'inscricao'
        ];
    }

    /**
     * Formata data e hora
     */
    private function formatDateAndHour($data)
    {
        // Cast das datas
        if ($this->keyHas(['data', 'nascimento', 'dataNascimento', 'inscricao'], $data)) {
            foreach (['data', 'nascimento', 'dataNascimento', 'inscricao'] as $field) {
                if (!empty($data[$field])) {
                    $data[$field] = (int) Util::formatDate($data[$field], 'DD/MM/YYYY', 'YYYYMMDD');
                }
            }
        }

        // Cast das horas
        if ($this->keyHas(['hora'], $data)) {
            foreach (['hora'] as $field) {
                if (!empty($data[$field])) {
                    $data[$field] = Util::formatDate($data[$field], 'HH:II:SS', 'HHIISS');
                }
            }
        }

        return $data;
    }
}
