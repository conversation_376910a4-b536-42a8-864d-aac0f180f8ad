<?php

namespace App\Sources;

abstract class SupplierSource extends Source
{
    abstract protected function getData(array $data): array;

    public function process($data)
    {
        try {
            $data = $this->getData($data);
            if (empty($data['cpf'])) {
                throw new Exception('O cpf está vazio');
            }

            $data['cpf'] = Util::unMask($data['cpf']);

            $operation = $this->getEs($data['cpf']) ? 'update' : 'insert';
            $this->upsert($data, $operation);

            return true;
        } catch (\Exception $e) {
            if (\config('env.APP_ENV') == 'local' || \config('env.APP_ENV') == 'testing') {
                throw new Exception($e->getMessage(), $e->getCode(), $e);
            }

            Util::logError(
                "Erro interno ao processar {$data['cpf']}: {$e->getMessage()} , DATA: " . json_encode($data),
                storage_path('logs/' . self::__CLASS__ . '.log')
            );

            return false;
        }
    }

    /**
     * Insert or Update into Elastic
     *
     * @param array $data
     * @param string $operation update or insert
     * @return bool
     */
    private function upsert(array $data, string $operation)
    {
        $es_model = new EsModel();
        $message = [];

        $birthDateKey = self::BIRTHDATE['key'];
        $data[$birthDateKey] = (int) Util::formatDate(
            $data[$birthDateKey],
            self::BIRTHDATE['format'],
            'YYYYMMDD'
        );

        $this->fill($message, self::MAPPING, $data);

        if ($operation != 'update') {
            $message['data_cad'] = (int) date('Ymd');
        }

        $message['data_alt'] = (int) date('Ymd');

        if (count($message) == 0) {
            return true;
        }

        $isSuccessfull = (bool) $es_model->{$operation}($data['cpf'], $message);
        if (!$isSuccessfull) {
            Util::logError(
                "Erro inserir/atualizar registro no ES {$data['cpf']}: " . json_encode($data),
                storage_path("logs/" . self::__CLASS__ . ".log")
            );
            return false;
        }

        return true;
    }
}
