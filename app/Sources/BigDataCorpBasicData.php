<?php

namespace App\Sources;

use App\Models\Elastic\SpinePF as EsModel;
use App\Util\Util;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;

class BigDataCorpBasicData extends Source
{
    public function process($data)
    {
        if (empty($data['Result']) || empty($data['Result'][0]['BasicData'])) {
            return false;
        }

        $data = $data['Result'][0]['BasicData'];

        $data['cpf'] = $data['TaxIdNumber'];

        try {
            if (empty($data['cpf'])) {
                throw new Exception('O cpf está vazio');
            }

            $data['cpf'] = Util::unMask($data['cpf']);

            $operation = $this->getEs($data['cpf']) ? 'update' : 'insert';
            $this->upsert($data, $operation);

            return true;
        } catch (\Exception $e) {
            if (\config('env.APP_ENV') == 'local' || \config('env.APP_ENV') == 'testing') {
                throw new Exception($e->getMessage(), $e->getCode(), $e);
            }

            Util::logError(
                "Erro interno ao processar {$data['cpf']}: {$e->getMessage()} , DATA: " . json_encode($data),
                storage_path('logs/BigDataCorpBasicData.log')
            );

            return false;
        }
    }

    /**
     * Get User from Elastic
     *
     * @param string $id
     * @return bool
     */
    public function getEs($id)
    {
        $es_model = new EsModel();

        $params = [
            'index' => $es_model->es_index,
            'type' => $es_model->es_type,
            'id' => $id,
        ];

        if (!$es_model->get($params)) {
            return false;
        }

        return true;
    }

    /**
     * Insert or Update into Elastic
     *
     * @param array $data
     * @param string $operation update or insert
     * @return bool
     */
    private function upsert(array $data, string $operation)
    {
        $es_model = new EsModel();

        $message = [];

        $fields = [
            'cpf' => 'TaxIdNumber',
            'data_nascimento' => 'BirthDate',
            'nome' => 'Name',
            'mae' => 'MotherName',
            'obito' => 'HasObitIndication',
        ];

        $data['BirthDate'] = (int) $this->formatBirthDate($data['BirthDate']);
        $this->fillBigDataCorpBasicData($message, $fields, $data);

        if ($operation != 'update') {
            $message['data_cad'] = (int) date('Ymd');
        }

        $message['data_alt'] = (int) date('Ymd');

        if (count($message) == 0) {
            return true;
        }

        $isSuccessfull = (bool) $es_model->{$operation}($data['cpf'], $message);
        if (!$isSuccessfull) {
            Util::logError(
                "Erro inserir/atualizar registro no ES {$data['cpf']}: " . json_encode($data),
                storage_path("logs/BigDataCorpBasicData.log")
            );
            return false;
        }

        return true;
    }

    private function fillBigDataCorpBasicData(&$model, $fields, $data)
    {
        foreach ($fields as $field => $model_field) {
            if (!Arr::has($data, $model_field)) {
                continue;
            }

            $model[$field] = Arr::get($data, $model_field, '');
        }
    }

    private function formatBirthDate($date)
    {
        $date = Carbon::parse($date)->format('Y-m-d');
        return str_replace('-', '', $date);
    }
}
