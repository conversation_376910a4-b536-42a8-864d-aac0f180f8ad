<?php

namespace App\Sources;

use App\Models\Elastic\SpinePF as EsModel;
use App\Util\Util;
use Exception;

class TargetDataCpf extends Source
{
    public function process($rawData)
    {
        try {
            $data = current($rawData['result'])['pessoa']['cadastral'];

            if (empty($data['CPF'])) {
                throw new Exception('O cpf está vazio');
            }

            $data['cpf'] = Util::unMask($data['CPF']);

            $operation = $this->getEs($data['cpf']) ? 'update' : 'insert';
            $this->upsert($data, $operation);

            return true;
        } catch (\Exception $e) {
            if (\config('env.APP_ENV') == 'local' || \config('env.APP_ENV') == 'testing') {
                throw new Exception($e->getMessage(), $e->getCode(), $e);
            }

            Util::logError(
                "Erro interno ao processar: {$e->getMessage()} , DATA: " . json_encode($data),
                storage_path('logs/TargetDataCpf.log')
            );

            return false;
        }
    }

    /**
     * Get User from Elastic
     *
     * @param string $id
     * @return bool
     */
    public function getEs($id)
    {
        $es_model = new EsModel();

        $params = [
            'index' => $es_model->es_index,
            'type' => $es_model->es_type,
            'id' => $id,
        ];

        if (!$es_model->get($params)) {
            return false;
        }

        return true;
    }

    /**
     * Insert or Update into Elastic
     *
     * @param array $data
     * @param string $operation update or insert
     * @return bool
     */
    private function upsert(array $data, string $operation)
    {
        $es_model = new EsModel();

        $message = [];

        $data['name'] = "{$data['nomePrimeiro']} {$data['nomeMeio']} {$data['nomeUltimo']}";
        $data['motherName'] = "{$data['maeNomePrimeiro']} {$data['maeNomeMeio']} {$data['maeNomeUltimo']}";

        $fields = [
            'cpf' => 'cpf',
            'data_nascimento' => 'dataNascimento',
            'nome' => 'name',
            'mae' => 'motherName',
            'obito' => 'obito',
        ];

        $data['dataNascimento'] = (int) Util::formatDate($data['dataNascimento'], 'YYYY-MM-DD', 'YYYYMMDD');

        $this->fill($message, $fields, $data);

        if ($operation != 'update') {
            $message['data_cad'] = (int) date('Ymd');
        }

        $message['data_alt'] = (int) date('Ymd');

        if (count($message) == 0) {
            return true;
        }

        $isSuccessfull = (bool) $es_model->{$operation}($data['cpf'], $message);
        if (!$isSuccessfull) {
            Util::logError(
                "Erro inserir/atualizar registro no ES {$data['cpf']}: " . json_encode($data),
                storage_path("logs/TargetDataCpf.log")
            );
            return false;
        }

        return true;
    }
}
