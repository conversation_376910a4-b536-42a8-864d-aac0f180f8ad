<?php

namespace App\Jobs;

use App\Factories\SourceFactory;
use App\Jobs\Job;
use App\Util\Util;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessDoc extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    protected string $source = '';
    protected array $data = [];
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($source, $data)
    {
        $this->source = ucfirst($source);
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(SourceFactory $sourceFactory)
    {
        try {
            $source = $sourceFactory->create($this->source, $this->data);

            if (!call_user_func_array([$source, 'process'], [$this->data])) {
                return [
                    "status" => "false",
                    "message" => "Não foi",
                ];
            }

            return [
                "status" => "success",
                "message" => "Foi",
            ];
        } catch (\Exception $e) {
            if (\config('env.APP_ENV') == 'local') {
                throw $e;
            }

            Util::logError("Quebrou: " . json_encode($this->data));
        }
    }
}
