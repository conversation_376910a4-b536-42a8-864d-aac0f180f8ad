<?php

namespace App\Jobs;

use App\Jobs\Job;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

class ProcessSource extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    public $documento;
    public $source;
    
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($documento, $source)
    {
        $this->documento    = $documento;
        $this->source       = $source;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $class = "\\App\\Models\\Sources\\{$this->source}";
        $source = new $class;

        return $source->find($this->documento);
    }
}
