<?php

namespace App\Console\Commands;

use App\Sources\ReceitaFederalPf;
use Illuminate\Console\Command;

class MassNomeMaeUpdate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'nome-mae:update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to mass update criteria mothers names';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        /*
         * Adicione a chave e valor mae => mae no mapeamento de campos disponiveis para atualização
         * no metodo getFields() da classe abaixo.
         * E.g
         * return [
            ...,
            'inscricao' => 'inscricao',
            'mae' => 'mae'
        ];
         */
        $receita = new ReceitaFederalPf([]);

        /*
         * Adicionar um json seguindo a formatação do arquivo source_data/nomesMae.json
         * Sendo a chave 'CPF' o documento do critério que deseja atualizar o nome da mãe e a chave 'mae'
         * o nome da mãe do critério.
         */
        $rawData = file_get_contents(__DIR__ . "/source_data/nomesMae.json");

        $json = json_decode($rawData, true);

        foreach ($json as $value) {
            if (strlen($value['CPF']) == 10) {
                $value['CPF'] = '0' . $value['CPF'];
            }

            $user['cpf'] = $value['CPF'];
            $user['mae'] = $value['mae'];

            $work = $receita->updateEs($user);

            if ($work) {
                $this->info("Nome da mae atualizado com sucesso! " . $user['cpf']);
            } else {
                $this->error("erro ao atualizar o nome da mae do documento: " . $user['cpf']);
            }
        }

        return Command::SUCCESS;
    }
}
