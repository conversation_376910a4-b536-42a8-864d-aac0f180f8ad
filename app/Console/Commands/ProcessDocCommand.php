<?php

namespace App\Console\Commands;

use App\Factories\SourceFactory;
use Illuminate\Console\Command;

class ProcessDocCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'process:doc {source}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Processa a fonte informada.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(SourceFactory $sourceFactory)
    {
        $source = $this->argument('source');
        $data = json_decode(
            file_get_contents(__DIR__ . "/source_data/{$source}.json"),
            true
        );
        
        $source = $sourceFactory->create($source, $data);
        $source->process($data);
    }
}
