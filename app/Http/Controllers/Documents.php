<?php

namespace App\Http\Controllers;

use App\Factories\SourceFactory;
use App\Jobs\ProcessDoc;
use App\Util\Util;
use Exception;
use Illuminate\Http\Request;
use Validator;

class Documents extends Controller
{
    private $sources = [
        'ReceitaFederalPf',
        'ReceitaFederalPj',
        'QsaBoaVista',
        'BoavistaParticipacaoEmpresas',
        'BoaVistaParticipacaoEmpresasScore',
        'BoaVistaQsaScore',
        'ReceitaFederalSimplesNacional',
        'BigDataCorpBasicData',
        'TargetDataCpf',
        'SpcQsa',
        'NewQuod'
    ];

    public function newDoc(Request $request)
    {
        $validation = Validator::make($request->all(), [
            'source' => 'required|string',
            'data' => 'required|array',
        ]);

        if ($validation->fails()) {
            $messages = $validation->errors();

            if ($messages->has('source')) {
                return [
                    'status' => 'error',
                    'message' => 'Source Inválido',
                ];
            }

            if ($messages->has('data')) {
                return [
                    'status' => 'error',
                    'message' => 'Data Inválido',
                ];
            }
        }

        try {
            // Recebe os parametros
            $source = ucfirst($request->input('source'));
            $data = $request->input('data');

            // Verifica se já foi implementado o source
            if (array_search($source, $this->sources) === false) {
                return [
                    "status" => "error",
                    "message" => "Source: {$source} não implementado",
                ];
            }

            // Instancia o Jobs
            $proc = new ProcessDoc($source, $data);
            $proc->onQueue(\config('env.DYNAMO_PREFIX_TABLE') . 'spine');

            // Se for debug, executa o job
            if ($request->input('debug')) {
                return $proc->handle(app(SourceFactory::class));
            }

            // Manda o job pra fila
            $this->dispatch($proc);

            return [
                "status" => "success",
                "message" => "Documento adicionado na fila",
            ];
        } catch (Exception $e) {
            if (\config('env.APP_ENV') == 'local') {
                throw $e;
            }

            Util::logError("Erro interno: " . $e->getMessage() . " " . $e->getLine() . " {$e->getFile()}");

            return [
                "status" => "error",
                "message" => "Erro interno",
            ];
        }
    }
}
