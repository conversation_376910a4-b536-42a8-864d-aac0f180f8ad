<?php

namespace App\Services;

use App\Models\SpineCaptura\Latlon;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Psr\Http\Message\ResponseInterface;
use Exception;
use DB;
use Illuminate\Support\Arr;

class GeolocationService
{
    public const DNS = "*******";
    public const URL_GOOGLE = "https://maps.googleapis.com/maps/api/geocode/json";
    public const URL_HERE = "https://geocode.search.hereapi.com/v1/geocode";

    private $logradouro;
    private $numero;
    private $municipio;
    private $uf;
    private $cep;
    private $pais;
    private $internacional;
    private $location = false;

    public function __construct(
        $logradouro,
        $numero,
        $municipio,
        $uf,
        $cep,
        $pais,
        $internacional = false
    ) {
        $this->logradouro = $logradouro;
        $this->numero = $this->clearNumber($numero);
        $this->municipio = $municipio;
        $this->uf = $uf;
        $this->cep = $cep;
        $this->pais = $pais;
        $this->internacional = $internacional;
    }

    private function clearNumber($numero)
    {
        $numero = preg_replace('/\./isu', '', $numero);

        if (!preg_match('/^[0-9]+/isu', $numero, $match)) {
            return null;
        }

        $numero = trim($match[0]);

        if (!ctype_digit($numero)) {
            return null;
        }

        return (int) $numero;
    }

    public function getGeolocation($onlyOnline = false)
    {
        try {
            if (!$onlyOnline) {
                $this->getLocalGeolocation();

                if ($this->location === false && !$this->internacional) {
                    $this->getLocalByPostalCode();
                }
            }

            if ($this->location === false) {
                $this->getHereGeolocation();
            }

            if ($this->location === false && !empty($this->municipio) && \config('env.DOUBLE_HERE_USE')) {
                $this->getHereByCity();
            }

            if ($this->location === false && \config('env.GOOGLE_USE')) {
                $this->getGoogleGeoLocation();
            }
        } catch (RequestException $e) {
            throw $e;
        } catch (Exception $e) {
            throw $e;
        }

        if ($this->location !== false &&
            in_array($this->location['from'], ['here', 'google'])
        ) {
            $this->addNewLocal();
        }

        return $this->location;
    }

    // **** LOCAL SEARCH ****
    private function getLocalGeolocation()
    {
        // Procura exato ou por range de numero
        if (!empty($this->numero) && is_numeric($this->numero)) {
            $results = $this->getLocalExactOrRange();

            if ($results) {
                $mock = [
                    'lat' => (float) $results->lat,
                    'lon' => (float) $results->lon,
                ];

                $this->location = $mock;
                $this->location['from'] = 'local-exact-range';

                return $this->location;
            }
        }

        // Procura por cep
        if (empty($this->numero) || !is_numeric($this->numero)) {
            $results = app(Latlon::class)
                ->where('logradouro', "{$this->logradouro}")
                ->where('uf', "{$this->uf}")
                ->where('cep', "{$this->cep}")
                ->select('numero', 'lat', 'lon')
                ->first();

            if ($results) {
                $mock = [
                    'lat' => (float) $results->lat,
                    'lon' => (float) $results->lon,
                ];
                $this->location = $mock;
                $this->location['from'] = 'local-cep';

                return $this->location;
            }
        }

        return $this->location;
    }

    private function getLocalExactOrRange()
    {
        $minimal = ($this->numero - 100) < 0 ? 0 : $this->numero - 100;

        $results = app(Latlon::class)
            ->where('logradouro', "{$this->logradouro}")
            ->whereBetween('numero', [$minimal, $this->numero + 100])
            ->where('uf', "{$this->uf}")
            ->where('cep', "{$this->cep}")
            ->select('numero', 'lat', 'lon');

        $exact = app(Latlon::class)
            ->where('logradouro', "{$this->logradouro}")
            ->where('numero', (int) $this->numero)
            ->where('uf', "{$this->uf}")
            ->where('cep', "{$this->cep}")
            ->unionAll($results)
            ->select('numero', 'lat', 'lon')
            ->first();

        return $exact;
    }

    private function getLocalByPostalCode()
    {
        $results = app(Latlon::class)
            ->where('uf', "{$this->uf}")
            ->where('cep', "{$this->cep}")
            ->select('numero', 'lat', 'lon')
            ->first();

        if ($results) {
            $mock = [
                'lat' => (float) $results->lat,
                'lon' => (float) $results->lon,
            ];

            $this->location = $mock;
            $this->location['from'] = 'local-only-cep';

            return $this->location;
        }

        return $this->location;
    }

    private function addNewLocal()
    {
        $data = [
            'logradouro' => $this->logradouro,
            'numero' => $this->numero,
            'bairro' => null,
            'cidade' => $this->municipio,
            'uf' => $this->uf,
            'cep' => $this->cep,
            'lat' => $this->location['lat'],
            'lon' => $this->location['lon'],
        ];

        Latlon::create($data);
    }

    // **** HERE SEARCH ****
    private function getHereGeolocation()
    {
        $client = new Client();
        try {
            $query = [
                'apiKey' => \config('env.HERE_API_KEY'),
                'q' => $this->getAddress(), // Usando getAddress para formar o parâmetro q
                'lang' => 'pt-BR', // Idioma da resposta
            ];
    
            if ($this->internacional) {
                Arr::forget($query, 'lang'); // Removemos o idioma para buscas internacionais
            }
    
            $query = http_build_query($query);
    
            $url = self::URL_HERE . "?{$query}";
    
            $response = $client->get($url);
    
            $this->handleResponseHere($response);
        } catch (Exception $e) {
            $this->handleError($e);
        }
    
        return $this->location;
    }
    
    private function getHereByCity()
    {
        $client = new Client();
        try {
            // Usando a função getAddress para compor a cidade, estado e país para o parâmetro q
            $address = "{$this->municipio}, {$this->uf}, {$this->pais}";
            $query = [
                'apiKey' => \config('env.HERE_API_KEY'),
                'q' => $address, // Passando o endereço de cidade como parâmetro q
                'lang' => 'pt-BR', // Idioma da resposta
            ];
    
            if ($this->internacional) {
                Arr::forget($query, 'lang'); // Removemos o idioma para buscas internacionais
            }
    
            $query = http_build_query($query);
    
            $url = self::URL_HERE . "?$query";
            $response = $client->get($url);
    
            $this->handleResponseHere($response);
        } catch (Exception $e) {
            $this->handleError($e);
        }
    
        return $this->location;
    }
    
    private function getAddress()
    {
        if ($this->internacional) {
            return "{$this->logradouro}, {$this->numero}, {$this->municipio}, {$this->pais}";
        }
    
        return "{$this->logradouro}, {$this->numero}, {$this->municipio} - {$this->uf}, "
            . substr_replace($this->cep, '-', -3, 0) . ", {$this->pais}";
    }
    
    private function handleResponseHere(ResponseInterface $response)
    {
        $data = $response->getBody()->getContents();
    
        $dados = json_decode($data, true);
    
        if (isset($dados['items']) && !empty($dados['items'])) {
            $dado = $dados['items'][0]; // Primeiro resultado da busca
    
            if (!empty($dado)) {
                $this->location = [
                    'lat' => (float) data_get($dado, 'position.lat'),
                    'lon' => (float) data_get($dado, 'position.lng'),
                    'from' => 'here',
                ];
            }
        } else {
            throw new Exception("Erro HERE: Nenhum resultado encontrado.");
        }
    }
    // **** GOOGLE SEARCH ****
    private function getGoogleGeoLocation()
    {
        $client = new Client();

        try {
            $query = [
                'address' => $this->getAddress(),
                'key' => \config('env.GOOGLE_API_KEY'),
                'components' => 'country:BR',
            ];

            if ($this->internacional) {
                Arr::forget($query, 'components');
            }

            $query = http_build_query($query);

            $url = self::URL_GOOGLE . "?{$query}";

            $response = $client->get($url);

            $this->handleResponse($response);
        } catch (Exception $e) {
            $this->handleError($e);
        }

        return $this->location;
    }

    private function handleResponse(ResponseInterface $response)
    {
        $dados = json_decode($response->getBody(), true);

        if ($dados['status'] == "OK") {
            $results = collect($dados['results']);

            $dado = $results->first();

            $this->location = [
                'lat' => (float) $dado['geometry']['location']['lat'],
                'lon' => (float) $dado['geometry']['location']['lng'],
                'from' => 'google',
            ];

            return;
        }

        $error = 'Erro no google: ';
        
        if (isset($dados['error_message'])) {
            $error = "{$error} {$dados['error_message']}";
        }

        if (isset($dados['status'])) {
            $error = "{$error} {$dados['status']}";
        }

        throw new Exception($error);
    }

    private function handleError($error)
    {
        if (!$this->internacional) {
            throw $error;
        }

        $this->location = false;
    }
}
