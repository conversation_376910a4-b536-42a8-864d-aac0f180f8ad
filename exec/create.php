<?php

require_once __DIR__ . '/../src/vendor/autoload.php';

use Laminas\Code\Generator\ClassGenerator;
use Laminas\Code\Generator\FileGenerator;
use Laminas\Code\Generator\MethodGenerator;

function create($name)
{
    try {
        $name = createClass($name);
        fwrite(STDOUT, sprintf("\033[32m%s\033[0m", 'Crawler \'' . $name . '\' criada com sucesso!' . PHP_EOL));
    } catch (\Exception $e) {
        fwrite(STDERR, sprintf("\033[41m%s\033[0m", $e->getMessage()) . PHP_EOL);
    }
}
function createClass($name)
{
    if (empty($name) || !is_string($name)) {
        throw new \Exception('Parâmetro de entrada inválido');
    }
    $name = preg_replace('/[^a-zA-Z]/isu', '', $name);
    $crawlerFolderPath = __DIR__ . '/../src/Crawler/' . $name;
    $testPath = __DIR__ . '/../src/Tests/' . $name . '.json';
    validateNames($name, $crawlerFolderPath, $testPath);
    createCrawler($name, $crawlerFolderPath);
    createTest($name, $testPath);
    return $name;
}

function createCrawler($name, $crawlerFolderPath)
{
    $class = (new ClassGenerator())
        ->setName($name)
        ->setNamespaceName('App\Crawler\\' . $name)
        ->addUse('App\Crawler\Spider')
        ->setExtendedClass('App\Crawler\Spider')
        ->addMethods([
            MethodGenerator::fromArray([
                'name'       => 'start'
            ]),
            MethodGenerator::fromArray([
                'name'       => 'validateAndSetCrawlerAttributes'
            ]),
        ]);

    $crawler = FileGenerator::fromArray([
        'classes' => [$class]
    ])->generate();

    if (!is_dir($crawlerFolderPath)) {
        mkdir($crawlerFolderPath);
    }
    file_put_contents($crawlerFolderPath . '/' . $name . '.php', $crawler);
}

function createTest($name, $testPath)
{
    file_put_contents($testPath, print_r(json_encode([
        'retry' => '1',
        'source' => $name,
        'param' => new stdClass()
    ], JSON_PRETTY_PRINT), true));
}

function validateNames($name, $crawlerFolderPath, $testPath)
{
    if (empty($name)) {
        throw new \Exception('Nome da fonte inválido');
    }
    $name = ucwords($name);
    if (file_exists($crawlerFolderPath . '/' . $name . '.php')) {
        throw new \Exception('Crawler já existente');
    }
    if (file_exists($testPath)) {
        throw new \Exception('Teste já existente');
    }
}

create($argv[1]);
