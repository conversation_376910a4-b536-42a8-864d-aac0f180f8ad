server {
    listen 443 ssl;

    server_name spine-update.uplexis.com;

    ssl_certificate /etc/nginx/ssl/uplexis.com.bundle.crt;
    ssl_certificate_key /etc/nginx/ssl/uplexis.com.key;

    root /var/www/public;

    index index.php index.html;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
        gzip_static on;

        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    proxy_send_timeout 900s;
    proxy_read_timeout 900s;
    proxy_connect_timeout 900s;
    send_timeout 900s;

    fastcgi_send_timeout 900s;
    fastcgi_read_timeout 900s;

    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_hide_header X-Powered-By;
        fastcgi_hide_header X-Powered-By;

        fastcgi_send_timeout 900s;
        fastcgi_read_timeout 900s;

        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

	error_log /var/log/nginx/spine_update_error.log;
	access_log off;
}
