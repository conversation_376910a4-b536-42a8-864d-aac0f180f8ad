#!/bin/bash

# We need to install dependencies only for Docker
[[ ! -e /.dockerenv ]] && exit 0

apk add --update --no-cache aws-cli

# define which key to use
ACCESS_KEY=$AWS_ACCESS_KEY_ID_prod;
ACCESS_KEY_SECRET=$AWS_SECRET_ACCESS_KEY_prod;

if [[ "$ENV" == "qa" ]]; then
    ACCESS_KEY=$AWS_ACCESS_KEY_ID_qa;
    ACCESS_KEY_SECRET=$AWS_SECRET_ACCESS_KEY_qa;
fi

# Create credentials and config
aws configure set aws_access_key_id ${ACCESS_KEY}
aws configure set aws_secret_access_key ${ACCESS_KEY_SECRET}
aws configure set default.region us-east-1

rm -rf /var/cache/apk/* && rm -rf /tmp/*
