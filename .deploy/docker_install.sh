#!/bin/bash

# We need to install dependencies only for Docker
[[ ! -e /.dockerenv ]] && exit 0

ACCESS_KEY=$AWS_ACCESS_KEY_ID
ACCESS_KEY_SECRET=$AWS_SECRET_ACCESS_KEY

if [[ "$ENV" == "qa" ]]; then
    ACCESS_KEY=$AWS_ACCESS_KEY_ID_QA
    ACCESS_KEY_SECRET=$AWS_SECRET_ACCESS_KEY_QA
fi

apk add --update --no-cache git composer zip nodejs npm

apk add --no-cache python3 py3-pip \
    && pip3 install --upgrade pip \
    && pip3 install awscli

composer self-update --2

npm install -g serverless

serverless config credentials --provider aws --key $ACCESS_KEY --secret $ACCESS_KEY_SECRET --profile serverless --overwrite

rm -rf /var/cache/apk/* && rm -rf /tmp/*
