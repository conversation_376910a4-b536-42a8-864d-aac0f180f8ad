<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use App\Sources\ReceitaFederalPf as Receita;

class ReceitaFederalPfTest extends TestCase
{
    /**
     * Teste de processamento da fonte receita federal pf sem informar cpf.
     *
     * @expectedException \App\Exceptions\ReceitaFederalException
     */
    public function testNotHasCpf()
    {
        $receita = (new Receita);

        $mock = [
            'cpf' => '',
            'nascimento' => fake()->date('d/m/Y'),
            'nome' => fake()->name(),
            'situacao' => fake()->realText(30),
            'data' => fake()->date('d/m/Y'),
            'hora' => fake()->time(),
            'chave' => fake()->regexify('[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}'),
        ];

        $this->assertArrayHasKey('cpf', $mock);

        $receita->process($mock);
    }
}
