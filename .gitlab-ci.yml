stages:
  - sonarqube-check
  - .pre
  - build
  - publish
  - deploy

variables:
  ENV: prod
  KMS: KMS_prod
  AWS_PROFILE: default
  CLUSTER_NAME: uplexis
  SERVICE_NAME: spine-update-service
  ECR_BASE_URL: "342743391564.dkr.ecr.us-east-1.amazonaws.com"
  CLUSTER_QUEUE_NAME: uplexis-ec2
  SERVICE_QUEUE_NAME: svc-${CI_PROJECT_NAME}-supervisor
  SONAR_TOKEN: ${SONAR_TOKEN}
  SONAR_HOST_URL: ${SONAR_HOST_URL}
  SONAR_PROJECT_KEY: ${SONAR_PROJECT_KEY}

before_script:
  - > 
    if [ "$CI_COMMIT_REF_NAME" != "master" ]; then
      ENV="qa";
      KMS="KMS_qa"
      SERVICE_QUEUE_NAME="spine-update-supervisor-service";
      SERVICE_NAME="spine-update-service";
      ECR_BASE_URL="857717895630.dkr.ecr.us-east-1.amazonaws.com";
      export AWS_PROFILE="gitlab-ci-qa";
    fi;
  - ECR_URL="${ECR_BASE_URL}/${CI_PROJECT_NAME}"
  - ECR_QUEUE_URL="${ECR_BASE_URL}/${CI_PROJECT_NAME}-supervisor"
  - echo "Ambiente -> ${ENV}"
  - export PROFILE=${AWS_PROFILE}
  - export KMS=${!KMS}
  - CREDENTIALS=$(bash .deploy/credentials.sh)

sonarqube-check:
  stage: sonarqube-check
  image:
    name: sonarsource/sonar-scanner-cli:5.0
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
  - |
    sonar-scanner \
      -Dsonar.projectKey="${SONAR_PROJECT_KEY}" \
      -Dsonar.qualitygate.wait=true \
      -Dsonar.gitlab.project_id="${CI_PROJECT_ID}" \
      -Dsonar.host.url="${SONAR_HOST_URL}" \
      -Dsonar.login="${SONAR_TOKEN}"
  allow_failure: true
  only:
    - merge_requests
    - develop
  except:
    - master
    - qa

pre:
  stage: .pre
  script:
    - $(aws ecr get-login --no-include-email)
    - docker images && docker ps
  only:
    - master
    - qa

build:
  stage: build
  script:
    - if [ ! -z `docker ps -aq -f ancestor=${ECR_URL}-${ENV}` ]; then docker stop $(docker ps -a -f ancestor=${ECR_URL}-${ENV} -q); else echo "sem processos rodando"; fi
    - if [ ! -z `docker container ls -aq -f ancestor=${ECR_URL}-${ENV}` ]; then docker container rm -f $(docker container ls -a -f ancestor=${ECR_URL}-${ENV} -q); else echo "sem containers no ambiente"; fi
    - if [ ! -z `docker images -aq ${ECR_URL}-${ENV}` ]; then docker image rm -f $(docker images -a ${ECR_URL}-${ENV} -q); else echo "sem images docker no ambiente"; fi
    - eval docker build ${CREDENTIALS} --file=Dockerfile.${ENV} -t ${ECR_URL}-${ENV}:latest --no-cache --pull .
  only:
    - master
    - qa

build_cron:
  stage: build
  script:
    - if [ ! -z `docker ps -aq -f ancestor=${ECR_QUEUE_URL}-${ENV}` ]; then docker stop $(docker ps -a -f ancestor=${ECR_QUEUE_URL}-${ENV} -q); else echo "sem processos rodando"; fi
    - if [ ! -z `docker container ls -aq -f ancestor=${ECR_QUEUE_URL}-${ENV}` ]; then docker container rm -f $(docker container ls -a -f ancestor=${ECR_QUEUE_URL}-${ENV} -q); else echo "sem containers no ambiente"; fi
    - if [ ! -z `docker images -aq ${ECR_QUEUE_URL}-${ENV}` ]; then docker image rm -f $(docker images -a ${ECR_QUEUE_URL}-${ENV} -q); else echo "sem images docker no ambiente"; fi
    - cp ./.supervisor/Dockerfile.${ENV} Dockerfile
    - eval docker build ${CREDENTIALS} -t ${ECR_QUEUE_URL}-${ENV}:latest .
  only:
    - master
    - qa

publish:
  stage: publish
  script:
    - docker push ${ECR_URL}-${ENV}:latest
    - docker push ${ECR_QUEUE_URL}-${ENV}:latest
  only:
    - master
    - qa
deploy:
  stage: deploy
  script: 
    - aws ecs update-service --cluster ${CLUSTER_NAME}-${ENV} --service ${SERVICE_NAME} --force-new-deployment   
    - aws ecs update-service --cluster ${CLUSTER_QUEUE_NAME}-${ENV} --service ${SERVICE_QUEUE_NAME} --force-new-deployment
  only:
    - qa

deploy:
  stage: deploy
  script: 
    - aws ecs update-service --cluster ${ENV}-${CLUSTER_NAME} --service ${SERVICE_NAME} --force-new-deployment   
    - aws ecs update-service --cluster ${CLUSTER_QUEUE_NAME}-${ENV} --service ${SERVICE_QUEUE_NAME} --force-new-deployment
  only:
    - master    
   
