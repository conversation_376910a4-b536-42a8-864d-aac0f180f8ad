stages:
  - deploy

variables:
  ENV: prod

default:
  image: node:18-alpine

before_script:
  - if [ "$CI_COMMIT_REF_NAME" == "qa" ]; then ENV="qa"; fi
  - if [ "$CI_COMMIT_REF_NAME" == "develop" ]; then ENV="hml"; fi
  - ash .deploy/docker_install.sh >> /dev/null
  - npm install -g serverless@3.34.0 --quiet
  - echo "Ambiente -> ${ENV}"

deploy:
  stage: deploy
  script:
    - cp .env.${ENV} .env
    - npm install
    - serverless deploy --stage=${ENV}
  only:
    - qa
    - develop
    - master