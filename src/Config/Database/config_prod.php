<?php

define('PGSQL_HOST', 'db1-replica.uplexis.com.br');
define('PGSQL_HOST_WRITE', 'db1.b.us-east-1.uplexis.com.br');
define('PGSQL_DB_UPMINER', 'upminerdossie');
define('PGSQL_DB_CAPTURA', 'captura3');
define('PGSQL_USER', 'postgres');
define('PGSQL_PASSWORD', 'Andr01d');
define('PGSQL_PORT', 5432);

define('PGSQL_HOST_SOURCES_RESULTS', 'db-captura-lambda.cluster-cwzf4ktimvid.us-east-1.rds.amazonaws.com');
define('PGSQL_DB_SOURCES_RESULTS', 'resultadofontes');
define('PGSQL_PASSWORD_SOURCES_RESULTS', 'bIFlFI8xXnbkysb7zJTq');

define('PGSQL_HOST_UPSEARCH', 'upsearch-prod-instance-1.cwzf4ktimvid.us-east-1.rds.amazonaws.com');
define('PGSQL_DB_UPSEARCH', 'upsearch');
define('PGSQL_PASSWORD_UPSEARCH', 'C6qsm6pK8Mvs2yXhuAo');

define('PGSQL_HOST_BILLING', 'billing-dev-instance-1.cwzf4ktimvid.us-east-1.rds.amazonaws.com');
define('PGSQL_USER_BILLING', 'postgres');
define('PGSQL_DB_BILLING', 'billing');
define('PGSQL_BILLING_PASSWORD', 'Omqu6YHlWUa2avPRxC3B');

define('DB_DATA_ENRICHMENT_HOST', 'data-enrichment-instance-1.cwzf4ktimvid.us-east-1.rds.amazonaws.com');
define('DB_DATA_ENRICHMENT_DATABASE', 'postgres');
define('DB_DATA_ENRICHMENT_USER', 'postgres');
define('DB_DATA_ENRICHMENT_PASSWORD', 'zwI6UTuusy8qmgrNAhgC');

define('ELASTICSEARCH_HOST', 'https://vpc-upminerv5-cafgvo4hwomy5idvkne2ekty2e.us-east-1.es.amazonaws.com');

define(
    'MONGODB_SOURCES_CONNECTION_STRING',
    'mongodb+srv://sources:<EMAIL>/admin?retryWrites=true&w=majority'
);

define('DYNAMODB_TABLE_UPMINER_EXTRATO', 'upminer_extrato_fornecedor');