<?php

define('PGSQL_HOST', 'db1.qa.uplexis.com.br');
define('PGSQL_HOST_WRITE', 'db1.qa.uplexis.com.br');
define('PGSQL_DB_UPMINER', 'upminerdossie');
define('PGSQL_DB_CAPTURA', 'captura3');
define('PGSQL_USER', 'postgres');
define('PGSQL_PASSWORD', 'Andr01d');
define('PGSQL_PORT', 5432);

define('PGSQL_HOST_SOURCES_RESULTS', 'db-captura-lambda-qa-cluster.cluster-cu7goubbl3mv.us-east-1.rds.amazonaws.com');
define('PGSQL_DB_SOURCES_RESULTS', 'resultadofontes');
define('PGSQL_PASSWORD_SOURCES_RESULTS', 'bIFlFI8xXnbkysb7zJTq');

define('PGSQL_HOST_UPSEARCH', 'upsearch-qa.cu7goubbl3mv.us-east-1.rds.amazonaws.com');
define('PGSQL_DB_UPSEARCH', 'upsearch');
define('PGSQL_PASSWORD_UPSEARCH', 'C6qsm6pK8Mvs2yXhuAo');

define('PGSQL_HOST_BILLING', 'billing-dev.cu7goubbl3mv.us-east-1.rds.amazonaws.com');
define('PGSQL_USER_BILLING', 'postgres');
define('PGSQL_DB_BILLING', 'billing');
define('PGSQL_BILLING_PASSWORD', 'Omqu6YHlWUa2avPRxC3B');

define('DB_DATA_ENRICHMENT_HOST', 'localhost');
define('DB_DATA_ENRICHMENT_DATABASE', 'postgres');
define('DB_DATA_ENRICHMENT_USER', 'postgres');
define('DB_DATA_ENRICHMENT_PASSWORD', 'password');

define('ELASTICSEARCH_HOST', 'https://vpc-upminerv5-kgwobtef5z5e32pwvvmsptikpq.us-east-1.es.amazonaws.com');

define(
    'MONGODB_SOURCES_CONNECTION_STRING',
    'mongodb+srv://sources-qa:<EMAIL>/admin?retryWrites=true&w=majority'
);

define('DYNAMODB_TABLE_UPMINER_EXTRATO', 'upminer_extrato_fornecedor_qa');