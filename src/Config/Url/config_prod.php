<?php

define('LAMBDA', 'captura-prd');
define('LAMBDA_UPSEARCH_SOURCES', 'upsearch-sources-prod-search_books_async_escavador');
define('LAMBDA_UPSEARCH_SOURCES_PDF', 'upsearch-sources-prod-search_pdf_escavador');
define('LAMBDA_LUMEN', 'https://lambda.uplexis.com/');
define('LAMBDA_URL', 'https://5un3wv2xo5.execute-api.us-east-1.amazonaws.com/captura/prd');
define('UPSEARCH', 'https://upsearch.uplexis.com');
define('CAPTURA', 'http://captura.qa.uplexis.com.br/');
define('UNITFOUR_URL', CAPTURA . "unitfour/web/");
define('BILHETADOR_URL', 'http://oyqd9zfy4h.execute-api.us-east-1.amazonaws.com/bilhetador/prd');
define('ELASTICSEARCH_LOG_API_URL', 'https://vpc-captura-cgs2zugmdyczp5llvdqeichw2q.us-east-1.es.amazonaws.com');
define('ELASTICSEARCH_API_URL', 'http://elasticsearch-api.uplexis.com.br/elasticsearch_client/public/api.php');
define('TARGET_DATA_API_URL', 'https://api.targetdata-smart.com/api');
define('INFO_SIMPLES_URL', 'https://api.infosimples.com/api/v1/');
define('INFO_SIMPLES_URL_V2', 'https://api.infosimples.com/api/v2/');
define('BIGDATACORP_PEOPLE_URL', 'https://bigboost.bigdatacorp.com.br/peoplev2?AccessToken=');
define('BIGDATACORP_COMPANIES_URL', 'https://bigboost.bigdatacorp.com.br/companies?AccessToken=');
define('DTEC_URL', 'http://dtec-flex.com.br/dtecflexWS/rest/<produto>/search');
define('VEICULOS_CONSUMER_URL', 'http://www.redeintegraconsultas.com.br/consultas/webservice.php?');
define('UNIONSOLUTION_URL', 'http://ws.rgdoautomovel.com.br/');
define('VALIDADOR_RG_URL', 'https://www2.multidocumentos.com.br/servTeledoc/ServTeledoc.asmx?WSDL');
define('CCFFEDERALWEB_URL', 'http://www.federalweb.com.br/webservice.asmx?WSDL');
define('CIALDNB_TESTE_TOKEN', 'https://cialdnb.spearwatch.com/rest-api/v1/foos/1');
define('CIALDNB_GET_TOKEN', "https://cialdnb.spearwatch.com/rest-api/oauth/token");
define('CIALDNB_SCREEN_URL', 'https://cialdnb.spearwatch.com/rest-api/v1/screen');
define('KURRIER_URL', 'http://www.kurierservicos.com.br/kurierconsult/api/processos?');
define('SPC_URL', 'https://servicos.spc.org.br/spc/remoting/ws/consulta/consultaWebService?wsdl');
define('API_CAPTCHA', 'https://api-captcha-v2.uplexis.com');
define('SPINE_UPDATE_URL', 'https://spine-update.uplexis.com/new');
define('PPE_TITULAR_URL', 'https://consumer.bvsnet.com.br/ConsultaPPEWeb/consulta/');
define('ESCAVADOR_API_URL', 'https://api.escavador.com/api/v1/');
define('ESCAVADOR_API_V2_URL', 'https://api.escavador.com/api/v2/');
define('S3_STATIC_BUCKET', 'static.uplexis.com.br');
define('S3_STATIC_URL', 'https://s3.sa-east-1.amazonaws.com/static.uplexis.com.br/');
define('CHATGPT_URL', 'https://uplexis-test.brownarrow.ai');
define('AML_LOGIN_URL', 'https://api.autenticacao.riskmoney.com.br/login');
define('AML_SEARCH_URL', 'https://api.parceiros-esg.amlduediligence.com.br/pessoa');
define('BUFFER_BROWNARROW_URL', 'https://uplexis-test.brownarrow.ai');
