<?php

define('LAMBDA', 'captura-qa');
define('LAMBDA_UPSEARCH_SOURCES', 'upsearch-sources-qa-search_books_async_escavador');
define('LAMBDA_UPSEARCH_SOURCES_PDF', 'upsearch-sources-qa-search_pdf_escavador');
define('LAMBDA_LUMEN', 'https://lambda.qa.uplexis.com.br/');
define('LAMBDA_URL', 'https://seyd9txkdl.execute-api.us-east-1.amazonaws.com/captura/qa');
define('UPSEARCH', 'https://upsearch.qa.uplexis.com.br');
define('CAPTURA', 'http://teste-captura.qa.uplexis.com.br/');
define('UNITFOUR_URL', CAPTURA . "unitfour/web/");
define('BILHETADOR_URL', 'http://z297yzcli9.execute-api.us-east-1.amazonaws.com/bilhetador/qa');
define('ELASTICSEARCH_LOG_API_URL', 'https://vpc-captura-ayi4r7ewjqpdhmsqej7itugcei.us-east-1.es.amazonaws.com');
define('ELASTICSEARCH_API_URL', 'http://elasticsearch-api.uplexis.com.br/elasticsearch_client/public/api.php');
define('TARGET_DATA_API_URL', 'https://api.targetdata-smart.com/api');
define('INFO_SIMPLES_URL', 'https://api.infosimples.com/api/v1/');
define('INFO_SIMPLES_URL_V2', 'https://api.infosimples.com/api/v2/');
define('BIGDATACORP_PEOPLE_URL', 'https://bigboost.bigdatacorp.com.br/peoplev2?AccessToken=');
define('BIGDATACORP_COMPANIES_URL', 'https://bigboost.bigdatacorp.com.br/companies?AccessToken=');
define('DTEC_URL', 'http://dtec-flex.com.br/dtecflexWS/rest/<produto>/search');
define('VEICULOS_CONSUMER_URL', 'http://www.redeintegraconsultas.com.br/consultas/webservice.php?');
define('UNIONSOLUTION_URL', 'http://ws.rgdoautomovel.com.br/');
define('VALIDADOR_RG_URL', 'https://www2.multidocumentos.com.br/servTeledoc/ServTeledoc.asmx?WSDL');
define('CCFFEDERALWEB_URL', 'http://www.federalweb.com.br/webservice.asmx?WSDL');
define('CIALDNB_TESTE_TOKEN', 'https://cialdnb.spearwatch.com/rest-api/v1/foos/1');
define('CIALDNB_GET_TOKEN', "https://cialdnb.spearwatch.com/rest-api/oauth/token");
define('CIALDNB_SCREEN_URL', 'https://cialdnb.spearwatch.com/rest-api/v1/screen');
define('KURRIER_URL', 'http://www.kurierservicos.com.br/kurierconsult/api/processos?');
define('SPC_URL', 'https://servicos.spc.org.br/spc/remoting/ws/consulta/consultaWebService?wsdl');
define('API_CAPTCHA', 'https://api-captcha-v2.qa.uplexis.com.br');
define('SPINE_UPDATE_URL', 'https://spine-update.qa.uplexis.com.br/new');
define('PPE_TITULAR_URL', 'https://consumer.bvsnet.com.br/ConsultaPPEWeb/consulta/');
define('ESCAVADOR_API_URL', 'https://api.escavador.com/api/v1/');
define('ESCAVADOR_API_V2_URL', 'https://api.escavador.com/api/v2/');
define('S3_STATIC_BUCKET', 'estatico.uplexis.com.br');
define('S3_STATIC_URL', 'https://estatico.uplexis.com.br/');
define('CHATGPT_URL', ' https://uplexis-test.brownarrow.ai');
define('AML_LOGIN_URL', 'https://api.autenticacao.riskmoney.com.br/login');
define('AML_SEARCH_URL', 'https://api.parceiros-esg.amlduediligence.com.br/pessoa');
define('BUFFER_BROWNARROW_URL', 'https://uplexis-test3.brownarrow.ai');
