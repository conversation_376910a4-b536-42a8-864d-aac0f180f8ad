import axios from 'axios';
import { HttpException } from 'src/utils/errorHandler';

/**
 * Classe responsável por consultar o Elastic Search
 * 
 * <AUTHOR> <<EMAIL>>
 * @since 21/10/2024
 */
export class ElasticSearchHelper {
    
    /**
     * Faz uma busca no Elasticsearch.
     * 
     * <AUTHOR> <<EMAIL>>
     * @since 01/07/2022
     * @param {string} index - índice do Elasticsearch
     * @param {string} type - tipo de documento
     * @param {object} body - corpo da requisição
     * @return {object} - resultado da busca
     * @throw HttpException - quando não é encontradi resultados
     */
    async query(index: string, type: string, body: object) {
        const results = await axios.post(
            `${process.env.ELASTIC_HOST}/${index}/${type}/_search`,
            body, { 
                headers: { 'Content-Type': 'application/json' }
            }
        );
        if (results) {
            return results.data;
        }

        throw new HttpException('Nenhum resultado', 2);
    }
}