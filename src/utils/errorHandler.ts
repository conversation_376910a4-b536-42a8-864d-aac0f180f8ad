/**
 * Classe de exceção personalizada.
 * 
 * <AUTHOR> <<EMAIL>>
 * @since 01/07/2022
 * @param {string} msg - A mensagem de erro associada à exceção.
 * @param {number} code - O código de status HTTP associado à exceção (padrão: 500).
 * @property {Object} body - O corpo da resposta de erro a ser enviado ao cliente.
 * @property {number} statusCode - O código de status HTTP da exceção.
 */
export class HttpException extends Error {
    body: Object;
    statusCode: number;
    
    constructor(msg: string, code = 500) {
        super(msg);

        this.name = this.constructor.name;

        Error.captureStackTrace(this, this.constructor);
        this.body = { message: msg };
        this.statusCode = code;
    }
}