/**
 * Classe utilitária para funções auxiliares, bem como
 * formatação de data, validação de data, validação de cpf e etc.
 * 
 * <AUTHOR> <<EMAIL>>
 * @since 01/07/2022
 */
export class Utils {
    /**
     * Converte uma data do formato YmdHis para o formato d/m/Y H:i:s.
     * 
     * <AUTHOR> <<EMAIL>>
     * @since 01/07/2022
     * @param {string} data - data no formato YmdHis
     * @return {string} - data formatada
     */
    formata_data_hora(data: string) {
        data = String(data);
        if (data.length == 14) {
            data =
                data.substring(6, 8) +
                '/' +
                data.substring(4, 6) +
                '/' +
                data.substring(0, 4) +
                ' ' +
                data.substring(8, 10) +
                ':' +
                data.substring(10, 12) +
                ':' +
                data.substring(12, 14);
        }

        return data;
    }

    /**
     * Converte uma data do formato Ymd para o formato d/m/Y.
     * 
     * <AUTHOR> <<EMAIL>>
     * @since 01/07/2022
     * @param {string} data - data no formato YmdHis
     * @return {string} - data formatada
     */
    formata_data(data: string) {
        data = String(data);
        if (data.length == 8) {
            data = data.substring(6, 8) + '/' + data.substring(4, 6) + '/' + data.substring(0, 4);
        }

        return data;
    }

    /**
     * Separa o dia, o mês e o ano de uma data e
     * chama a função de validação desses dados.
     * 
     * <AUTHOR> Brito <<EMAIL>>
     * @since 01/07/2022
     * @param {string} data - data no formato Ymd
     * @return {boolean} - validação da data
     */
    validateDate(date: string) {
        const year = parseInt(date.substring(0, 4));
        const month = parseInt(date.substring(4, 6));
        const day = parseInt(date.substring(6, 8));

        return this.checkDate(month, day, year);
    }

    /**
     * Verifica se uma data é válida.
     * 
     * <AUTHOR> Brito <<EMAIL>>
     * @since 01/07/2022
     * @param {number} month - mês
     * @param {number} day - dia
     * @param {number} year - ano
     * @return {boolean} - validação da data
     */
    checkDate(month: number, day: number, year: number) {
        if (month < 1 || month > 12) {
            return false;
        }

        if (year < 1) {
            return false;
        }

        const maxDaysInMonth = this.daysInMonth(month, year);

        if (day < 1 || day > maxDaysInMonth) {
            return false;
        }

        return true;
    }

    /**
     * Retorna a quantidade de dias que um mês teve ou terá com base no ano.
     * 
     * <AUTHOR> Brito <<EMAIL>>
     * @since 01/07/2022
     * @param {number} month - mês
     * @param {number} year - ano
     * @return {number}
     */
    daysInMonth(month: number, year: number) {
        return new Date(year, month, 0).getDate();
    }

    /**
     * Ordena um objeto pelas chaves em ordem alfabética.
     * 
     * <AUTHOR> Brito <<EMAIL>>
     * @since 01/07/2022
     * @param obj - objeto original
     * @return {object} - objeto ordenado
     */
    sortObj(obj: any) {
        return Object.keys(obj)
            .sort()
            .reduce(function (result: any, key) {
                result[key] = obj[key];
                return result;
            }, {});
    }

    /**
     * Valida um cpf.
     * 
     * <AUTHOR> Brito <<EMAIL>>
     * @since 01/07/2022
     * @param {string} recievedCpf - cpf
     * @return {string | boolean} - cpf ou a validação dele
     */
    validateCPF(recievedCpf: string) {
        const cpf = recievedCpf.replace(/[^\d]+/g, '');
        if (cpf == '') return false;
        // Elimina CPFs invalidos conhecidos
        if (
            cpf.length != 11 ||
            cpf == '00000000000' ||
            cpf == '11111111111' ||
            cpf == '22222222222' ||
            cpf == '33333333333' ||
            cpf == '44444444444' ||
            cpf == '55555555555' ||
            cpf == '66666666666' ||
            cpf == '77777777777' ||
            cpf == '88888888888' ||
            cpf == '99999999999'
        )
            return false;
        // Valida 1o digito
        let add = 0;
        for (let i = 0; i < 9; i++) add += parseInt(cpf.charAt(i)) * (10 - i);
        let rev = 11 - (add % 11);
        if (rev == 10 || rev == 11) rev = 0;
        if (rev != parseInt(cpf.charAt(9))) return false;
        // Valida 2o digito
        add = 0;
        for (let i = 0; i < 10; i++) add += parseInt(cpf.charAt(i)) * (11 - i);
        rev = 11 - (add % 11);
        if (rev == 10 || rev == 11) rev = 0;
        if (rev != parseInt(cpf.charAt(10))) return false;
        return cpf;
    }

    /**
     * Converte uma string em um valor booleano.
     * 
     * <AUTHOR> Pereira <<EMAIL>>
     * @since 23/10/2024
     * @param {string} string - string que será convertida
     * @return {boolean} - valor booleano correpondente
     */
    stringToBoolean(string: string) {
        return typeof string == 'string' && (string.toLowerCase() == 'true' || string == '1');
    }
}
