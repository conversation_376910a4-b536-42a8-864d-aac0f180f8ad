import { formatJSONResponse } from '@libs/api-gateway';

/**
 * Classe responsável por formatar respostas do endpoint search.
 * 
 * <AUTHOR> <<EMAIL>>
 * @since 21/10/2024
 */
export class SearchResponseFormatter {
    /**
     * Monta a resposta da listagem de empresas ou pessoas,
     * com validação caso não tenha resultados.
     * 
     * <AUTHOR> <PERSON>rito <<EMAIL>>
     * @since 01/07/2022
     * @param results - resultado da busca do elastic
     * @param {boolean} isArray - se results é um array
     * @return {object}
     */
    simpleFormat(results: any, isArray = true) {
        if (isArray && results.length < 1) {
            return formatJSONResponse({  suggestions: [], msg: 'Nenhum resultado encontrado' });
        }
        return formatJSONResponse({ suggestions: [results] });
    }

    /**
     * Formata a resposta da listagem de pessoas, reduzindo e reordenando 
     * os campos trazidos pelo Elasticsearch
     * 
     * <AUTHOR> <<EMAIL>>
     * @since 01/07/2022
     * @param results - objeto de resposta original
     * @return {object} - objeto formatado 
     */
    formatResultIndividuos(results: any) {
        const data = results.data || results;
        if (!data.length) {
            return formatJSONResponse({  suggestions: [], msg: 'Nenhum resultado encontrado' });
        }
        
        const formated = results.data 
            ? this.getBaseResponse(results)
            : {suggestions: []};

        data.forEach((result: any) => {
            const { cpf, nome, sexo, data_nascimento, mae } = result;
            formated.suggestions.push({
                cpf,
                nome,
                sexo,
                data_nascimento,
                mae,
            });
        });
        return formatJSONResponse(formated);
    }

    /**
     * Formata a resposta da listagem de empresas.
     * 
     * <AUTHOR> Brito <<EMAIL>>
     * @since 01/07/2022
     * @param results - objeto de resposta original
     * @return {object} - objeto formatado
     */
    formatResultCNPJ(results: any) {
        const suggestions = this.getBaseResponse(results);

        Object.keys(results.data).forEach((result) => {
            suggestions.suggestions.push(results.data[result]);
        });
        
        return formatJSONResponse(suggestions);
    }

    /**
     * Retorna o json base da resposta.
     * 
     * <AUTHOR> Pereira <<EMAIL>>
     * @since 21/10/2024
     * @param results - objeto de resposta original
     * @return {object} - objeto formatado
     */
    getBaseResponse(results: any) {
        return {
            per_page: results.perPage,
            page: results.page,
            total_pages: results.totalPages,
            total: results.total,
            suggestions: []
        };
    }
}