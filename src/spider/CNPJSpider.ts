import { SpinePj } from 'src/repositories/spinePj';
import { multiPropertyObj } from 'src/types/multiPropertyObj';
import { HttpException } from 'src/utils/errorHandler';
import { Utils } from 'src/utils/Utils';
import { ElasticSearchHelper } from 'src/utils/ElasticSearchHelper';

/**
 * Classe responsável pelas consultas no Elasticsearch,
 * para encontrar empresas.
 * 
 * <AUTHOR> <<EMAIL>>
 * @since 01/07/2022
 * @property {Object} SpinePj - instância da classe SpinePj
 * @property {Object} utils - instância da classe Utils
 */
export class CNPJSpider {
    spinePj: SpinePj;
    utils: Utils;
    elasticSearch: ElasticSearchHelper;

    /**
     * Define o valor das propriedades.
     * 
     * <AUTHOR> <<EMAIL>>
     * @since 01/07/2022
     */
    constructor() {
        this.spinePj = new SpinePj();
        this.utils = new Utils();
        this.elasticSearch = new ElasticSearchHelper();
    }

    /**
     * Faz uma busca por cnpj no Elasticsearch e retorna o resultado 
     * com alguns dados formatados.
     * 
     * <AUTHOR> Brito <<EMAIL>>
     * @since 01/07/2022
     * @param {string} cnpj - cnpj buscado
     * @return {object} - resultado da busca
     */
    async searchByCnpj(cnpj: string) {
        if (!cnpj) {
            return false;
        }

        const result = await this.get_spine_pj(cnpj);

        const suggestion = {
            razao_social: result.razao_social,
            nome_fantasia: result.nome_fantasia,
            cnpj: result.cnpj,
            matriz: result.matriz ? 'sim' : 'nao',
            logradouro: `${result.logr_tipo} ${result.logr_nome}`,
            numero: result.logr_numero,
            complemento: result.logr_complemento,
            bairro: result.bairro,
            municipio: result.cidade,
            uf: result.uf,
            cep: result.cep,
            data_abertura: this.utils.formata_data(result.data_abertura),
            situacao_cadastral: result.situacao_cadastral,
            data_situacao: this.utils.formata_data(result.data_situacao),
            motivo_situacao: result.motivo_situacao,
            situacao_especial: result.situacao_especial,
            data_especial: result.data_especial ? result.data_especial : null,
            motivo_especial: result.motivo_especial ? result.motivo_especial : null,
            hora_consulta: null,
            data_consulta: null,
        };

        return suggestion;
    }

    /**
     * Faz uma busca por razão social ou cnpj no Elasticsearch.
     * 
     * <AUTHOR> Brito <<EMAIL>>
     * @since 01/07/2022
     * @param {string} query - razão social
     * @param {number} page - número da página
     * @param {number} qt - quantidade de itens por página
     * @param {boolean} isGeneric - se o retorno é para ser no formato strig: "cnpj - (uf) - nome da empresa"
     * @return {object} - resultado da busca
     */
    async searchByNomeOrCnpj(query: string, page: number, qt: number, isGeneric = false) {
        let params: multiPropertyObj = {};

        params = {
            query: {
                bool: {
                    should: [],
                },
            },
        };

        params.size = qt;
        params.from = (page - 1) * qt;

        const busca_razao_social = {
            query_string: {
                query: `${query}*`,
                default_field: 'razao_social',
                minimum_should_match: '100%',
                analyze_wildcard: true,
            },
        };

        const busca_cnpj = {
            query_string: {
                query: `${query}*`,
                default_field: 'cnpj',
                minimum_should_match: '100%',
                analyze_wildcard: true,
            },
        };

        params.query.bool.should.push(busca_razao_social);
        params.query.bool.should.push(busca_cnpj);

        const results: multiPropertyObj = await this.elasticSearch
            .query('spine_pj', 'pj', params);

        const hits = results.hits.hits;

        const suggestions = [];

        const sortedHits = hits.sort((a, b) => a._source.razao_social.localeCompare(b._source.razao_social));

        sortedHits.forEach((hit: multiPropertyObj) => {
            let obj: any;

            if (isGeneric) {
                let data_uf = '';
                if (hit._source.uf) {
                    data_uf = ` - (${hit._source.uf}) `;
                }
                obj = `${hit._source.cnpj}${data_uf} - ${hit._source.razao_social}`;
            } else {
                obj = {
                    cnpj: hit._source.cnpj,
                    uf: hit._source.uf ? hit._source.uf.toUpperCase() : '',
                    razao_social: hit._source.razao_social,
                };
            }

            suggestions.push(obj);
        });

        return { suggestions };
    }

    /**
     * Faz uma busca por razão social no Elasticsearch, podendo ter outros filtros.
     * 
     * <AUTHOR> Brito <<EMAIL>>
     * @since 01/07/2022
     * @param {string} query - razão social
     * @param {number} page - número da página
     * @param {number} qt - quantidade de itens por página
     * @param {string} municipio - 
     * @param {string} nome_fantasia - 
     * @param {string} data_abertura_ini - período inicial de data de abertura
     * @param {string} data_abertura_fim - período final de data de abertura
     * @param {string} uf - estado
     * @param {string} isMain - se a busca é por empresas matrizes
     * @param {string} useFullSearch - se a busca é pelo nome exato
     * @return {object} - resultado da busca
     * @throw HttpException
     */
    async searchByNome(
        query: string,
        page: number,
        qt: number,
        municipio: string,
        nome_fantasia: string,
        data_abertura_ini: string,
        data_abertura_fim: string,
        uf: string,
        isMain: string | null = null,
        useFullSearch: string | null = null
    ) {
        let params: multiPropertyObj = {};

        params = {
            query: {
                bool: {
                    must: [],
                    filter: [],
                },
            },
        };

        params.size = qt;
        params.from = (page - 1) * qt;

        const nameSearched = this.utils.stringToBoolean(useFullSearch) ? `"${query}"` : query;
        const busca_razao_social = {
            query_string: {
                query: `${nameSearched}*`,
                default_field: 'razao_social',
                minimum_should_match: '100%',
                analyze_wildcard: true,
            },
        };
        params.query.bool.must.push(busca_razao_social);

        if (municipio) {
            const busca_municipio = {
                query_string: {
                    query: `${municipio}*`,
                    default_field: 'cidade',
                    minimum_should_match: '99%',
                    analyze_wildcard: true,
                },
            };
            params.query.bool.must.push(busca_municipio);
        }

        if (!(isMain === null || isMain === undefined)) {
            const matriz = this.utils.stringToBoolean(isMain) ? 'True' : 'False';
            const busca_tipo = {
                query_string: {
                    query: matriz,
                    default_field: 'matriz',
                    minimum_should_match: '100%',
                }
            };
            params.query.bool.must.push(busca_tipo);
        }

        if (uf) {
            const busca_uf = {
                query_string: {
                    query: `${uf}*`,
                    default_field: 'uf',
                    minimum_should_match: '100%',
                    analyze_wildcard: true,
                },
            };
            params.query.bool.must.push(busca_uf);
        }

        if (nome_fantasia) {
            const busca_nome_fantasia = {
                query_string: {
                    query: `${nome_fantasia}*`,
                    default_field: 'nome_fantasia',
                    minimum_should_match: '100%',
                    analyze_wildcard: true,
                },
            };
            params.query.bool.must.push(busca_nome_fantasia);
        }

        if (data_abertura_ini || data_abertura_fim) {
            if (data_abertura_ini && !this.utils.validateDate(data_abertura_ini)) {
                throw new HttpException('Data de Abertura inicial inválida', 2);
            }

            if (data_abertura_fim && !this.utils.validateDate(data_abertura_fim)) {
                throw new HttpException('Data de Abertura final inválida', 2);
            }

            const dateFilter: multiPropertyObj = {
                data_abertura: {
                    format: 'yyyyMMdd',
                },
            };

            if (data_abertura_ini) {
                dateFilter.data_abertura.gte = data_abertura_ini;
            }

            if (data_abertura_fim) {
                dateFilter.data_abertura.lte = data_abertura_fim;
            }

            params.query.bool.filter.push({ range: dateFilter });
        }

        const results = await this.elasticSearch.query('spine_pj', 'pj', params);

        const hits = results.hits.hits;

        const suggestions = [];

        const sortedHits = hits.sort((a, b) => a._source.razao_social.localeCompare(b._source.razao_social));

        sortedHits.forEach((hit: multiPropertyObj) => {
            const nome = `${hit._source.razao_social} - ${hit._source.cnpj}`;
            suggestions[nome] = hit._source;
        });

        const total = results.hits.total;
        const totalPages = Math.ceil(total / qt);

        return {
            perPage: qt,
            page,
            totalPages,
            total,
            data: suggestions
        };
    }

    /**
     * Faz uma busca por cnpj no DynamoDB.
     * 
     * <AUTHOR> Brito <<EMAIL>>
     * @since 01/07/2022
     * @param {string} cnpj - cnpj buscado
     * @return {object} - resultado da busca
     * @throw HttpException
     */
    async get_spine_pj(cnpj: string) {
        //verificar se é numérico
        if (!cnpj.match(/\d+/g)) {
            throw new HttpException('cnpj em formato inválido', 2);
        }

        const result = await this.spinePj.find(cnpj);

        if (result.length < 1) {
            throw new HttpException('Nenhum resultado', 2);
        }

        return result;
    }
}
