import { multiPropertyObj } from '../types/multiPropertyObj';
import { ElasticSearchHelper } from 'src/utils/ElasticSearchHelper';

/**
 * Classe responsável pelas consultas no Elasticsearch
 * para encontrar cidades.
 * 
 * <AUTHOR> <<EMAIL>>
 * @since 21/10/2024
 * @property {Object} utils - instância da classe Utils
 */
export class CitySpider {
    elasticSearch: ElasticSearchHelper;

    /**
     * Define o valor das propriedades.
     * 
     * <AUTHOR> <<EMAIL>>
     * @since 21/10/2024
     */
    constructor() {
        this.elasticSearch = new ElasticSearchHelper();
    }

    /**
     * Faz uma busca filtrando por estado no Elasticsearch para obter 
     * as cidades do mesmo.
     * 
     * <AUTHOR> <<EMAIL>>
     * @since 21/10/2024
     * @param {string} uf - UF do estado.
     * @return {string[]} - Cidades do estado.
     */
    async search(uf: string) {
        let field = process.env.APP_ENV == "qa" ? "cidade.keyword" : "cidade.raw";

        const params: multiPropertyObj = {
            _source: ["cidade"],
            size: 0,
            query: {
                constant_score: {
                    filter: {
                        terms: {
                            uf: [uf.toLowerCase()]
                        }
                    }
                }
            },
            aggs: {
                by_cidades: {
                    terms: {
                        field,
                        size: 10000,
                        order: {
                            _term: "asc"
                        }
                    }
                }
            }
        };
        const response: multiPropertyObj = await this.elasticSearch
            .query('spine_pj', 'pj', params);
        
        return response.aggregations.by_cidades.buckets.map(city => city.key);
    }
}
