import { multiPropertyObj } from '../types/multiPropertyObj';
import { HttpException } from '../utils/errorHandler';
import { UFCodes } from '../utils/UFCodes';
import { Utils } from '../utils/Utils';
import { ElasticSearchHelper } from 'src/utils/ElasticSearchHelper';

/**
 * Classe responsável pelas consultas no Elasticsearch,
 * para encontrar pessoas físicas (pf).
 * 
 * <AUTHOR> <<EMAIL>>
 * @since 01/07/2022
 * @property {Object} utils - instância da classe Utils
 */
export class CPFSpider {
    utils: Utils;
    elasticSearch: ElasticSearchHelper;

    /**
     * Define o valor das propriedades.
     * 
     * <AUTHOR> <<EMAIL>>
     * @since 01/07/2022
     */
    constructor() {
        this.utils = new Utils();
        this.elasticSearch = new ElasticSearchHelper();
    }

    /**
     * Faz uma busca por cpf no Elasticsearch e retorna o nome da pessoa.
     * 
     * <AUTHOR> <<EMAIL>>
     * @since 01/07/2022
     * @param {string} cpf - cpf da pessoa buscada
     * @return {object} - resultado da busca
     */
    async searchByCpf(cpf: string) {
        const result = await this.get_spine_pf(cpf);
        return { nome: result.nome };
    }

     /**
     * Faz uma busca pelo nome exato no Elasticsearch, para encontrar homônimos.
     * 
     * <AUTHOR> Brito <<EMAIL>>
     * @since 01/07/2022
     * @param {string} name - nome da pessoa buscada
     * @param {string} uf - estado
     * @return {object} - resultado da busca
     */
    async searchHomonimos(name: string, uf: string | null = null) {
        let params: multiPropertyObj = {};

        params = {
            query: {
                bool: {
                    must: [],
                    filter: [],
                },
            },
            sort: [],
        };

        params.size = 10000;
        params.from = 0;

        const query_string_name = {
            query_string: {
                query: `"${name}"`,
                default_field: 'nome',
                minimum_should_match: '100%',
            },
        };
        params.query.bool.must.push(query_string_name);

        if (uf) {
            const ufCode = this.regionUf(uf);
            if (!ufCode) {
                throw new HttpException('UF Inválida', 2);
            }
            const ufFilter = {
                regexp: {
                    cpf: {
                        value: `[0-9]{8}${ufCode}[0-9]{2}`,
                    },
                },
            };

            params.query.bool.filter.push(ufFilter);
        }

        const sort_order = {
            cpf: {
                order: 'asc',
            },
        };

        params.sort.push(sort_order);

        const results: multiPropertyObj = await this.elasticSearch
            .query('spine_pf', 'pf', params);

        const hits = results.hits.hits;

        const resultArray: string[] = [];

        hits.find((hit: multiPropertyObj) => {
            if (hit._source.nome == name) {
                resultArray.push(hit._source);
            }
        });

        return resultArray;
    }

    /**
     * Faz uma busca por nome no Elasticsearch, podendo ter outros filtros.
     * 
     * <AUTHOR> Brito <<EMAIL>>
     * @since 01/07/2022
     * @param {string} name - nome da pessoa buscada
     * @param {number} page - número da página
     * @param {number} qt - quantidade de itens por página
     * @param {string} uf - estado
     * @param {string} initial_birth_date - período inicial de data de aniversário
     * @param {string} final_birth_date - período final de data de aniversário
     * @param {string} mother_name - nome da mãe
     * @param {string} useFullSearch - se a busca é pelo nome exato
     * @param {string} motherFullSearch - se a busca é pelo nome exato da mãe
     * @return {object} - resultado da busca
     * @throw HttpException
     */
    async searchByNome(
        name: string,
        page: number,
        qt: number,
        uf: string | null = null,
        initial_birth_date: string | null = null,
        final_birth_date: string | null = null,
        mother_name: string | null = null,
        useFullSearch: string | null = null,
        motherFullSearch: string | null = null
    ) {
        let params: multiPropertyObj = {};

        params = {
            query: {
                bool: {
                    must: [],
                    filter: [],
                },
            },
        };

        params.size = qt;
        params.from = (page - 1) * qt;

        const nameSearched = this.utils.stringToBoolean(useFullSearch) ? `"${name}"` : name;
        const query_string_name = {
            query_string: {
                query: `${nameSearched}*`,
                default_field: 'nome',
                minimum_should_match: '100%',
            },
        };
        params.query.bool.must.push(query_string_name);

        const motherName = this.utils.stringToBoolean(motherFullSearch) ? `"${mother_name}"` : mother_name;
        if (mother_name) {
            const query_string_mother_name = {
                query_string: {
                    query: `${motherName}*`,
                    default_field: 'mae',
                    minimum_should_match: '100%',
                },
            };
            params.query.bool.must.push(query_string_mother_name);
        }

        if (initial_birth_date || final_birth_date) {
            if (initial_birth_date && !this.utils.validateDate(initial_birth_date)) {
                throw new HttpException('Data de Nascimento inicial inválida', 2);
            }

            if (final_birth_date && !this.utils.validateDate(final_birth_date)) {
                throw new HttpException('Data de Nascimento final inválida', 2);
            }

            const dateFilter: multiPropertyObj = {
                data_nascimento: {
                    format: 'yyyyMMdd',
                },
            };

            if (initial_birth_date) {
                dateFilter.data_nascimento.gte = initial_birth_date;
            }
            
            if (final_birth_date) {
                dateFilter.data_nascimento.lte = final_birth_date;
            }

            params.query.bool.filter.push({ range: dateFilter });
        }

        if (uf) {
            const ufCode = this.regionUf(uf);
            if (!ufCode) {
                throw new HttpException('UF Inválida', 2);
            }
            const ufFilter = {
                regexp: {
                    cpf: {
                        value: `[0-9]{8}${ufCode}[0-9]{2}`,
                    },
                },
            };

            params.query.bool.filter.push(ufFilter);
        }

        const results: multiPropertyObj = await this.elasticSearch
            .query('spine_pf', 'pf', params);

        const hits = results.hits.hits;
        const sortedHits = hits.sort((a, b) => a._source.nome.localeCompare(b._source.nome));

        const resultsArray = [];
        sortedHits.forEach((hit: multiPropertyObj) => {
            resultsArray.push(hit._source);
        });

        const total = results.hits.total;
        const totalPages = Math.ceil(total / qt);

        return {
            perPage: qt,
            page,
            totalPages,
            total,
            data: resultsArray
        };
    }

    /**
     * Faz uma busca por cpf no Elasticsearch.
     * 
     * <AUTHOR> Brito <<EMAIL>>
     * @since 01/07/2022
     * @param {string} cpf - cpf da pessoa buscada
     * @return {object} - resultado da busca
     * @throw HttpException
     */
    async get_spine_pf(cpf: string) {
        const params = {
            query: {
                match: {
                    cpf: cpf,
                },
            },
        };

        const results: multiPropertyObj = await this.elasticSearch
            .query('spine_pf', 'pf', params);

        const fileteredResults = results.hits.hits[0]._source;
        if (!fileteredResults) {
            throw new HttpException('Nenhum resultado encontrado', 2);
        }

        return fileteredResults;
    }

    /**
     * Através do estado recebido, obtém um número que será usado em uma regex
     * para obter os cpfs de uma determinada região.
     * 
     * <AUTHOR> Brito <<EMAIL>>
     * @since 01/07/2022
     * @param {string} uf - estado
     * @return {number}
     */
    regionUf(uf: string) {
        const upperUf = uf.toUpperCase();

        if (!UFCodes[upperUf]) {
            return false;
        }

        return UFCodes[upperUf];
    }
}
