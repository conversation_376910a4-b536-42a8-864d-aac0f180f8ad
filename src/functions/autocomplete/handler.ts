import { formatJSONResponse } from '@libs/api-gateway';
import { middyfy } from '@libs/lambda';
import { APIGatewayEvent } from 'aws-lambda';
import { CNPJSpider } from 'src/spider/CNPJSpider';
import { CPFSpider } from '../../spider/CPFSpider';

/**
 * Retorna uma lista de empresas ou pessoas, com seus respectivos documentos,
 * com base nos filtros recebidos.
 * 
 * <AUTHOR> <<EMAIL>>
 * @since 01/07/2022
 * @param {APIGatewayEvent} event - evento da api gateway
 * @return {object} - json contendo as sugestões
 */
export const autocomplete = async (event: APIGatewayEvent) => {
    const query = event.queryStringParameters;

    if (!process.env.ELASTIC_HOST) {
        return formatJSONResponse({ message: 'Env não definido!' });
    }

    if (!query || !query.query) {
        return formatJSONResponse({ message: 'nenhum parâmetro recebido' });
    }

    let npage = parseInt(query.pagina);
    let nqtd = parseInt(query.qtd);

    if (isNaN(npage)) {
        npage = 1;
    }

    if (isNaN(nqtd)) {
        nqtd = 20;
    }
    switch (query.field) {
        case 'individuos':
            return individuos(query.query, npage, nqtd);
        case 'upmap_empresas':
            return upmap_empresas(query.query, npage, nqtd, false);
        default:
            return upmap_empresas(query.query, npage, nqtd, true);
    }
};

/**
 * Retorna uma lista de pessoas com base no nome recebido.
 * 
 * <AUTHOR> Brito <<EMAIL>>
 * @since 01/07/2022
 * @param {string} query - nome da pessoa
 * @param {number} page - número da página
 * @param {number} qt - quantidade de itens por página
 * @return {object} - json contendo a lista de pessoas
 */
async function individuos(query: string, page: number, qt: number) {
    try{
        const spider = new CPFSpider();
        const results = await spider.searchByNome(query, page, qt);
        const nomes = [];
        results.data.forEach(hit => {
            nomes.push(`${hit.cpf} - ${hit.nome}`);
        });
        return simpleFormat(nomes);
    }catch(e){
        return formatJSONResponse(e);
    }
}

/**
 * Retorna uma lista de empresas com base no nome ou cnpj recebido.
 * 
 * <AUTHOR> Brito <<EMAIL>>
 * @since 01/07/2022
 * @param {string} query - nome ou cnpj da empresa
 * @param {number} page - número da página
 * @param {number} qt - quantidade de itens por página
 * @param {boolean} generic - formato genérico, string: "cnpj - (uf) - nome da empresa"
 * @return {object} - json contendo a lista de empresas
 */
async function upmap_empresas(query: string, page: number, qt: number, generic: boolean) {

    try{
        const regex = new RegExp(/[.*+?^><=!${}()|[\]\\]/gi);
        const cleanQuery = query.replace(regex, '\\$&');
        const spider = new CNPJSpider();
        const result = await spider.searchByNomeOrCnpj(cleanQuery, page, qt, generic);
        const nomes = result.suggestions;
        return simpleFormat(nomes);
    }catch(e){
        return formatJSONResponse(e);
    }
}

/**
 * Monta a resposta da listagem de empresas/pessoas.
 * 
 * <AUTHOR> Brito <<EMAIL>>
 * @since 01/07/2022
 * @param results - resultado da busca do elastic
 * @return {object}
 */
function simpleFormat(results: any) {
    if (results.length < 1) {
        return formatJSONResponse({ suggestions: ['Nenhum resultado encontrado'] });
    }
    return formatJSONResponse({suggestions: results});
}

export const main = middyfy(autocomplete);
