import { formatJSONResponse } from '@libs/api-gateway';
import { middyfy } from '@libs/lambda';
import { APIGatewayEvent } from 'aws-lambda';
import { CNPJSpider } from 'src/spider/CNPJSpider';
import { CPFSpider } from 'src/spider/CPFSpider';
import { CitySpider } from 'src/spider/CitySpider';
import { multiPropertyObj } from 'src/types/multiPropertyObj';
import { SearchResponseFormatter } from 'src/utils/SearchResponseFormatter';

/**
 * Faz uma busca no Elasticsearch retornando dados de alguma(s) empresa(s)
 * ou pessoa(s) com base nos filtros recebidos.
 * 
 * <AUTHOR> <<EMAIL>>
 * @since 01/07/2022
 * @param {APIGatewayEvent} event - evento da api gateway
 * @return {object} - resposta formatada do Elasticsearch
 */
const search = async (event: APIGatewayEvent) => {
    const query = event.queryStringParameters;
    if (!process.env.ELASTIC_HOST) {
        return formatJSONResponse({ message: 'Env não definido!' });
    }

    let npage = parseInt(query.pagina);
    let nqtd = parseInt(query.qtd);

    if (isNaN(npage)) {
        npage = 1;
    }

    if (isNaN(nqtd)) {
        nqtd = 20;
    }

    if (query.field == 'cities') {
        return searchCities(query);
    }

    const responseFormatter = new SearchResponseFormatter();
    if (query.field == 'individuos') {
        return individuos(query, npage, nqtd, responseFormatter);
    }

    return searchCNPJ(query, npage, nqtd, responseFormatter);
};

/**
 * Retorna uma lista de pessoas buscando no Elasticsearch pelo cpf ou nome.
 * 
 * <AUTHOR> Brito <<EMAIL>>
 * @since 01/07/2022
 * @param {multiPropertyObj} query - filtos recebidos
 * @param {number} page - número da página
 * @param {number} qt - quantidade de itens por página
 * @param {SearchResponseFormatter} responseFormatter - formata as respostas
 * @return {object} - resposta formatada do Elasticsearch
 */
async function individuos(
    query: multiPropertyObj,
    page: number,
    qt: number,
    responseFormatter: SearchResponseFormatter
) {
    try {
        const spider = new CPFSpider();
        if (query.cpf) {
            return responseFormatter.simpleFormat(
                await spider.searchByCpf(query.cpf),
                false
            );
        }
    
        if (!query.query) {
            return formatJSONResponse({ message: 'parâmetro query não recebido' });
        }
    
        if (query.homonimos) {
            return responseFormatter.formatResultIndividuos(
                await spider.searchHomonimos(query.query, query.uf)
            );
        }
    
        const result = await spider.searchByNome(
            query.query,
            page,
            qt,
            query.uf,
            query.data_nascimento_inicio,
            query.data_nascimento_fim,
            query.query2,
            query.useFullSearch,
            query.motherFullSearch
        );
    
        return responseFormatter.formatResultIndividuos(result);
    } catch(e) {
        return formatJSONResponse(e);
    }
}

/**
 * Retorna uma lista de cidades de um estado buscando no Elasticsearch.
 * 
 * <AUTHOR> Pereira <<EMAIL>>
 * @since 21/10/2024
 * @param {multiPropertyObj} query - filtos recebidos.
 * @return {object} - resposta formatada do Elasticsearch.
 */
async function searchCities(query: multiPropertyObj) {
    try {
        const spider = new CitySpider();

        if (!query.uf) {
            return formatJSONResponse({ message: 'parâmetro uf não recebido' });
        }

        const result = await spider.search(
            query.uf
        );

        return formatJSONResponse(result);
    } catch(e){
        return formatJSONResponse(e);
    }
}

/**
 * Retorna uma lista de empresas buscando no Elasticsearch pelo cnpj ou razão social.
 * 
 * <AUTHOR> Brito <<EMAIL>>
 * @since 01/07/2022
 * @param {multiPropertyObj} query - filtos recebidos
 * @param {number} page - número da página
 * @param {number} qt - quantidade de itens por página
 * @param {SearchResponseFormatter} responseFormatter - formata as respostas
 * @return {object} - resposta formatada do Elasticsearch
 */
async function searchCNPJ(
    query: multiPropertyObj,
    page: number,
    qt: number,
    responseFormatter: SearchResponseFormatter
) {
    try {
        const spider = new CNPJSpider();
        if (query.cnpj) {
            return responseFormatter.simpleFormat(
                await spider.searchByCnpj(query.cnpj),
                false
            );
        }

        if (!query.query) {
            return formatJSONResponse({ message: 'parâmetro query não recebido' });
        }

        const regex = new RegExp(/[.*+?^><=!${}()|[\]\\]/gi);
        const cleanQuery = query.query.replace(regex, '\\$&');

        const result = await spider.searchByNome(
            cleanQuery,
            page,
            qt,
            query.municipio,
            query.nome_fantasia,
            query.data_abertura_ini,
            query.data_abertura_fim,
            query.uf,
            query.isMain,
            query.useFullSearch
        );

        return responseFormatter.formatResultCNPJ(result);
    } catch(e){
        return formatJSONResponse(e);
    }
}

export const main = middyfy(search);
