import middy from '@middy/core';
import middyJsonBodyParser from '@middy/http-json-body-parser';

/**
 * Converte um handler de AWS Lambda em um formato compatível com a biblioteca Middy,
 * adicionando o middleware `middyJsonBodyParser` para analisar o corpo das 
 * solicitações HTTP como JSON.
 * 
 * <AUTHOR> <<EMAIL>>
 * @since 01/07/2022
 * @param {Function} handler - O handler principal do AWS Lambda.
 * @returns {Function} - O handler modificado com o middleware `middyJsonBodyParser`.
 */
export const middyfy = (handler) => {
    return middy(handler).use(middyJsonBodyParser());
};
