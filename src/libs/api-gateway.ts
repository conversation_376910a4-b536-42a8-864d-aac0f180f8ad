import type { APIGatewayProxyEvent, APIGatewayProxyResult, Handler } from 'aws-lambda';
import type { FromSchema } from 'json-schema-to-ts';

type ValidatedAPIGatewayProxyEvent<S> = Omit<APIGatewayProxyEvent, 'body'> & { body: FromSchema<S> };
export type ValidatedEventAPIGatewayProxyEvent<S> = Handler<ValidatedAPIGatewayProxyEvent<S>, APIGatewayProxyResult>;

/**
 * Formata o objeto de resposta convertendo em JSON e 
 * definindo o código de status como 200.
 * 
 * <AUTHOR> <<EMAIL>>
 * @since 01/07/2022
 * @param response - objeto de resposta original
 * @return - objeto de resposta formatado
 */
export const formatJSONResponse = (response: Record<string, unknown>) => {
    return {
        statusCode: 200,
        body: JSON.stringify(response),
    };
};
