import { DynamoDB, DynamoDBClientConfig } from '@aws-sdk/client-dynamodb';
import { marshall, unmarshall } from '@aws-sdk/util-dynamodb';

/**
 * Classe responsável pelas consultas na tabela spine_pj do dynamo.
 * 
 * <AUTHOR> <<EMAIL>>
 * @since 01/07/2022
 * @property {DynamoDBClientConfig} clientparams - configurações de conexão com o dynamo
 */
export class SpinePj {
    clientparams: DynamoDBClientConfig;

     /**
     * Define as configurações de conexão com o dynamo.
     * 
     * <AUTHOR> <<EMAIL>>
     * @since 01/07/2022
     */
    constructor() {
        this.clientparams = {
            region: process.env.AWS_REGION_STRING
        };
    }

    /**
     * Faz uma busca um item na tabela spine_pj.
     * 
     * <AUTHOR> <<EMAIL>>
     * @since 01/07/2022
     * @param {string} cnpj - id do item buscado
     */
    async find(cnpj: string) {
        const dynamodb = new DynamoDB(this.clientparams);
        const params = {
            TableName: 'spine_pj',
            Key: marshall({
                cnpj,
            }),
        };

        const { Item } = await dynamodb.getItem(params);

        dynamodb.destroy();

        return unmarshall(Item);
    }
}
