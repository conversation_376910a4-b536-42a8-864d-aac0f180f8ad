<?php

namespace App\Crawler\Arrolamentos;

use App\Crawler\Spider;
use App\Manager\ElasticsearchManager;
use Exception;

class Arrolamentos extends Spider
{
    private const ES_INDEX = "upjuris_diarios";
    private const ES_TYPE = "diario";

    private const TAGS = 'dosp';
    private const LIMITE_DEFAULT = 10;
    private const PALAVRAS_CHAVE = [
        'arrolamentos',
        'heranca',
        'herança',
        'inventário',
        'inventario',
        'partilha',
        'herdeiro',
        'inventariante',
        'interessado'
    ];
    private const BASE_PDF_URL = 'http://dw.uplexis.com.br/docviewer/navigate?file={file_name}.pdf';

    protected function start()
    {
        $processos = $this->getProcessos();
        $results = $this->parseResult($processos);

        return $results;
    }

    /**
     * Retorna os processos
     *
     * @return void
     * <AUTHOR> - 2019-02-18
     * Revisão
     * @version 1.0.0
     */
    public function getProcessos()
    {
        $query = $this->createQuery();

        return (new ElasticsearchManager())->search($query);
    }

    /**
     * Valida os parametros passados (obrigatórios)
     *
     * @return void
     * <AUTHOR> Machado - 2019-02-18
     * Revisão
     * @version 1.0.0
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['termo'])) {
            throw new Exception('Parâmetro termo é obrigatório');
        }

        $this->param['termo'] = $this->formatarTermo($this->param['termo']);
        $this->param['limite'] = $this->param['limite'] ??  self::LIMITE_DEFAULT;
    }

    /**
     * Monta a query que será usada na pesquisa no elasticsearch
     *
     * @return array
     * <AUTHOR> Machado - 2019-02-18
     * Revisão
     * @version 1.0.0
     */
    public function createQuery()
    {
        $params = [];
        $params['index'] = self::ES_INDEX;
        $params['type'] = self::ES_TYPE;
        // $params['body']['track_scores'] = true;
        $params['body']['size'] = $this->param['limite'];
        $params['body']['timeout'] = '4m';
        $params['body']['_source'] = ['caderno', 'pagina', 'data'];

        // Configura a paginação
        if (!empty($this->param['pagina'])) {
            $params['body']['from'] = (($this->param['pagina'] - 1) * $this->param['limite']) + 1;
        }

        $query = '"' . $this->param['termo'] . '"';
        $query .= ' AND ("';
        foreach (self::PALAVRAS_CHAVE as $key => $palavraChave) {
            if ($key != 0) {
                $query .= '" OR "';
            }
            $query .= $palavraChave;
        }
        $query .= '")';

        // Configura o termo de busca
        $params['body']['query']['bool']['must'][] = array(
            'query_string'  =>  array(
                'query' =>  $query,
                'default_field' => 'texto',
            ),
        );

        $params['body']['query']['bool']['must'][] = array(
            'query_string' => array(
                'query' => self::TAGS,
                'default_field' => 'tags',
            )
        );

        // Configura o highlight (corte do texto onde há ocorrencia)
        $params['body']['highlight']['fields'][] = ['texto' => [
            'fragment_size' => 500,
            'number_of_fragments' => 1,
            'highlight_query' => ['bool' => ['must' => ['match' => ['texto' => ['query' => $this->param['termo']]]]]]
        ]];

        return $params;
    }

    /**
     * Resume os resultados do elastic
     *
     * @param array $result
     * @return array
     * <AUTHOR> Machado - 2019-02-18
     * Revisão
     * @version 1.0.0
     */
    public function parseResult($result)
    {
        $results = [];

        $results['search_time'] = $result['took'];
        $results['total_docs'] = $result['hits']['total'];
        $results['total_pagina'] = count($result['hits']['hits']);
        $results['max_result'] = $this->param['limite'] * $this->param['pagina'];
        $results['resultados'] = [];

        foreach ($result['hits']['hits'] as $k => $v) {
            $results['resultados'][] = [
                'pagina' => $v['_source']['pagina'],
                'posicao' => ($this->param['limite'] * $this->param['pagina']) + ($k + 1),
                'relevancia' => '',
                'caderno' => $v['_source']['caderno'],
                'data' => $v['_source']['data'],
                'filename' => '',
                'pdf' => $this->createPdfUrl($v['_source']),
                'texto' => '',
            ];
        }
        return $results;
    }

    public function createPdfUrl($source)
    {
        $dateFormat = explode('/', $source['data']);
        $dateFormat = $dateFormat[2] . $dateFormat[1] . $dateFormat[0];

        $pageFormat = str_pad($source['pagina'], 8, '0', STR_PAD_LEFT);

        $pdfName = $source['caderno'];
        $pdfName .= '_' . $dateFormat;
        $pdfName .= '_' . $pageFormat;

        return str_replace('{file_name}', $pdfName, self::BASE_PDF_URL);
    }

    /**
     * Formata o termo
     *
     * @param string $termo
     * @return string
     * <AUTHOR>
     * Revisão
     * @version 1.0.0
     */
    private function formatarTermo($termo)
    {
        $termo = trim($termo);

        $termo = preg_replace('!\s+!', ' ', $termo);

        $termo = $this->escapeElasticSearchTermo($termo);

        return $termo;
    }

    /**
     * Remove caracteres proibidos/reservados do elastic
     *
     * @param string $termo
     * @return string
     * <AUTHOR>
     * Revisão
     * @version 1.0.0
     */
    private function escapeElasticSearchTermo($termo)
    {
        $reservedChars = array(
            '\\', '+', '-', '=', '&&', '||', '!', '(', ')', '{', '}', '[', ']', '^', '"', '~', '*', '?', ':', '/'
        );
        $prohibitedChars = array('>', '<');

        foreach ($prohibitedChars as $char) {
            $termo = str_replace($char, '', $termo);
        }
        foreach ($reservedChars as $char) {
            $termo = str_replace($char, '\\' . $char, $termo);
        }
        return $termo;
    }
}
