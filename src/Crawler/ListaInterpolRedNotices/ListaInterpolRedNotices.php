<?php

namespace App\Crawler\ListaInterpolRedNotices;

use App\Crawler\Spider;
use Exception;

class ListaInterpolRedNotices extends Spider
{
    private const BASE_URL = 'https://ws-public.interpol.int/notices/v1/red?';
    private const SITE = "https://www.interpol.int/How-we-work/Notices/View-Red-Notices";

    public function start()
    {
        $results = $this->makeRequest();
        $results = $this->setCountryByIsoListAndParseData($results);
        return $results;
    }

    /**
     * Faz a requisição e retorna os dados da API.
     * <AUTHOR>
     * @return array $results
     */
    private function makeRequest()
    {
        $names = explode(" ", $this->param['name']);
        $countNames = count($names);
        $results = [];

        for ($i = 1; $i < $countNames; $i++) {
            $params = [
                'name' => implode(" ", array_slice($names, 0, $i)),
                'forename' => implode(" ", array_slice($names, $i, $countNames))
            ];

            $params = http_build_query($params);

            $json = json_decode($this->getResponse(self::BASE_URL . $params), true);

            if (empty($json['_embedded']['notices'])) {
                $params = [
                    'name' => implode(" ", array_slice($names, $i, $countNames)),
                    'forename' => implode(" ", array_slice($names, 0, $i))
                ];

                $params = http_build_query($params);

                $json = json_decode($this->getResponse(self::BASE_URL . $params), true);

                if (empty($json['_embedded']['notices'])) {
                    continue;
                }
            }

            foreach ($json['_embedded']['notices'] as $result) {
                $results[] = json_decode($this->getResponse($result['_links']['self']['href']), true);
            }

            // break;
        }

        if (empty($results)) {
            throw new Exception('Nenhum resultado encontrado!', 2);
        }

        return $results;
    }



    public function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['name'])) {
            throw new Exception('Parâmetro Inválido', 6);
        }
    }

    /**
     *  A relação de países pode ser encontrada neste link: https://pt.wikipedia.org/wiki/ISO_3166-2
     *
     * @param $results
     * @return array
     */
    public function setCountryByIsoListAndParseData($results)
    {
        foreach ($results as &$result) {
            if (!empty($result['nationalities'])) {
                foreach ($result['nationalities'] as $nationality) {
                    $result['nationality'][] = $this->getCountry($nationality);
                }
            }

            if ($result['sex_id'] == 'M') {
                $result['sex'] = 'Male';
            } else {
                $result['sex'] = 'Female';
            }

            $age = date_diff(date_create($result['date_of_birth']), date_create(date("Y-m-d")));

            $result['date_of_birth'] = date('d/m/Y', strtotime($result['date_of_birth']));
            $result['age'] = $age->format('%y');

            if (!empty($result['languages_spoken_ids'])) {
                foreach ($result['languages_spoken_ids'] as $language) {
                    $result['language'][] = $this->getLanguage($language);
                }
            }
        }

        return $results;
    }

    /**
     *  Retorna o país pelo indice.
     *
     * @param $country
     * @return string
     */
    public function getCountry($country)
    {
        $countries = [
            'AF' => 'Afghanistan',
            'AX' => 'Aland Islands',
            'AL' => 'Albania',
            'DZ' => 'Algeria',
            'AS' => 'American Samoa',
            'AD' => 'Andorra',
            'AO' => 'Angola',
            'AI' => 'Anguilla',
            'AQ' => 'Antarctica',
            'AG' => 'Antigua And Barbuda',
            'AR' => 'Argentina',
            'AM' => 'Armenia',
            'AW' => 'Aruba',
            'AU' => 'Australia',
            'AT' => 'Austria',
            'AZ' => 'Azerbaijan',
            'BS' => 'Bahamas',
            'BH' => 'Bahrain',
            'BD' => 'Bangladesh',
            'BB' => 'Barbados',
            'BY' => 'Belarus',
            'BE' => 'Belgium',
            'BZ' => 'Belize',
            'BJ' => 'Benin',
            'BM' => 'Bermuda',
            'BT' => 'Bhutan',
            'BO' => 'Bolivia',
            'BA' => 'Bosnia And Herzegovina',
            'BW' => 'Botswana',
            'BV' => 'Bouvet Island',
            'BR' => 'Brazil',
            'IO' => 'British Indian Ocean Territory',
            'BN' => 'Brunei Darussalam',
            'BG' => 'Bulgaria',
            'BF' => 'Burkina Faso',
            'BI' => 'Burundi',
            'KH' => 'Cambodia',
            'CM' => 'Cameroon',
            'CA' => 'Canada',
            'CV' => 'Cape Verde',
            'KY' => 'Cayman Islands',
            'CF' => 'Central African Republic',
            'TD' => 'Chad',
            'CL' => 'Chile',
            'CN' => 'China',
            'CX' => 'Christmas Island',
            'CC' => 'Cocos (Keeling) Islands',
            'CO' => 'Colombia',
            'KM' => 'Comoros',
            'CG' => 'Congo',
            'CD' => 'Congo, Democratic Republic',
            'CK' => 'Cook Islands',
            'CR' => 'Costa Rica',
            'CI' => 'Cote D\'Ivoire',
            'HR' => 'Croatia',
            'CU' => 'Cuba',
            'CY' => 'Cyprus',
            'CZ' => 'Czech Republic',
            'DK' => 'Denmark',
            'DJ' => 'Djibouti',
            'DM' => 'Dominica',
            'DO' => 'Dominican Republic',
            'EC' => 'Ecuador',
            'EG' => 'Egypt',
            'SV' => 'El Salvador',
            'GQ' => 'Equatorial Guinea',
            'ER' => 'Eritrea',
            'EE' => 'Estonia',
            'ET' => 'Ethiopia',
            'FK' => 'Falkland Islands (Malvinas)',
            'FO' => 'Faroe Islands',
            'FJ' => 'Fiji',
            'FI' => 'Finland',
            'FR' => 'France',
            'GF' => 'French Guiana',
            'PF' => 'French Polynesia',
            'TF' => 'French Southern Territories',
            'GA' => 'Gabon',
            'GM' => 'Gambia',
            'GE' => 'Georgia',
            'DE' => 'Germany',
            'GH' => 'Ghana',
            'GI' => 'Gibraltar',
            'GR' => 'Greece',
            'GL' => 'Greenland',
            'GD' => 'Grenada',
            'GP' => 'Guadeloupe',
            'GU' => 'Guam',
            'GT' => 'Guatemala',
            'GG' => 'Guernsey',
            'GN' => 'Guinea',
            'GW' => 'Guinea-Bissau',
            'GY' => 'Guyana',
            'HT' => 'Haiti',
            'HM' => 'Heard Island & Mcdonald Islands',
            'VA' => 'Holy See (Vatican City State)',
            'HN' => 'Honduras',
            'HK' => 'Hong Kong',
            'HU' => 'Hungary',
            'IS' => 'Iceland',
            'IN' => 'India',
            'ID' => 'Indonesia',
            'IR' => 'Iran, Islamic Republic Of',
            'IQ' => 'Iraq',
            'IE' => 'Ireland',
            'IM' => 'Isle Of Man',
            'IL' => 'Israel',
            'IT' => 'Italy',
            'JM' => 'Jamaica',
            'JP' => 'Japan',
            'JE' => 'Jersey',
            'JO' => 'Jordan',
            'KZ' => 'Kazakhstan',
            'KE' => 'Kenya',
            'KI' => 'Kiribati',
            'KR' => 'Korea',
            'KW' => 'Kuwait',
            'KG' => 'Kyrgyzstan',
            'LA' => 'Lao People\'s Democratic Republic',
            'LV' => 'Latvia',
            'LB' => 'Lebanon',
            'LS' => 'Lesotho',
            'LR' => 'Liberia',
            'LY' => 'Libyan Arab Jamahiriya',
            'LI' => 'Liechtenstein',
            'LT' => 'Lithuania',
            'LU' => 'Luxembourg',
            'MO' => 'Macao',
            'MK' => 'Macedonia',
            'MG' => 'Madagascar',
            'MW' => 'Malawi',
            'MY' => 'Malaysia',
            'MV' => 'Maldives',
            'ML' => 'Mali',
            'MT' => 'Malta',
            'MH' => 'Marshall Islands',
            'MQ' => 'Martinique',
            'MR' => 'Mauritania',
            'MU' => 'Mauritius',
            'YT' => 'Mayotte',
            'MX' => 'Mexico',
            'FM' => 'Micronesia, Federated States Of',
            'MD' => 'Moldova',
            'MC' => 'Monaco',
            'MN' => 'Mongolia',
            'ME' => 'Montenegro',
            'MS' => 'Montserrat',
            'MA' => 'Morocco',
            'MZ' => 'Mozambique',
            'MM' => 'Myanmar',
            'NA' => 'Namibia',
            'NR' => 'Nauru',
            'NP' => 'Nepal',
            'NL' => 'Netherlands',
            'AN' => 'Netherlands Antilles',
            'NC' => 'New Caledonia',
            'NZ' => 'New Zealand',
            'NI' => 'Nicaragua',
            'NE' => 'Niger',
            'NG' => 'Nigeria',
            'NU' => 'Niue',
            'NF' => 'Norfolk Island',
            'MP' => 'Northern Mariana Islands',
            'NO' => 'Norway',
            'OM' => 'Oman',
            'PK' => 'Pakistan',
            'PW' => 'Palau',
            'PS' => 'Palestinian Territory, Occupied',
            'PA' => 'Panama',
            'PG' => 'Papua New Guinea',
            'PY' => 'Paraguay',
            'PE' => 'Peru',
            'PH' => 'Philippines',
            'PN' => 'Pitcairn',
            'PL' => 'Poland',
            'PT' => 'Portugal',
            'PR' => 'Puerto Rico',
            'QA' => 'Qatar',
            'RE' => 'Reunion',
            'RO' => 'Romania',
            'RU' => 'Russian Federation',
            'RW' => 'Rwanda',
            'BL' => 'Saint Barthelemy',
            'SH' => 'Saint Helena',
            'KN' => 'Saint Kitts And Nevis',
            'LC' => 'Saint Lucia',
            'MF' => 'Saint Martin',
            'PM' => 'Saint Pierre And Miquelon',
            'VC' => 'Saint Vincent And Grenadines',
            'WS' => 'Samoa',
            'SM' => 'San Marino',
            'ST' => 'Sao Tome And Principe',
            'SA' => 'Saudi Arabia',
            'SN' => 'Senegal',
            'RS' => 'Serbia',
            'SC' => 'Seychelles',
            'SL' => 'Sierra Leone',
            'SG' => 'Singapore',
            'SK' => 'Slovakia',
            'SI' => 'Slovenia',
            'SB' => 'Solomon Islands',
            'SO' => 'Somalia',
            'ZA' => 'South Africa',
            'GS' => 'South Georgia And Sandwich Isl.',
            'ES' => 'Spain',
            'LK' => 'Sri Lanka',
            'SD' => 'Sudan',
            'SR' => 'Suriname',
            'SJ' => 'Svalbard And Jan Mayen',
            'SZ' => 'Swaziland',
            'SE' => 'Sweden',
            'CH' => 'Switzerland',
            'SY' => 'Syrian Arab Republic',
            'TW' => 'Taiwan',
            'TJ' => 'Tajikistan',
            'TZ' => 'Tanzania',
            'TH' => 'Thailand',
            'TL' => 'Timor-Leste',
            'TG' => 'Togo',
            'TK' => 'Tokelau',
            'TO' => 'Tonga',
            'TT' => 'Trinidad And Tobago',
            'TN' => 'Tunisia',
            'TR' => 'Turkey',
            'TM' => 'Turkmenistan',
            'TC' => 'Turks And Caicos Islands',
            'TV' => 'Tuvalu',
            'UG' => 'Uganda',
            'UA' => 'Ukraine',
            'AE' => 'United Arab Emirates',
            'GB' => 'United Kingdom',
            'US' => 'United States',
            'UM' => 'United States Outlying Islands',
            'UY' => 'Uruguay',
            'UZ' => 'Uzbekistan',
            'VU' => 'Vanuatu',
            'VE' => 'Venezuela',
            'VN' => 'Viet Nam',
            'VG' => 'Virgin Islands, British',
            'VI' => 'Virgin Islands, U.S.',
            'WF' => 'Wallis And Futuna',
            'EH' => 'Western Sahara',
            'YE' => 'Yemen',
            'ZM' => 'Zambia',
            'ZW' => 'Zimbabwe',
        ];

        return $countries[$country];
    }

    /**
     *  Retorna o idioma pelo indice.
     *
     * @param $language
     * @return string
     */
    public function getLanguage($language)
    {
        $languages = [
            "ab" => "Abkhazian",
            "abk" => "Abkhazian",
            "aa" => "Afar",
            "aar" => "Afar",
            "af" => "Afrikaans",
            "afr" => "Afrikaans",
            "sq" => "Albanian",
            "alb" => "Albanian",
            "sqi" => "Albanian",
            "am" => "Amharic",
            "amh" => "Amharic",
            "ar" => "Arabic",
            "ara" => "Arabic",
            "an" => "Aragonese",
            "arg" => "Aragonese",
            "hy" => "Armenian",
            "arm" => "Armenian",
            "hye" => "Armenian",
            "as" => "Assamese",
            "asm" => "Assamese",
            "ae" => "Avestan",
            "ave" => "Avestan",
            "ay" => "Aymara",
            "aym" => "Aymara",
            "az" => "Azerbaijani",
            "aze" => "Azerbaijani",
            "ba" => "Bashkir",
            "bak" => "Bashkir",
            "eu" => "Basque",
            "baq" => "Basque",
            "eus" => "Basque",
            "be" => "Belarusian",
            "bel" => "Belarusian",
            "bn" => "Bengali",
            "ben" => "Bengali",
            "bh" => "Bihari",
            "bih" => "Bihari",
            "bi" => "Bislama",
            "bis" => "Bislama",
            "bs" => "Bosnian",
            "bos" => "Bosnian",
            "br" => "Breton",
            "bre" => "Breton",
            "bg" => "Bulgarian",
            "bul" => "Bulgarian",
            "my" => "Burmese",
            "bur" => "Burmese",
            "mya" => "Burmese",
            "ca" => "Catalan",
            "cat" => "Catalan",
            "ch" => "Chamorro",
            "cha" => "Chamorro",
            "ce" => "Chechen",
            "che" => "Chechen",
            "zh" => "Chinese",
            "chi" => "Chinese",
            "zho" => "Chinese",
            "cu" => "Church Slavic, Slavonic, Old Bulgarian",
            "chu" => "Church Slavic, Slavonic, Old Bulgarian",
            "cv" => "Chuvash",
            "chv" => "Chuvash",
            "kw" => "Cornish",
            "cor" => "Cornish",
            "co" => "Corsican",
            "cos" => "Corsican",
            "hr" => "Croatian",
            "hrv" => "Croatian",
            "scr" => "Croatian",
            "cs" => "Czech",
            "cze" => "Czech",
            "ces" => "Czech",
            "da" => "Danish",
            "dan" => "Danish",
            "dv" => "Divehi, Dhivehi, Maldivian",
            "div" => "Divehi, Dhivehi, Maldivian",
            "nl" => "Dutch",
            "dut" => "Dutch",
            "nld" => "Dutch",
            "dz" => "Dzongkha",
            "dzo" => "Dzongkha",
            "en" => "English",
            "eng" => "English",
            "eo" => "Esperanto",
            "epo" => "Esperanto",
            "et" => "Estonian",
            "est" => "Estonian",
            "fo" => "Faroese",
            "fao" => "Faroese",
            "fj" => "Fijian",
            "fij" => "Fijian",
            "fi" => "Finnish",
            "fin" => "Finnish",
            "fr" => "French",
            "fre" => "French",
            "fra" => "French",
            "gd" => "Gaelic, Scottish Gaelic",
            "gla" => "Gaelic, Scottish Gaelic",
            "gl" => "Galician",
            "glg" => "Galician",
            "ka" => "Georgian",
            "geo" => "Georgian",
            "kat" => "Georgian",
            "de" => "German",
            "ger" => "German",
            "deu" => "German",
            "el" => "Greek, Modern",
            "gre" => "Greek, Modern",
            "ell" => "Greek, Modern",
            "gn" => "Guarani",
            "grn" => "Guarani",
            "gu" => "Gujarati",
            "guj" => "Gujarati",
            "ht" => "Haitian, Haitian Creole",
            "hat" => "Haitian, Haitian Creole",
            "ha" => "Hausa",
            "hau" => "Hausa",
            "he" => "Hebrew",
            "heb" => "Hebrew",
            "hz" => "Herero",
            "her" => "Herero",
            "hi" => "Hindi",
            "hin" => "Hindi",
            "ho" => "Hiri Motu",
            "hmo" => "Hiri Motu",
            "hu" => "Hungarian",
            "hun" => "Hungarian",
            "is" => "Icelandic",
            "ice" => "Icelandic",
            "isl" => "Icelandic",
            "io" => "Ido",
            "ido" => "Ido",
            "id" => "Indonesian",
            "ind" => "Indonesian",
            "ia" => "Interlingua",
            "ina" => "Interlingua",
            "ie" => "Interlingue",
            "ile" => "Interlingue",
            "iu" => "Inuktitut",
            "iku" => "Inuktitut",
            "ik" => "Inupiaq",
            "ipk" => "Inupiaq",
            "ga" => "Irish",
            "gle" => "Irish",
            "it" => "Italian",
            "ita" => "Italian",
            "ja" => "Japanese",
            "jpn" => "Japanese",
            "jv" => "Javanese",
            "jav" => "Javanese",
            "kl" => "Kalaallisut",
            "kal" => "Kalaallisut",
            "kn" => "Kannada",
            "kan" => "Kannada",
            "ks" => "Kashmiri",
            "kas" => "Kashmiri",
            "kk" => "Kazakh",
            "kaz" => "Kazakh",
            "km" => "Khmer",
            "khm" => "Khmer",
            "ki" => "Kikuyu, Gikuyu",
            "kik" => "Kikuyu, Gikuyu",
            "rw" => "Kinyarwanda",
            "kin" => "Kinyarwanda",
            "ky" => "Kirghiz",
            "kir" => "Kirghiz",
            "kv" => "Komi",
            "kom" => "Komi",
            "ko" => "Korean",
            "kor" => "Korean",
            "kj" => "Kuanyama, Kwanyama",
            "kua" => "Kuanyama, Kwanyama",
            "ku" => "Kurdish",
            "kur" => "Kurdish",
            "lo" => "Lao",
            "lao" => "Lao",
            "la" => "Latin",
            "lat" => "Latin",
            "lv" => "Latvian",
            "lav" => "Latvian",
            "li" => "Limburgan, Limburger, Limburgish",
            "lim" => "Limburgan, Limburger, Limburgish",
            "ln" => "Lingala",
            "lin" => "Lingala",
            "lt" => "Lithuanian",
            "lit" => "Lithuanian",
            "lb" => "Luxembourgish, Letzeburgesch",
            "ltz" => "Luxembourgish, Letzeburgesch",
            "mk" => "Macedonian",
            "mac" => "Macedonian",
            "mkd" => "Macedonian",
            "mg" => "Malagasy",
            "mlg" => "Malagasy",
            "ms" => "Malay",
            "may" => "Malay",
            "msa" => "Malay",
            "ml" => "Malayalam",
            "mal" => "Malayalam",
            "mt" => "Maltese",
            "mlt" => "Maltese",
            "gv" => "Manx",
            "glv" => "Manx",
            "mi" => "Maori",
            "mao" => "Maori",
            "mri" => "Maori",
            "mr" => "Marathi",
            "mar" => "Marathi",
            "mh" => "Marshallese",
            "mah" => "Marshallese",
            "mo" => "Moldavian",
            "mol" => "Moldavian",
            "mn" => "Mongolian",
            "mon" => "Mongolian",
            "na" => "Nauru",
            "nau" => "Nauru",
            "nv" => "Navaho, Navajo",
            "nav" => "Navaho, Navajo",
            "nd" => "Ndebele, North",
            "nde" => "Ndebele, North",
            "nr" => "Ndebele, South",
            "nbl" => "Ndebele, South",
            "ng" => "Ndonga",
            "ndo" => "Ndonga",
            "ne" => "Nepali",
            "nep" => "Nepali",
            "se" => "Northern Sami",
            "sme" => "Northern Sami",
            "no" => "Norwegian",
            "nor" => "Norwegian",
            "nb" => "Norwegian Bokmal",
            "nob" => "Norwegian Bokmal",
            "nn" => "Norwegian Nynorsk",
            "nno" => "Norwegian Nynorsk",
            "ny" => "Nyanja, Chichewa, Chewa",
            "nya" => "Nyanja, Chichewa, Chewa",
            "oc" => "Occitan, Provencal",
            "oci" => "Occitan, Provencal",
            "or" => "Oriya",
            "ori" => "Oriya",
            "om" => "Oromo",
            "orm" => "Oromo",
            "os" => "Ossetian, Ossetic",
            "oss" => "Ossetian, Ossetic",
            "pi" => "Pali",
            "pli" => "Pali",
            "pa" => "Panjabi",
            "pan" => "Panjabi",
            "fa" => "Persian",
            "per" => "Persian",
            "fas" => "Persian",
            "pl" => "Polish",
            "pol" => "Polish",
            "pt" => "Portuguese",
            "por" => "Portuguese",
            "ps" => "Pushto",
            "pus" => "Pushto",
            "qu" => "Quechua",
            "que" => "Quechua",
            "rm" => "Raeto-Romance",
            "roh" => "Raeto-Romance",
            "ro" => "Romanian",
            "rum" => "Romanian",
            "ron" => "Romanian",
            "rn" => "Rundi",
            "run" => "Rundi",
            "ru" => "Russian",
            "rus" => "Russian",
            "sm" => "Samoan",
            "smo" => "Samoan",
            "sg" => "Sango",
            "sag" => "Sango",
            "sa" => "Sanskrit",
            "san" => "Sanskrit",
            "sc" => "Sardinian",
            "srd" => "Sardinian",
            "sr" => "Serbian",
            "scc" => "Serbian",
            "srp" => "Serbian",
            "sn" => "Shona",
            "sna" => "Shona",
            "ii" => "Sichuan Yi",
            "iii" => "Sichuan Yi",
            "sd" => "Sindhi",
            "snd" => "Sindhi",
            "si" => "Sinhala, Sinhalese",
            "sin" => "Sinhala, Sinhalese",
            "sk" => "Slovak",
            "slo" => "Slovak",
            "slk" => "Slovak",
            "sl" => "Slovenian",
            "slv" => "Slovenian",
            "so" => "Somali",
            "som" => "Somali",
            "st" => "Sotho, Southern",
            "sot" => "Sotho, Southern",
            "es" => "Spanish, Castilian",
            "spa" => "Spanish, Castilian",
            "su" => "Sundanese",
            "sun" => "Sundanese",
            "sw" => "Swahili",
            "swa" => "Swahili",
            "ss" => "Swati",
            "ssw" => "Swati",
            "sv" => "Swedish",
            "swe" => "Swedish",
            "tl" => "Tagalog",
            "tgl" => "Tagalog",
            "ty" => "Tahitian",
            "tah" => "Tahitian",
            "tg" => "Tajik",
            "tgk" => "Tajik",
            "ta" => "Tamil",
            "tam" => "Tamil",
            "tt" => "Tatar",
            "tat" => "Tatar",
            "te" => "Telugu",
            "tel" => "Telugu",
            "th" => "Thai",
            "tha" => "Thai",
            "bo" => "Tibetan",
            "tib" => "Tibetan",
            "bod" => "Tibetan",
            "ti" => "Tigrinya",
            "tir" => "Tigrinya",
            "to" => "Tonga",
            "ton" => "Tonga",
            "ts" => "Tsonga",
            "tso" => "Tsonga",
            "tn" => "Tswana",
            "tsn" => "Tswana",
            "tr" => "Turkish",
            "tur" => "Turkish",
            "tk" => "Turkmen",
            "tuk" => "Turkmen",
            "tw" => "Twi",
            "twi" => "Twi",
            "ug" => "Uighur",
            "uig" => "Uighur",
            "uk" => "Ukrainian",
            "ukr" => "Ukrainian",
            "ur" => "Urdu",
            "urd" => "Urdu",
            "uz" => "Uzbek",
            "uzb" => "Uzbek",
            "vi" => "Vietnamese",
            "vie" => "Vietnamese",
            "vo" => "Volapuk",
            "vol" => "Volapuk",
            "wa" => "Walloon",
            "wln" => "Walloon",
            "cy" => "Welsh",
            "wel" => "Welsh",
            "cym" => "Welsh",
            "fy" => "Western Frisian",
            "fry" => "Western Frisian",
            "wo" => "Wolof",
            "wol" => "Wolof",
            "xh" => "Xhosa",
            "xho" => "Xhosa",
            "yi" => "Yiddish",
            "yid" => "Yiddish",
            "yo" => "Yoruba",
            "yor" => "Yoruba",
            "za" => "Zhuang, Chuang",
            "zha" => "Zhuang, Chuang",
            "zu" => "Zulu",
            "zul" => "Zulu"
        ];

        return $languages[strtolower($language)];
    }
}
