<?php

namespace App\Crawler\CertidaoAcoesExecucoesCiveisCriminaisTrf;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

/**
 * Certidão de Ações e Execuções Cíveis e Criminais - TRF2
 */
class CertidaoAcoesExecucoesCiveisCriminaisTrf extends Spider
{
    private const BASE_URL = "https://certidoes.trf2.jus.br/certidoes/#/";
    private const REQUEST_URL = "https://certidoes.trf2.jus.br/certidoes/rest/certidao/solicitar";
    private const CAPTCHA_TOKEN = "6Ld5lNwiAAAAAHsHbvz5CobDk1v8R3Q_nS0eol7R";
    private const PATH_STATIC_S3 = S3_STATIC_URL;
    private const PATH_CERTIDAO_S3 = 'captura/certidao_acoes_execucoes_civeis_criminais_trf2/';

    private $cpfCnpj;
    private $tipoCertidao;
    private $tipoDocumento;

    public function start()
    {
        $response = $this->makeRequest();
        $file = $this->makePdfAndGetText($response);
        $result = $this->parseText($file['textPdf']);

        $data = $result;
        $data['pdf'] = $file['urlPdf'];

        return $data;
    }

    /**
     * @throws \JsonException
     * @throws \Exception
     */
    private function makeRequest()
    {
        $retry = 3;
        do {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, self::REQUEST_URL);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($this->getParams()));

            $headers = [
                'Content-Type: application/json',
                'User-Agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) 
                 AppleWebKit/537.36 (KHTML, like Gecko)
                 Chrome/********* Mobile Safari/537.36',
                'X-Recaptcha-Token:' . $this->resolveRecaptcha()

            ];

            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            $result = curl_exec($ch);
            curl_close($ch);

            $result = json_decode($result, true, 512, JSON_THROW_ON_ERROR);

            $this->checkWarning($result);

            if ($result['conteudo']) {
                return $result['conteudo'];
            }
            $retry--;
        } while ($retry > 0);
        throw new Exception('Não foi possível capturar as informações da página.', 6);
    }

    public function checkWarning($result)
    {
        if (preg_match('/emitida\sde\sforma\sauto/is', $result['msg'])) {
            throw new Exception(
                'Essa certidão não pôde ser emitida de forma automática pelo site da fonte.' .
                ' Isso ocorre quando o sistema necessita realizar análises nos processos vinculados ao critério',
                6
            );
        }
    }

    /**
     * @return array
     */
    private function getParams()
    {
        return [
            "identificacao" => Document::formatCpfOrCnpj($this->cpfCnpj),
            "nomeSocial" => "",
            "tipoCertidao" => $this->tipoCertidao,
            "tipoIdentificacao" => $this->tipoDocumento

        ];
    }

    private function makePdfAndGetText($file)
    {
        $uniqid = md5(uniqid(rand(), true));
        $certificateName = "$uniqid.pdf";
        $certificateLocalPath = "/tmp/{$certificateName}";
        $certificateS3Path = self::PATH_CERTIDAO_S3 . $certificateName;
        $certificateUrl = self::PATH_STATIC_S3 . $certificateS3Path;

        (new Pdf())->saveHtmlToPdf(utf8_decode($file), $certificateLocalPath);

        (new S3(new StaticUplexisBucket()))->save($certificateS3Path, $certificateLocalPath);

        $text = (new Pdf())->getTextFromPdf($certificateLocalPath, ['layout', 'nopgbrk']);

        return ["urlPdf" => $certificateUrl, "textPdf" => $text];
    }

    /**
     * @return array
     *
     * @param  string  $text
     *
     * @throws \Exception
     */
    private function parseText(string $text)
    {
        $patterns = [
            'numeroCertidao' => ['@N.\s(\d.*)@'],
            'status' => ['@(CERTIFICAMOS[\s\S]*CPF\/CNPJ\sn.*)@'],
            'observacoes' => ['@Observações:([\s\S]*?)Código@']
        ];

        $data = Util::parseDados($patterns, $text);
        return array_map("utf8_decode", $data);
    }


    private function resolveRecaptcha(): string
    {
        $retry = 3;
        do {
            if (!empty(self::CAPTCHA_TOKEN)) {
                return $this->solveReCaptcha(self::CAPTCHA_TOKEN, self::BASE_URL);
            }
            $retry--;
        } while ($retry > 0);

        throw new Exception("Erro ao localizar dados do captcha na página.", 3);
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->cpfCnpj = trim($this->param['cpf_cnpj']);
        $this->tipoCertidao = $this->param['tipo_certidao'];

        if (empty($this->cpfCnpj) || !Document::validarCpfOuCnpj($this->cpfCnpj)) {
            throw new Exception('Parâmetro Inválido', 1);
        }
        if (empty($this->tipoCertidao)) {
            throw new Exception('É necessário informar o Tipo da Certidão', 1);
        }

        /* Tipos de certidões: 1177577044650467166 — Cível;
                               8771923921911470077 — Criminal;
                               1901172859473917108 — Eleitoral */

        $arrayTypes = array('1177577044650467166', '8771923921911470077', '1901172859473917108');

        if (!in_array($this->tipoCertidao, $arrayTypes, true)) {
            throw new Exception('Tipo de Certidão Inválido', 1);
        }

        $this->tipoDocumento = Document::validarCpf($this->cpfCnpj) ? "CPF" : "CNPJ";
    }
}
