<?php

namespace App\Crawler\DiarioOficial;

use App\Crawler\Spider;
use App\Manager\ElasticsearchManager;
use Exception;
use GuzzleHttp\Client;

class DiarioOficial extends Spider
{
    private $es_index   = "upjuris";
    private $es_type    = "diario";

    private $limite_default = 50;

    protected function validateAndSetCrawlerAttributes()
    {
        if (!isset($this->param['termo']) || empty($this->param['termo'])) {
            throw new Exception("Termo de busca é obrigatorio", 1);
        }
    }

    /**
     *  Faz a busca no Elastic Search
     */
    protected function start()
    {
        $this->param['limite'] = $this->param['limite'] ??  $this->limite_default;

        $query = $this->createQuery();

        return (new ElasticsearchManager())->search($query);
    }

    /**
     * Cria a query do elasticsearch
     */
    public function createQuery()
    {
        $params = [];
        $params['index'] = $this->es_index;
        $params['type'] = $this->es_type;

        $params['body']['track_scores'] = true;

        // Configura limite
        $params['body']['size'] = $this->param['limite'];

        // Configura o termo de busca
        $params['body']['query']['bool']['must'][] = array(
            'query_string'  =>  array(
                'query' =>  '"' . $this->param['termo'] . '"',
                'default_field' => 'texto',
            ),

        );

        // Configura a paginação
        if (isset($this->param['pagina']) && !empty($this->param['pagina'])) {
            $params['body']['from'] = (($this->param['pagina'] - 1) * $this->param['limite']) + 1;
        }

        // Configura filtro por processo
        if (isset($this->param['processo']) && !empty($this->param['processo'])) {
            $processo = $this->param['processo'];

            $params['body']['query']['bool']['must'][] = array(
                'query_string'  =>  array(
                    'query' =>  '"' . $this->formatarTermo($processo) . '"',
                    'default_field' => 'processo',
                    'phrase_slop' => 0,
                    'minimum_should_match' => '100%'
                ),

            );
        }

        // Configura filtro por datas
        $data = [];
        if (isset($this->param['data_inicio']) && !empty($this->param['data_inicio'])) {
            $data_inicio = $this->param['data_inicio'];
            if (!is_integer($data_inicio)) {
                $data_inicio = date('d/m/Y', strtotime($data_inicio));
                $data['gte'] = $data_inicio;
            }
        }
        if (isset($this->param['data_fim']) && !empty($this->param['data_fim'])) {
            $data_fim = $this->param['data_fim'];
            if (!is_integer($data_fim)) {
                $data_fim = date('d/m/Y', strtotime($data_fim));
                $data['lte'] = $data_fim;
            }
        }

        if (count($data) > 0) {
            $params['body']['query']['bool']['filter'][] = array(
                'range' =>  array(
                    'data'  =>  $data,
                )
            );
        }

        // Confgiura filtro por caderno e regiao
        $regiao = '';
        $caderno = '';
        if (isset($this->param['regiao']) && !empty($this->param['regiao'])) {
            $regiao = $this->param['regiao'];
        }
        if (isset($this->param['caderno']) && !empty($this->param['caderno'])) {
            $caderno = $this->param['caderno'];
        }
        if (!empty($regiao) || !empty($caderno)) {
            $regiaoCaderno = '';
            $regiaoCaderno .= !empty($regiao) ? $regiao . '' : '*';
            $regiaoCaderno .= $caderno ??  '*';

            $params['body']['query']['bool']['must'][] = array(
                'query_string' => array(
                    'query' => $regiaoCaderno,
                    'default_field' => 'caderno',
                    'default_operator' => 'AND',
                    'minimum_should_match' => '100%'
                )
            );
        }

        // Configura o highlight (corte do texto onde há ocorrencia)
        $params['body']['highlight']['fields'][] = ['texto' => [
            'fragment_size'         => 500,
            'number_of_fragments'   => 1
        ]];

        // Ordenação
        if (isset($this->param['ordenacao']) && !empty($this->param['ordenacao'])) {
            $ordem = (substr($this->param['ordenacao'], 0, 1) == '-') ? 'desc' : 'asc';
            $campo = trim(str_replace(['-', '+'], '', $this->param['ordenacao']));
            $params['body']['sort'][$campo]['order'] = $ordem;
        }

        return $params;
    }

    public function parseResult($result)
    {
        $response = [];

        $response['search_time']    = $result['took'];
        $response['total_docs']     = $result['hits']['total'];
        $response['total_pagina']   = count($result['hits']['hits']);
        $response['max_result']     = $this->param['limite'] * $this->param['pagina'];
        $response['resultados']     = [];

        foreach ($result['hits']['hits'] as $k => $v) {
            $response['resultados'][] = [
                'pagina'        => $v['_source']['pagina'],
                'posicao'       => ($this->param['limite'] * $this->param['pagina']) + ($k + 1),
                'relevancia'    => $v['_score'],
                'caderno'       => $v['_source']['caderno'],
                'data'          => $v['_source']['data'],
                'filename'      => $v['_source']['filename'],
                'texto'         => $v['highlight']['texto'][0],
            ];
        }

        return $response;
    }

    /**
     * @param string $termo
     * @return string
     */
    private function formatarTermo($termo)
    {
        $termo = trim($termo);

        $termo = preg_replace('!\s+!', ' ', $termo);

        $termo = $this->escapeElasticSearchTermo($termo);

        return $termo;
    }

    /**
     * @param string $termo
     * @return string
     */
    private function escapeElasticSearchTermo($termo)
    {
        $reservedChars = array(
            '\\', '+', '-', '=', '&&', '||', '!', '(', ')', '{', '}', '[', ']', '^', '"', '~', '*', '?', ':',  '/'
        );
        $prohibitedChars = array('>', '<');

        foreach ($prohibitedChars as $char) {
            $termo = str_replace($char, '', $termo);
        }
        foreach ($reservedChars as $char) {
            $termo = str_replace($char, '\\' . $char, $termo);
        }
        return $termo;
    }
}
