<?php

namespace App\Crawler\InformacaoJuridica;

use App\Crawler\Spider;
use App\Helper\Str;
use App\Helper\Document;
use Exception;

class InformacaoJuridica extends Spider
{
    private const MAIN_URL = 'https://www.cnj.jus.br/improbidade_adm/consultar_requerido.php';
    private const IMPROBIDADE_SEARCH_BASE_URL = 'http://www.cnj.jus.br/improbidade_adm/';
    private const PATTERNS = [
        'data_cadastramento' => '/<span ><b>Data do Cadastramento:<\/b><\/span>' .
            '\s*<\/td>\s*<td >\s*(.*?)\s*<\/td>\s*<\/tr>/is',
        'num_processo' => '/<span ><b>N(?:.*?)mero do Processo:<\/b><\/span>' .
            '<\/td>\s*<td width=\"85%\"\s*style=\"font-color: blue\"><b><a\s*href=\"(?:.*?)' .
            '\">\s*<font color=\'blue\'>(.*?)<\/font><\/a>\s*<\/b><\/td>/is',
        'esfera' => '/hierarquia-label-direita-esfera\'\s*tipOrgao\s*=\'(?:.*?)\'\s*' .
            'codEsfera\s*=\'(?:.*?)\'\s*>\s*(.*?)\s*<\/label>\s*<\/span><\/div><\/div>/is',
        'tribunal' => '/hierarquia-label-direita-descendente-0\'\s*tipOrgao\s*=\'(?:.*?)' .
            '\'\s*seqOrgao\s*=\'(?:.*?)\'\s*>\s*(.*?)\s*<\/label>\s*<\/span><\/div><\/div>/is',
        'grau_jurisdicao' => '/hierarquia-label-direita-descendente-1\'\s*tipOrgao\s*=\'' .
            '(?:.*?)\'\s*seqOrgao\s*=\'(?:.*?)\'\s*>\s*(.*?)\s*<\/label>\s*<\/span><\/div><\/div>/is',
        'gabinete' => '/hierarquia-label-direita-descendente-2\'\s*tipOrgao\s*=\'(?:.*?)' .
            '\'\s*seqOrgao\s*=\'(?:.*?)\'\s*>\s*(.*?)\s*<\/label>\s*<\/span><\/div><\/div>/is',
        'sub_secao' => '/hierarquia-label-direita-descendente-3\'\s*tipOrgao\s*=\'(?:.*?)' .
            '\'\s*seqOrgao\s*=\'(?:.*?)\'\s*>\s*(.*?)\s*<\/label>\s*<\/span><\/div><\/div>/is',
        'nome' => '/class=\'link\'><font style=\'font-size: 8pt;color: blue\'>(.*?)<\/font>' .
            '<\/a><\/span>/is'
    ];

    private $criterion;
    private $urlQuery;
    private $totalResults;

    /**
     * @return array
     * @throws Exception
     */
    protected function validateAndSetCrawlerAttributes()
    {
        $name = $document = '';

        if (!empty($this->param['documento'])) {
            $document = $this->criterion = preg_replace('@\D+@', '', $this->param['documento']);

            if (!Document::validarCpfOuCnpj($this->criterion)) {
                throw new Exception("Documento inválido", 1);
            }
        }

        if (!empty($this->param['nome'])) {
            $name = urlencode($this->param['nome']);
            $name = strtoupper(trim($this->param['nome']));
            $this->criterion = $name;

            $name = str_replace(' ', '%20', $name);
        }

        $this->urlQuery = "?validar=form&rs=pesquisarRequeridoGetTabela&rst=&rsrnd=" . time() . rand(100, 999) . '&' .
            "rsargs[]=&rsargs[]=&rsargs[]=&rsargs[]=" . $document . "&rsargs[]=" . $name .
            "&rsargs[]=&rsargs[]=I&rsargs[]=0" .
            "&rsargs[]=POSICAO_INICIAL_PAGINACAO_PHP0&rsargs[]=QUANTIDADE_REGISTROS_PAGINACAO15";
    }

    /**
     * Encapsula métodos responsáveis por validar e pesquisar dados retornando o resultado final
     * @return array
     */
    protected function start()
    {
        $url = self::MAIN_URL . $this->urlQuery;

        $this->setAlternativeProxy();
        $response = $this->getResponse($url);
        $entries = $this->filterResponse($response);
        $result = $this->getAndParseResults($entries);

        return $result;
    }

    /**
     * @param $response
     * @return array
     * @throws Exception
     */
    private function filterResponse($response)
    {
        if (substr_count($response, 'href') <= 0) {
            throw new Exception('Nenhum Resultado Encontrado', 2);
        }

        if (preg_match_all("/Os caracteres da imagem n.o correspondem ao texto digitado/is", $response)) {
            throw new Exception('Captcha Incorreto', 100);
        }

        //Seleciona todas entradas que possuem o criterio, mesmo que parcialmente
        $patternEntries = "/<a href=.'(.*?).'(?:.*?)onmouseout=writetxt\\(0\\);>(.*?)<\\/a>(?:.n\\s*<\\/td>";
        $patternEntries .= "<td class=..text2..>.n\\s*(.*?).n\\s*)?(?:.*?)<a " .
            "href=.'(.*?).'(?:.*?)onmouseout=writetxt\\(0\\);>(.*?)<\\/a>/is";
        preg_match_all($patternEntries, $response, $entriesMatched);

        $entries = [];
        for ($i = 0; $i < count($entriesMatched[0]); $i++) {
            $entries[] = array(
                'nome' => array(
                    'texto' => $entriesMatched[2][$i],
                    'link' => $entriesMatched[1][$i]
                ),
                'processo' => array(
                    'texto' => $entriesMatched[5][$i],
                    'link' => $entriesMatched[4][$i]
                ),
                'documento' => array(
                    'texto' => $entriesMatched[3][$i]
                )
            );
        }

        $this->totalResults = count($entries);

        // verifica se o nome da entrada da captura é igual ao do documento inserido
        foreach ($entries as $key => $value) {
            $nome = preg_replace("/[^a-zA-Z0-9]+/", "", $entries[$key]['nome']['texto']);
            $documento = preg_replace("/[^a-zA-Z0-9]+/", "", $entries[$key]['documento']['texto']);
            $original = preg_replace("/[^a-zA-Z0-9]+/", "", $this->criterion);

            if ($original != $nome && $original != $documento) {
                unset($entries[$key]);
            }
        }

        if (empty($entries)) {
            throw new Exception("Nenhum resultado encontrado", 2);
        }

        return $entries;
    }

    /**
     * @param $entries
     * @return array
     */
    private function getAndParseResults($entries)
    {
        $results = [];
        $cont = 0;

        foreach ($entries as $entry) {
            $url = self::IMPROBIDADE_SEARCH_BASE_URL . $entry['nome']['link'];
            $response = Str::encoding($this->getResponse($url));
            $array = [];

            //Parseia as informações principais do cliente a partir de um array de regex
            foreach (self::PATTERNS as $key => $p) {
                preg_match_all($p, $response, $patternMatched);
                $array[$key] = $patternMatched[1][0];
            }
            //Adiciona as URLs a partir do padrão previamente estabelecido
            $array['num_process_href']  = $entry['processo']['link'];
            $array['uri']               = self::IMPROBIDADE_SEARCH_BASE_URL . $entry['nome']['link'];

            // Os assuntos relacionados são inseridos por javascript, por isso o regex
            // busca pelas funções de JS que irão inserir tais dados
            $array['assunto_relacionados'] = [];
            preg_match_all("/addAssunto\\((?:.*?),'(.*?)',(?:.*?)\\)/is", $response, $patternMatched);
            foreach ($patternMatched[1] as $key => $assunto) {
                $array['assunto_relacionados'][$key + 1] = $assunto;
            }

            //Regex que seleciona a situacao
            if (
                preg_match_all(
                    "/class='link'><font style='font-size: 8pt;color: blue'>(?:.*?)<\\/font><\\/a>" .
                        "<\\/span>\\s*<\\/td>\\s*<td><span >Ativo<\\/span><\\/td>/is",
                    $response,
                    $patternMatched
                )
            ) {
                $array['situacao'] = "Ativo";
            } else {
                $array['situacao'] = "Parametro não encontrado";
            }

            //Regex que seleciona o tipo de julgamento
            if (
                preg_match_all(
                    "/<input type=\"radio\" name=\"tipo_pena\" checked=checked value=\"C\" " .
                        "disabled=\"disabled/is",
                    $response,
                    $patternMatched
                )
            ) {
                $array['tipo_julgamento'] = "Orgao colegiado";
            } else {
                $array['tipo_julgamento'] = "Transito em julgado";
            }

            //Busca as condenações por REGEX e as parseia. O site está bem mal padronizado,
            //todas as opções estão no HTML com value setado para SIM, só que escondidas por
            //style display none. Além disso há uma das opções que é setada por javascript
            preg_match_all(
                "/<tr (?!style='display: none).{0,25}>\\s*<td (?:.{1,100})>\\s*<span(?:\\s|\\s*id(?:.*?))" .
                    ">(.*?)<\\/span>\\s*<\\/td>\\s*<td(?:.*?)>\\s*<font style=\"font-size: 8pt; color: black;\"" .
                    "><b>(.*?)<\\/b>\\s*<\\/font>(?(?=\\s)(?:\\s*<span (?:.*?)>(.*?)<\\/span>(?:\\s*<span " .
                    "(?:.*?)>(.*?)<\\/span>\\s*<span (?:.*?)>(.*?)<\\/span>)?|\\s*)|(?:.*?)<span >(.*?)<\\/span>)/is",
                $response,
                $patternMatched
            );

            foreach ($patternMatched[1] as $key => $condenacoes) {
                $condenacaoValue = preg_replace(
                    '!\s+!',
                    ' ',
                    $patternMatched[3][$key] . ' ' .
                        $patternMatched[4][$key] . ' ' .
                        $patternMatched[5][$key] . ' ' .
                        $patternMatched[6][$key]
                );
                $condenacaoValue = str_replace('&nbsp;', '', $condenacaoValue);

                if ($condenacoes == '<!-- carregado via javascript -->') {
                    $condenacoes = 'Pena privativa de liberdade aplicada';
                }

                $array['info_condenacao'][$key++] = array(
                    'label' =>  preg_replace('!\s+!', ' ', $condenacoes),
                    'value' =>  $patternMatched[2][$key] . ' ' . $condenacaoValue,
                );
            }

            $results["resultados"][$cont++] = $array;
            $results["n_matches_total"] = $this->totalResults;
        }

        return $results;
    }
}
