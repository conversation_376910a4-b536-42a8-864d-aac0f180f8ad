<?php

namespace App\Crawler\ImprensaOficial;

use Exception;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use Symfony\Component\DomCrawler\Crawler;
use App\Crawler\CadastroPj\CadastroPjSpider;
use App\Manager\S3\Bucket\StaticUplexisBucket;

class ImprensaOficial extends Spider
{
    private const CONDIARIO_URL = 'http://balancos.imprensaoficial.com.br/Condiario.asp';
    private const BUSCA_NOME_URL = 'http://balancos.imprensaoficial.com.br/BuscaNome.asp';
    private const RESULT_CONSULTA_URL = 'http://balancos.imprensaoficial.com.br/ResultConsulta.asp';
    private const PUBLICACAO_URL = 'http://balancos.imprensaoficial.com.br/Publicacao.asp';
    private const PAGINACAO_URL = 'http://balancos.imprensaoficial.com.br/MaisArr.asp';
    private const PDF_URL = 'http://document.imprensaoficial.com.br/';

    private const S3_DOMAIN = S3_STATIC_BUCKET . '/';
    private const S3_FILE_PATH = 'upminer/balanco_patrimonial/{cnpj}/{year}/{file_name}.pdf';

    /**
     * Range de anos que deve consultar
     *
     * @var integer
     */
    private const SINCE = 10;

    /**
     * URLs e conteúdo da tag a
     *
     * @var array
     */
    private $pdfData = [];

    /**
     * Critério que será consultado
     *
     * @var String
     */
    private $cnpj;

    /**
     * Razão social a ser consultada
     *
     * @var string
     */
    private $razaoSocial;

    /**
     * S3 PDF paths
     *
     * @var array
     */
    private $s3Paths = [];

    /**
     * Valida se tem os parametros necessários
     *
     * @return void
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['cnpj'])) {
            throw new Exception('CNPJ precisa ser informado');
        }

        if (!Document::validarCnpj($this->param['cnpj'])) {
            throw new Exception("CNPJ inválido");
        }

        $this->cnpj = preg_replace('/\W/', '', $this->param['cnpj']);
        $spider = new CadastroPjSpider();
        $spine = $spider->getSpinePj($this->cnpj);
        if (empty($spine['razao_social'])) {
            throw new Exception('Não foi possível encontrar a razão social');
        }
        $this->razaoSocial = $spine['razao_social'];
    }

    /**
     * Inicia a consulta
     *
     * @return void
     */
    protected function start()
    {
        $this->getResponse(self::CONDIARIO_URL);

        $html = $this->getResponse(self::BUSCA_NOME_URL, 'POST', $this->getFormParams());
        $this->hasCompany($html);

        $this->paginate();

        $this->sortDataPdf();
        $this->filterDataPdf();

        if ($this->hasPdfOnS3()) {
            return $this->returnFromS3();
        }

        return $this->downloadAllPdfs();
    }

    /**
     * Valida se existe pdfs no s3
     *
     * @return boolean
     */
    private function hasPdfOnS3()
    {
        foreach ($this->pdfData as $year => $data) {
            try {
                $s3Path = str_replace(
                    [
                        '{cnpj}',
                        '{year}',
                        '{file_name}'
                    ],
                    [
                        $this->cnpj,
                        $year,
                        "{$this->cnpj}_{$year}"
                    ],
                    self::S3_FILE_PATH
                );
            } catch (Exception $e) {
                return false;
            }
        }
        return true;
    }

    /**
     * Retorna os dados do s3 (sem ter que baixar, pois eles já existem no s3)
     *
     * @return array
     */
    private function returnFromS3()
    {
        $response = [];
        foreach ($this->pdfData as $year => $data) {
            $s3Path = str_replace(
                [
                    '{cnpj}',
                    '{year}',
                    '{file_name}'
                ],
                [
                    $this->cnpj,
                    $year,
                    "{$this->cnpj}_{$year}"
                ],
                self::S3_FILE_PATH
            );
            $response['documents'][] = [
                'year' => $year,
                'link' => self::S3_DOMAIN . $s3Path
            ];
        }
        return $response;
    }

    /**
     * Baixa as páginas dos pdfs
     *
     * @return array
     */
    private function downloadAllPdfs()
    {
        $listPdf = [];
        foreach ($this->pdfData as $year => $data) {
            $path = '/tmp/imprensaoficial-' . $this->cnpj . '-' . $year . '.pdf';
            $this->mountPdf($data, $path);

            $listPdf['documents'][] = [
                'link' => $this->sendPdfS3($year),
                'year' => $year
            ];
        }
        return $listPdf;
    }

    /**
     * Envia os pdfs baixados para o s3
     *
     * @param string $date
     * @return string
     */
    private function sendPdfS3($date)
    {
        $s3Path = str_replace(
            [
                '{cnpj}',
                '{year}',
                '{file_name}'
            ],
            [
                $this->cnpj,
                $date,
                "{$this->cnpj}_{$date}"
            ],
            self::S3_FILE_PATH
        );
        $path = '/tmp/imprensaoficial-' . $this->cnpj . '-' . $date . '.pdf';
        (new S3(new StaticUplexisBucket()))->save($s3Path, $path);

        return self::S3_DOMAIN . $s3Path;
    }

    /**
     * Une as páginas dos pdf por ano
     *
     * @param array $data
     * @param string $path
     * @return void
     */
    private function mountPdf($data, $path)
    {
        $pathsToMerge = [];
        foreach ($data as $dataByDate) {
            $content = $this->getResponse($dataByDate['url']);
            $pagePath = "/tmp/impressaoficial-{$this->cnpj}-{$dataByDate['date']}-{$dataByDate['page']}.pdf";
            file_put_contents($pagePath, $content);
            $pathsToMerge[] = $pagePath;
        }
        (new Pdf())->mergePdfs($pathsToMerge, $path);
    }

    /**
     * Pega os links de todas as páginas
     *
     * @return void
     */
    private function paginate()
    {
        $this->getResponse(self::CONDIARIO_URL, 'GET', ['nome_empresa' => $this->razaoSocial]);
        $this->getResponse(self::RESULT_CONSULTA_URL, 'POST', $this->getFormParams());

        $page = 1;
        $html = $this->getResponse(self::PUBLICACAO_URL);
        while ($this->hasContent($html)) {
            $this->startCrawler($html);
            $page++;
            $html = $this->getResponse(self::PAGINACAO_URL . "?posicao=" . $page);
        }
    }

    /**
     * Ordenas os pdfs (por data e página)
     *
     * @return void
     */
    private function sortDataPdf()
    {
        usort($this->pdfData, function ($a, $b) {
            if ($a['date'] == $b['date']) {
                if ($a['page'] == $b['page']) {
                    return 0;
                }
                return ($a['page'] < $b['page']) ? -1 : 1;
            }

            $aDateFormat = str_replace('-', '', $a['date']);
            $bDateFormat = str_replace('-', '', $b['date']);
            return ($aDateFormat < $bDateFormat) ? -1 : 1;
        });

        $pdfData = $this->pdfData;
        $this->pdfData = [];
        foreach ($pdfData as $data) {
            $year = explode('-', $data['date'])[0];
            $this->pdfData[$year][] = $data;
        }
    }

    /**
     * Filtra os pdfs encontrados (atualmente está pegando os últimos 10 anos)
     *
     * @return void
     */
    private function filterDataPdf()
    {
        $sinceDate = date('Y') - self::SINCE;
        foreach ($this->pdfData as $year => $data) {
            if ($year < $sinceDate) {
                unset($this->pdfData[$year]);
            }
        }
    }

    /**
     * Pega os dados necessários das páginas
     *
     * @param string $html
     * @return void
     */
    private function startCrawler($html)
    {
        $crawler = new Crawler($html);
        $crawler->filter('table:nth-child(4) > tr > td')->each(function ($node) {
            $patterns = [
                'url' => ['/href=\"(.*)\"/isU'],
                'date' => ['/font.*>.*(\d{2}\/\d{2}\/\d{4})/isU'],
                'page' => ['/font.*>.*p..g.\s(\d+)\s/isU']
            ];
            $parseData = Util::parseDados($patterns, $node->html());
            $this->pdfData[] = [
                'url' => $this->getPdfUrl($parseData['url']),
                'date' => implode('-', array_reverse(explode('/', $parseData['date']))),
                'page' => $parseData['page']
            ];
        });
    }

    /**
     * Transforma a url da lista de página na url do pdf
     *
     * @param string $fullUrl
     * @return string
     */
    private function getPdfUrl($fullUrl)
    {
        $url = htmlspecialchars_decode(urldecode($fullUrl));
        $url = preg_replace(['/.*\?imagem=/', '/\&.*/'], '', $url);
        return self::PDF_URL . $url;
    }

    /**
     * Válida se a página consultada ainda tem dados a serem consumidos
     *
     * @param string $html
     * @return boolean
     */
    private function hasContent($html)
    {
        $crawler = new Crawler($html);
        if (trim($crawler->filter('table:nth-child(4)')->html())) {
            return true;
        }
        return false;
    }

    /**
     * Valida se foi encontrado a empresa na busca
     *
     * @param string $html
     * @return boolean
     */
    private function hasCompany($html)
    {
        $crawler = new Crawler($html);
        if (!$crawler->selectLink($this->razaoSocial)) {
            throw new Exception('Não há dados para o critério informado');
        }
    }

    /**
     * Retorna os parametros do formulário básico
     *
     * @return array
     */
    private function getFormParams()
    {
        return [
            'txtNome_Empresa' => $this->razaoSocial,
            'publicacao_id' => '61',
            'tab_tipo_secao_id' => '18',
            'tab_tipo_caderno_id' => '2',
            'tab_tipo_documento_id' => '1',
            'pesquisar.x' => '45',
            'pesquisar.y' => '6'
        ];
    }
}
