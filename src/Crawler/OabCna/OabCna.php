<?php

namespace App\Crawler\OabCna;

use App\Crawler\Spider;
use Exception;

class OabCna extends Spider
{
    private const URL_HOME = 'https://cna.oab.org.br';
    private const URL_SEARCH = 'https://cna.oab.org.br/Home/Search';
    private const RETRIES = 3;
    private $storedCookies = '';
    private $criterion;

    /**
     *  Valida parâmetros da fonte
     *
     *  <AUTHOR> 27/09/2018
     *  @version 1.0.0
     *
     *  @return string
     *
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        if (empty($this->param['nome'])) {
            throw new Exception("Critério não informado ou inválido.", 1);
        }

        $this->criterion = $this->param['nome'];
    }


    /**
     *  Início da execução da fonte CNA - Cadastro Nacional de Advogados (82)
     *
     *  <AUTHOR> 12/09/2018
     *
     *  @version 1.0.0
     *
     *  @return array
     *
     */
    protected function start()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        $this->headers = [
            'Referer'       => self::URL_HOME,
            'User-Agent'    =>
            'Mozilla/5.0 (X11; Linux x86_64) ' .
                'AppleWebKit/536.11 (KHTML, like Gecko) ' .
                'Ubuntu/12.04 Chromium/20.0.1132.47 Chrome/20.0.1132.47 Safari/536.11'
        ];

        // $this->setProxy();
        $this->setCurlOpt(
            array(
                CURLOPT_PROXY => 'br.gw.uplexis.com.br:8080',
                CURLOPT_PROXYUSERPWD => 'cap:_1+_@_tftpASs1',
                CURLOPT_RETURNTRANSFER => 1,
                CURLOPT_HEADER => 1
            )
        );

        $success = false;
        for ($i = 0; $i < self::RETRIES && !$success; $i++) {
            $results = $this->captura();
            $success = $results->Success;
        }

        if (!$success) {
            throw new Exception("Erro: Requisição final sem sucesso.", 3);
        }

        if (empty($results->Data)) {
            throw new Exception("Nenhum registro encontrado.", 2);
        }

        return $results->Data;
    }

    private function extractCookies($html)
    {
        preg_match_all('/^Set-Cookie:\s*([^;]*)/mi', $html, $matches);
        $cookies = [];

        foreach ($matches[1] as $item) {
            parse_str($item, $cookie);
            $cookies = array_merge($cookies, $cookie);
        }

        if (empty($cookies)) {
            throw new \Exception('Não foram encontrados os cookies.');
        }

        return $cookies;
    }

    /**
     *  Realiza as requisições necessárias para consulta e quebra de captcha
     *
     *  <AUTHOR> Monteiro
     *
     *  @version 1.0.0
     *
     *  @return object $results
     *
     */
    private function captura()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $data = $this->getResponse(self::URL_HOME, 'GET', [], array('Upgrade-Insecure-Requests: 1'));

        if (empty($this->storedCookies)) {
            $this->storedCookies = $this->extractCookies($data);
        }

        $params = array(
            'IsMobile'              => "false",
            'NomeAdvo'              => $this->criterion,
            'Insc'                  => "",
            'Uf'                    => "",
            'TipoInsc'              => "",
            '__RequestVerificationToken'  => $this->storedCookies['__RequestVerificationToken']
        );

        $this->setCurlOpt(
            array(
                CURLOPT_HEADER => 0
            )
        );

        try {
            $json = $this->getResponse(
                self::URL_SEARCH,
                'POST',
                $params,
                array('Upgrade-Insecure-Requests: 1', 'Referer: ' . self::URL_HOME)
            );

            $results = json_decode($json);
        } catch (Exception $e) {
            throw $e;
        }

        return $results;
    }
}
