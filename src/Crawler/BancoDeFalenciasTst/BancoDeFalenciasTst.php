<?php

namespace App\Crawler\BancoDeFalenciasTst;

use App\Crawler\Spider;
use App\Helper\Document;
use Exception;

class BancoDeFalenciasTst extends Spider
{
    private const BASE_URL = "https://bancofalencia.tst.jus.br";
    private const BASE_URL_API = "/rest/consultas/empresas/?";
    private $razaoSocial = "";
    private $cnpj = "";
    private $limit;

    public function start()
    {
        $data = $this->makeRequest();
        $data = array_slice($data, 0, $this->limit);
        return $data;
    }


    public function makeRequest()
    {
        $this->setProxy();

        $params = [
            'razaoSocial' => $this->razaoSocial,
            'cnpj' => $this->cnpj,
            'numProc' => "",
            'numDigProc' => "",
            'numAnoProc' => "",
            'numJusticaProc' => "",
            'numOrgaoProc' => "",
            'numVaraProc' => "",
            'classe' => "",
            'tipoOcorrencia' =>  -1,
            'fonte' => -1
        ];

        $headers = [
            "User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0",
            "Host: bancofalencia.tst.jus.br"
        ];

        $params = http_build_query($params);
        $result = $this->getResponse(self::BASE_URL . self::BASE_URL_API . $params, 'GET', [], $headers);
        $result = json_decode($result, true);

        if (empty($result)) {
            throw new Exception('Nada encontrado', 2);
        }

        return $result;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->limit = !isset($this->param['limit']) ? 50 : $this->param['limit'];

        if (empty(trim($this->param['razao_cnpj']))) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        $param = trim($this->param['razao_cnpj']);
        $this->razaoSocial = $param;
        if (is_numeric(Document::removeMask($param))) {
            $this->razaoSocial = "";
            $this->cnpj = Document::removeMask($param);
        }
    }
}
