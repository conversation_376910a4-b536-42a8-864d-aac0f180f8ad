<?php

namespace App\Crawler\AcordosLenienciaVigentesCgu;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Str;
use App\Helper\Util;
use Exception;

class AcordosLenienciaVigentesCgu extends Spider
{
    private const BASE_URL = "https://portaldatransparencia.gov.br";
    private string $criterio;
    private int $limite;
    private array $headers;

    public function start()
    {
        $data = $this->makeRequest();

        if (!$data['recordsTotal']) {
            throw new Exception("A pesquisa não encontrou nenhum dado correspondente.", 2);
        }

        return $this->parseResult($data['data']);
    }

    /**
     * Busca os acordos de leniencia vigentes
     *
     * @return array
     * <AUTHOR> - 03.02.23
     *
     * @throws \JsonException
     */
    private function makeRequest(): array
    {
        $query = [
            "paginacaoSimples" => 'true',
            "tamanhoPagina" => $this->limite,
            "offset" => 0,
            "direcaoOrdenacao" => "asc",
            "colunaOrdenacao" => "nomeSancionado",
            "cadastro" => 4,
            $this->param => $this->criterio,
            "colunasSelecionadas" => 'linkDetalhamento,cadastro,cpfCnpj,nomeSancionado,ufSancionado,orgao,
                                        categoriaSancao,dataPublicacao,valorMulta'
        ];

        $this->headers = [
            "User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0",
        ];

        $url = self::BASE_URL . '/sancoes/consulta/resultado?' . http_build_query($query);

        $result = $this->getResponse($url, 'GET', [], $this->headers);
        return json_decode($result, true, 512, JSON_THROW_ON_ERROR);
    }

    private function getDetalhes(string $url): array
    {
        $html = $this->getResponse(self::BASE_URL . $url, 'GET', [], $this->headers);
        $html = Str::cleanString($html);

        $patterns = [
            'tipoAcordo' => ['@<strong>Tipo\sde\sacordo<\/strong>.<span>(.*?)<\/span>@u'],
            'fundamentacaoLegal' => ['@<strong>Fundamenta..o\slegal<\/strong>.<span>(.*?)<\/span>@u'],
            'dtInicioAcordo' => ['@<strong>Data\sde\sin.cio\sdo\sacordo<\/strong>.<span>(.*?)<\/span>@u'],
            'dtFimAcordo' => ['@<strong>Data\sde\sfim\sdo\sacordo<\/strong>.<span>(.*?)<\/span>@u'],
            'descricao' => ['@<strong>Descri..o\sda\sfundamenta..o\slegal<\/strong>.<span>(.*?)<\/span>@u'],
            'termos' => ['@<strong>Termos\sdo\sacordo<\/strong>.<span>(.*?)<\/span>@u'],
            'valorFinal' => ['@<strong>Valor\sfinal\sdo\sAcordo<\/strong>.<span>(.*?)<\/span>@u'],
            'valorMulta1' => ['@<strong>Valor\sda\smulta\s.\sLei\sn..12.846.13<\/strong>.<span>(.*?)<\/span>@u'],
            'valorMulta2' => ['@<strong>Valor\sda\smulta\s.\sLei\sn..8.429.92<\/strong>.<span>(.*?)<\/span>@u'],
            'valorRessarcimento' => ['@<strong>Valor\sdo\sressarcimento.<\/strong>.<span>(.*?)<\/span>@u'],
            'situacao' => ['@<strong>Situação\sdo\sAcordo\sde\sLeni.ncia<\/strong>.<span>(.*?)<\/span>@u'],
            'numProcesso' => ['@<strong>N.mero\sdo\sprocesso<\/strong>.<span>(.*?)<\/span>@u'],
            'orgaoResponsavel' => ['@<strong>Nome<\/strong>.<span>(.*?)<\/span>@u'],

        ];

        preg_match('@Efeitos\sdo\sAcordo\sde\sLeni.ncia(.*)<\/div>\s<\/main>@u', $html, $efeitos);

        $efeitos = $this->parseEfeitos($efeitos[1]);
        $data = Util::parseDados($patterns, $html);

        $data = array_map("utf8_decode", $data);
        $data['efeitos'] = $efeitos;
        return $data;
    }

    private function parseResult(array $data): array
    {
        $result = [];
        foreach ($data as $key => $value) {
            $detalhes = $this->getDetalhes($value['linkDetalhamento']);
            $array = [
                'cpfCnpj' => explode(", ", $value['cpfCnpj']),
                'nomeSancionado' => explode(", ", $value['nomeSancionado']),
                'dtPublicacao' => $value['dataPublicacao']
            ];

            $result[] = array_merge($array, $detalhes);
        }

        return $result;
    }

    private function parseEfeitos(string $html): array
    {
        $efeito = Util::queryXPath($html, '//*[@class="col-xs-12 col-sm-3"]');
        $detalhes = Util::queryXPath($html, '//*[@class="col-xs-12 col-sm-9"]');
        $array = array_combine($efeito, $detalhes);

        $result = [];
        foreach ($array as $key => $value) {
            $result[] = [
                'efeito' => $key,
                'detalhes' => $value
            ];
        }
        return $result;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->criterio = $this->param['criterio'];
        $this->limite = $this->param['limit'] ?? 100;
        $this->param = "nomeSancionado";

        if (empty($this->criterio)) {
            throw new Exception("Parâmetro de busca inválido!!", 6);
        }

        if (Document::validarCpfOuCnpj($this->criterio)) {
            $this->param = "cpfCnpj";
            $this->criterio = Document::removeMask($this->criterio);
        }
    }
}
