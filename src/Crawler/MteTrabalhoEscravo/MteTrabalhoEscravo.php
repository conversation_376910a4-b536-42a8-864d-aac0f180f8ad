<?php

namespace App\Crawler\MteTrabalhoEscravo;

use App\Crawler\Spider;
use App\Factory\PostgresDB;
use App\Helper\Document;
use Exception;
use App\Factory\MongoDB;

class MteTrabalhoEscravo extends Spider
{
    private const INDEX_MONGODB = 'employer_document';
    private const LIMIT = 50;

    protected $documento;

    protected function start()
    {
        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('common')
            ->setCollection('mte_trabalho_escravo');

        $fields = ['documento'];
        $results = $manager->atlasSearch(
            Document::formatCpfOrCnpj($this->documento),
            self::INDEX_MONGODB,
            self::LIMIT,
            $fields
        );

        foreach ($results as $result) {
            $data[] = [
                'id_documento' => $result['id_documento'],
                'ano' => $result['ano'],
                'uf' => $result['uf'],
                'empregador' => $result['empregador'],
                'documento' => $result['documento'],
                'estabelecimento' => $result['estabelecimento'],
                'trab_envolvidos' => $result['trab_envolvidos'],
            ];
        }

        if (empty($result)) {
            throw new Exception('Nada encontrado!', 2);
        }

        return $data;
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro invalido', 1);
        }

        if (!Document::validarCpfOuCnpj($this->param['cpf_cnpj'])) {
            throw new Exception('Documento não é um CPF ou CNPJ válido', 1);
        }

        $this->documento = preg_replace('/\D/isu', '', $this->param['cpf_cnpj']);
    }
}
