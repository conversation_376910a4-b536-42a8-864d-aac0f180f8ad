<?php

namespace App\Crawler\CertidaoCeatTrtDFTO;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

class CertidaoCeatTrtDFTO extends Spider
{
    private $cpfCnpj = "";
    private $tipoPessoa = "";
    private $token = "";
    private $cnpj = false;
    private const BASE_URL = "https://www.trt10.jus.br/certidao_online/ServletVisualizaDocumento";
    private const URL_REQUEST = "https://www.trt10.jus.br/certidao_online/jsf/publico/certidaoOnline.jsf";
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const CERTIDAO_S3_PATH = 'captura/certidao_ceat_trt10_DFTO/';

    public function start()
    {
        $this->setProxy();
        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::CERTIDAO_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;

        $html = $this->makeRequest();
        $pdf = $this->makeRequestCertificate($html);
        $text = $this->savePdfAndReturnText($pdf);

        return $this->parseData($text);
    }

    private function makeRequest()
    {
        return $this->getResponse(self::URL_REQUEST);
    }

    private function makeRequestCertificate($html)
    {
        $token = $this->getToken($html);

        preg_match('/ViewState:\d+"\s+value="([\s\S]*?)"/', $html, $viewState);

        if ($this->cnpj) {
            $paramsCnpj = [
                'javax.faces.partial.ajax' => 'true',
                'javax.faces.source' => 'tvPrincipal:frmEmitirCertidaoOnline:radioTipoPessoa',
                'javax.faces.partial.execute' => 'tvPrincipal:frmEmitirCertidaoOnline:radioTipoPessoa',
                'javax.faces.partial.render' =>
                'tvPrincipal:frmEmitirCertidaoOnline:gridCpfCnpj tvPrincipal:frmEmitirCertidaoOnline:gridNome',
                'javax.faces.behavior.event' => 'change',
                'javax.faces.partial.event' => 'change',
                'tvPrincipal:frmEmitirCertidaoOnline' => 'tvPrincipal:frmEmitirCertidaoOnline',
                'tvPrincipal:frmEmitirCertidaoOnline:radioTipoPessoa' => 'CNPJ',
                'tvPrincipal:frmEmitirCertidaoOnline:mskCpfCnpj' => '',
                'g-recaptcha-response' => $token,
                'javax.faces.ViewState' => $viewState[1]
            ];

            $this->getResponse(self::URL_REQUEST, 'POST', $paramsCnpj);
        }

        $params = [
            'javax.faces.partial.ajax' => true,
            'javax.faces.source' => "tvPrincipal:frmEmitirCertidaoOnline:btnConsultar",
            'javax.faces.partial.execute' => "@all",
            'javax.faces.partial.render' =>
            'tvPrincipal:frmEmitirCertidaoOnline:gridNome tvPrincipal:frmEmitirCertidaoOnline:btnGerarCertidao',
            'tvPrincipal:frmEmitirCertidaoOnline:btnConsultar' => 'tvPrincipal:frmEmitirCertidaoOnline:btnConsultar',
            'tvPrincipal:frmEmitirCertidaoOnline' => 'tvPrincipal:frmEmitirCertidaoOnline',
            'tvPrincipal:frmEmitirCertidaoOnline:radioTipoPessoa' => $this->tipoPessoa,
            'tvPrincipal:frmEmitirCertidaoOnline:mskCpfCnpj' => $this->cpfCnpj,
            'g-recaptcha-response' => $token,
            'javax.faces.ViewState' => $viewState[1],
        ];

        $this->getResponse(self::URL_REQUEST, 'POST', $params);

        $paramsRequest = [
            'javax.faces.partial.ajax' => true,
            'javax.faces.source' => "tvPrincipal:frmEmitirCertidaoOnline:btnGerarCertidao",
            'javax.faces.partial.execute' => "@all",
            'tvPrincipal:frmEmitirCertidaoOnline:btnGerarCertidao' =>
            "tvPrincipal:frmEmitirCertidaoOnline:btnGerarCertidao",
            'tvPrincipal:frmEmitirCertidaoOnline' => 'tvPrincipal:frmEmitirCertidaoOnline',
            'tvPrincipal:frmEmitirCertidaoOnline:radioTipoPessoa' => $this->tipoPessoa,
            'tvPrincipal:frmEmitirCertidaoOnline:mskCpfCnpj' => $this->cpfCnpj,
            'g-recaptcha-response' => $token,
            'javax.faces.ViewState' => $viewState[1],
        ];

        $this->getResponse(self::URL_REQUEST, 'POST', $paramsRequest);

        $paramsGet = [
            'nomeArquivo' => 'Certidao',
            'tipoDownload' => 'inline',
            'tipoConteudo' => 'application_pdf',
        ];

        $paramsGet = http_build_query($paramsGet);
        $result = $this->getResponse(self::BASE_URL . '?' . $paramsGet);

        return $result;
    }

    /**
     * Resolve o captcha e seta a resposta
     * <AUTHOR> Pereira
     * @return string
     */
    private function getToken($html)
    {
        preg_match('/sitekey="(.*)"/isU', $html, $siteKey);
        return $this->solveReCaptcha($siteKey[1], self::URL_REQUEST);
    }

    /**
     * Retorna o texto do PDF
     * <AUTHOR> Pereira
     * @return string
     */
    private function savePdfAndReturnText($pdf)
    {
        file_put_contents($this->certificateLocalPath, $pdf);

        $text = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'layout',
            'nopgbrk'
        ]);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        $this->pdf = $this->certificateUrl;

        return $text;
    }

    private function parseData($text)
    {
        $patterns = [
            'nome' => ['@NOME:\s(.*)\n@'],
            'documento' => ['@CPF\/CNPJ:\s(.*)\n@'],
            'expedicao' => ['@Expedição:\s(.*)\n@'],
            'codAutenticidade' => ['@ticidade:\s(.*)\n@'],
            'validade' => ['@Válida\saté\s(.*)\n@'],
            'situacao' => ['@(CERTIFICA-SE[\s\S]*?solicitante\.)@'],
            'observacoes' => ['@(OBSERVAÇÕES[\s\S]*?)\n+\s+A\saute@']
        ];

        $data = Util::parseDados($patterns, $text);
        $data = array_map("utf8_decode", $data);
        $data['status'] = 'NEGATIVA';

        if (preg_match('/CONSTA\(M\)\sa\(s\)/', $data['situacao'])) {
            $data['status'] = 'POSITIVA';
            preg_match('/solicitante\.\\n+([\s\S]*)\n+OBSERVAÇÕES/', $text, $acoes);
            $data['acoes'] = $acoes[1];
        }

        $data['pdf'] = $this->pdf;

        return $data;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->cpfCnpj = trim($this->param['cpf_cnpj']);

        if (!Document::validarCpfOuCnpj($this->cpfCnpj)) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        if (Document::validarCnpj($this->cpfCnpj)) {
            $this->cpfCnpj = Document::formatCnpj($this->cpfCnpj);
            $this->tipoPessoa = 'CNPJ';
            $this->cnpj = true;
        } elseif (Document::validarCpf($this->cpfCnpj)) {
            $this->cpfCnpj = Document::formatCpf($this->cpfCnpj);
            $this->tipoPessoa = 'CPF';
        }
    }
}
