<?php

namespace App\Crawler\CvmPautaJulgamentos;

use App\Crawler\Spider;
use App\Helper\Pdf;
use App\Helper\Str;
use Exception;

/**
 * Classe para busca dos processos da CVM Pauta Julgamentos
 *
 * @version 1.0.0
 * @version 1.0.1 - Alterações para pegar dados dos novos modelos de PDF
 * @version 1.0.2 - Modificando as constantes para nova URL do site
 *
 * <AUTHOR> - 04/07/2018
 * <AUTHOR> Santos - 23/12/2020
 *
 */
class CvmPautaJulgamentos extends Spider
{
    private const BASE_URL = 'https://www.gov.br/cvm/pt-br';
    private const PDF_PAGE_URL = '/assuntos/processos/pautas-de-julgamento';
    private const SIMILARIDADE = 85;

    private $name = '';
    private $sessions = [
        'I',
        'II',
        'III',
        'IV',
        'V',
        'VI',
        'VII'
    ];

    /**
     * Função para validar os critério<PERSON> de entrada
     *
     * @version 1.0.0
     * <AUTHOR> - 04/07/2018
     *
     * @return void
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['nome'])) {
            throw new Exception('Parâmetro de criterio inválido', 1);
        }

        $this->name = $this->param['nome'];
    }

    /**
     * Função principal de busca
     *
     * @version 1.0.0
     * <AUTHOR> Favoreto Makhoul - 04/07/2018
     *
     * @return void
     */
    protected function start()
    {
        $this->setProxy();
        $pdfUrl = '';
        $pdfUrl = $this->getPdfUrl();
        $text = $this->getPdfToText($pdfUrl);
        $text = Str::onlyEncoding($text);
        $dataArray = $this->parseTextToArray($text);
        $results = $this->filterDataToResults($dataArray, $this->name);
        return $results;
    }

    /**
     * Função para buscar a URL do PDF a partir do site principal da CV
     *
     * @version 1.0.0
     * @version 1.0.1 - Correção da regex para busca o novo link do Pdf
     *
     * <AUTHOR> Favoreto Makhoul - 04/07/2018M
     * <AUTHOR> Santos - 23/12/2020
     *
     * @return string
     */
    private function getPdfUrl()
    {
        $html = $this->getResponse(self::BASE_URL . self::PDF_PAGE_URL);

        if (
            preg_match(
                "/https:\/\/www\.gov\.br\/cvm.*\.pdf(\?_ga=(\d*(\.|\-)\d*)*)*/m",
                $html,
                $output
            )
        ) {
            $url =  $output[0];
        } else {
            throw new Exception('URL do PDF de pautas não encontrada', 3);
        }

        return $url;
    }

    /**
     * Função que busca e parseia o PDF dos processos da CVM
     *
     * @version 1.0.0
     * <AUTHOR> Favoreto Makhoul - 04/07/2018
     *
     * @param string $pdfUrl
     *
     * @return void
     */
    private function getPdfToText($pdfUrl)
    {
        $pdf = $this->getResponse($pdfUrl);

        $pdfPath = '/tmp/cvmPauta' . uniqid() . '.pdf';

        file_put_contents($pdfPath, $pdf);

        $text = (new Pdf())->getTextFromPdf($pdfPath, [
            'nopgbrk',
            'layout',
            'fixed 4'
        ]);

        file_exists($pdfPath) && unlink($pdfPath);
        return $text;
    }

    /**
     * Função para parsear o texto para um array
     *
     * @version 1.0.0
     * <AUTHOR> Favoreto Makhoul - 04/07/2018
     *
     * @param string $text
     *
     * @return void
     */
    private function parseTextToArray($text)
    {
        $sessionText = '';

        $result = [];
        foreach ($this->sessions as $index => $session) {
            $sessionText = $this->getSessionContent($text, $session, $this->getEndSession($index));
            if (empty($sessionText)) {
                continue;
            }

            $result[$session] = $this->parseSessions($sessionText);
        }

        return $result;
    }

    /**
     * Pega o identificador da última sessão
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 03/10/2019
     *
     * @return array
     */
    private function getEndSession($index)
    {
        if (isset($this->sessions[$index + 1])) {
            return $this->sessions[$index + 1];
        }

        return 'null';
    }

    /**
     * Pega os dados de uma sessão
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 03/10/2019
     *
     * @return string
     */
    private function getSessionContent($text, $startNumber, $endNumber)
    {
        $result = [];

        preg_match(
            "/(\\s*?\\s{$startNumber}\\s(-|\\?))((?<content>[^>]*)\\s{$endNumber}\\s(-|\\?)|(?<content2>[^>]*))/i",
            $text,
            $result
        );

        if (isset($result['content2'])) {
            return $result['content2'];
        }

        return $result['content'];
    }

    /**
     * Parse das sessões (I, II, III, IV, V, VI)
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 03/10/2019
     *
     * @return array
     */
    private function parseSessions($content)
    {
        $processos = preg_split("/PAS\\sCVM/usi", $content);

        $result['descricao'] = $this->parseSessionName($processos[0]);
        $result['processos'] = null;
        $response = null;

        array_shift($processos);

        foreach ($processos as $key => $processo) {
            $response = $this->parseSession($processo);

            if ($response != null) {
                $result['processos'][] = $response;
            }
        }

        return $result;
    }

    /**
     * Parse do nome da sessnão
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 03/10/2019
     *
     * @return string
     */
    private function parseSessionName($nome)
    {
        $result = [];
        preg_match('#([^>]*):\s#i', $nome, $result);
        return trim($result[1]);
    }

    /**
     * Parse da sessão
     *
     * @version 1.0.0
     * @version 1.0.1 - Alteração
     *
     * <AUTHOR> Mesquita 03/10/2019
     * <AUTHOR> Santos - 21/01/2021
     *
     * @param array $content
     *
     * @return array $result
     */
    private function parseSession($content)
    {
        $result = [];
        $matches = [];
        foreach ($this->getPatterns() as $key => $pattern) {
            preg_match($pattern, $content, $matches);
            $result[$key] = $matches['result1'] ??  $matches['result'];
        }
        $result = $this->cleanArray($result);
        $result['relator'] = $result['relator(a)'] . ' / ' . $result['procurador(a)'];
        if (empty($result['nome']) && empty($result['sei']) && empty($result['data'])) {
            return null;
        }

        return $result;
    }

    /**
     * Monta array com as Regex de cada campo
     *
     * @version 1.0.0
     * @version 1.0.1 - Ajuste nos padrões das regexs
     *
     * <AUTHOR> Mesquita 03/10/2019
     * <AUTHOR> Santos - 21/01/2021
     *
     * @return array
     */
    private function getPatterns()
    {
        return [
            'pas' => '#n(°|..)\s(?<result>[^>]*?)\s#i',
            'nome' => '#n(°|..)\s([^>]*?)\s.\s(?<result>[^>]*?)\s?(Data:|\()#is',
            'sei' => '#\(sei\s?n..\s(?<result>[^>]*?)\)#is',
            'data' => '#Data:\s*(?<result>[0-9+A-ÿ\W]+?)\n#is',
            'horario' => '#Hor..rio:\s*(?<result>[0-9hH\s*]+)?\n#is',
            'superintendencia' => '#Superintend..ncia:\s(?<result>[A-ÿ\s]+?)\n#is',
            'relator(a)' => '#Relatora?:\s(?<result>[A-ÿ\W\s*]+?)\n#is',
            'procurador(a)' => '#Procuradora?:\s(?<result>[A-ÿ\W\s*]+?)\n#is',
            'local' => '#local:\s?(?<result>[^>]*?)objeto.do.processo#is',
            'objeto' => '#Objeto\s*do\s*processo:?\s*(?<result>[A-ÿ0-9\W]+?)Pauta[A-ÿ\s*]+Eletrônico#is',
            'publicacao' => '#(?<result>Pauta\s*[A-ÿ\W]+Di..rio\s*Eletr..nico[\WA-z0-9]+)#is'
        ];
    }

    /**
     * Função para filtrar os dados da resposta e apenas incluir os processos relacionados ao critério informado
     *
     * @version 1.0.0
     * @version 1.0.1 - Alterações
     *
     * <AUTHOR> Favoreto Makhoul - 04/07/2018
     *
     * @param array $data
     * @param string $nome
     *
     * @return array
     */
    private function filterDataToResults($data, $nome)
    {
        $resultado = false;

        foreach ($data as $key => $array) {
            $response[$key]['descricao'] = $array['descricao'];
            $response[$key]['processos'] = [];

            foreach ($array['processos'] as $value) {
                $similaridade = $this->matchWithName($value, $nome);
                if ($similaridade >= self::SIMILARIDADE) {
                    $resultado = true;
                    $response[$key]['processos'][] = $value;
                }
            }
        }

        if (!$resultado) {
            throw new Exception('Nenhum processo encontrado', 2);
        }

        return $response;
    }

    /**
     * Remove quebras de linha e espços em excesso
     *
     * @version 1.0.0
     *
     * <AUTHOR> Santos - 21/01/2021
     *
     * @param  array $arrayResult
     *
     * @return array $arrayClear
     */
    private function cleanArray($arrayResult)
    {
        foreach ($arrayResult as $key => $value) {
            $arrayClear[$key] = Str::cleanString($value);
        }
        return $arrayClear;
    }

    /**
     * Compara o nome buscado com os nomes no processo
     *
     * @version 1.0.0
     *
     * <AUTHOR> Santos 21/01/2021
     *
     * @param  array $value
     * @param  string $nome
     *
     * @return int $similaridade
     */
    private function matchWithName($value, $nome)
    {

        $value['nome'] = trim($value['nome']);
        $value['procurador(a)'] = trim($this->clearText($value['procurador(a)']));
        $value['relator(a)'] = trim($this->clearText($value['relator(a)']));
        $nome = trim($this->clearText($nome));
        $similaridade = '';

        $matchWithNames = array(
                    0 => $value['nome'],
                    1 => $value['procurador(a)'],
                    2 => $value['relator(a)']
        );

        foreach ($matchWithNames as $key => $value) {
            similar_text(strtolower($value), strtolower($nome), $similaridade);
            if ($similaridade >= self::SIMILARIDADE) {
                break;
            }
        }

        return $similaridade;
    }

    /**
     * Remove titulos dos nome que vem na busca.
     *
     * @version 1.0.0
     *
     * <AUTHOR> Santos - 21/01/2021
     * @param  string $text
     *
     * @return string $clearText
     */
    private function clearText($text)
    {
        $patterns = array(
                        0 => 'Dra.',
                        1 => 'Dr.',
                        2 => 'Diretor',
                        3 => 'Diretora'
        );

        $clearText = $text;
        foreach ($patterns as $key => $pattern) {
            $clearText = str_replace($pattern, '', $clearText);
        }

        return $clearText;
    }
}
