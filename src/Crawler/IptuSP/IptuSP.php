<?php

namespace App\Crawler\IptuSP;

use App\Crawler\Spider;
use App\Factory\PostgresDB;
use App\Helper\Document;
use Exception;

class IptuSP extends Spider
{
    private $limit;

    public function start()
    {
        $nome_documento = $this->param['nome_documento'];

        if (preg_match("/^[a-zA-Z]/", $nome_documento)) {
            $result = $this->getDataName($nome_documento);
        }
        if (preg_match("/^\d/", $nome_documento)) {
            $result = $this->getDataDocument(Document::removeMask($nome_documento));
        }

        if (empty($result)) {
            throw new Exception("A pesquisa não encontrou nenhum dado correspondente.", 2);
        }

        return $result;
    }


    public function validateAndSetCrawlerAttributes()
    {
        $this->limit = $this->param['limit'] ?? 100;

        $nome_documento = trim($this->param['nome_documento']);
        if (empty($nome_documento)) {
            throw new Exception('Parâmetro Inválido', 1);
        }
    }

    /**
     * Função de busca na base de dados pelo nome do contribuinte 1 ou 2
     *
     * @return mixed
     *
     * @param $nome_documento
     *
     * @throws \Exception
     */
    public function getDataName($nome_documento)
    {
        $data = (new PostgresDB())->connectCaptura()
            ->select('*')
            ->from('common.iptu_sp')
            ->where("nome_contribuinte1 LIKE :nome_contribuinte1")
            ->orWhere("nome_contribuinte2 LIKE :nome_contribuinte2")
            ->orderBy('id')
            ->setParameter('nome_contribuinte1', '%' . $nome_documento . '%')
            ->setParameter('nome_contribuinte2', '%' . $nome_documento . '%')
            ->setMaxResults($this->limit)
            ->execute()
            ->fetchAll();

        if (!$data) {
            throw new Exception('Nenhum resultado encontrado', 2);
        }

        return $data;
    }

    /**
     * Função de busca na base de dados pelo cpf/cnpj do contribuinte 1 ou 2
     *
     * @return mixed
     *
     * @param $nome_documento
     *
     * @throws \Exception
     */
    public function getDataDocument($nome_documento)
    {
        $data = (new PostgresDB())->connectCaptura()
            ->select('*')
            ->from('common.iptu_sp')
            ->where("cpf_cnpj_contribuinte1 = :cpf_cnpj_contribuinte1")
            ->orWhere("cpf_cnpj_contribuinte2 = :cpf_cnpj_contribuinte2")
            ->orderBy('id')
            ->setParameter('cpf_cnpj_contribuinte1', $nome_documento)
            ->setParameter('cpf_cnpj_contribuinte2', $nome_documento)
            ->setMaxResults($this->limit)
            ->execute()
            ->fetchAll();

        if (!$data) {
            throw new Exception('Nenhum resultado encontrado', 2);
        }

        return $data;
    }
}
