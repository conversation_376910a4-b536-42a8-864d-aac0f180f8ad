<?php

namespace App\Crawler\Cfc;

use App\Crawler\Spider;
use Exception;
use App\Helper\Document;

/**
 *  Clase de consulta da fonte CFC - CONSELHO FEDERAL DE CONTABILIDADE
 *
 *  <AUTHOR> - 17/07/2018
 *  Revisão:
 *  <AUTHOR> - 03/08/2018 - Adicionada validação dos parâmetros e
 *  finalizado o método parseResults para realizar consultas no Cfc
 *  <AUTHOR> - 06/08/2018 - Resultados obtidos com sucesso da página do Cfc.
 *  <AUTHOR> - 09/08/2018 - Finalização da nova classe Cfc.
 *  @version 2.2
 *  Refatoração:
 *  <AUTHOR> - 13/05/2022 - Refatoração da fonte
 *  @version 3.0
 */
class Cfc extends Spider
{
    /**
     * URL de acesso a fonte
     *
     * @var string
     */
    private const BASE_URL = 'https://www3.cfc.org.br/SPW/ConsultaNacionalCFC/cfc';
    private const URL_PESQUISA_PF = 'https://www3.cfc.org.br/spw/apis/consultacadastralcfc/cfc/ListarProfissional';
    private const URL_PESQUISA_PJ = 'https://www3.cfc.org.br/spw/apis/consultacadastralcfc/cfc/ListarEmpresa';

    protected function start()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__;
        }

        $this->setProxy();

        $document = $this->param['nome_cpf_cnpj'];
        $limit = $this->param['limit'];

        if (Document::validarCnpj($document)) {
            $document_type = 'CpfCnpj';
            $search_type = 'contains';
            $serach = Document::formatCpfOrCnpj($document);
            $url = self::URL_PESQUISA_PJ;
            $dataIndexes = 'companyOffice';
        } elseif (Document::validarCpf($document)) {
            $document_type = 'CpfCnpj';
            $search_type = 'contains';
            $serach = Document::formatCpfOrCnpj($document);
            $url = self::URL_PESQUISA_PF;
            $dataIndexes = 'person';
        } else {
            $document_type = 'Nome';
            $search_type = 'startswith';
            $serach = str_replace(',', '', htmlspecialchars_decode($document));
            $url = self::URL_PESQUISA_PF;
            $dataIndexes = 'person';
        }

        $params = [
            'DefaultSort' => '',
            'Filter' => "[[['" . $document_type . "','" . $search_type . "','" . $serach . "']]]",
            'Group' => '',
            'GroupSummary' => '',
            'IsCountQuery' => 'false',
            'PaginateViaPrimaryKey' => 'false',
            'PreSelect' => '',
            'PrimaryKey' => '',
            'RemoteGrouping' => 'false',
            'RemoteSelect' => 'false',
            'RequireGroupCount' => 'false',
            'RequiteTotalCount' => 'false',
            'Select' => '',
            'Skip' => '0',
            'Sort' => '',
            'StringToLower' => 'false',
            'Take' => $limit,
            'TotalSummary' => '',
            'Conselho' => '',
            '' => ''
        ];

        // Passe pela url principal
        $this->getResponse(self::BASE_URL, 'GET');

        // Faz a pesquisa
        $results = $this->getResponse($url . '?' . str_replace('%2C', ',', http_build_query($params)), 'GET');
        $results = json_decode($results);
        $results = $results->data;

        if (!$results) {
            if ($dataIndexes == 'person') {
                $url = self::URL_PESQUISA_PJ;
                $dataIndexes = 'companyOffice';

                // Se pesquisa for por nome e vier vazio, pesquisa novamente por empresa
                $results = $this->getResponse($url . '?' . str_replace('%2C', ',', http_build_query($params)), 'GET');
                $results = json_decode($results);
                $results = $results->data;

                if (!$results) {
                    throw new Exception("Nada encontrado", 2);
                }
            } else {
                throw new Exception("Nada encontrado", 2);
            }
        }

        $this->result = [
            'person' => [],
            'companyOffice' => []
        ];

        foreach ($results as $k => $result) {
            $indexes[$k] = [
                'name' => trim($result->Nome),
                'registerNumber' => $result->Registro,
                'registerType' => $result->Descricao_Tipo_Registro,
                'category' => ($dataIndexes == 'person') ? $result->Descricao_Categoria : '',
                'crc' => $result->EstadoConselho,
                'status' => $result->SituacaoCadastral
            ];
        }

        if ($dataIndexes == 'person') {
            $this->result['person'] = $indexes;
        } else {
            $this->result['companyOffice'] = $indexes;
        }

        return $this->result;
    }

    protected function validateAndSetCrawlerAttributes()
    {
    }
}
