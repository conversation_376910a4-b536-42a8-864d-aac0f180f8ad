<?php

namespace App\Crawler\Google;

use App\Crawler\Spider;
use App\Crawler\Google\GoogleResult;
use App\Helper\Str;
use Exception;

class Google extends Spider
{
    private const SEARCH_URI = 'https://www.googleapis.com/customsearch/v1';
    private const UPLEXIS_CX = '011037585287182197482:vjyua_g4uy0';

    private const DEFAULT_RESULTS_COUNT = 30;

    private const MAX_PAGES = 10;
    private const MAX_RESULTS_PER_PAGE = 10; // 10 é o máximo de resultados por página

    private const MAX_ALLOWED_RESULTS = 100;

    public $debug = false;

    private $aVetor = [];

    private $query = '';
    private $queryArray = [];
    private $googleHost = '';
    private $numOcorrencias = self::DEFAULT_RESULTS_COUNT;
    private $dominiosPermitidos = null;
    private $dominiosNaoPermitidos = null;

    protected function start()
    {
        $this->setAlternativeProxy();
        return $this->search();
    }

    /**
     * Valida os parametros enviados
     *
     * @return void
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['query'])) {
            throw new Exception('Query é parâmetro obrigatório');
        }
        if (empty($this->param['googleHost'])) {
            throw new Exception('googleHost é parâmetro obrigatório');
        }

        $this->query = $this->param['query'];

        $this->query = Str::completeSerialize($this->query);

        if (isset($this->param['queryArray'])) {
            $this->queryArray = $this->param['queryArray'];

            foreach ($this->queryArray as $key => $value) {
                $this->queryArray[$key] = Str::completeSerialize($value);
            }
        }

        $this->googleHost = $this->param['googleHost'];
        $this->bag = '';

        if ($this->param['bag']) {
            $this->bag = $this->param['bag'];
        }

        if ($this->param['numOcorrencias']) {
            $this->numOcorrencias = $this->param['numOcorrencias'];
        }

        if ($this->param['dominiosPermitidos']) {
            $this->dominiosPermitidos = $this->param['dominiosPermitidos'];
        }

        if ($this->param['dominiosNaoPermitidos']) {
            $this->dominiosNaoPermitidos = $this->param['dominiosNaoPermitidos'];
        }
    }

    /**
     * Faz a busca por um determinado termo no Google.
     * @param type $query
     * @param type $numOcorrencias
     * @return GoogleResult[]
     * @throws Exception
     */
    public function search()
    {
        $numOcorrencias = $this->numOcorrencias;
        $googleHost = $this->googleHost;
        $dominiosPermitidos = $this->dominiosPermitidos;
        $dominiosNaoPermitidos = $this->dominiosNaoPermitidos;
        $bag = $this->bag;
        $resultsCount = 0;

        if (count($this->queryArray) == 0) {
            $this->queryArray[] = $this->query;
        }

        foreach ($this->queryArray as $this->query) {
            // validação de query
            if (preg_match('/\*\*|xxx|\"\*\" AND|\* AND/iu', $this->query) || strlen($this->query) < 2) {
                throw new Exception('Nenhum resultado encontrado!', 2);
            }

            // $criteriaAndBag = explode(strtoupper(' AND '), $this->query);
            $criteriaAndBag = preg_split("/\s.[(AND)].\s+/ui", $this->query);
            $query = trim($criteriaAndBag[0]);
            $query = str_replace('"', '', $query);

            if (!empty($criteriaAndBag[1])) {
                $bag = str_replace(array("(","'",")",'"'), '', $criteriaAndBag[1]);
                $bag = str_replace(' OR ', '|', $bag);
            }

            $api_keys = array(
                "AIzaSyD3cZ2QPOg1WzKF-eenIO37d77JmTnXJ0A", //OK
                //"AIzaSyB6o0Al0Dt-awGN395y_BgNvi4oiJc5-yo", //Chave com erro de limit
                "AIzaSyANyhV1nfATWMV_y1SqqS_2x1t89mFaCgk", //OK
                "AIzaSyCMnqex0Zg3f12uvTcCT1LVqcs6qX33az0", //OK
                "AIzaSyB6Z9MAdAWjWosGODdJo9YUuVYsZZdjlEs", //OK
            );

            if (strlen($googleHost) > 2) {
                $googleHost = $this->setPaisUrl($googleHost);
            }

            $excludeDomain = str_replace('#', ' ', $dominiosNaoPermitidos);
            print "Sites ou Termos Excluidos: " . $excludeDomain . PHP_EOL;

            if ($this->debug) {
                print __METHOD__ . '(' . $googleHost . '|' . $query . ', ' . $numOcorrencias . ')' . PHP_EOL;
                print __METHOD__ . " Palavras Chaves: " . $bag . PHP_EOL;
            }

            // ajusta a quantidade de resultados se a quantidade solicitada for maior que o maximo permitido
            if ($numOcorrencias > self::MAX_ALLOWED_RESULTS) {
                if ($this->debug) {
                    print "Quantidade solicitada: ${numOcorrencias}. Ajustando quantidade de resultados para " .
                        self::MAX_ALLOWED_RESULTS . " (máximo permitido)." . PHP_EOL;
                }

                $numOcorrencias = self::MAX_ALLOWED_RESULTS;
            }

            for ($page = 1; $page <= self::MAX_PAGES; $page++) {
                $response = json_decode($this->getDataFromAPI(array(
                    'key' => $api_keys[array_rand($api_keys, 1)],
                    'cx' => self::UPLEXIS_CX,
                    'gl' => $googleHost,
                    'q' => $this->query,
                    'hq' => $query,
                    'orTerms' => $bag,
                    'excludeTerms' => $excludeDomain,
                    'filter' => 0,
                    'safe' => 'medium',
                    'num' => self::MAX_RESULTS_PER_PAGE,
                    'start' => (10 * ($page - 1)) + 1,
                )), true);

                if (isset($response['error'])) {
                    $msgsException = [];
                    foreach ($response['error']['errors'] as $error) {
                        $msgsException[] = $error['message'];
                    }
                    throw new Exception('Erro(s) ao consultar o Google: ' . implode(', ', $msgsException), 3);
                }

                if ($response['searchInformation']['totalResults'] > 0) {
                    foreach ($response['items'] as $result) {
                        if (!$this->checkResult($result, $query, $dominiosPermitidos, $dominiosNaoPermitidos)) {
                            continue;
                        }

                        $gcs = new GoogleResult();
                        $gcs->setTitle(isset($result['title']) ? strip_tags(self::slugify($result['title'])) : '');
                        $gcs->setUrl(isset($result['link']) ? strip_tags($result['link']) : '');
                        $gcs->setSnippet('');
                        if (isset($result['snippet'])) {
                            $gcs->setSnippet(strip_tags(self::slugify($result['snippet'])));
                        }
                        $this->aVetor[] = $gcs;
                        $resultsCount++;
                        if ($resultsCount >= $numOcorrencias) {
                            break 3;
                        }
                    }
                } else {
                    break;
                }
            }
        }

        return $this->filtroUnico($this->aVetor);
    }

    public function filtroUnico($array)
    {
        if ($this->debug) {
            print __METHOD__ . PHP_EOL;
        }
        $result = [];

        $serialized_array = array_map("serialize", $array);
        foreach ($serialized_array as $key => $val) {
            $result[$val] = true;
        }

        return array_map("unserialize", (array_keys($result)));
    }

    /**
     * Verifica o resultado
     *
     * <AUTHOR> Prates - 04/09/2019
     *
     * @param  array $result
     * @param  string $query
     * @param  string $allowedDomains
     * @param  string $notAllowedDomains
     *
     * @return boolean
     */
    private function checkResult($result, $query, $allowedDomains = '', $notAllowedDomains = '')
    {
        if (!empty($allowedDomains) && !preg_match($allowedDomains, strip_tags($result['link']))) {
            return false;
        }

        if (!empty($notAllowedDomains) && preg_match($notAllowedDomains, strip_tags($result['link']))) {
            return false;
        }

        preg_match('/\"([^"]*)/i', $query, $criteria);
        $criteria = $this->removeAccents($criteria[1]);
        //Verifica se tem o critério no resultado
        foreach ($result as $value) {
            if (is_array($value)) {
                continue;
            }
            if (preg_match('/' . $criteria . '/i', $this->removeAccents($value))) {
                return true;
            }
        }

        return false;
    }

    /**
     * Chama a API do Google
     * @param array $queryParameters
     * @return type
     */
    private function getDataFromAPI($queryParameters)
    {
        if ($this->debug) {
            print __METHOD__ . PHP_EOL;
        }

        return $this->getResponse(self::SEARCH_URI . '?' . http_build_query($queryParameters, '', '&'), 'GET');
    }

    /**
     *  A relação de países pode ser encontrada neste link: https://pt.wikipedia.org/wiki/ISO_3166-1
     *
     * @param $pais
     * @return mixed
     */
    public function setPaisUrl($pais)
    {
        $urlPaisLista = array(
            'ALL' => 'en',
            'ASC' => 'ac',
            'AND' => 'ad',
            'ARE' => 'ae',
            'AFG' => 'af',
            'ATG' => 'ag',
            'AIA' => 'ai',
            'ARM' => 'am',
            'AGO' => 'ao',
            'ARG' => 'ar',
            'ASM' => 'as',
            'AUT' => 'at',
            'AUS' => 'au',
            'AZE' => 'az',
            'BIH' => 'ba',
            'BGD' => 'bd',
            'BEL' => 'be',
            'BFA' => 'bf',
            'BGR' => 'bg',
            'BHR' => 'bh',
            'BDI' => 'bi',
            'BEN' => 'bj',
            'BRN' => 'bn',
            'BOL' => 'bo',
            'BRA' => 'br',
            'BHS' => 'bs',
            'BWA' => 'bw',
            'BLR' => 'by',
            'BLZ' => 'bz',
            'CAN' => 'ca',
            'KHM' => 'kh',
            'CCK' => 'cc',
            'COD' => 'cd',
            'CAF' => 'cf',
            'CAT' => 'ca',
            'COG' => 'cg',
            'CHE' => 'ch',
            'CIV' => 'ci',
            'COK' => 'ck',
            'CHL' => 'cl',
            'CMR' => 'cm',
            'CHN' => 'cn',
            'COL' => 'co',
            'CRI' => 'cr',
            'CUB' => 'cu',
            'CPV' => 'cv',
            'CZE' => 'cz',
            'DEU' => 'de',
            'DJI' => 'dj',
            'DNK' => 'dk',
            'DMA' => 'dm',
            'DOM' => 'do',
            'DZA' => 'dz',
            'ECU' => 'ec',
            'EST' => 'ee',
            'EGY' => 'eg',
            'ESP' => 'es',
            'FIN' => 'fi',
            'FJI' => 'fj',
            'FSM' => 'fm',
            'FRA' => 'fr',
            'GAB' => 'ga',
            'GEO' => 'ge',
            'GUF' => 'gf',
            'GGY' => 'gg',
            'GHA' => 'gh',
            'GIB' => 'gi',
            'GRL' => 'gl',
            'GMB' => 'gm',
            'GLP' => 'gp',
            'GRC' => 'gr',
            'GTM' => 'gt',
            'GUY' => 'gy',
            'HKG' => 'hk',
            'HND' => 'hn',
            'HRV' => 'hr',
            'HTI' => 'ht',
            'HUN' => 'hu',
            'IDN' => 'id',
            'IRQ' => 'iq',
            'IRL' => 'ie',
            'ISR' => 'il',
            'IMN' => 'im',
            'IND' => 'in',
            'IOT' => 'io',
            'ISL' => 'is',
            'ITA' => 'it',
            'JEY' => 'je',
            'JAM' => 'jm',
            'JOR' => 'jo',
            'JPN' => 'jp',
            'KEN' => 'ke',
            'KIR' => 'ki',
            'KGZ' => 'kg',
            'KOR' => 'kr',
            'KWT' => 'kw',
            'KAZ' => 'kz',
            'LAO' => 'la',
            'LBN' => 'lb',
            'LCA' => 'lc',
            'LIE' => 'li',
            'LKA' => 'lk',
            'LSO' => 'ls',
            'LTU' => 'lt',
            'LUX' => 'lu',
            'LVA' => 'lv',
            'LBY' => 'ly',
            'MAR' => 'ma',
            'MDA' => 'md',
            'MNE' => 'me',
            'MDG' => 'mg',
            'MKD' => 'mk',
            'MLI' => 'ml',
            'MNG' => 'mn',
            'MSR' => 'ms',
            'MLT' => 'mt',
            'MUS' => 'mu',
            'MDV' => 'mv',
            'MWI' => 'mw',
            'MEX' => 'mx',
            'MYS' => 'my',
            'MOZ' => 'mz',
            'NAM' => 'na',
            'NER' => 'ne',
            'NFK' => 'nf',
            'NGA' => 'ng',
            'NIC' => 'ni',
            'NLD' => 'nl',
            'NOR' => 'no',
            'NPL' => 'np',
            'NRU' => 'nr',
            'NIU' => 'nu',
            'NZL' => 'nz',
            'OMN' => 'om',
            'PAN' => 'pa',
            'PER' => 'pe',
            'PHL' => 'ph',
            'PAK' => 'pk',
            'PCN' => 'pn',
            'PRI' => 'pr',
            'PSE' => 'ps',
            'PRT' => 'pt',
            'PRY' => 'py',
            'QAT' => 'qa',
            'ROU' => 'ro',
            'SRB' => 'rs',
            'RUS' => 'ru',
            'RWA' => 'rw',
            'SAU' => 'sa',
            'SLB' => 'sb',
            'SYC' => 'sc',
            'SWE' => 'se',
            'SGP' => 'sg',
            'SHN' => 'sh',
            'SVN' => 'si',
            'SVK' => 'sk',
            'SLE' => 'sl',
            'SEN' => 'sn',
            'SMR' => 'sm',
            'SOM' => 'so',
            'STP' => 'st',
            'SLV' => 'sv',
            'TCD' => 'td',
            'TGO' => 'tg',
            'THA' => 'th',
            'TKL' => 'tk',
            'TLS' => 'tl',
            'TKM' => 'tm',
            'TON' => 'to',
            'TUN' => 'tn',
            'TUR' => 'tr',
            'TTO' => 'tt',
            'TWN' => 'tw',
            'TZA' => 'tz',
            'UKR' => 'ua',
            'UGA' => 'ug',
            'GBR' => 'uk',
            'USA' => 'us',
            'URY' => 'uy',
            'UZB' => 'uz',
            'VCT' => 'vc',
            'VEN' => 've',
            'VGB' => 'vg',
            'VIR' => 'vi',
            'VNM' => 'vn',
            'VUT' => 'vu',
            'WSM' => 'ws',
            'ZAF' => 'za',
            'ZMB' => 'zm',
            'ZWE' => 'zw',
        );

        return $urlPaisLista[$pais];
    }

    private function slugify($string)
    {
        $string = html_entity_decode($string, ENT_QUOTES, 'UTF-8');
        $string = preg_replace('~[^\\pL\d.]+~u', ' ', $string);
        $string = trim($string, ' ');
        return $string;
    }

    /**
     * Remove Acentos
     *
     * <AUTHOR> Prates< - 04/09/2019
     *
     * @param  string $string
     * @return string
     */
    private function removeAccents($string)
    {
        return trim(strtr(
            utf8_decode($string),
            utf8_decode('àáâãäçèéêëìíîïñòóôõöùúûüýÿÀÁÂÃÄÇÈÉÊËÌÍÎÏÑÒÓÔÕÖÙÚÛÜÝ'),
            'aaaaaceeeeiiiinooooouuuuyyAAAAACEEEEIIIINOOOOOUUUUY'
        ));
    }
}
