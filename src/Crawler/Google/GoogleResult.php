<?php

namespace App\Crawler\Google;

class GoogleResult
{
    /**
     * Representa a title
     *
     * @var string
     */
    public $title;

    /**
     * Representa a snippet
     *
     * @var string
     */
    public $snippet;

    /**
     * Representa a url
     *
     * @var string
     */
    public $url;

    public function __construct($title = null, $snippet = null, $url = null)
    {
        $this->title = $title;
        $this->snippet = $snippet;
        $this->url = $url;
    }

    public function getTitle()
    {
        return $this->title;
    }

    public function getSnippet()
    {
        return $this->snippet;
    }

    public function getUrl()
    {
        return $this->url;
    }

    public function setTitle($v)
    {
        $this->title = $v;
    }

    public function setSnippet($v)
    {
        $this->snippet = $v;
    }

    public function setUrl($v)
    {
        $this->url = $v;
    }
}
