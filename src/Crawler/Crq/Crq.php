<?php

namespace App\Crawler\Crq;

use App\Crawler\Spider;
use Exception;
use App\Helper\Str;
use App\Helper\Document;

class Crq extends Spider
{
    private const URL_4REGIAO_MAIN = 'https://www.crq4.org.br/';
    private const URL_4REGIAO = 'https://www.crq4.org.br/default.php?p=consultapublica/cppf.php';
    private const URL_3REGIAO = 'http://sistema.crq3.org.br:8080/' .
        'siscafweb/carregaConselho.do?1564753147118&tipoAcesso=4&s=1&tipoConsulta=pf&controle=0&sigla=crqrj&ini=1';
    private const URL_3REGIAO_CAPTCHA = 'http://sistema.crq3.org.br:8080/siscafweb/captcha.jpg';
    private const POST_URL_4REGIAO = 'https://www.crq4.org.br/default.php?p=consultapublica/resccppf.php';
    private const POST_URL_3REGIAO = 'http://sistema.crq3.org.br:8080/siscafweb/pesquisaRegistro.do';

    private $cpf = '';

    private $limit = 0;

    protected function start()
    {
        $terceira = null;

        //$terceira = $this->getDadosTerceiraRegiao();
        $this->setProxy();
        $quarta = $this->getDadosQuartaRegiao();
        $results = $this->getUniDados($terceira, $quarta);

        return $results;
    }

    private function getDadosQuartaRegiao()
    {
        $this->getResponse(self::URL_4REGIAO_MAIN);
        $html = $this->getResponse(self::URL_4REGIAO);
        $html = Str::encoding($html);

        preg_match('/<img src="consultapublica\/captcha.php(.*?)"\sid="captcha"/', $html, $capctha_url);

        $capctha_url = "https://www.crq4.org.br/consultapublica/captcha.php?" . $capctha_url[1];

        $this->getImageAndBreakCaptcha($capctha_url);

        $params = array(
            'numero' => $this->param['criterio'],
            'cod_cap' => $this->captcha
        );

        $response =  html_entity_decode(
            $this->getResponse(
                self::POST_URL_4REGIAO,
                'POST',
                $params
            ),
            ENT_NOQUOTES,
            'UTF-8'
        );

        //remove o document.location
        $pattern = '/<script>document\.(.*?)<\/script>/m';
        $response = preg_replace($pattern, '', $response);
        $response = Str::encoding($response);

        //Pega os dados da quarta regiao
        preg_match_all('/<b>Nome<\/b><\/td> <td bgcolor=\'#ffffff\'>(.*?)<\/td>/m', $response, $quimicoNome);
        preg_match_all('/<b>Carteira<\/b><\/td> <td bgcolor=\'#ffffff\'>(.*?)\s?<\/td>/m', $response, $quimicoCarteira);
        preg_match_all('/Localidade<\/b><\/td> <td bgcolor=\'#ffffff\'>(.*?)<\/td>/m', $response, $localQuimico);
        preg_match_all('/Habilitação<\/b><\/td> <td bgcolor=\'#ffffff\'>(.*?)<\/td>/m', $response, $habQuimico);
        preg_match_all('/<b>Situação<\/b><\/td> <td bgcolor=\'#ffffff\'>\s(.*?)\s<\/td>/m', $response, $sitQuimico);

        $nome = $quimicoNome[1];

        foreach ($nome as $key => $value) {
            $data[] = [
                'nome' => $quimicoNome[1][$key],
                'carteira' => $quimicoCarteira[1][$key],
                //'localidae' => $localQuimico[1][$key],
                'habilitacao' => $habQuimico[1][$key],
                'situacao' => $sitQuimico[1][$key],
                'regiao' => 'Quarta regiao'
            ];

            $contador = count($data);

            if ($contador >= $this->limit) {
                break;
            }
        }
        return $data;
    }

    private function getDadosTerceiraRegiao()
    {
        $html = $this->getResponse(self::URL_3REGIAO);
        $html = Str::encoding($html);

        $this->getImageAndBreakCaptcha(self::URL_3REGIAO_CAPTCHA);

        $params = [
            'registroconselho' => '',
            'nome' => $this->nome,
            'criterioNome' => '2',
            'categoriaatual' => '',
            'cidade' => '',
            'situacaoatual' => '',
            'cpf' => $this->cpf,
            'consulta' => '1',
            'respostaInput' => $this->captcha
        ];

        $response =  html_entity_decode(
            $this->getResponse(
                self::POST_URL_3REGIAO,
                'POST',
                $params
            ),
            ENT_NOQUOTES,
            'UTF-8'
        );
        $pattern = '/<script>document\.(.*?)<\/script>/m';
        $response = preg_replace($pattern, '', $response);

        $response = Str::encoding($response);

        preg_match('/pesquisaRegistro\.do\?pagina=10&consulta=1">(.*?)<\/a>/m', $response, $pag);
        preg_match_all('/width="nome" class="tabletdLeft">\s(.*?)\s*?<\/td>/m', $response, $nome);
        preg_match_all('/"categoriaAtual " class="tabletd">\s(.*?)\s?<\/td>/m', $response, $habilitacao);
        preg_match_all('/"situacaoAtual " class="tabletdLeft">\s(.*?)\s?<\/td>/m', $response, $situacao);
        preg_match_all('/<td class="tabletd" id="" height="20" width="80">\s(.*?)\s?<\/td>/m', $response, $carteira);

        foreach ($nome[1] as $key => $value) {
            $data[] = [
                'nome' => $value,
                'carteira' => $carteira[1][$key],
                'habilitacao' => $habilitacao[1][$key],
                'situacao' => $situacao[1][$key],
                'regiao' => 'Terceira região'
            ];
            $contador = count($data);
            if ($contador >= $this->limit) {
                break;
            }
        }
        return $data;
    }

    private function getUniDados($terceira, $quarta)
    {
        if (!empty($terceira) && !empty($quarta)) {
            return array_merge($terceira, $quarta);
        }

        if (empty($terceira) && !empty($quarta)) {
            return $quarta;
        }

        if (!empty($terceira) && empty($quarta)) {
            return $terceira;
        }

        throw new Exception("Não foi encontrado resultado para esta consulta", 2);
    }

    /**
     * Salva imagem do captcha
     *
     * <AUTHOR> Alves
     *
     * @version 1.0.0
     *
     * @return void
     */
    private function getImageCaptcha($url)
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__;
        }

        file_put_contents($this->captcha_path, $this->getResponse($url));
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if (!empty($this->param['limite'])) {
            $this->limit = $this->param['limite'];
        }

        $param = $this->param['criterio'];

        if (Document::validarCpf($param)) {
            $this->cpf = Document::removeMask($param);
        } else {
            $this->nome = $param;
        }
    }
}
