<?php

namespace App\Crawler\SerasaPep;

use App\Crawler\Spider;
use Exception;
use App\Helper\Document;
use Symfony\Component\DomCrawler\Crawler;

class SerasaPep extends Spider
{
    private const BASE_URL = 'https://sitenet.serasa.com.br/Logon/';
    private const LOGIN_URL = 'https://sitenet.serasa.com.br/Logon/autentica';
    private const PEP_BASE_URL = 'https://sitenet05.serasa.com.br/PEP/default.aspx';
    private const DEBUG = false;

    private $usuario;
    private $cpf;
    private $senha;

    protected function start()
    {
        if (self::DEBUG) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        $parameter = $this->getParameterAndlogin($this->usuario, $this->senha);

        $searchResponse = $this->getSearchResponse($this->cpf, $parameter);

        $this->checkErrors($searchResponse);

        return [
            'analytics' => $this->getPepAnalytics($searchResponse),
            'titular' => $this->getPepTitular($searchResponse)
        ];
    }

    private function checkErrors($html)
    {
        if (self::DEBUG) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }
        if (
            preg_match(
                "/<span\\s*id=.[^<]*?.\\s*style=.[^<]*?.>\\s*<font\\s*face=.[^<]*?.\\s*" .
                    "size=.\\d*?.>\\s*([^<]*?)\\s*<\\/font>\\s*<\\/span>\\s*<\\/div>\\s*<\\/form>/",
                $html,
                $output
            )
        ) {
            if (preg_match("/n.{0,4}o\\s*consta/isu", $html)) {
                throw new Exception($output[1], 2);
            }
            throw new Exception($output[1], 3);
        }
    }

    private function getPepAnalytics($searchResponse)
    {
        try {
            $crawler = new Crawler($searchResponse);

            if (count($crawler->filter('#Conteudo_ucInformacoesRelacionadoAnalytics_pnlRelacionado .m-top8')) < 1) {
                return [];
            }

            return $crawler->filter('#Conteudo_ucInformacoesRelacionadoAnalytics_pnlRelacionado .m-top8')
                ->each(function ($node) {
                    return [
                        'cpf' => trim($node->filterXPath(
                            "//*[@id='Conteudo_ucInformacoesRelacionadoAnalytics_lblCpf']"
                        )->text()),
                        'tipo' => trim($node->filterXPath(
                            "//*[@id='Conteudo_ucInformacoesRelacionadoAnalytics_lblIdentificacao']"
                        )->text()),
                        'nome' => trim($node->filterXPath(
                            "//*[@id='Conteudo_ucInformacoesRelacionadoAnalytics_lblNome']"
                        )->text()),
                    ];
                });
        } catch (\Exception $e) {
            throw new Exception('Falha ao parsear os dados Pep Analytics: ' . $e->getMessage(), 3);
        }
    }

    private function getPepTitular($searchResponse)
    {

        try {
            $crawler = new Crawler($searchResponse);

            if (
                count($crawler->filter('#Conteudo_ucInformacoesTitular_pnlCargo hr')) < 1
                && count($crawler->filter('#Conteudo_ucInformacoesTitular_lblCpf')) < 1
            ) {
                return [];
            }


            $getCargos = [];
            if (count($crawler->filter('#Conteudo_ucInformacoesTitular_pnlCargo hr')) > 0) {
                $getCargos = $this->getCargos($searchResponse);
            }

            $getRelacionados = [];
            if (count($crawler->filter('#Conteudo_ucInformacoesTitular_pnlRelacionados hr')) > 0) {
                $getRelacionados = $this->getRelacionados($searchResponse);
            }

            if (count($crawler->filter('#Conteudo_ucInformacoesRelacionadoAnalytics_pnlTitulares .m-top8')) > 0) {
                $qtdRelacionados = count(
                    $crawler->filter('#Conteudo_ucInformacoesRelacionadoAnalytics_pnlTitulares .m-top8')
                ) + 1;

                $getInfoRel = function ($info, $position) use ($crawler) {
                    $position = $position - 1;
                    return trim($crawler->filterXPath(
                        "//*[@id='Conteudo_ucInformacoesRelacionadoAnalytics_rptTitulares_{$info}_{$position}']"
                    )->text());
                };

                $getInfo2 = function ($info) use ($crawler, $searchResponse) {
                    $match = [];
                    preg_match(
                        '/Conteudo_ucInformacoesRelacionadoAnalytics_lbl' . $info . '/',
                        $searchResponse,
                        $match
                    );

                    $aux = '-';

                    if (!empty($match[0][0])) {
                        $crawler = $crawler->filterXPath(
                            "//*[@id='Conteudo_ucInformacoesRelacionadoAnalytics_lbl{$info}']"
                        )->text();

                        $aux = trim($crawler);
                    }

                    return $aux;
                };

                for ($x = 1; $x < $qtdRelacionados; $x++) {
                    $relacionado = array(
                        'nome' => $getInfoRel('lblNome', $x),
                        'documento' => $getInfoRel('lnkCpfTitular', $x),
                        'relacionamento' => 'POSSÍVEL RELACIONADO'
                    );
                    $getRelacionados[] = $relacionado;
                }

                return [
                    'cpf' => $getInfo2('Cpf'),
                    'tipo' => $getInfo2('Identificacao'),
                    'nome' => $getInfo2('Nome'),
                    'nascimento' => $getInfo2('DataNascimento'),
                    'cargos' => $getCargos,
                    'relacionados' => $getRelacionados,
                ];
            }

            $getInfo = function ($info) use ($crawler) {
                return trim($crawler->filterXPath(
                    "//*[@id='Conteudo_ucInformacoesTitular_lbl{$info}']"
                )->text());
            };

            return [
                'cpf' => $getInfo('Cpf'),
                'tipo' => $getInfo('Identificacao'),
                'nome' => $getInfo('Nome'),
                'nascimento' => $getInfo('DataNascimento'),
                'cargos' => $getCargos,
                'relacionados' => $getRelacionados,
            ];
        } catch (\Exception $e) {
            throw new Exception('Falha ao parsear os dados Pep Titular: ' . $e->getMessage(), 3);
        }
    }

    /**
     * Retorna um array com os cargos encontrados
     *
     * @param   string $html
     * @return  void
     * <AUTHOR> Machado - 01/08/2018
     * Revisão:
     * @version 1.0.0
     */
    private function getCargos($html)
    {
        if (self::DEBUG) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }
        $cargos = [];
        $crawler = new Crawler($html);
        $qtdCargos = count($crawler->filter('#Conteudo_ucInformacoesTitular_pnlCargo hr')) + 1;

        $getInfo = function ($info, $position) use ($crawler) {
            return trim($crawler->filterXPath(
                "//*[@id='Conteudo_ucInformacoesTitular_rptCargos_lbl{$info}_{$position}']"
            )->text());
        };
        for ($x = 0; $x < $qtdCargos; $x++) {
            $cargo = array(
                'descricao' => $getInfo('Cargo', $x),
                'inicio' => $getInfo('DataNomeacao', $x),
                'fim' => $getInfo('DataExoneracao', $x),
                'motivo' => $getInfo('MotivoExoneracao', $x),
                'orgaoEmpresa' => $getInfo('Orgao', $x),
                'endereco' => $getInfo('EnderecoOrgao', $x),
            );
            $cargos[] = $cargo;
        }
        return $cargos;
    }

    private function getRelacionados($html)
    {
        if (self::DEBUG) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }
        $relacionados = [];
        $crawler = new Crawler($html);
        $qtdRelacionados = count($crawler->filter('#Conteudo_ucInformacoesTitular_pnlRelacionados hr')) + 1;

        $getInfo = function ($info, $position) use ($crawler) {
            return trim($crawler->filterXPath(
                "//*[@id='Conteudo_ucInformacoesTitular_rptRelacionados_{$info}_{$position}']"
            )->text());
        };
        for ($x = 0; $x < $qtdRelacionados; $x++) {
            $relacionado = array(
                'nome' => $getInfo('lblNome', $x),
                'documento' => $getInfo('lnkCpfRelacionado', $x),
                'relacionamento' => $getInfo('lblRelacionamento', $x)
            );
            $relacionados[] = $relacionado;
        }
        return $relacionados;
    }

    private function getSearchResponse($cpf, $parameter)
    {
        if (self::DEBUG) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }
        $html = $this->getResponseSSLv6(self::PEP_BASE_URL . '?Param=' . $parameter, 'GET');

        if (preg_match('/Usu.*?rio\ssem\sacesso\sao\sPEP/is', $html)) {
            throw new Exception('Usuário sem acesso ao PEP.');
        }

        if (!preg_match('/<input.*id="Conteudo_txtCpf".*>/isu', $html)) {
            throw new Exception('Falha ao acessar a pagina de busca de PEP');
        }

        if (
            preg_match("/__VIEWSTATE.\\s*value=.([^<]*?).\\s*\\/>/", $html, $outputViewState)
            && preg_match("/__VIEWSTATEGENERATOR.\\s*value=.([^<]*?).\\s*\\/>/", $html, $outputViewStateGenerator)
            && preg_match("/__EVENTVALIDATION.\\s*value=.([^<]*?).\\s*\\/>/", $html, $outputEventValidation)
        ) {
            $viewState = $outputViewState[1];
            $viewStateGenerator = $outputViewStateGenerator[1];
            $eventValidation = $outputEventValidation[1];
        } else {
            throw new Exception('Falha ao captura os parametros de busca', 3);
        }

        $postParams = array(
            '__EVENTTARGET' => '',
            '__EVENTARGUMENT' => '',
            '__VIEWSTATE' => $viewState,
            '__VIEWSTATEGENERATOR' => $viewStateGenerator,
            '__EVENTVALIDATION' => $eventValidation,
            'ctl00$Conteudo$txtCpf' => $cpf,
            'ctl00$Conteudo$txtNome' => '',
            'ctl00$Conteudo$btnPesquisar' => 'Pesquisar'
        );

        $headers = array(
            'Accept-encoding' => 'gzip',
            'Connection: keep-alive',
            'Host: sitenet05.serasa.com.br',
            'Origin: https://sitenet05.serasa.com.br',
            'Referer: https://sitenet05.serasa.com.br/PEP/default.aspx',
            'Upgrade-Insecure-Requests: 1'
        );

        $response = $this->getResponseSSLv6(self::PEP_BASE_URL, 'POST', $postParams, $headers);

        if (preg_match("/exibirMensagem\\(\\'.*\\',\\'(.*)\\'\\);/", $response, $erro) && !empty($erro[1])) {
            if (strpos($erro[1], 'CPF digitado não consta na base de dados') !== false) {
                throw new Exception("CPF não encontrado", 2);
            }
            throw new Exception($erro[1]);
        }

        return $response;
    }

    private function getParameterAndlogin($usuario, $senha)
    {
        if (self::DEBUG) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        $response = $this->getResponseSSLv6(self::BASE_URL);
        $frkey = file_get_contents(dirname(__FILE__) . '/frkey.txt');
        $frkey2 = file_get_contents(dirname(__FILE__) . '/frkey2.txt');
        $params = [
           'CHECKBOX_TROCA_SENHA' => '',
           'FLAG_ALTERADA' => '',
           'FPRINT' => 'cf6c62ddcfc3e17053a9c0794aac80a0',
           'FRKEY' => $frkey,
           'FRKEY2' => $frkey2,
           'LOGON' => $this->usuario,
           'SENHA' => $this->senha,
        ];

        $html = $this->getResponseSSLv6(self::LOGIN_URL, 'POST', $params);

        if (preg_match('/(?:usu.*?rio|senha).*inv.*?lido/is', $html)) {
            throw new Exception('Usuário e/ou senha inválido.', 6);
        } elseif (preg_match('/acesso.inv.*?lid./is', $html)) {
            throw new Exception('Acesso inválido.', 6);
        }

        if (
            preg_match(
                "/<form\\s*id=.formulario.\\s*action=\"([^<]*?)\"\\s*method=.post.>\\s*" .
                    "<input\\s*type=.hidden.\\s*name=\"([^<]*?)\"\\s*value=\"([^<]*?)\"/isu",
                $html,
                $output
            )
        ) {
            $url = $output[1];
            $postData = array(
                $output[2] => $output[3]
            );

            $this->getResponseSSLv6($url, 'POST', $postData);

            return $output[3];
        } elseif (preg_match("/param:\\s*.(.*?).\\s*,/isu", $html, $output)) {
            return $output[1];
        }

        throw new Exception('Falha durante processo de login', 6);
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if (self::DEBUG) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }
        if (isset($this->param['cpf']) && !empty($this->param['cpf']) && Document::validarCpf($this->param['cpf'])) {
            $cpf = \App\Helper\Document::formatCpfOrCnpj($this->param['cpf']);
        } else {
            throw new Exception('Parâmetro de criterio inválido', 1);
        }
        if (isset($this->auth['usuario']) && !empty($this->auth['usuario'])) {
            $usuario = trim($this->auth['usuario']);
        } else {
            throw new Exception('Parâmetro de login inválido', 1);
        }
        if (isset($this->auth['senha']) && !empty($this->auth['senha'])) {
            $senha = trim($this->auth['senha']);
        } else {
            throw new Exception('Parâmetro de senha inválido', 1);
        }

        $this->cpf = $cpf;
        $this->usuario = $usuario;
        $this->senha = $senha;
    }
}
