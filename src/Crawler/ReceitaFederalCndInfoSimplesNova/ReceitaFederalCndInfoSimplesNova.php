<?php

namespace App\Crawler\ReceitaFederalCndInfoSimplesNova;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\InfoSimplesManager;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

class ReceitaFederalCndInfoSimplesNova extends Spider
{
    private $manager;
    private $documento = "";
    private $certificateName = "";
    private $certificateLocalPath = "";
    private $certificateS3Path = "";
    private $certificateUrl = "";
    private $pdf = null;
    private const MFP_S3_PATH = 'captura/receita_federal_cnd/';

    public function start()
    {
        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::MFP_S3_PATH . $this->certificateName;
        $this->certificateUrl = S3_STATIC_URL . $this->certificateS3Path;
        $this->manager = new InfoSimplesManager();

        try {
            $responseData = json_encode(
                $this->manager->searchReceitaFederalCndNova($this->documento)
            );

            $jsonData = json_decode($responseData, true);

            $result = $this->parseData($jsonData['data'][0]);
            $result['pdf'] = $this->pdf;

            return $result;
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    private function parseData($response)
    {
        if (!empty($response['site_receipt'])) {
            $pdf = $this->getResponse($response['site_receipt']);

            if (preg_match('/PDF\-1\.4/is', $pdf)) {
                $text = $this->savePdfAndReturnText($pdf);

                return $this->getParseDataToResponse($text);
            }
        }
    }

    private function savePdfAndReturnText($pdf)
    {
        file_put_contents($this->certificateLocalPath, $pdf);
        $text = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'layout',
            'nopgbrk'
        ]);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        $this->pdf = $this->certificateUrl;

        return $text;
    }

    private function getParseDataToResponse($result)
    {
        $result = utf8_decode($result);
        $arrPatterns = [
            'documento' => ['/[cnpj|cpf]:(.*?)\\n/i', null],
            'razao_social' => ['/nome:.(.*?)\\n/i', null],
            'titulo' => ['/(Ressalv.*)/is', null],
            'data_emissao' => ['/emitida\s.s\s\d{2}:\d{2}:\d{2}\sdo\sdia\s(.*?)\s<hora\se\sdata\sde/i', null],
            'hora_emissao' => ['/emitida\s.s\s(.*?)\sdo/i', null],
            'data_validade' => ['/v.lida\sat.\s(.*?)\.\\n/i', null],
            'codigo_controle' => ['/C.digo.de.controle.da.certid.o:\s(.*?)\\n/i', null],
            'source' => 'Receita Federal'
        ];

        $parseDados = Util::parseDados($arrPatterns, $result);

        return $parseDados;
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCpfOuCnpj($this->param['documento']) || empty($this->param['documento'])) {
            throw new Exception("Parâmetro inválido", 1);
        }

        $this->documento = $this->param['documento'];
    }
}
