<?php

namespace App\Crawler\CvmCadastroHtml;

use App\Crawler\CvmCadastroHtml\Models\CvmCadastroHtmlModel;
use App\Crawler\Spider;
use App\Helper\Document;
use Exception;

class CvmCadastroHtml extends Spider
{
    private $cnpj;
    private $html = '';
    private $obj;
    private const BASE_URL = 'https://cvmweb.cvm.gov.br/SWB/Sistemas/SCW/CPublica/' .
                             'CiaAb/ResultBuscaParticCiaAb.aspx?CNPJNome=<cnpj>&TipoConsult=C';
    private const CAPTCHA_URL = 'http://sistemas.cvm.gov.br/asp/cvmwww/captcha/aspcaptcha.asp';

    public function start()
    {
        $this->searchByCnpj();
        $this->stepFinalParse($this->html);

        if (!($this->obj instanceof CvmCadastroHtmlModel)) {
            throw new Exception("Nao foi possivel recuperar os dados!");
        }

        return $this->obj;
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCnpj($this->param['cnpj'])) {
            throw new Exception("Parâmentro inválido.", 1);
        }

        $this->cnpj = Document::removeMask($this->param['cnpj']);
    }

    private function searchByCnpj()
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $url = preg_replace('/<cnpj>/is', $this->cnpj, self::BASE_URL);

        $response = $this->getResponse($url);

        $this->whereAmI($response, self::BASE_URL);
    }

    private function whereAmI($response, $url = false)
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }
        $response = utf8_encode($response);

        #Pediu captcha na lista de empresas
        $pattern = '@Digite\s*o\s*n.*?mero\s*que\s*aparece\s*abaixo@is';
        if (preg_match($pattern, $response)) {
            $this->stepRenovaCaptchaEmpresa($response, $url);
            return;
        }

        #Solicitou captcha na página inicial
        $pattern = '@Digite\s*o\s*n.*?mero\s*que\s*aparece\s*ao\s*lado@is';
        if (preg_match($pattern, $response)) {
            $this->stepRenovaCaptcha($response, $url);
            return;
        }

        #Erro no quebra captcha
        $pattern = '@N.*?mero\s*incorreto@is';
        if (preg_match($pattern, $response)) {
            $this->stepRenovaCaptcha($response, $url);
            return;
        }

        #Alguma mensagem de erro foi identificada
        $pattern = '@<p.*?Msg.*?>(.*?)</p>@is';
        if (preg_match($pattern, $response, $match)) {
            $msg = trim($match[1]);
            if ($msg === "Não foram encontrados participantes para esta consulta") {
                $this->noResultParse($response);
                return;
            }
            throw new Exception($msg, 3);
        }

        #Lista de Empresas
        $pattern = '@>\s*CVM\s*-\s*DADOS\s*CADASTRAIS\s*-\s*RESULTADO\s*DA\s*PESQUISA\s*</@is';
        if (preg_match($pattern, $response)) {
            $this->getCompanyList($response);
            return;
        }

        $pattern = '/Forbidden\:\s*Access\s*is\s*denied/i';
        if (preg_match($pattern, $response)) {
            throw new Exception("Erro acesso negado", 1);
        }

        #Tela Final, dados da Empresa
        $this->html .= $response . '---OK';
        return;
    }

    private function stepRenovaCaptcha($result, $url = false)
    {
        $this->getImageAndBreakCaptcha(self::CAPTCHA_URL);
        $url_nova = preg_replace("/strCAPTCHA=\d\d\d\d/", "strCAPTCHA=" . $this->captcha, $url);

        $result = $this->getResponse($url_nova, 'GET', [], [
            'Referer' => $url
        ]);

        return $this->whereAmI($result, $url_nova);
    }

    private function stepRenovaCaptchaEmpresa($result, $url = false)
    {
        $this->getImageAndBreakCaptcha(self::CAPTCHA_URL);
        $url_nova = preg_replace('/RedirCad.asp/', 'CadDemAgent.asp', $url);
        $url_nova = preg_replace("/strCAPTCHA=\d\d\d\d/", "strCAPTCHA=" . $this->captcha, $url_nova);

        $result = $this->getResponse($url_nova, 'GET', [], [
            'Referer' => $url
        ]);

        $pattern = '@Digite\s*o\s*n.*?mero\s*que\s*aparece\s*abaixo@is';
        if (preg_match($pattern, $result)) {
            $url_nova = preg_replace('/CadDemAgent.asp/', 'CadAdmCart.asp', $url_nova);
            return $this->whereAmI($result, $url_nova);
        }

        return $this->whereAmI($result, $url_nova);
    }

    private function getCompanyList($response)
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }
        $pattern = '@(Tipo_Partic=.*?)\'@is';
        if (preg_match_all($pattern, $response, $matches)) {
            foreach ($matches[1] as $link) {
                $link = str_replace(' ', '', $link);
                $url = (strpos($link, 'strCAPTCHA'))
                ? self::BASE_URL . "RedirCad.asp?$link"
                : self::BASE_URL . "RedirCad.asp?strCAPTCHA={$this->captcha}&$link";

                $response = $this->getResponse($url, 'GET', [], [
                    'Referer' => self::BASE_URL
                ]);

                $this->whereAmI($response, $url);
            }
        } else {
            throw new Exception('Impossível identificar link na Lista de Empresas', 4);
        }
    }

    private function stepFinalParse($result)
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }
        $result = preg_replace('#(href=[\'"]?)(/asp)#is', "$1http://sistemas.cvm.gov.br$2", $result);
        $result = base64_encode(gzcompress(utf8_decode($result), 9));
        $cvmModel = new CvmCadastroHtmlModel();
        $cvmModel->html = $result;
        $this->obj = $cvmModel;
    }

    private function noResultParse($response)
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }
        $this->html = $response;
    }
}
