<?php

namespace App\Crawler\ReceitaFederalCnd;

use Exception;
use App\Helper\Pdf;
use App\Helper\Document;
use App\Manager\InfoSimplesManager;
use App\Manager\S3\S3;
use App\Manager\S3\Bucket\StaticUplexisBucket;

/**
 * Classe da fonte Receita Fedaral CDN - InfoSimples
 *
 * <AUTHOR> <<EMAIL>>
 *
 */
class ReceitaFederalCndInfoSimples
{
    private const DOC_CPF = 2;
    private const DOC_CNPJ = 1;

    private $documento = '';
    private $docType = 0;
    private $certificateName = '';
    private $certificatePathS3  = '';
    private $certificatePath = '';
    private $requestManager;
    private $manager;
    /**
     * Construtor da Classe ReceitaFederalCndInfoSimples
     *
     * @param String $documento         Parâmetro informado
     * @param Int    $docType           Tipo do documento indormado
     * @param String $certificatePathS3 Caminho do S3
     * @param String $certificateName   Nome do certidicado que será salvo
     * @param String $tempPath          Caminho da pasta temp
     * @param String $certificatePath   Caminho do certificado
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 15/04/2019
     *
     * @return $[type]
     */
    public function __construct(
        $documento,
        $docType,
        $certificatePathS3,
        $certificateName,
        $tempPath,
        $certificatePath,
        $requestManager
    ) {
        $this->documento = $documento;
        $this->docType         = $docType;
        $this->certificateName = $certificateName;
        $this->certificatePathS3  = $certificatePathS3;
        $this->_tempPath  = $tempPath;
        $this->certificatePath  = $certificatePath;
        $this->requestManager = $requestManager;
        $this->manager = new InfoSimplesManager();
    }

    /**
     * Monta URL para acessar API e pegar o jSON de retorno
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 16/04/2019
     *
     * @return array
     */
    public function getData()
    {
        return $this->getJsonData();
    }

    /**
     * Pega a resposta Json da API
     *
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 16/04/2019
     *
     * @return array
     */
    private function getJsonData()
    {
        try {
            $responseData = json_encode(
                $this->manager->searchReceitaFederalCnd($this->documento)
            );
            if ($responseData == "") {
                throw new Exception("Falha ao capturar informações em InfoSimples", 6);
            }
            $jsonData       = json_decode($responseData);
            $response       = $this->checkData($jsonData);
            return $response;
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    /**
     * Checa a resposta da API para ver se houve erro ou não
     *
     * @param Json $jsonData - Json de retorno da API
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 16/04/2019
     *
     * @return array
     */
    private function checkData($jsonData)
    {
        if ($jsonData->code == 200) {
            return $this->getResultSuccess($jsonData);
        }

        if ($jsonData->code == 611) {
            return $this->getResultNotIssued($jsonData);
        }

        if ($jsonData->code == 607) {
            throw new Exception('Parâmetro de criterio inválido', 1);
        }

        if ($jsonData->code == 605) {
            throw new Exception('Erro: Falha ao capturar informações IS Timeout', 3);
        }

        if ($jsonData->code == 620) {
            throw new Exception(implode(', ', $jsonData->errors), 6);
        }

        $errMsg = $jsonData->code_message;
        if (!empty($jsonData->errors)) {
            $errMsg = implode(', ', $jsonData->errors);
        }
        throw new Exception($errMsg, 3);
    }

    /**
     * Monta Resposta com o Certificado Emitido
     *
     * @param Json $jsonData - Json de retorno da API
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 16/04/2019
     *
     * @return array
     */
    private function getResultSuccess($jsonData)
    {
        if (!$jsonData->data || !$jsonData->receipt->sites_urls || count($jsonData->receipt->sites_urls) == 0) {
            throw new Exception('Erro: Falha ao capturar resposta', 3);
        }

        $htmlUrl = $jsonData->receipt->sites_urls[0];

        $responseHtml = $this->requestManager->getResponseByFileGet(
            $htmlUrl
        );

        $documento = '';

        if ($this->docType == self::DOC_CPF) {
            $documento = $jsonData->data->normalizado_cpf;
        } elseif ($this->docType == self::DOC_CNPJ) {
            $documento = $jsonData->data->normalizado_cnpj;
        }

        $data_emissao = preg_replace(
            '/\s\d{1,2}:\d{1,2}:\d{1,2}/i',
            '',
            $jsonData->data->normalizado_consulta_datahora
        );

        $hora_emissao = preg_replace(
            '/\d{1,2}\/\d{1,2}\/\d{4}/i',
            '',
            $jsonData->data->normalizado_consulta_datahora
        );

        $descricao = preg_replace(
            '/(CPF|CNPJ).\s\d+.\d+.\d+.\d+(.\d+)?/i',
            '',
            $jsonData->data->descricao
        );

        $descricao = preg_replace(
            '/Certid.o\semi(.*)/i',
            '',
            $descricao
        );

        $descricao = preg_replace(
            '/C.digo\sde\scon(.*)inv.+dará\seste\sdocumento./i',
            '',
            $descricao
        );

        $this->savePdf($responseHtml);

        $result = [
            "documento" => $documento,
            "razao_social" => $jsonData->data->razao_social,
            "data_emissao" => $data_emissao,
            "hora_emissao" => $hora_emissao,
            "data_validade" => $jsonData->data->validade,
            "codigo_controle"  => $jsonData->data->consulta_comprovante,
            "titulo" => $descricao,
            "pdf" => $this->certificatePathS3,
            "source" => 'infoSimples'
        ];

        return $result;
    }

    /**
     * Monta Resposta quando não tem parametros para emitir o certificado
     *
     * @param Json $jsonData - Json de retorno da API
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 16/04/2019
     *
     * @return array
     */
    private function getResultNotIssued($jsonData)
    {
        if (!$jsonData->receipt->sites_urls || count($jsonData->receipt->sites_urls) == 0) {
            throw new Exception('Erro: Falha ao capturar tela anterior', 3);
        }

        $htmlUrl = $jsonData->receipt->sites_urls[0];

        $responseHtml = $this->requestManager->getResponseByFileGet(
            $htmlUrl
        );

        $this->savePdf($responseHtml);

        $result['documento'] = $this->documento;
        $result['razao_social'] = '-';
        $result['data_emissao'] = date('d/m/Y');
        $result['hora_emissao'] = date('H:i:s');
        $result['data_validade'] = '-';
        $result['codigo_controle'] = '-';
        $result['titulo'] = 'As informações disponíveis na Secretaria da Receita Federal do Brasil - ' .
            'RFB sobre o contribuinte ' . Document::formatCpfOrCnpj($this->documento) .
            ' são insuficientes para a emissão de certidão por meio da Internet. <br> ' .
            'Para consultar sua situação fiscal, acesse https://cav.receita.fazenda.gov.br . ' .
            'Para maiores esclarecimentos, consulte a página http://idg.receita.fazenda.gov.br/' .
            'orientacao/tributaria/certidoes-e-situacao-fiscal/orientacoes-gerais .';
        $result['pdf'] = $this->certificatePathS3;
        $result['source'] = 'infoSimples';

        return $result;
    }

    /**
     * Salvar o PDF na AWS S3
     *
     * @param String $html - html da ultima tela
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 16/04/2019
     *
     * @return empty
     */
    private function savePdf($html)
    {
        if (!$this->html4pdf($html, $this->certificateName)) {
            throw new Exception("Erro ao gravar PDF!", 3);
        }

        (new S3(new StaticUplexisBucket()))->save(
            'captura/receita_federal_cnd/' . $this->certificateName,
            $this->certificatePath
        );
    }

    /**
     * Converte o HTML em um PDF temporário
     *
     * @param String $html - html da ultima tela
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 16/04/2019
     *
     * @return Boolean
     */
    private function html4pdf($html)
    {
        (new Pdf())->saveHtmlToPdf($html, $this->certificatePath);

        $res = false;
        if (file_exists($this->certificatePath)) {
            $res = true;
        }
        return $res;
    }
}
