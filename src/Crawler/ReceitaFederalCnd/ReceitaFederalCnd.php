<?php

namespace App\Crawler\ReceitaFederalCnd;

use Exception;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Helper\Util;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Exception\PdfException;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use Carbon\Carbon;

/**
 * Classe de da fonte Receita Federal (23) CND
 * Certidão de Débitos Relativos a Créditos Tributários Federais e à Dívida Ativa da União
 *
 * <AUTHOR>
 * <AUTHOR> - 29/05/2018 - Correção das URLs de consulta
 * <AUTHOR> - 20/01/2022 - Correção das URLs de consulta
 */
class ReceitaFederalCnd extends Spider
{
    private const BASE_URL = 'https://solucoes.receita.fazenda.gov.br';
    private const HOST_URL = 'solucoes.receita.fazenda.gov.br';
    private const URL = self::BASE_URL . '/Servicos/certidaointernet/<type>/Emitir';
    private const ISSUE_CERTIFICATE_URL = self::BASE_URL
        . '/Servicos/certidaointernet/<type>/Consultar/RelacaoCertidao';
    private const RETRY = 5;
    private const RETRY_SITE_ERRORS = 5;
    private const DEFAULT_PARSE = 1;
    private const INSUFFICIENT_DATA_PARSE = 2;
    private const TYPE_CPF = 'PF';
    private const TYPE_CNPJ = 'PJ';
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const MFP_S3_PATH = 'captura/receita_federal_cnd/';
    private $url;
    private $certificateName;
    private $certificateLocalPath;
    private $certificateS3Path;
    private $certificateUrl;
    private $pdf = null;
    private $retrySiteErrors = 0;
    private $retries = 0;
    public $debug;
    private $document;
    private $type;
    /**
     *  Valida e retorna os parametros
     *
     *  <AUTHOR> Borges Pereira - 20/01/2022
     *
     *  @version 1.0.0
     *
     *  @return array
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }
        if (!Document::validarCpfOuCnpj($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }
        $this->param['cpf_cnpj'] = preg_replace('#\D#isu', '', $this->param['cpf_cnpj']);
        $this->document = $this->param['cpf_cnpj'];
        $this->type =  Document::validarCpf($this->param['cpf_cnpj']) ? self::TYPE_CPF : self::TYPE_CNPJ;
    }
    /**
     * Fonte (23) CND
     *
     *  <AUTHOR> Borges Pereira - 20/01/2022
     *
     *  @version 1.0.0
     *
     *  @return array
     */
    protected function start()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }
        try {
            $uniqd = md5(uniqid(rand(), true));
            $this->certificateName = "{$uniqd}.pdf";
            $this->certificateLocalPath = "/tmp/{$this->certificateName}";
            $this->certificateS3Path = self::MFP_S3_PATH . $this->certificateName;
            $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;
            $this->url = str_replace('<type>', $this->type, self::URL);
            $this->setAlternativeProxy();
            return $this->issueCertificate();
        } catch (Exception $e) {
            $this->retries++;
            if (
                $e->getCode() == 2
                || $this->retries >= self::RETRY || $e->getCode() == 6
            ) {
                throw new PdfException($e->getMessage(), $e->getCode(), $this->pdf);
            }

            return $this->start();
        }
    }
    /**
     *  Iniciar emissão da certidão
     *
     *  <AUTHOR> Borges Pereira - 20/01/2022
     *
     *  @version 1.0.0
     *
     *  @return array
     */
    private function issueCertificate()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $result = $this->postDocument();

        if (empty($result)) {
            throw new Exception('Tentativas excedidas', 3);
        }

        return $result;
    }
    /**
     *  Resolver o captcha
     *
     *  <AUTHOR> Borges Pereira - 20/01/2022 - Alterado para hCatpcha.
     *
     *  @version 1.0.0
     *
     *  @return string
     */
    private function resolveCaptcha()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $html = $this->getResponseWithRetry($this->url);

        if (!preg_match('/data-sitekey=\'(.*?)\'/isu', $html, $matches)) {
            throw new Exception('Não foi possível recuperar o sitekey', 3);
        }
        $this->captcha = $this->solveHCaptcha($matches[1], 'https://' . self::HOST_URL);
    }
    /**
     *  Enviar post de emissão da certidão
     *
     *  <AUTHOR> Borges Pereira - 20/01/2022
     *  <AUTHOR> Vidal - 01/04/2022 - Comentei geração de segunda via por instabilidade.
     *
     *  @version 1.0.0
     *
     *  @return array
     */
    private function postDocument()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $this->resolveCaptcha();

        $params = [
            'NI' => Document::formatCpfOrCnpj($this->document),
            'h-captcha-response' => $this->captcha
        ];

        $headers = [
            'Host: ' . self::HOST_URL,
            'Origin: ' . self::BASE_URL,
            'Referer: ' . $this->url,
            'Upgrade-Insecure-Requests: 1',
            'Content-Type: application/x-www-form-urlencoded',
            'User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
        ];

        $response = $this->getResponseWithRetry($this->url . '/Verificar', 'POST', $params, $headers);

        if (preg_match("/inscrição cancelada por multiplicidade [\s\S]*?\./", $response, $message)) {
            throw new Exception($message[0], 6);
        }

        if (preg_match('/deve.ser.emitida.para.o.cnpj.da.matriz/is', $response)) {
            return $this->checkResponseWithoutResult($response);
        }

        if (preg_match("/suspensa pela Secretaria da Receita Federal do Brasil/is", $response)) {
            throw new Exception("Inscrição suspensa pela Secretaria da Receita Federal do Brasil", 6);
        }

        if (preg_match("/inscrição pendente de regularização/is", $response)) {
            $msg = 'Inscrição pendente de regularização. Documento: ' . Document::formatCpfOrCnpj($this->document);
            throw new Exception($msg, 6);
        }

        //Escolher entre gerar nova certidão ou pegar segunda via
        // if (preg_match('/Emiss.*o.de.nova.certid.*<\/a>/i', $response)) {
        //     $result = $this->postIssueDocument();

        //     if (!empty($result)) {
        //         return $result;
        //     }

        //     // Como não conseguiu pegar na segunda via, vai atualizar o captcha.
        //     $this->resolveCaptcha();
        //     $params['h-captcha-response'] = $this->captcha;
        //     $this->getResponseWithRetry($this->url . '/Verificar', 'POST', $params);
        // }

        return $this->getCertificate();
    }

    /**
     *  Checa e retorna o parse resultado
     *
     *  <AUTHOR> Borges Pereira - 20/01/2022 - Verificando se o retorno é um PDF.
     *
     *  @version 1.0.0
     *
     *  @return array
     */
    private function checkAndParseResult($result)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (is_array($result)) {
            return $result;
        }

        if (preg_match('/Proxy.Error/i', $result)) {
            if ($this->retrySiteErrors >= self::RETRY_SITE_ERRORS) {
                throw new Exception('Erro de proxy', 3);
            }

            $this->retrySiteErrors++;
            return $this->issueCertificate();
        }

        //Certidão emitida, fazer o parse
        if (preg_match('/PDF\-1\.4/is', $result)) {
            return $this->parseData($result, self::DEFAULT_PARSE);
        }

        if (
            preg_match(
                '/insuficientes/i',
                $result
            )
        ) {
            $this->saveHtmlToPdf($result);
            return $this->parseData($result, self::INSUFFICIENT_DATA_PARSE);
        }

        if ($this->retrySiteErrors >= self::RETRY_SITE_ERRORS) {
            throw new Exception('Houve um erro com o conteúdo da certidão', 3);
        }

        $this->retrySiteErrors++;
        return $this->issueCertificate();
    }

    /**
     *  Retorna certificado
     *
     *  <AUTHOR> Borges Pereira - 20/01/2022
     *
     *  @version 1.0.0
     */
    public function getCertificate()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $url = $this->url . "/Emitir?Ni=$this->document";

        $res = [];

        $response = function () use ($url) {
            $this->setAlternativeProxy();
            $content = $this->getResponseWithRetry($url);
            while (preg_match('/em.processamento/i', $content)) {
                // Aguardar 15 segundos para emissão do certificado
                echo 'Aguardando 15 segundos para a emissão do certificado' . PHP_EOL;
                sleep(15);
                $content = $this->getResponseWithRetry($url);
            }

            ['Id' => $id, 'Status' => $status] = json_decode($content, true);
            if ($status == "MENSAGEM") {
                echo 'O site retornou uma mensagem, aguardando' . PHP_EOL;

                //Recuperar o ID da mensagem
                // ['Id' => $id,] = json_decode($content, true);

                sleep(20);

                if ($id) {
                    $content = $this->getResponseWithRetry($this->url . "/ResultadoEmissao/$id");

                    $res = $this->checkIssueCertificate($content);
                }
            }

            if (isset($res)) {
                if (is_array($res)) {
                    return $res;
                }
            }

            return $content;
        };

        return $this->checkAndParseResult($response());
    }

    public function checkIssueCertificate($content)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }
        $msg = '';

        if (preg_match("/insuficientes para a emiss/is", $content)) {
            $msg = 'As informações disponíveis na Secretaria da Receita Federal do Brasil - RFB e na ' .
                    'Procuradoria-Geral da Fazenda Nacional - PGFN sobre o contribuinte são insuficientes ' .
                    'para a emissão de certidão por meio da Internet.';
        }

        if (preg_match("/situa.*?o cadastral declarada inapta [\s\S]*?\./is", $content, $message)) {
            $msg = $message[0];
        }

        if (preg_match("/inscri.*?o cancelada por multiplicidade [\s\S]*?\./is", $content, $message)) {
            $msg = $message[0];
        }

        if ($msg) {
            $result = [
                'documento' => Document::formatCpfOrCnpj($this->document),
                'razao_social' => '',
                'titulo' => $msg,
                'source' => 'Receita Federal'
            ];
            return $result;
        }
    }

    public function checkResponseWithoutResult($response)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }
        $cnpj = '';
        if (preg_match('/(\d{2}\.\d{3}\.\d{3}\/\d{4}\-\d{2})/', $response, $match)) {
            $cnpj = ': ' . $match[1];
        }
        $msg = 'A certidão deve ser emitida para o CNPJ da matriz' . $cnpj;
        $result = [
            'documento' => Document::formatCpfOrCnpj($this->document),
            'razao_social' => '',
            'titulo' => $msg,
            'source' => 'Receita Federal'
        ];
        return $result;
    }

    /**
     *  Converter HTML para PDF e salvar no S3
     *
     *  <AUTHOR> Borges Pereira - 20/01/2022
     *
     *  @version 1.0.0
     */
    private function saveHtmlToPdf($response)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        (new Pdf())->saveHtmlToPdf($response, $this->certificateLocalPath);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        $this->pdf = $this->certificateUrl;
    }

    /**
     *  Salvar o PDF no s3 e retornar texto
     *
     *  <AUTHOR> Borges Pereira - 20/01/2022
     *
     *  @version 1.0.0
     *
     *  @return string
     */
    private function savePdfAndReturnText($pdf)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        file_put_contents($this->certificateLocalPath, $pdf);
        $text = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'layout',
            'nopgbrk'
        ]);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        $this->pdf = $this->certificateUrl;

        return $text;
    }

    /**
     *  Post para pegar certidão que já foi gerada 2 via
     *
     *  <AUTHOR> Borges Pereira - 20/01/2022 - Ajuste para pegar a 2 via no novo site
     *
     *  @version 1.0.0
     *
     *  @return string
     */
    private function postIssueDocument()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $this->resolveCaptcha();

        $params = [
            'Ni' => $this->document,
            'TipoPesquisa' => $this->type == 'PJ' ? 1 : 2,
            'PeriodoInicio' => Carbon::now()->subMonths(6)->format('d/m/Y'),
            'PeriodoFim' => Carbon::now()->format('d/m/Y'),
            'h-captcha-response' => $this->captcha
        ];

        $url = str_replace('<type>', $this->type, self::ISSUE_CERTIFICATE_URL);

        $html = $this->getResponseWithRetry($url, 'POST', $params);

        if (!preg_match('/<h3>rela.*o.*emitidas/i', $html)) {
            // Não conseguiu gerar a lista de certidões já emitidas.
            return [];
        }

        preg_match_all(
            '/<tr\sclass=\".*\">\s*<td[^>]*>.*\s*<td[^>]*>(.*?)<\/td>\s*.*\s.*\s*<td[^>]*>'
                . '(.*?)<\/td>.*\s*.*\s*.*href="(.*?)"/i',
            $html,
            $matches
        );

        $links = $matches[3];

        foreach ($links as $index => $link) {
            $tipo = html_entity_decode(strtolower($matches[1][$index]));
            $situacao = html_entity_decode(strtolower($matches[2][$index]));

            if ($tipo == 'negativa' && $situacao == 'válida') {
                $response = $this->getResponseWithRetry(self::BASE_URL . $link);
                return $this->checkAndParseResult($response);
            }
        }
        return [];
    }

    public function verifyEmptyPdf($pdf)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        file_put_contents($this->certificateLocalPath, $pdf);

        $text = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'layout',
            'nopgbrk'
        ]);

        if (empty($text)) {
            throw new Exception('Erro de PDF em branco', 3);
        }
    }

    /**
     *  Parse dos dados e salva PDF no S3
     *
     *  <AUTHOR> Borges Pereira - 20/01/2022 - Ajuste para verificar o tipo de parse
     *
     *  @version 1.0.0
     *
     *  @return array
     */
    private function parseData($result, $type)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $this->verifyEmptyPdf($result);

        if ($type == self::DEFAULT_PARSE) {
            $text = $this->savePdfAndReturnText($result);
        } else {
            $text = htmlspecialchars_decode($this->cleanText($result));
        }

        $result = $this->getParseDataToResponse($text, $type);
        $result['pdf'] = $this->certificateUrl;
        return $result;
    }

    /**
     *  Limpa quebras da string
     *
     *  @param string $text
     *
     *  <AUTHOR> Borges Pereira - 20/01/2022
     *
     *  @version 1.0.0
     *
     *  @return string
     */
    public static function cleanText($text)
    {
        $text = preg_replace('/\n/', " ", $text);
        $text = preg_replace('/\s{2,}/', " ", $text);
        $text = str_replace('&nbsp;', '', $text);
        return preg_replace('/\t/', " ", $text);
    }

    /**
     *  Parse dos dados de acordo com a resposta
     *
     *  <AUTHOR> Borges Pereira - 20/01/2022 - Ajuste para fazer o parse do texto do PDF.
     *
     *  @version 1.0.0
     *
     *  @return array
     */
    private function getParseDataToResponse($result, $type)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $result = utf8_decode($result);
        $arrPatterns = [
            'documento' => ['/[cnpj|cpf]:(.*?)\\n/i', null],
            'razao_social' => ['/nome:.(.*?)\\n/i', null],
            'titulo' => ['/(Ressalv.*)/is', null],
            'data_emissao' => ['/emitida\s.s.\d{2}:\d{2}:\d{2}\sdo\sdia\s(.*?).<hora.e.data.de/i', null],
            'hora_emissao' => ['/emitida\s.s\s(.*?)\sdo/i', null],
            'data_validade' => ['/v.lida\sat.\s(.*?)\.\\n/i', null],
            'codigo_controle' => ['/C.digo.de.controle.da.certid.o:\s(.*?)\\n/i', null],
            'source' => 'Receita Federal'
        ];

        if ($type == self::INSUFFICIENT_DATA_PARSE) {
            $arrPatterns['titulo'] = ['/(as\sinforma..es\sdispon.veis.*?atendimento.*?\.)/i', null];
        }

        $parseDados = Util::parseDados($arrPatterns, $result);

        if ($type == self::INSUFFICIENT_DATA_PARSE) {
            $parseDados['documento'] = Document::formatCpfOrCnpj($this->document);
            $parseDados['titulo'] = preg_replace('/\s<br(.*?)a>./iu', '', $parseDados['titulo']);
            $texto = $parseDados['titulo'];
            preg_match('/dispon.veis na\s(.*?)\ssobre/iu', $texto, $match);
            $texto = preg_replace('/' . $match[1] . '/iu', '<strong>' . $match[1] . '</strong>', $texto);
            preg_match('/contribuinte\s(.*?)\ss.o/iu', $texto, $match);
            $documento = str_replace('/', '\/', $match[1]);
            $texto = preg_replace('/' . $documento . '/iu', '<strong>' . $match[1] . '</strong>', $texto);
            $texto = str_replace('insuficientes', '<strong>insuficientes</strong>', $texto);
            $parseDados['titulo'] = $texto;
        }

        return $parseDados;
    }

    /**
     * Repete a requisição até obter resultado. As vezes retorna página em branco por causa de bloqueio
     *
     * <AUTHOR> Vidal - 01/04/2022
     * <AUTHOR> VIdal - 04/05/2022 - Add headers e user-agent (Twitterbot)
     *
     * @param string $url
     * @param string $method
     * @param array $param
     * @param array $headers
     *
     * return string
     */
    private function getResponseWithRetry(
        $url,
        $method = 'GET',
        $param = [],
        $headers = []
    ) {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }
        $retries = 0;
        $patternEmpty = '/empty.reply/i';
        $patternRejected = '/request.rejected/i';
        $requestRejectError = 'Requisição bloqueada pelo site';
        $patternErroConsulta = '/Não foi possível realizar a consulta. Tente mais tarde./i';
        $requestErroConsulta = 'Não foi possível realizar a consulta.';

        if (empty($headers)) {
            $headers = [
                'Host: ' . self::HOST_URL,
                'Upgrade-Insecure-Requests: 1',
                'User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
            ];
        }

        while ($retries < self::RETRY) {
            try {
                $error = null;
                $this->setAlternativeProxy();
                $response = $this->getResponse($url, $method, $param, $headers);
                if (preg_match($patternRejected, $response)) {
                    throw new Exception($requestRejectError, 3);
                }
                if (preg_match($patternErroConsulta, $response)) {
                    throw new Exception($requestErroConsulta, 3);
                }
                break;
            } catch (\Exception $e) {
                if ($retries == self::RETRY) {
                    throw $e;
                }
                if (
                    !preg_match($patternEmpty, $e->getMessage())
                    && $e->getMessage() != $requestRejectError
                    && $e->getMessage() != $requestErroConsulta
                ) {
                    break;
                }
                echo 'A requisição retornou uma página vazia ou rejeitada por bloqueio. Repetindo...' . PHP_EOL;
                $retries++;
            }
        }

        return $response;
    }
}
