<?php

namespace App\Crawler\LicenciamentoAmbientalRJ;

use App\Crawler\Spider;
use App\Helper\Document;
use DateTime;
use Exception;

class LicenciamentoAmbientalRJ extends Spider
{
    private const BASE_URL = "http://***********";
    private $limit;
    private $name = "";
    private $cpfCnpj = "";
    private $data = [];


    public function start()
    {
        $this->makeRequest();
        return $this->data;
    }

    /**
     * Faz a requisição de cada item.
     * <AUTHOR>
     * @return void
     */
    private function makeRequest()
    {
        $params = [
            "CpfCnpj" => $this->cpfCnpj,
            "NomeRequerente" => $this->name,
            "tipoDoc" => "CNPJ",
            "NumeroLicenca" => "",
            "NumeroProcessoAdministrativo" => "",
            "TipoLicenca" => "",
            "Atividade" => "",
        ];

        $results = $this->getResponse(
            self::BASE_URL . "/SCUP.API/ConsultaUnificada/ConsultaProcessos",
            'POST',
            $params
        );

        if ($results === "[]") {
            throw new Exception('Sem resultados encontrados.', 2);
        }

        $results = json_decode($results, true);

        $this->data['existeMaisResultados'] = 0;

        if (count($results) > $this->limit) {
            $this->data['existeMaisResultados'] = 1;
        }

        $results = array_slice($results, 0, $this->limit);

        foreach ($results as $result) {
            $this->makeRequestAndParseDetails($result);
        }
    }

    /**
     * Faz a requisição e estrutura os dados dos detalhes dos itens.
     * <AUTHOR> Pereira
     * @param string $result
     * @return void
     */
    private function makeRequestAndParseDetails($result)
    {
        $url = "/SCUP.API//detalhamento/consultaDetalhamento";
        $parameters = "/{$result['IdProcesso']}/{$result['IdLicenca']}/{$result['IdUnidade']}";
        $data = $this->getResponse(self::BASE_URL . $url . $parameters);
        $data = json_decode($data, true);

        if (empty($data["Empreendimento"])) {
            $data["Empreendimento"][] = $result;
        }

        if ($result['banco'] == 3 || $result['banco'] == 2) {
            $params = [
                "IdProcesso" => $result['IdProcesso'],
                "NumeroProcesso" => $result['NumeroProcesso']
            ];

            $data = $this->getResponse(
                self::BASE_URL . "/SCUP.API//detalhamento/consultaDetalhamentoPad",
                "POST",
                $params
            );

            $data = json_decode($data, true);

            if (empty($data["Empreendimento"]) && count($data["EmpreendimentoAMBIENTE"]) == 0) {
                $data["Empreendimento"][] = $result;
            } elseif (count($data["EmpreendimentoAMBIENTE"]) > 0) {
                $data["Empreendimento"] = $data["EmpreendimentoAMBIENTE"];
            }

            foreach ($data["Tramitacao"] as $key => $value) {
                $data["Tramitacao"][$key]["DATA_ENVIO"] = $this->formatDataYYYMMDD(
                    $data["Tramitacao"][$key]["DATA_ENVIO"]
                );
            }
        }

        $this->data[] = $data;
    }

    /**
     * Trata data yyyymmdd para dd/mm/yyyy.
     * <AUTHOR> Pereira
     * @param string $data
     * @return string
     */
    private function formatDataYYYMMDD($data)
    {
        $date = DateTime::createFromFormat('Ymd', $data);
        return $date->format('d/m/Y');
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->limit = $this->param['limit'];

        if (empty($this->limit)) {
            $this->limit = 10;
        }

        if (empty($this->param['cpf_cnpj_name'])) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        if (!is_numeric(Document::removeMask(trim($this->param['cpf_cnpj_name'])))) {
            $this->name = $this->param['cpf_cnpj_name'];
        } else {
            $this->cpfCnpj = Document::removeMask(trim($this->param['cpf_cnpj_name']));
            $this->cpfCnpj = Document::formatCpfOrCnpj($this->cpfCnpj);
        }
    }
}
