<?php

namespace App\Crawler\Anac;

use App\Crawler\Spider;
use Exception;
use App\Factory\MongoDB;

class Anac extends Spider
{
    private const INDEX_MONGODB = 'search';
    private const LIMIT = 15;
    private $type;

    protected function start()
    {
        $results = $this->getParams();
        $dados = $this->formataArray($results);

        //Para quando a consulta não encontrar nenhum resultado
        if (empty($dados) || $dados == 'OK') {
            throw new Exception('Nada encontrado', 2);
        }

        return $dados;
    }

    protected function getParams()
    {
        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('common')
            ->setCollection('anac');

        if ($this->type == 'doc') {
            $fields = ['cpf_cnpj_prop','cpf_cnpj_op'];
            $results = $manager->atlasSearch(
                $this->param['cpf_cnpj'],
                self::INDEX_MONGODB,
                $this->limit,
                $fields
            );

            return $results;
        }

        $fields = ['proprietario','operador'];

        $results = $manager->atlasSearch(
            $this->param['nome'],
            self::INDEX_MONGODB,
            $this->limit,
            $fields,
            'phrase'
        );

        return $results;
    }

    protected function formataArray($vetor)
    {
        $dados = [];

        foreach ($vetor as $value) {
            $dados[] = $value;
        }

        return $dados;
    }

    protected function validateAndSetCrawlerAttributes()
    {
        $this->limit = $this->param['limite'] ?? self::LIMIT;

        if (!empty($this->param['nome'])) {
            $this->type = 'nome';
        } elseif (isset($this->param['cpf_cnpj']) && !empty($this->param['cpf_cnpj'])) {
            $this->type = 'doc';
        } else {
            throw new Exception('Parâmetro inválido');
        }
    }
}
