<?php

namespace App\Crawler\Sintegra;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Util;
use Exception;

class Sintegra extends Spider
{
    private const URL = 'https://dfe-portal.svrs.rs.gov.br/Nfe/Ccc';
    private const URL_RESULT = 'https://dfe-portal.svrs.rs.gov.br/Nfe/CccContribuinte';
    private $cnpj;
    private $data;

    public function start()
    {
        foreach ($this->getListUfId() as $ufId) {
            $this->getData($ufId);
        }

        if (empty($this->data)) {
            throw new Exception("Contribuinte não localizado no Cadastro Centralizado de Contribuintes.", 2);
        }

        return $this->data;
    }

    private function getData($ufId)
    {
        $html = $this->getResponse(self::URL);

        preg_match('/data-sitekey="(.*)?"/', $html, $siteKey);

        $token = $this->solveReCaptcha($siteKey[1], self::URL);

        $params = [
            'Origem' => 'PortalDfe',
            'TipoPesquisa' => 'CodInscrMf',
            'CodUf' => $ufId,
            'Ambiente' => 1,
            'TipoInscrMf' => 1,
            'CodInscrMf' => $this->cnpj,
            'CodIe' => '',
            'g-recaptcha-response' => $token,
            'recaptchaValor' => $token
        ];

        $html = $this->getResponse(self::URL, 'POST', $params);

        preg_match('/hash:"(.*)?"/', $html, $hash);
        preg_match_all('/carregaContribuinte\(\d*,\d*\s*\d*,\d*,\d*,(\d*)\)">/', $html, $codeIes);

        if (empty($codeIes[1])) {
            return;
        }

        foreach ($codeIes[1] as $codeIe) {
            $params = [
                'codUf' => $ufId,
                'codInscrMf' => $this->cnpj,
                'tipoInscrMf' => 1,
                'codIe' => $codeIe,
                'ambiente' => 1,
                'hash' => $hash[1],
            ];

            $result = $this->getResponse(self::URL_RESULT, 'POST', $params);

            $this->data[] = $this->parseData($result);
        }
    }

    private function parseData($result)
    {
        $data = [];

        $patterns = [
            'nome_empresa' => ['@Nome\sda\sEmpresa:<\/label>[\s\S]*?col-sm.*\n\s*([\s\S]*?)\n*\s*<@'],
            'uf' => ['@>UF:<\/label>[\s\S]*?col-sm.*\n\s*([\s\S]*?)\n*\s*<@'],
            'cnpj' => ['@>CNPJ:<\/label>[\s\S]*?col-sm.*\n\s*([\s\S]*?)\n*\s*<@'],
            'situacao_cnpj' => ['@\sCNPJ:<\/label>[\s\S]*?col-sm.*\n\s*([\s\S]*?)\n*\s*<@'],
            'inscricao_estadual_ie' => ['@Estadual\s\(IE\):<\/label>[\s\S]*?col-sm.*\n\s*([\s\S]*?)\n*\s*<@'],
            'situacao_ie' => ['@Situação\sIE:<\/label>[\s\S]*?col-sm.*\n\s*([\s\S]*?)\n*\s*<@'],
            'tipo_ie' => ['@Tipo\sIE:<\/label>[\s\S]*?col-sm.*\n\s*([\s\S]*?)\n*\s*<@'],
            'cnae_principal' => ['@CNAE\sPrincipal:<\/label>[\s\S]*?col-sm.*\n\s*([\s\S]*?)\n*\s*<@'],
            'data_situacao_uf' => ['@Data\sSituação\sna\sUF:<\/label>[\s\S]*?col-sm.*\n\s*([\s\S]*?)\n*\s*<@'],
        ];

        $identificacaoContribuinte = Util::parseDados($patterns, $result);
        $identificacaoContribuinte['situacao_ie'] = html_entity_decode($identificacaoContribuinte['situacao_ie']);
        $identificacaoContribuinte['situacao_cnpj'] = html_entity_decode($identificacaoContribuinte['situacao_cnpj']);
        $data['relacao_contribuinte']['contribuinte']['identificacao_contribuinte'] = $identificacaoContribuinte;

        $patterns = [];

        $patterns = [
            'nome_fantasia' => ['@Nome\sFantasia:<\/label>[\s\S]*?col-sm.*\n\s*([\s\S]*?)\n*\s*<@'],
            'data_inicio_atividade' => ['@Data\sInício\sAtividade:<\/label>[\s\S]*?col-sm.*\n*\s*([\s\S]*?)\n*\s*<@'],
            'data_fim_atividade' => ['@Data\sFim\sAtividade:<\/label>[\s\S]*?col-sm.*\n*\s*([\s\S]*?)\n*\s*<@'],
            'regime_de_tributacao' => ['@Regime\sde\sTributa\D*o:<\/label>[\s\S]*?col-sm.*\n*\s*([\s\S]*?)\n*\s*<@'],
            'informacao_ie_destinatario' =>
            ['@Informação\sda\sIE\scomo\sDestinatário:<\/label>[\s\S]*?col-sm.*\n*\s*([\s\S]*?)\n*\s*<@'],
            'porte_empresa' => ['@Porte\sda\sEmpresa:<\/label>[\s\S]*?col-sm.*\n\s*([\s\S]*?)\n*\s*<@'],
            'cnae_principal' => ['@CNAE\sPrincipal:<\/label>[\s\S]*?col-sm.*\n*\s*([\s\S]*?)\n*\s*<@'],
            'credito_presumido' => ['@Cr\D*dito\sPresumido:<\/label>[\s\S]*?col-sm.*\n*\s*([\s\S]*?)\n*\s*<@'],
            'tipo_produtor' => ['@Tipo\sProdutor:<\/label>[\s\S]*?col-sm.*\n\s*([\s\S]*?)\n*\s*<@'],
        ];

        $dadosContribuintes = Util::parseDados($patterns, $result);
        $dadosContribuintes['informacao_ie_destinatario'] =
            html_entity_decode($dadosContribuintes['informacao_ie_destinatario']);
        $dadosContribuintes['credito_presumido'] = html_entity_decode($dadosContribuintes['credito_presumido']);
        $dadosContribuintes['tipo_produtor'] = html_entity_decode($dadosContribuintes['tipo_produtor']);
        $data['relacao_contribuinte']['contribuinte']['dados_contribuintes'] = $dadosContribuintes;

        $patterns = [];

        $patterns = [
            'municipio_ibge' => ['@Munic\D*pio\sIBGE:<\/label>[\s\S]*?col-sm.*\n*\s*([\s\S]*?)\n*\s*<@'],
            'logradouro' => ['@Logradouro:<\/label>[\s\S]*?col-sm.*\n*\s*([\s\S]*?)\n*\s*<@'],
            'complemento' => ['@Complemento:<\/label>[\s\S]*?col-sm.*\n*\s*([\s\S]*?)\n*\s*<@'],
            'cep' => ['@CEP:<\/label>[\s\S]*?col-sm.*\n*\s*([\s\S]*?)\n*\s*<@'],
            'uf_localizacao' => ['@UF\sde\sLocaliza\D*o:<\/label>[\s\S]*?col-sm.*\n*\s*([\s\S]*?)\n*\s*<@'],
            'nro' => ['@Nro:<\/label>[\s\S]*?col-sm.*\n*\s*([\s\S]*?)\n*\s*<@'],
            'bairro' => ['@Bairro:<\/label>[\s\S]*?col-sm.*\n*\s*([\s\S]*?)\n*\s*<@'],
        ];

        $dadosEndereco = Util::parseDados($patterns, $result);
        $dadosEndereco['bairro'] = html_entity_decode($dadosEndereco['bairro']);
        $dadosEndereco['municipio_ibge'] = html_entity_decode($dadosEndereco['municipio_ibge']);
        $data['relacao_contribuinte']['contribuinte']['dados_endereco'] = $dadosEndereco;

        preg_match_all('/<tr>([\s\S]*?)\s*<\/tr>\n*\s*<\/table>/', $result, $matches);

        foreach ($matches[1] as $match) {
            preg_match_all('/<td.*?>\n*\s*([\s\S]*?)\s*<\/td>/', $match, $datas);

            preg_match('/<a\shref="javascript:;"[\s\S]*?\/a>\n*\s*(.*)\n/', $match, $nsu);

            if (!empty($nsu[1])) {
                $data['relacao_contribuinte']['historico'][] = [
                    'nsu_movto' => $nsu[1],
                    'uf' => $datas[1][1],
                    'CNPJ' => $datas[1][2],
                    'ie' => $datas[1][3],
                    'situacao_ie' => html_entity_decode($datas[1][4]),
                    'situacao_cnpj' => html_entity_decode($datas[1][5]),
                    'regime_icms' => html_entity_decode($datas[1][7]),
                    'data_situacao' => $datas[1][9],
                    'data_atualizacao' => $datas[1][11],
                    'motivo' => html_entity_decode($datas[1][13]),
                ];
            }
        }

        preg_match_all('/<tr\s>([\s\S]*?)<\/tr>\n*\s*<tr\sstyle=/', $result, $mesmos);

        foreach ($mesmos[1] as $mesmoscnpj) {
            preg_match_all('/<td>(.*)<\/td>/', $mesmoscnpj, $mesmocnpj);

            $data['relacao_contribuinte']['mesmo_cnpj_uf'][] = [
                'uf_cadastro' => $mesmocnpj[1][0],
                'cnpj' => $mesmocnpj[1][1],
                'ie' => $mesmocnpj[1][2],
                'tipo_ie' => html_entity_decode($mesmocnpj[1][3]),
                'situacao_ie' => html_entity_decode($mesmocnpj[1][4]),
                'situacao_cnpj' => html_entity_decode($mesmocnpj[1][5]),
                'uf_endereco' => $mesmocnpj[1][6],
            ];
        }

        return $data;
    }

    private function getListUfId()
    {
        return [
            '12',
            '27',
            '13',
            '16',
            '29',
            '23',
            '53',
            '32',
            '52',
            '21',
            '31',
            '50',
            '51',
            '15',
            '25',
            '26',
            '22',
            '41',
            '33',
            '24',
            '11',
            '14',
            '43',
            '42',
            '28',
            '35',
            '17'
        ];
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['cnpj'])) {
            throw new Exception('Parâmetro inválido', 1);
        }

        if (!Document::validarCpfOuCnpj($this->param['cnpj'])) {
            throw new Exception('Documento não é válido', 1);
        }

        $this->cnpj = Document::removeMask($this->param['cnpj']);
    }
}
