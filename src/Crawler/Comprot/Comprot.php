<?php

namespace App\Crawler\Comprot;

use App\Crawler\Spider;
use App\Helper\Document;
use Exception;

/**
 * Fonte Comprot - Minist<PERSON>rio da Fazenda
 * https://comprot.fazenda.gov.br/comprotegov/site/index.html#ajax/processo-consulta.html
 *
 * Davi Souto - 21/12/2017
 */
class Comprot extends Spider
{
    private const MAIN_URL = "https://comprot.fazenda.gov.br/comprotegov/api/processo";
    private const CAPTCHA_ID = "https://comprot.fazenda.gov.br/comprotegov/api/config/captcha" ;

    private $data_inicial = false; // mm/dd/yyyy    - Default: 01/01/1900
    private $data_final = false; // mm/dd/yyyy      - Default: Data atual

    private $total_processos = 0;
    private $processos_restantes = false;
    private $limit = 12;

    private $final = false;

    /**
     * Iniciar consulta
     * @return $array
     */
    protected function start()
    {
        $this->setAlternativeProxy();

        $this->setCurlOpt([
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $doc = Document::removeMask($this->param['cpf_cnpj']);
        $this->type = (strlen($doc) == 11) ? 'cpf' : 'cnpj';

        return $this->search();
    }

    protected function validateAndSetCrawlerAttributes()
    {
        $this->validateDocument();

        $this->validateDates();

        // $this->validateLimit();
    }

    private function validateDocument()
    {
        if (empty($this->param['cpf_cnpj'])) {
            throw new Exception("Documento para consulta não informado", 1);
        }

        if (!Document::formatCpfOrCnpj($this->param['cpf_cnpj'])) {
            throw new Exception("CPF/CNPJ informado é inválido", 1);
        }
    }

    private function validateDates()
    {
        $this->data_inicial = $this->param['data_inicial'] ??  "01/01/1900";
        $this->data_final = $this->param['data_final'] ??  date("m/d/Y");

        $this->data_inicial = strtotime($this->data_inicial . " 03:00:00") * 1000;
        $this->data_final = strtotime($this->data_final . " 03:00:00") * 1000;

        if (isset($this->param['data_inicial']) && !empty($this->param['data_inicial'])) {
            $d = explode('/', $this->param['data_inicial']);
            $d = strtotime($d[1] . '/' . $d[0] . '/' . $d[2] . " 03:00:00") * 1000;
            $this->data_inicial = $d;
        }

        if ($this->data_inicial > $this->data_final) {
            throw new Exception("Data inicial não pode ser maior que a data final", 1);
        }
    }

    private function validateLimit()
    {
        // Limite
        if (!empty($this->param['limite'])) {
            $this->limit = $this->param['limite'];
        }

        if ($this->limit >= 50) {
            $this->limit = 50;
        }
    }

    /**
     * Realiza a busca utilizando os parâmetros informados.
     * Caso já tenha sido feita uma pesquisa anterior, pode ser usado o número do ultimo processo para paginar.
     *
     * <AUTHOR> Souto - ??
     *
     * <AUTHOR> Santana - Refatorada classe para fazer a consulta baseado no tipo do parâmetro passado.
     *
     * @param string $numero_processo
     *
     * @return $array
     */
    private function search($numero_processo = false)
    {

        $now = (date("m/d/Y H:i:s.v") . substr(microtime(false), 3, 3));
        $now = strtotime($now) * 100;

        $params = [
            'cpfCnpjComMascara' => Document::formatCpfOrCnpj($this->param['cpf_cnpj']),
            'cpfCnpj' => Document::removeMask($this->param['cpf_cnpj']),
            'nomeInteressado' => '',
            'tipoPesquisa' => $this->type,
            'dataInicial' => $this->data_inicial,
            'dataFinal' => $this->data_final,
            'numeroUltimoProcesso' => '',
            '_' => $now
        ];

        if ($numero_processo) {
            $params['numeroUltimoProcesso'] = $numero_processo;
        }

        $captchaString = $this->getCaptchaInfo();

        $header = [
            'Captcha: ' . $captchaString,
            'Referer: https://comprot.fazenda.gov.br/comprotegov/site/index.html',
            'X-Requested-With: XMLHttpRequest',
        ];

        $params = http_build_query($params);

        $response =  $this->getResponse(
            self::MAIN_URL . '?' . $params,
            'GET',
            null,
            $header
        );

        $this->checkResponse($response);

        $data = json_decode($response, true);

        if (empty($data)) {
            throw new Exception('Nenhum registro encontrado', 2);
        }

        $dados_processos = $this->parseProcessos($data);

        return $this->verifyStep($dados_processos, $numero_processo);
    }

    private function getCaptchaInfo()
    {
        $retry = 5;
        do {
            try {
                $this->setAlternativeProxy();

                $res = json_decode($this->getResponse(self::CAPTCHA_ID), true);

                if (empty($res['hcaptchaSiteKey'])) {
                    throw new Exception("Falha ao recuperar o Captcha", 3);
                }

                $captchaString = $this->solveHCaptchaWithTwocaptcha($res['hcaptchaSiteKey'], self::MAIN_URL);

                if (empty($captchaString)) {
                    throw new Exception("Falha ao capturar o Captcha", 1);
                }

                return json_encode([
                    'response' => $captchaString
                ]);
            } catch (Exception $e) {
                print PHP_EOL . 'Erro: ' . $e->getMessage() . PHP_EOL;
                $retry--;
            }
        } while ($retry >= 0);
    }

    /**
     * Tratar resultados
     * @param array $data
     * @return array
     */
    private function parseProcessos($data)
    {
        if (!is_array($this->final)) {
            $this->final = array(
                'processos'         =>  [],
                'total_processos'   =>  0
            );
        }

        $processos = array(
            'processos'         =>  [],
            'total_processos'   =>  0
        );

        if (is_array($data) && array_key_exists('processos', $data) && count($data['processos']) > 0) {
            foreach ($data['processos'] as $processo) {
                try {
                    $dados_processo = $this->getDadosProcesso($processo['numeroProcessoPrincipal']);
                } catch (Exception $e) {
                    echo 'Erro: ' . $e->getMessage() . PHP_EOL;
                    echo 'Tentando novamente' . PHP_EOL;
                    $dados_processo = $this->getDadosProcesso($processo['numeroProcessoPrincipal']);
                }

                $processos['processos']['p_' . $processo['numeroProcessoPrincipal']] = array(
                    'numero_processo'   =>  $processo['numeroProcessoEditado'],
                    'nome_interessado'  =>  trim($processo['nomeInteressado']),
                    'data_protocolo'    =>  $this->fixData($processo['dataProtocolo']),
                    'dados'             =>  $dados_processo
                );

                // Limite de processos
                if ((count($processos['processos']) + $this->final['total_processos']) >= $this->limit) {
                    break;
                }
            }

            if (!$this->processos_restantes) {
                $this->total_processos = $data['totalDeProcessosEncontrados'];
                $this->processos_restantes = $this->total_processos;
            }

            $this->processos_restantes -= count($processos['processos']);
        } else {
            $this->processos_restantes = 0;
        }

        return $processos;
    }

    /**
     * Buscar e tratar dados de um processo
     * @param string $numero_processo
     * @return array
     */
    private function getDadosProcesso($numero_processo)
    {
        $captchaString = $this->getCaptchaInfo();
        $header = [
            'Captcha: ' . $captchaString,
            'Referer: https://comprot.fazenda.gov.br/comprotegov/site/index.html',
            'X-Requested-With: XMLHttpRequest',
        ];

        $data = $this->getResponse(
            self::MAIN_URL . "/" . $numero_processo,
            'GET',
            [],
            $header
        );
        $data = json_decode($data, true);

        if (!$data || !is_array($data)) {
            $data = [];
        }

        $dados_processo = array(
            'processo'          =>  [],
            'movimentos'        =>  [],
            'posicionamentos'   =>  [],
            'mensagemErroMovimento'         =>  false,
            'mensagemErroPosicionamento'    =>  false,
        );

        if (is_array($data)) {
            // Processo
            if (array_key_exists('processo', $data)) {
                $dados_processo['processo'] = array(
                    'documento_origem'  =>  $data['processo']['numeroDocumentoOrigem'],
                    'procedencia'       =>  $data['processo']['nomeProcedencia'],
                    'assunto'           =>  $data['processo']['nomeAssunto'],
                    'sistemas'          =>  $data['processo']['indicadorVirtual'],
                    'profisc'           => (!empty($data['processo']['indicadorProfisc'])
                        && $data['processo']['indicadorProfisc'] != "0"),
                    'e_processo'        => (!empty($data['processo']['indicadorEProcesso'])
                        && $data['processo']['indicadorEProcesso'] != "0"),
                    'sief'              => (!empty($data['processo']['indicadorSief'])
                        && $data['processo']['indicadorSief'] != "0"),
                    'orgao_origem'      =>  $data['processo']['nomeOrgaoOrigem'],
                    'orgao_destino'     =>  $data['processo']['nomeOrgaoDestino'],
                    'movimentado_em'    =>  $data['processo']['dataMovimento'],
                    'sequencia'         =>  $data['processo']['numeroSequencia'],
                    'numero_relacao'    =>  $data['processo']['numeroRelacao'],
                    'situacao'          =>  trim($data['processo']['situacao']),
                    'uf'                =>  $data['processo']['siglaUfMovimento'],
                );

                if ($data['processo']['indicadorCpfCnpj'] == 2) {
                    $dados_processo['processo']['cnpj'] = str_pad(
                        (string) $data['processo']['numeroCpfCnpj'],
                        14,
                        "0",
                        STR_PAD_LEFT
                    );
                } else {
                    $dados_processo['processo']['cpf'] = str_pad(
                        (string) $data['processo']['numeroCpfCnpj'],
                        11,
                        "0",
                        STR_PAD_LEFT
                    );
                }
            }

            // Movimentos
            if (array_key_exists('movimentos', $data)) {
                foreach ($data['movimentos'] as $movimento) {
                    $dados_processo['movimentos'][] = array(
                        'data'          =>  $movimento['dataMovimentoEditada'],
                        'tipo'          =>  $movimento['nomeTipoMovimento'],
                        'sequencia'     =>  $movimento['numeroSequencia'],
                        'relacao'       =>  $movimento['numeroRMRAAJDJ'],
                        'origem'        =>  $movimento['nomeOrgaoOrigem'],
                        'destino'       =>  $movimento['nomeOrgaoDestino'],
                        'caixa_arquivo' =>  $movimento['numeroCaixa'],
                        'temporalidade' =>  $movimento['nomeTemporalidade']
                    );
                }
            }

            // Posicionamentos
            if (array_key_exists('posicionamentos', $data)) {
                $dados_processo['posicionamentos'] = $data['posicionamentos'];
            }

            // Mensagens
            if (array_key_exists('mensagemErroMovimento', $data) && !empty($data['mensagemErroMovimento'])) {
                $dados_processo['mensagemErroMovimento'] = $data['mensagemErroMovimento'];
            }

            if (array_key_exists('mensagemErroPosicionamento', $data) && !empty($data['mensagemErroPosicionamento'])) {
                $dados_processo['mensagemErroPosicionamento'] = $data['mensagemErroPosicionamento'];
            }
        }

        return $dados_processo;
    }

    /**
     * Verificar se deve buscar a próxima página de processos ou finalizar a consulta
     * @param array $dados_processos
     * @param string $numero_processo
     * @return array
     */
    private function verifyStep($dados_processos, $numero_processo = false)
    {
        $this->final['processos'] = array_merge($dados_processos['processos'], $this->final['processos']);

        $this->final['total_processos'] = count($this->final['processos']);
        $this->final['qtd_processos_encontrados'] = $this->total_processos;

        // Percorrer páginas
        if ($this->final['total_processos'] < $this->limit && $this->processos_restantes > 0) {
            end($this->final['processos']);

            $lastProcessNumber = preg_replace("#[^0-9]*#is", "", key($this->final['processos']));

            return $this->search($lastProcessNumber);
        }

        return $this->final;
    }

    /**
     * Corrige data, caso esteja sem o primeiro dígito do dia (dias de 1 a 9)
     * @param string $data
     * @return string
     */
    private function fixData($data)
    {
        if (strlen($data) > 7) {
            return $data;
        }

        return '0' . $data;
    }

    /**
     * Verifica a resposta recebida
     * @param string $response
     */
    private function checkResponse($response)
    {
        if (preg_match('@n.o\s*confere@isu', $response)) {
            throw new Exception('Não foi possível resolver o captcha. Tente novamente.', 3);
        }

        if (preg_match('@n.o\s*foi\s*poss.vel@isu', $response)) {
            throw new Exception('Não foi possível executar a operação. Tente novamente.', 3);
        }
    }
}
