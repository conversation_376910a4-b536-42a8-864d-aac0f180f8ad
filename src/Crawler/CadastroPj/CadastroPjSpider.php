<?php

namespace App\Crawler\CadastroPj;

use App\Manager\DynamoManager;
use App\Manager\ColunaVertebralManager;
use App\Manager\RequestManager;
use App\Manager\SnsManager;

/**
 * Classe spider de busca
 *
 * <AUTHOR>
 *
 */
class CadastroPjSpider
{
    private $dynamoManager;
    private $colunaVertebralManager;

    /**
     * Construtor do spider
     *
     * @version 1.0.0
     * <AUTHOR>
     *
     * @return void
     */
    public function __construct()
    {
        $this->dynamoManager = new DynamoManager(false);
        $this->colunaVertebralManager = new ColunaVertebralManager();
    }

    /**
     * Função de busca dos dados da Spine Pf
     *
     * @version 1.0.0
     * <AUTHOR>
     *
     * @param string $cpf
     * @return array
     */
    public function getSpinePf($cpf)
    {
        $spine = $this->dynamoManager->getItem('spine_pf', [
            'cpf' => $cpf
        ]);
        return $spine;
    }

    /**
     * Função de busca dos dados da Spine Pj
     *
     * @version 1.0.0
     * <AUTHOR>
     *
     * @param string $cpf
     * @return array
     */
    public function getSpinePj($cnpj)
    {
        $spine = $this->dynamoManager->getItem('spine_pj', [
            'cnpj' => (string) $cnpj
        ]);

        $this->requestUpdateSpine($cnpj);
        return $spine ?? [];
    }

    /**
     * Função para atualizar os dados da spine
     *
     * @version 1.0.0
     * <AUTHOR>
     *
     * @param string $cpf
     * @return array
     */
    private function requestUpdateSpine($doc)
    {
        $snsManager = new SnsManager([
            'access_key' => AWS_ACCESS_KEY,
            'secret_key' => AWS_SECRET_KEY
        ]);

        $target = 'arn:aws:sns:us-east-1:' . ACCOUNT_ID . ':ReceitaFederalLambda';
        $subject = 'Atualização SPINE PJ';
        $message = json_encode([
            "retry" => "2",
            "source" => "ReceitaFederalPjGeneric",
            "param" => [
                "documento" => $doc
            ]
        ]);

        $snsManager->sendMessage($target, $subject, $message);
    }

    /**
     * Função de busca dos dados da Spine Pj Telefones
     *
     * @version 1.0.0
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 08/06/2018 - Otimizadas as buscas
     *
     * @param string $cnpj
     * @return array
     */
    public function getSpinePjTelefones($cnpj)
    {
        $conditions = 'cnpj = :cnpj';
        $expression = [
            ':cnpj' => $cnpj
        ];
        return $this->dynamoManager->getQuery(
            'spine_pj_telefones',
            $conditions,
            $expression,
            false,
            'ALL_ATTRIBUTES',
            true,
            100
        );
    }

    /**
     * Função de busca dos dados da Spine Simples Nacional
     *
     * @version 1.0.0
     * <AUTHOR>
     *
     * @param string $cnpj
     * @return array
     */
    public function getSpineSimplesNacional($cnpj)
    {
        $conditions = 'cnpj = :cnpj';
        $expression = [
            ':cnpj' => $cnpj
        ];
        return $this->dynamoManager->getQuery('spine_pj_simplesnacional', $conditions, $expression);
    }

    /**
     * Função de busca dos dados da Spine Cnae
     *
     * @version 1.0.0
     * <AUTHOR>
     *
     * @param string $id
     * @return array
     */
    public function getSpineCnae($id)
    {
        $conditions = 'id = :id';
        $expression = [
            ':id' => $id
        ];
        return $this->dynamoManager->getQuery('spine_pj_cnae', $conditions, $expression);
    }

    /**
     * Função de busca dos dados da Spine QSA
     *
     * @version 1.0.0
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 06/08/2018 - Otimizadas as buscas
     *
     * @param string $cnpj
     * @param string $ano
     * @return array
     */
    public function getSpinePjQsa($cnpj, $ano)
    {
        $anoAnterior = false;
        if ($ano === date('Y')) {
            $anoAnterior = $ano - 1;
        }
        $ano = (string)$ano;

        $index = 'cnpj-ano-index';
        $conditions = 'cnpj = :cnpj AND ano = :ano';
        $expression = [
            ':cnpj' => $cnpj,
            ':ano' => $ano
        ];

        $sociedades = $this->dynamoManager
            ->getQuery('spine_pj_qsa', $conditions, $expression, $index);


        if (count($sociedades) === 0 && date('Y') - $anoAnterior === 1) {
            return $this->getSpinePjQsa($cnpj, $anoAnterior);
        } elseif (count($sociedades) > 0) {
            $documents = [];
            foreach ($sociedades as $sociedade) {
                $documents[] = $sociedade['documento_socio'];
            }
            $nameArray = [];
            $nameArrayNovo = [];
            if (count($documents) > 99) {
                $documentsSepareted = array_chunk($documents, 90);
                foreach ($documentsSepareted as $documentGroup) {
                    $nameArray[] = $this->colunaVertebralManager->getDocumentsNames($documentGroup);
                }
                foreach ($nameArray as $nameArrayIndex) {
                    foreach ($nameArrayIndex as $key => $item) {
                        $nameArrayNovo[$key] = $item;
                    }
                }
                $nameArray = $nameArrayNovo;
            } else {
                $nameArray = $this->colunaVertebralManager->getDocumentsNames($documents);
            }
            foreach ($sociedades as $key => $sociedade) {
                if (isset($nameArray[$sociedade['documento_socio']])) {
                    $sociedades[$key]['nome'] = $nameArray[$sociedade['documento_socio']];
                } else {
                    $sociedades[$key]['nome'] = '';
                }
            }
        }
        return $sociedades;
    }
}
