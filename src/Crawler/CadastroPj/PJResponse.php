<?php

namespace App\Crawler\CadastroPj;

class PJResponse
{
    private const MAP_CNJ = [
        "1015" => "ORGAO PUBLICO DO PODER EXECUTIVO FEDERAL",
        "1023" => "ORGAO PUBLICO DO PODER EXECUTIVO ESTADUAL OU DO DISTRITO FEDERAL",
        "1031" => "ORGAO PUBLICO DO PODER EXECUTIVO MUNICIPAL",
        "1040" => "ORGAO PUBLICO DO PODER LEGISLATIVO FEDERAL",
        "1058" => "ORGAO PUBLICO DO PODER LEGISLATIVO ESTADUAL OU DO DISTRITO FEDERAL",
        "1066" => "ORGAO PUBLICO DO PODER LEGISLATIVO MUNICIPAL",
        "1074" => "ORGAO PUBLICO DO PODER JUDICIARIO FEDERAL",
        "1082" => "ORGAO PUBLICO DO PODER JUDICIAR<PERSON> ESTADUAL",
        "1090" => "ORGAO AUTONOMO DE DIREITO PUBLICO",
        "1104" => "AUTARQUIA FEDERAL",
        "1112" => "AUTARQUIA ESTADUAL OU DO DISTRITO FEDERAL",
        "1120" => "AUTARQUIA MUNICIPAL",
        "1139" => "FUNDACAO FEDERAL",
        "1147" => "FUNDACAO ESTADUAL OU DO DISTRITO FEDERAL",
        "1155" => "FUNDACAO MUNICIPAL",
        "1163" => "ORGAO PUBLICO AUTONOMO FEDERAL",
        "1171" => "ORGAO PUBLICO AUTONOMO ESTADUAL OU DO DISTRITO FEDERAL",
        "1180" => "ORGAO PUBLICO AUTONOMO MUNICIPAL",
        "1198" => "COMISSAO POLINACIONAL",
        "1201" => "FUNDO PUBLICO",
        "1210" => "ASSOCIACAO PUBLICA",
        "2011" => "EMPRESA PUBLICA",
        "2038" => "SOCIEDADE DE ECONOMIA MISTA",
        "2046" => "SOCIEDADE ANONIMA ABERTA",
        "2054" => "SOCIEDADE ANONIMA FECHADA",
        "2062" => "SOCIEDADE EMPRESARIA LIMITADA",
        "2070" => "SOCIEDADE EMPRESARIA EM NOME COLETIVO",
        "2089" => "SOCIEDADE EMPRESARIA EM COMANDITA SIMPLES",
        "2097" => "SOCIEDADE EMPRESARIA EM COMANDITA POR ACOES",
        "2100" => "SOCIEDADE MERCANTIL DE CAPITAL E INDUSTRIA",
        "2127" => "SOCIEDADE EM CONTA DE PARTICIPACAO",
        "2135" => "EMPRESARIO (INDIVIDUAL)",
        "2143" => "COOPERATIVA",
        "2151" => "CONSORCIO DE SOCIEDADES",
        "2160" => "GRUPO DE SOCIEDADES",
        "2178" => "ESTABELECIMENTO, NO BRASIL, DE SOCIEDADE ESTRANGEIRA",
        "2194" => "ESTABELECIMENTO, NO BRASIL, DE EMPRESA BINACIONAL ARGENTINO-BRASILEIRA",
        "2216" => "EMPRESA DOMICILIADA NO EXTERIOR",
        "2224" => "CLUBE/FUNDO DE INVESTIMENTO",
        "2232" => "SOCIEDADE SIMPLES PURA",
        "2240" => "SOCIEDADE SIMPLES LIMITADA",
        "2259" => "SOCIEDADE SIMPLES EM NOME COLETIVO",
        "2267" => "SOCIEDADE SIMPLES EM COMANDITA SIMPLES",
        "2275" => "EMPRESA BINACIONAL",
        "2283" => "CONSORCIO DE EMPREGADORES",
        "2291" => "CONSORCIO SIMPLES",
        "2305" => "EMPRESA INDIVIDUAL DE RESPONSABILIDADE LIMITADA (DE NATUREZA EMPRESARIA)",
        "2313" => "EMPRESA INDIVIDUAL DE RESPONSABILIDADE LIMITADA (DE NATUREZA SIMPLES)",
        "3034" => "SERVICO NOTARIAL E REGISTRAL (CARTORIO)",
        "3069" => "FUNDACAO PRIVADA",
        "3077" => "SERVICO SOCIAL AUTONOMO",
        "3085" => "CONDOMINIO EDILICIO",
        "3107" => "COMISSAO DE CONCILIACAO PREVIA",
        "3115" => "ENTIDADE DE MEDIACAO E ARBITRAGEM",
        "3123" => "PARTIDO POLITICO",
        "3131" => "ENTIDADE SINDICAL",
        "3204" => "ESTABELECIMENTO, NO BRASIL, DE FUNDACAO OU ASSOCIACAO ESTRANGEIRAS",
        "3212" => "FUNDACAO OU ASSOCIACAO DOMICILIADA NO EXTERIOR",
        "3220" => "ORGANIZACAO RELIGIOSA",
        "3239" => "COMUNIDADE INDIGENA",
        "3247" => "FUNDO PRIVADO",
        "3999" => "ASSOCIACAO PRIVADA",
        "4014" => "EMPRESA INDIVIDUAL IMOBILIARIA",
        "4022" => "SEGURADO ESPECIAL",
        "4081" => "CONTRIBUINTE INDIVIDUAL",
        "4090" => "CANDIDATO A CARGO POLITICO ELETIVO",
        "4111" => "LEILOEIRO",
        "5010" => "ORGANIZACAO INTERNACIONAL",
        "5029" => "REPRESENTACAO DIPLOMATICA ESTRANGEIRA",
        "5037" => "OUTRAS INSTITUICOES EXTRATERRITORIAIS",
        "8885" => "NATUREZA JURIDICA INVALIDA"
    ];
    private const MAP = [
        "dados_gerais" => [
            "cnpj" => "cnpj",
            "razao_social" => "razao_social",
            "nome_fantasia" => "nome_fantasia",
            "data_abertura" => "data_abertura",
            "capital_social" => "capital_social",
            "matriz" => "matriz",
            "tipo" => "tipo_cnae",
            "dominio" => "dominio",
            "faixa_funcionarios" => "faixa_funcionarios",
            "numero_filiais" => "numero_filiais",
            "porte" => "porte",
            "setor" => "setor",
            "faturamento_anual_estimado" => "faturamento_anual_estimado",
            "tipo_estabelecimento" => "tipo_estabelecimento",
            "operacionalidade" => "operacionalidade",
            "motivo_situacao" => "motivo_situacao",
            "classe_risco" => "classe_risco"
        ],
        "situacao_cadastral" => [
            "descricao" => "situacao_cadastral",
            "data" => "data_situacao"
        ],
        "natureza_juridica" => [
            "codigo" => "natureza_juridica"
        ],
        "cnae" => [
            "cnae" => "cnae",
            "descricao" => "cnaeDescricao",
            "segmento" => "cnae_segmento"
        ],
        "simples_nacional" => [
            "cnpj" => "cnpj",
            "status" => "status_sn",
            "status_simei" => "status_simei",
            "data_simples_nacional" => "data_opcao_sn",
            "data_simei" => "data_opcao_simei",
            "data_consulta" => "data_consulta",
            "optante" => "simples_nacional"
        ],
        "endereco" => [
            "bairro" => "bairro",
            "cidade" => "cidade",
            "uf" => "uf",
            "cep" => "cep",
            "ibge" => "ibge",
            "numero" => "logr_numero",
            "complemento" => "logr_complemento",
            "logradouro" => "logradouro",
            "tipo" => "logr_tipo",
            "latitude" => "latitude",
            "longitude" => "longitude"
        ]
    ];
    private const MAP_INVERTED = [
        "telefones" => [
            "ddd" => "ddd",
            "telefone" => "telefone",
            "descricao" => "descricao",
            "rank" => "rank",
            "cnpj" => "cnpj"
        ],
        "socios" => [
            "documento_socio" => "documento",
            "nome" => "nome",
            "qualificacao" => "qualificacao",
            "participacao" => "percentual_participacao",
            "data_entrada" => "data_entrada"
        ]
    ];

    /**
     * @param $result
     * @return array
     */
    public static function dadosGerais($result)
    {
        $map = self::MAP['dados_gerais'];
        $dadosPessoais = [];

        foreach ($map as $field => $value) {
            $dadosPessoais[$field] = $result[$value] ?? '';
        }
        $dadosPessoais['matriz'] = $dadosPessoais['matriz'] == 'True'
            ? 'matriz'
            : 'filial';
        return [
            'informacoes_gerais' => $dadosPessoais
        ];
    }

    /**
     * @param $result
     * @return array
     */
    public static function naturezaJuridica($result)
    {
        $map = self::MAP['natureza_juridica'];
        $cnj = self::MAP_CNJ[$result[$map['codigo']]] ??  null;
        return [
            'natureza_juridica' => [
                'codigo' => $result[$map['codigo']],
                'descricao' => $cnj
            ]
        ];
    }

    /**
     * @param $result
     * @return array
     */
    public static function situacaoCadastral($result)
    {
        $map = self::MAP['situacao_cadastral'];
        return [
            'situacao_cadastral' => [
                'descricao' => $result[$map['descricao']],
                'data' => $result[$map['data']]
            ]
        ];
    }

    /**
     * @param $spider
     * @param $result
     * @return array
     */
    public static function cnae($spider, $result)
    {
        $map = self::MAP['cnae'];
        $idCnae = $result[$map['cnae']];
        $spiderCnae = $idCnae ? $spider->getSpineCnae($idCnae) : [];
        $descCnae = array_shift($spiderCnae)['descricao'];

        return [
            'cnae' => [
                'codigo' => $idCnae,
                'descricao' => $descCnae
            ]
        ];
    }

    /**
     * @param $result
     * @return array
     */
    public static function simplesNacional($result)
    {
        $result = array_shift($result);
        $map = self::MAP['simples_nacional'];
        return [
            'simples_nacional' => [
                'status' => $result[$map['status']],
                'status_simei' => $result[$map['status_simei']],
                'data_simples_nacional' => trim($result[$map['data_simples_nacional']]) ?? '',
                'data_simei' => trim($result[$map['data_simei']]) ?? ''
            ]
        ];
    }

    /**
     * @param $result
     * @return array
     */
    public static function mapEndereco($result)
    {
        $map = self::MAP['endereco'];

        $logradouro = $result[$map['logradouro']];
        if (!preg_match("#^\\s*\\b{$result[$map['tipo']]}\\b#iu", $logradouro)) {
            $logradouro = "{$result[$map['tipo']]} {$logradouro}";
        }

        return [
            'endereco' => [
                'bairro' => $result[$map['bairro']] ?? '',
                'cidade' => $result[$map['cidade']] ?? '',
                'uf' => $result[$map['uf']] ?? '',
                'cep' => $result[$map['cep']] ?? '',
                'ibge' => $result[$map['ibge']] ?? '',
                'numero' => $result[$map['numero']] ?? '',
                'complemento' => $result[$map['complemento']] ?? '',
                'logradouro' => $logradouro ?? '',
                'latitude' => $result[$map['latitude']] ?? '',
                'longitude' => $result[$map['longitude']] ?? ''
            ]
        ];
    }

    /**
     * @param $key
     * @param $array
     * @return array
     */
    public static function mapArray($key, $array)
    {
        $map = self::MAP_INVERTED[$key];

        $myArrs = [];
        if (is_array($array) && count($array) > 0) {
            foreach ($array as $values) {
                $myArr = [];
                // Adiciona os valores
                foreach ($values as $field => $value) {
                    if (isset($map[$field])) {
                        $myArr[$map[$field]] = $value;
                    }
                }

                // Adiciona os valores default
                foreach ($map as $field => $value) {
                    if (!is_array($value) && !isset($myArr[$value])) {
                        $myArr[$value] = null;
                    }
                }

                array_push($myArrs, $myArr);
            }
        }

        return [
            $key => $myArrs
        ];
    }
}
