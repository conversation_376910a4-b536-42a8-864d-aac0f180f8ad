<?php

namespace App\Crawler\CadastroPj;

use App\Crawler\CadastroPj\Helpers\SociosQsa;
use App\Crawler\Spider;
use App\Manager\RequestManager;
use App\Helper\Document;
use Exception;

/**
 * Classe de busca dos Dados Cadastrais PJ
 *
 * <AUTHOR>
 */
class CadastroPj extends Spider
{
    private const VERIFY_SOCIOS_LIMIT = 10;
    private const DEBUG = false;

    /**
     * Undocumented variable
     *
     * @var CadastroPjSpider
     */
    private $spider;

    private $documento;

    /**
     *
     * Função para mostrar qual foi a última função executada
     *
     * @version 1.0.0
     * <AUTHOR>
     *
     * @return void
     */
    public function debug()
    {
        if ($this->debug) {
            print PHP_EOL . debug_backtrace()[1]['function'] . PHP_EOL;
        }
    }

    /**
     * Função principal de busca do Cadastro PJ
     *
     * @version 1.0.0
     * <AUTHOR>
     * <AUTHOR> 08/07/2018 - Adicionado thread para chamar todas as buscas de uma vez so
     *
     * @return void
     */
    protected function start()
    {
        $this->requestManager = new RequestManager();
        $this->spider = new CadastroPjSpider();

        $body = $this->infoByDoc($this->documento);

        return $body;
    }

    /**
     * Função principal de busca dos dados
     *
     * @version 1.0.0
     * <AUTHOR>
     *
     * @param string $doc
     * @return array
     */
    public function infoByDoc($doc)
    {
        $this->debug();

        $result = [
            'spine' => $this->spider->getSpinePj($doc),
            'telefones' => $this->spider->getSpinePjTelefones($doc),
            'simples_nacional' => $this->spider->getSpineSimplesNacional($doc),
            'qsa' => $this->getQsa($doc)
        ];

        $this->mapResponse($result);
        return $result;
    }

    /**
     * Função para mapear a resposta da maneira correta
     *
     * @param array $result
     * @return void
     */
    private function mapResponse(&$result)
    {
        $this->debug();

        $spine = $result['spine'];
        $dadosGerais = PJResponse::dadosGerais($spine);
        $naturezaJuridica = PJResponse::naturezaJuridica($spine);
        $situacaoCadastral = PJResponse::situacaoCadastral($spine);
        $cnae = PJResponse::cnae($this->spider, $spine);

        $simplesNacional = PJResponse::simplesNacional($result['simples_nacional']);

        // Map
        $enderecos = PJResponse::mapEndereco($spine);
        $telefones = PJResponse::mapArray('telefones', $result['telefones']);
        $socios = PJResponse::mapArray('socios', $result['qsa']);

        $dadosGerais = array_merge($dadosGerais, $naturezaJuridica, $situacaoCadastral, $cnae, $simplesNacional);
        $result = array_merge($dadosGerais, $enderecos, $telefones, $socios);
    }


    /**
     * Função para buscar o QSA a partir de um documento
     *
     * @version 1.0.0
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 08/06/2018 - Otimizada as buscas
     *
     * @param string $doc
     *
     * @return array
     */
    private function getQsa($doc)
    {
        $ano = date('Y');
        $socios = $this->spider->getSpinePjQsa($doc, $ano);
        $ano--;

        foreach ($socios as &$socio) {
            try {
                // Verificar o histórico do QSA
                if (count($socios) <= static::VERIFY_SOCIOS_LIMIT) {
                    $socio = SociosQsa::verificarHistorico($this->spider, 'getSpinePjQsa', $socio, $doc, $ano);
                }

                $socio['participacao'] = number_format(floatval($socio['participacao']), 2);
            } catch (Exception $e) {
                if (static::DEBUG === true) {
                    print PHP_EOL . $e->getMessage() . PHP_EOL;
                }
            }
        }

        return $socios;
    }

    /**
     * Valida o CNPJ de entrada
     *
     * @version 1.0.0
     * <AUTHOR>
     *
     * @return object
     */
    protected function validateAndSetCrawlerAttributes()
    {
        $this->debug();
        $doc = preg_replace('#\D+#', '', $this->param['documento']);

        // Valida o parâmetro documento para verificar se é um CNPJ
        if (!Document::validarCnpj($doc)) {
            throw new Exception("O documento informado não é um CNPJ válido.", 1);
        }

        $this->documento = $doc;
    }
}
