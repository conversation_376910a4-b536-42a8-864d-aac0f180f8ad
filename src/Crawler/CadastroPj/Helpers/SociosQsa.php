<?php

namespace App\Crawler\CadastroPj\Helpers;

use App\Helper\Document;

class SociosQsa
{
    private const NUMBER_ATTRS = 2;
    private $spider;

    public function __call($name, $params)
    {
        return call_user_func_array([$this, $name], $params);
    }

    public static function __callStatic($name, $params)
    {
        $spider = array_shift($params);
        $class = new self($spider);
        return call_user_func_array([$class, $name], $params);
    }

    public function __construct($spider)
    {
        $this->spider = $spider;
    }

    /**
     * @param $doc
     * @return mixed
     */
    private function getNomeSocioByDoc($doc)
    {
        // Valida se o critério é um CNPJ ou CPF, para chamar a função adequada
        $fnc = Document::validarCnpj($doc) ? 'getSpinePj' : 'getSpinePf';
        // Chama a função
        $socio = $this->spider->{$fnc}($doc);
        // Retorna o nome ou razão social
        return isset($socio['nome']) ? $socio['nome'] : $socio['razao_social'];
    }

    /**
     * <AUTHOR>
     * @param $socio
     * @return array
     */
    private function validateQsa($socio)
    {
        // Valida se existe os atributos que são importantes
        return array_filter($socio, function ($value, $key) {
            switch ($key) {
                case 'qualificacao':
                    $isValid = !empty($value) ? true : false;
                    break;
                case 'data_entrada':
                    $isValid = !empty($value) ? true : false;
                    break;
                default:
                    $isValid = false;
            }
            return $isValid;
        }, ARRAY_FILTER_USE_BOTH);
    }

    /**
     * <AUTHOR> Oliveira
     * @param $fncGetQsa
     * @param $socio
     * @param $cnpj
     * @param $ano
     * @return mixed
     */
    private function verificarHistorico($fncGetQsa, $socio, $cnpj, $ano)
    {
        if (count($this->validateQsa($socio)) < static::NUMBER_ATTRS) {
            do {
                // Busca os dados dos sócios spine QSA
                $sociosPassado = $this->spider->{$fncGetQsa}($cnpj, (string) $ano);
                // Compara os sócios atuais com os anteriores para obter todas as informações relevantes
                foreach ($sociosPassado as $socioPassado) {
                    // Verifica se o documento é equivalente ao sócio atujal
                    if ($socioPassado['documento_socio'] == $socio['documento_socio']) {
                        $socio['data_entrada'] = $socioPassado['data_entrada'];
                        $socio['qualificacao'] = $socioPassado['qualificacao'];
                        // Se a partipação atual do sócio estiver vazia irá buscar do passado
                        if (empty($socio['participacao']) || ceil($socio['participacao']) == 0) {
                            $socio['participacao'] = $socioPassado['participacao'];
                        }
                    }
                }

                $ano--; // Decrementa o ano

                // O loop vai encerrar quando o ano for menor que 2013 ou se todas as informações foram validadas
            } while ($ano >= 2013 && count($this->validateQsa($socio)) < static::NUMBER_ATTRS);
        }
        return $socio;
    }
}
