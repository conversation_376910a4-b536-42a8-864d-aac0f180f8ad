<?php

namespace App\Crawler\Dbjus;

use App\Crawler\Spider;
use App\Factory\PostgresDB;
use Exception;
use App\Helper\Str;
use App\Manager\DbjusManager;
use Carbon\Carbon;

class Dbjus extends Spider
{
    private $limit = 100;
    private $searchParams = [];
    private $offset = 0;
    private $itensInPage = 80;
    private $totalProcessos = 0;
    private $search = array(
        "base" => "https://api.dbjus.com/v7",
        "listar_processos" => array(
            "url" => "/process/search",
            "method" => "POST",
            "description" => "Realiza uma consulta avançada em todos os processos da base DBJus."
        ),
        "detalhes_processo" => array(
            "url" => "/process/details?nup={process_id}",
            "method" => "GET",
            "description" => "Retorna detalhes completos sobre um processo capturado."
        ),
        "total_processos" => array(
            "url" => "/process/search/count",
            "method" => "POST",
            "description" => "Retorna o total de processos"
        ),
    );

    private $name;
    private $similarity;
    private $manager;
    private $errorDetalhe = 'Não foi possível capturar todos os detalhes para este processo, ' .
        'caso seja de seu interesse mais informações, favor consultar o órgão responsável.';

    public function __construct($param, $auth)
    {
        require_once __DIR__ . '/configDbjus.php';
        parent::__construct($param, $auth);
    }

    /**
     * Valida e retorna os parametros e o que será utilizado nas funções
     * Validates and returns the params and which will be used in functions
     *
     * @return array
     * <AUTHOR> Machado - 2018-04-26
     * Revisão: Jonathan Machado - 2018-04-26
     * Revisão: Vitor Hugo R. - 2018-06-29
     * @version 1.0.0
     * @version 1.1.0 - Adição de validação e retorno de similaridade
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        if (empty($this->param['nome'])) {
            throw new Exception("Parâmetro nome é obrigatório", 1);
        }

        $this->validateAndDefineFields();

        //Retirar . de um nome
        $this->name = $this->param['nome'] ?? [];

        $this->shares = $this->param['partes'] ?? [];

        //Se não tiver similaridade usar zero para retornar todos os processos do fornecedor
        $this->similarity = (int) $this->param['similarity'] ?? 0;
    }

    /**
     * Busca processos por nome das partes e traz os detalhes do processo
     * Search process by involved names and search process details
     *
     * @version 1.0.0
     * @version 1.1.0 - Adição para verificar se vai buscar por similaridade total ou não
     *
     * <AUTHOR> Vitor Hugo - 2018-06-29
     *
     * @return array
     */
    protected function start()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        ini_set('memory_limit', '-1');
        $this->manager = new DbjusManager();
        $lista_processos = $this->searchByNome();

        foreach ($lista_processos['processos'] as $processo_key => $processo) {
            $detalhes = $this->getDetalhesProcesso($processo['numero_processo']);

            if (empty($detalhes)) {
                continue;
            }

            $detalhes_processos[] = $this->preparaProcessos($detalhes);

            $lista_processos['processos'][$processo_key] = $this->preparaProcessos($processo);
        }

        $merge = $this->mergeDetalhesProcesso($lista_processos, $detalhes_processos);
        $processos = $this->similarityProcess($merge);

        if (empty($processos)) {
            throw new Exception("Não foi encontrado nenhum registro", NOTFOUND_ERROR);
        }

        return $processos;
    }

    private function similarityProcess($merge)
    {
        //Similaridade 10 ou menor não passar pela função e retornar na ordem do fornecedor
        if ($this->similarity <= 10) {
            return $merge;
        }

        $processos = [];
        $keysSimilarity = $this->getKeySimilarity();

        foreach ($keysSimilarity as $key) {
            $processos +=  $this->checkSimilarities($merge, $key);
        }

        return $processos;
    }

    /**
     * Valida e define os campos de busca
     * Validates and defines the search fields
     *
     * @return array
     * <AUTHOR> Machado - 2018-04-26
     * Revisão: Jonathan Machado - 2018-04-26
     * @version 1.0.0
     */
    private function validateAndDefineFields()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        $this->manager = new DbjusManager();
        if (empty($this->param['params'])) {
            return;
        }

        foreach ($this->param['params'] as $param => $value) {
            if (is_array($value)) {
                foreach ($value as $k => $v) {
                    if (empty($v)) {
                        unset($this->param['params'][$param][$k]);
                    }
                }
            }
        }

        foreach ($this->param['params'] as $param => $value) {
            if (empty($value)) {
                continue;
            }
            switch ($param) {
                case 'uf':
                    $arrValue = (array) $value;
                    foreach ($arrValue as $k_uf => $v_uf) {
                        $arrValue[$k_uf] = $this->manager->getIdUF($v_uf);
                    }
                    if (!empty($arrValue) && $arrValue[0] != '') {
                        $this->addTermFilter(FIELD_UF, $arrValue);
                    }
                    break;
                case 'assuntos':
                    $arrValue = (array) $value;
                    $arrValue = array_map(function ($str) {
                        return '"' . strtoupper($str) . '"';
                    }, $arrValue);
                    $arrValue = implode(" OR ", $arrValue);
                    if (!empty($arrValue)) {
                        $this->addSearchFilter($arrValue, FIELD_ASSUNTOS);
                    }
                    break;
                case 'instancias':
                    $arrValue = $value;
                    if (!is_array($value)) {
                        $arrValue = explode(',', $value);
                    }
                    if (isset($arrValue[0]) && strtoupper($arrValue[0]) != 'TODOS') {
                        $strValue = implode(" OR ", $arrValue);
                        $this->addSearchFilter($strValue, FIELD_INSTANCIA_ID);
                    }
                    break;
                case 'limite':
                    if (!empty($value)) {
                        $this->limit = (int) $value;
                    }
                    break;
            }
        }
    }

    /**
     * Busca processos pelo nome das partes
     * Search for process by involved names
     *
     * <AUTHOR>
     * @version 1.1.0 - Adição de validação e retorno de similaridade
     *          1.2.0 - Maximiliano Minucelli 05/12/2018 - Adição da similaridade no resultado
     *
     * @param string $nome
     * @param array $partes
     * @param int $similarity
     *
     * @return array
     */
    public function searchByNome()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        $partes = $this->validateSections();

        $nome = $this->setupName();

        // Adicionar nome no filtro
        $this->addSearchFilter($this->name, $this->sections);

        // Inicializar variaveis
        $processos['total_processos'] = 0;
        $processos['total_pesquisados'] = 0;
        $processos['processos'] = [];

        // Setar offset
        $this->setPage(0);

        // Buscar processos
        $continue = false;


        do {
            $_processos = $this->processListaProcessos($this->request($this->search['listar_processos']));

            // Verficar se retornou algum processo
            if (empty($_processos)) {
                break;
            }

            // Total de processos
            if ($processos['total_processos'] == 0) {
                $processos['total_processos'] = $this->totalProcessos;
            }

            // Adicionar processos ao array
            $processos['processos'] = array_merge($processos['processos'], $_processos);
            // Contar total de processos pesquisados até agora
            $processos['total_pesquisados'] += count($_processos);

            if (ENV == 'qa' || ENV == 'dev') {
                if (count($_processos) < 10) {
                    break;
                }
            } else {
                if (count($_processos) < 80) {
                    break;
                }
            }

            // Verificar se é necessário mesmo ir para página
            if ($processos['total_pesquisados'] >= $this->limit) {
                break;
            }

            // Setar próxima página
            $this->setPage((int) $processos['total_pesquisados']);

            if (empty($processos['total_pesquisados'])) {
                break;
            }

            // Verificar se ultrapassou o limite
            $continue = ($this->limit > $processos['total_pesquisados']);
        } while ($continue);

        if ($processos['total_pesquisados'] == 0) {
            throw new Exception("Não foi encontrado nenhum registro", NOTFOUND_ERROR);
        }

        // Apagar o que ultrapassar o limite
        // Como a consulta traz obrigatóriamente de 20 em 20 processos pode ser que seja ultrapassado o limite definido
        // pelo usuário.
        if ($processos['total_pesquisados'] > $this->limit) {
            array_splice($processos['processos'], $this->limit);
            $processos['total_pesquisados'] = $this->limit;
        }

        // return Str::removerAcentos($processos);

        return $processos;
    }

    private function preparaProcessos($processo)
    {
        array_walk_recursive(
            $processo,
            function (&$value) {
                if (gettype($value) == 'string') {
                    $value = Str::removerAcentos($value);
                }
            }
        );

        unset($value);
        return $processo;
    }

    /**
     * Valida as partes passadas por parâmetro
     * Validates all sections passed by parameter
     *
     * @param array $partes
     * @return array $partes
     * <AUTHOR> Revisão:
     * @version 1.0.0
     */
    private function validateSections()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        // Validar partes
        if (empty($this->sections)) {
            $this->sections = $this->param['partes'];
        } elseif (!$this->isValidParte($this->sections)) {
            throw new Exception('Tipo de parte para consulta não existe !', PARAM_ERROR);
        }

        if (!is_array($this->sections)) {
            $this->sections = array($this->sections);
        }

        // Forçar buscar por outras partes, outros advogados e recortes
        if (!in_array(FIELD_PARTES_OUTRAS, $this->sections)) {
            if (in_array(FIELD_PARTES_ATIVAS, $this->sections) || in_array(FIELD_PARTES_PASSIVAS, $this->sections)) {
                $this->sections[] = FIELD_PARTES_OUTRAS;
            }
        }

        if (!in_array(FIELD_PARTES_OUTRAS_ADVOGADOS, $this->sections)) {
            if (
                in_array(FIELD_PARTES_ATIVAS_ADVOGADOS, $this->sections)
                || in_array(FIELD_PARTES_PASSIVAS_ADVOGADOS, $this->sections)
            ) {
                $this->sections[] = FIELD_PARTES_OUTRAS_ADVOGADOS;
            }
        }

        if (!in_array(FIELD_DIARIOS_RECORTES, $this->sections)) {
            $this->sections[] = FIELD_DIARIOS_RECORTES;
        }
    }

    /**
     * Ajusta a string contendo o nome pesquisado
     * Setup an string which contains the searched term
     *
     * @param string $name
     * @param int $similarity
     * @return string $name
     * <AUTHOR> Machado - 2018-06-26
     * Revisão: Jonathan Machado - 2018-06-26
     * Revisão: Vitor Hugo - 2018-06-29
     * @version 1.0.0
     * @version 1.1.0 - Adição para verificar se vai buscar por similaridade total ou não
     */
    private function setupName()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        $name = Str::removerAcentos($this->name);
        $name = strtoupper($name);

        if ($this->similarity == 100) {
            // Forçar buscar por nome exato
            $name = !strpos($name, '"') ? '"' . $name . '"' : $name;
        }

        if ($this->similarity < 100) {
            $name = str_replace('"', "", $name);
        }

        return $name;
    }

    /**Salva o log na billing
     *
     *<AUTHOR>
     **/

    private function saveBilhetador($results, $tipoRequisicao = '')
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        $fonte = 'Dbjus - Detalhado';
        $criterio = $this->param['nome'];
        $tipo = 'Detalhe';

        if ($tipoRequisicao == 'Pesquisa') {
            $fonte = 'Dbjus - Resumido';
            $tipo = 'Resumo';
        }

        $manager = new DbjusManager();
        $manager->insertBilhetador($fonte, $results, $criterio, $tipo);
    }

    /**
     * Busca os detalhes de um processo
     * Search for a process details
     *
     * @param string $processo_id
     * @return array
     * <AUTHOR> Revisão:
     * @version 1.0.0
     */
    public function getDetalhesProcesso($processo_id)
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        // $this->resetParams();
        $this->searchParams['process_id'] = $processo_id;

        return $this->processDetalhesProcessos($this->request($this->search['detalhes_processo']));
    }

    /**
     * Adiciona filtro a pesquisa
     * Add filters the search
     *
     * @param string $text
     * @param array $fieldsToQuery
     * @return void
     * <AUTHOR> Revisão:
     * @version 1.0.0
     */

    public function addSearchFilter($text, $fieldsToQuery = [])
    {
        return $this->addFilter($text, FILTER_SEARCH_INPUTS, $fieldsToQuery);
    }

    public function addTermFilter($field, $acceptValues)
    {
        return $this->addFilter(strtoupper($field), FILTER_TERM_FILTERS, $acceptValues);
    }

    public function addRangeFilter($field, $fromValue, $toValue)
    {
        return $this->addFilter(strtoupper($field), FILTER_RANGE_FILTERS, array($fromValue, $toValue));
    }

    private function addFilter($text, $theFilter, $filters)
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        // Tratar parâmetros
        if (!is_array($filters)) {
            $filters = array($filters);
        }

        if (!is_array($theFilter)) {
            $theFilter = $this->getTheFilter($theFilter);
        }

        // Pegar tipo do filtro e campos
        $filter_type = $theFilter['filter_type'];
        $filter_fields = $theFilter['filter_fields'];
        $name_search = $theFilter['name_search'];

        // Criar array de filtro
        $final_filter = [];
        $final_filter[$name_search] = $text;

        // Adicionar campos ao filtro
        if (!is_array($filter_fields)) {
            $final_filter[$filter_fields] = $filters;
        } else {
            foreach ($filter_fields as $k_field => $filter_field) {
                if (!empty($filters[$k_field])) {
                    $final_filter[$filter_field] = $filters[$k_field];
                }
            }
        }

        $this->searchParams[$filter_type][] = $final_filter;

        return $this;
    }

    /*
     * Tratar o objeto de processos e retornar um array padronizado aos TJs
     *
     * $data : Object   ->  Objeto dos processos
     *
     * @return : Array
     */
    private function processListaProcessos($data)
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        if (is_object($data)) {
            if (!empty($data->total) && $this->totalProcessos == 0) {
                $this->totalProcessos = $data->total;
            }
            // Total de processsos
            $data = $data->hits; // Processos
        }

        if (!is_array($data) || (count($data) < 1)) {
            return [];
        }
        //throw new Exception("Nenhum registro encontrado", NOTFOUND_ERROR);

        $final = [];

        foreach ($data as $processo) {
            $arr_processo = [];
            $uf = "";
            //$arr_processo['andamentos'] = [];

            // Dados de identificação do processo
            $arr_processo['id_processo'] = $processo->processId;
            $arr_processo['numero_processo'] = $processo->nup;

            // Unidades Federais
            foreach ($processo->unidadesFederais as $processo_UF) {
                $uf .= $processo_UF->name . " / ";
            }

            $uf = substr($uf, 0, strlen($uf) - 3);

            $arr_processo['uf'] = $uf;

            // Instancias
            foreach ($processo->instancesSummaries as $processo_instancia) {
                $arr_processo_instancia = [];
                $arr_processo_instancia['nome'] = $processo_instancia->instanceName;
                $arr_processo_instancia['assuntos'] = (!empty($processo_instancia->assuntos))
                    ? implode(" / ", $processo_instancia->assuntos)
                    : [];
                $arr_processo_instancia['classes'] = (!empty($processo_instancia->classes))
                    ? implode(" / ", $processo_instancia->classes)
                    : [];

                $arr_processo['instancias'][] = $arr_processo_instancia;

                // Partes ativas
                if (!empty($processo_instancia->partesAtivas)) {
                    foreach ($processo_instancia->partesAtivas as $partesAtivas) {
                        $arr_processo['partes'][] = array('tipo' => 'ATIVA', 'nome' => $partesAtivas);
                    }
                }

                // Partes passivas
                if (!empty($processo_instancia->partesPassivas)) {
                    foreach ($processo_instancia->partesPassivas as $partesPassivas) {
                        $arr_processo['partes'][] = array('tipo' => 'PASSIVA', 'nome' => $partesPassivas);
                    }
                }

                // Outras partes
                if (!empty($processo_instancia->outrasPartes)) {
                    foreach ($processo_instancia->outrasPartes as $partesOutras) {
                        $arr_processo['partes'][] = array('tipo' => 'OUTRO', 'nome' => $partesOutras);
                    }
                }
            }

            $arr_processo['ultimo_diario'] = $processo->ultimoDiario;

            // Último processo
            if ($processo->ultimoDiario != null) {
                $arr_processo['ultimo_diario'] = "[" .
                    date("d/m/Y", substr($processo->ultimoDiario->date, 0, strlen($processo->ultimoDiario->date) - 3)) .
                    "] " . $processo->ultimoDiario->bookName . " - " . $processo->ultimoDiario->siteName;
            } else {
                $arr_processo['ultimo_diario'] = '';
            }

            // Último andamento
            if ($processo->ultimoAndamento != null) {
                $arr_processo['ultimo_andamento'] = "[" .
                    date(
                        "d/m/Y",
                        substr($processo->ultimoAndamento->date, 0, strlen($processo->ultimoAndamento->date) - 3)
                    ) . "] " . $processo->ultimoAndamento->title;
            } else {
                $arr_processo['ultimo_andamento'] = '';
            }

            $final[] = $arr_processo;
        }

        return $final;
    }

    /*
     * Tratar o objeto de detalhes do processo e retornar um array padronizado aos TJs
     *
     * $data : Object   ->  Objeto dos processos
     *
     * @return : Array
     */
    private function processDetalhesProcessos($data)
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        if (is_array($data) && isset($data['erro_detalhes'])) {
            return [
                'erro_detalhes' => $data['erro_detalhes']
            ];
        }

        if (is_object($data)) {
            $diaries = $data->diaries ??  [];
            $data = $data->instances;
        }
        if (!is_array($data) || (count($data) < 1)) {
            return [];
        }
        $data = $data[0];


        $final = [];

        // Dados processo
        $final['numero_processo'] = $data->nup;
        $final['valor'] = $data->valor;
        $final['local'] = $data->localAtual;
        $final['vara'] = $data->vara;
        $final['comarca'] = $data->comarca;
        $final['assuntos'] = implode(" / ", $data->assuntos);
        $final['classes'] = implode(" / ", $data->assuntos);

        // Datas processo
        $final['data_abertura'] = (!empty($data->dataAbertura))
            ? date('d/m/Y', substr($data->dataAbertura, 0, strlen($data->dataAbertura) - 3))
            : null;
        $final['data_atuacao'] = (!empty($data->dataAutuacao))
            ? date('d/m/Y', substr($data->dataAutuacao, 0, strlen($data->dataAutuacao) - 3))
            : null;
        $final['data_distribuicao'] = (!empty($data->dataDistribuicao))
            ? date('d/m/Y', substr($data->dataDistribuicao, 0, strlen($data->dataDistribuicao) - 3))
            : null;
        $final['data_encerramento'] = (!empty($data->dataEncerramento))
            ? date('d/m/Y', substr($data->dataEncerramento, 0, strlen($data->dataEncerramento) - 3))
            : null;
        $final['data_transito'] = (!empty($data->dataTransito))
            ? date('d/m/Y', substr($data->dataTransito, 0, strlen($data->dataTransito) - 3))
            : null;
        $final['data_inicio'] = (!empty($data->dataInicio))
            ? date('d/m/Y', substr($data->dataInicio, 0, strlen($data->dataInicio) - 3))
            : null;
        $final['data_fim'] = (!empty($data->dataFim))
            ? date('d/m/Y', substr($data->dataFim, 0, strlen($data->dataFim) - 3))
            : null;

        // Partes
        foreach ($data->partes as $parte) {
            $arr_parte = [];

            $arr_parte['tipo'] = $parte->tipo;
            $arr_parte['nome'] = $parte->nome;

            if (is_array($parte->advogados)) {
                foreach ($parte->advogados as $advogado) {
                    $final['advogados'][] = $this->detalhaAdvogado($advogado);
                }
            } else {
                $final['advogados'][] = $this->detalhaAdvogado($parte);
            }

            $final['partes'][] = $arr_parte;
        }

        // Andamentos
        $data->andamentos = array_slice($data->andamentos, 0, LIMIT_RESULTS, true);
        foreach ($data->andamentos as $andamento) {
            $arr_andamento = [];

            $arr_andamento['data'] = date('d/m/Y', substr($andamento->date, 0, strlen($andamento->date) - 3));
            $arr_andamento['titulo'] = $andamento->title;
            $arr_andamento['detalhes'] = Str::encoding($andamento->details);

            $final['andamentos'][] = $arr_andamento;
        }

        // Tratar duplicidade em advogados
        if (!empty($final['advogados'])) {
            // Criar array separando TIPO | NOME
            foreach ($final['advogados'] as $advogados) {
                $advogados_implode[] = $advogados['tipo'] . '|' . str_replace('|', '', $advogados['nome']);
            }

            // Apagar duplicados
            $advogados_implode = array_unique($advogados_implode);
            ksort($advogados_implode);

            // Criar novo array separando o tipo e o nome
            foreach ($advogados_implode as $advogado) {
                $advogados_final[] = array_combine(array('tipo', 'nome'), explode('|', $advogado));
            }

            // Setar novo array
            $final['advogados'] = $advogados_final;
        }

        // Tratar duplicidade em partes
        if (!empty($final['partes'])) {
            // Criar array separando TIPO | NOME
            foreach ($final['partes'] as $partes) {
                $partes_implode[] = $partes['tipo'] . '|' . str_replace('|', '', $partes['nome']);
            }

            // Apagar duplicados
            $partes_implode = array_unique($partes_implode);
            ksort($partes_implode);

            // Criar novo array separando o tipo e o nome
            foreach ($partes_implode as $parte) {
                $partes_final[] = array_combine(array('tipo', 'nome'), explode('|', $parte));
            }

            // Setar novo array
            $final['partes'] = $partes_final;
        }

        // Diários
        $diaries = array_slice($diaries, LIMIT_RESULTS * -1, LIMIT_RESULTS, true);
        if (!empty($diaries)) {
            foreach ($diaries as $diario) {
                $final['diarios'][] = array(
                    'data' => date('d/m/Y', substr($diario->date, 0, strlen($diario->date) - 3)),
                    'nome' => $diario->bookName . ' - ' . $diario->siteName,
                    'snippet' => strip_tags($diario->snippet),
                );
            }
        }

        return $final;
    }

    /**
     * Faz uma requisição recebendo os parametros como array
     * Makes a request receiving parameters as an array
     *
     * @param array $pageRequest
     * @return array
     * <AUTHOR> Kuniyoshi - 2019-02-13 - Pegando detalhes dos advogados
     * @version 2.0.0
     */
    public function detalhaAdvogado($advogado)
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        $advogados = [];
        $advogados['tipo'] = $advogado->tipo;
        $advogados['nome'] = $advogado->nome;

        switch (strtoupper($advogados['tipo'])) {
            case 'ADVOGADO_PARTE_ATIVA':
                $advogados['tipo'] = 'ATIVA';
                break;
            case 'ADVOGADO_PARTE_PASSIVA':
                $advogados['tipo'] = 'PASSIVA';
                break;
        }
        return $advogados;
    }


    /*
     * Unir os detalhes do processo com a lista de processos
     *
     * $lista_processos : Array      ->  Lista de processos
     * $detalhes_processos : array   ->  Detalhes dos processos
     *
     * @return Array
     */
    public function mergeDetalhesProcesso($lista_processos, $detalhes_procesos)
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        $lista_processos = $lista_processos['processos'];

        // Percorrer lista de processos
        foreach ($lista_processos as $k_proc => $processo) {
            // Percorrer lista de detalhes dos processos
            foreach ($detalhes_procesos as $detalhe) {
                if (isset($detalhe['erro_detalhes'])) {
                    $lista_processos[$k_proc]['erro_detalhes'] = $detalhe['erro_detalhes'];
                    break;
                }
                // Recuperar número do processo de ambos arrays
                $numproc_lista = trim($processo['numero_processo']);
                $numproc_detalhes = trim($detalhe['numero_processo']);

                // Tratar número do processo
                $numproc_lista = str_replace(array('.', '-', '_', '/', '\\'), '', $numproc_lista);
                $numproc_detalhes = str_replace(array('.', '-', '_', '/', '\\'), '', $numproc_detalhes);

                // Se o número de um for igual a do outro, então adicionar o detalhe do processo ao processo
                // atual e pular para verificar o próximo processo
                if ($numproc_lista == $numproc_detalhes) {
                    $lista_processos[$k_proc]['detalhes'] = [];
                    $lista_processos[$k_proc]['detalhes'] += $detalhe;
                    break;
                }
            }
        }

        // Remover partes duplicadas
        foreach ($lista_processos as $k_proc => $processo) {
            if (!empty($processo['partes']) && !empty($processo['detalhes']['partes'])) {
                unset($lista_processos[$k_proc]['detalhes']['partes']);
            }
        }

        return $lista_processos;
    }

    /**
     * Retorna se a parte passada como parâmetro é valida para consulta
     *
     * $parte : String  ->  Parte
     *
     * @return bool
     */
    public function isValidParte($partes)
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        if (!is_array($partes)) {
            $partes = array($partes);
        }

        foreach ($partes as $parte) {
            if (!in_array($parte, $this->getPartes())) {
                return false;
            }
        }

        return true;
    }

    /*
     * Retorna em um array todas as partes disponíveis para consulta
     *
     * @return Array
     */
    public function getPartes()
    {
        return array(
            FIELD_PARTES_ATIVAS,
            FIELD_PARTES_PASSIVAS,
            FIELD_PARTES_OUTRAS,
            FIELD_PARTES_PASSIVAS_ADVOGADOS,
            FIELD_PARTES_ATIVAS_ADVOGADOS,
            FIELD_PARTES_OUTRAS_ADVOGADOS,
            FIELD_DIARIOS_RECORTES
        );
    }

    /**
     * Fazer requisição da API
     *
     * @param pageRequest string
     * @param params array
     */

    /**
     * Faz uma requisição recebendo os parametros como array
     * Makes a request receiving parameters as an array
     *
     * @param array $pageRequest
     * @return array
     * <AUTHOR> Revisão: Jonathan Machado - 2018-04-26 - Trocado o client por RequestManager
     * @version 2.0.0
     */
    public function request($pageRequest)
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        $url = $pageRequest['url'];
        $pageRequest['params'] = $this->getSearchParams();
        $pageRequest['url'] = $this->search['base'] . $pageRequest['url'];
        $pageRequest['headers'] = [];

        try {
            // Setar parâmetros
            if (strtolower($pageRequest['method']) == 'get') {
                foreach ($pageRequest['params'] as $field => $value) {
                    if (is_array($value)) {
                        continue;
                    }
                    $strField = "{" . $field . "}";
                    $strValue = urlencode($value);
                    $url = $pageRequest['url'];
                    $pageRequest['url'] = str_replace($strField, $strValue, $url);
                }
            }

            if ($this->offset > 0) {
                if (!strpos($pageRequest['url'], '?')) {
                    $pageRequest['url'] .= '?';
                }

                $pageRequest['url'] .= '&offset=' . $this->offset;
            }

            $loginArr = [
                CURLOPT_USERPWD => DBJUS_USER . ":" . DBJUS_PASS,
            ];

            if (preg_match('/details.*?nup=/is', $pageRequest['url'])) {
                $loginArr[CURLOPT_TIMEOUT] = 120;
            }

            $this->setCurlOpt($loginArr);

            try {
                $response = $this->getResponse(
                    $pageRequest['url'],
                    $pageRequest['method'],
                    json_encode($pageRequest['params']),
                    $pageRequest['headers']
                );

                $result = json_decode($response, true);

                if (preg_match("/\/process\/search/", $pageRequest['url'])) {
                    if (is_array($result)) {
                        $this->saveBilhetador($response, 'Pesquisa');
                    } else {
                        throw new Exception('Erro ao acessar o servidor', 3);
                    }
                } else {
                    if (is_array($result)) {
                        $this->verifyInsertProcess($result['nup'], $response);
                    } else {
                        throw new Exception('Erro ao acessar o servidor', 3);
                    }
                }

                return json_decode($response);
            } catch (Exception $e) {
                //Existem processos onde o detalhe da timeout
                if (preg_match('/Operation.timed.out.after/isU', $e->getMessage())) {
                    return [
                        'erro_detalhes' => $this->errorDetalhe
                    ];
                }

                // Algumas vezes o servidor bloqueia o request por muitas requisições feitas em pouco tempo
                sleep(1);

                try {
                    $response = $this->getResponse(
                        $pageRequest['url'],
                        $pageRequest['method'],
                        json_encode($pageRequest['params']),
                        $pageRequest['headers']
                    );

                    $result = json_decode($response, true);

                    if (preg_match("/\/process\/search/", $pageRequest['url'])) {
                        if (is_array($result)) {
                            $this->saveBilhetador($response, 'Pesquisa');
                        } else {
                            throw new Exception('Erro ao acessar o servidor', 3);
                        }
                    } else {
                        if (is_array($result)) {
                            $this->verifyInsertProcess($result['nup'], $response);
                        } else {
                            throw new Exception('Erro ao acessar o servidor', 3);
                        }
                    };
                } catch (Exception $e) {
                    $msg = "Ocorreu um erro: " . $e->getMessage();
                    $msg .= "\n URL: {$pageRequest['url']} ";
                    $msg .= "\n Parametros: \n" . json_encode($pageRequest['params']);
                    throw new Exception($msg);
                }
            }

            // Verificar se ocorreram erros
            if ($response->status == 'error') {
                $json = '';
                $json = "Json de request: " . substr($pageRequest['params'], 0, 1000);
                $json .= "\nUrl do request: " . substr($pageRequest['url'], 0, 1000);

                throw new Exception("Response error {$response->getStatus()} - {$json}", REQUEST_ERROR);
            }

            // Retornar resultado
            return json_decode($response);
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * Verifica e insere um processo no banco
     *
     * @return null
     * <AUTHOR>
     * Revisão:
     * @version 1.0.0
     */
    private function verifyInsertProcess($process, $response)
    {
        if (ENV == 'qa') {
            return;
        }

        $db = new PostgresDB();

        $query = $db->connectBilling()
            ->select('process_nup')
            ->from('public.dbjus_process')
            ->where("'{$process}' = process_nup")
            ->execute()
            ->fetchAll();

        if (empty($query)) {
            $db->connectBilling()
                ->insert('public.dbjus_process')
                ->values(
                    array(
                        'process_nup' => '?',
                        'opening_date' => '?',
                    )
                )
                ->setParameter(0, $process)
                ->setParameter(1, Carbon::createFromFormat('YmdHis', (int) date('YmdHis')))
                ->execute();

            $this->saveBilhetador($response, 'Abertura');
        }

        $db->disconnect();
    }

    /**
     * Recupera a página
     * Gets the page
     *
     * @return float
     * <AUTHOR> Revisão:
     * @version 1.0.0
     */
    private function getPage($quantidade): float
    {
        $pagina = $quantidade - 1;

        return $pagina;
    }

    /**
     * Setar página
     * Obs.: O offset informa apenas o primeiro registro a ser buscado,
     * já essa função informa o offset da página
     *
     * Setting page
     * Nt.: The offset defines only the first data to be search, but this
     * set the page offset
     *
     * @param int $page
     * @return void
     * <AUTHOR> Revisão:
     * @version 1.0.0
     */
    private function setPage($page)
    {
        if ($page < 0) {
            $page = 0;
        }

        $this->setOffset($page);
    }

    /**
     * Seta offset
     * Set offset
     *
     * @param int $offset
     * @return void
     * <AUTHOR> Revisão:
     * @version 1.0.0
     */
    private function setOffset($offset)
    {
        if ($offset < 0) {
            $offset = 0;
        }

        $this->offset = $offset;
    }

    /**
     * Recupera o offset
     * Retuns the offset
     *
     * @return int
     * <AUTHOR> Revisão:
     * @version 1.0.0
     */
    private function getOffset()
    {
        $offset = $this->searchParams['offset'] ?? 0;

        if ($offset < 0 || empty($offset)) {
            $offset = 0;
        }

        return $offset;
    }

    /**
     * Retorna os parametros de busca
     * Returns the search params
     *
     * @return void
     * <AUTHOR> Revisão:
     * @version 1.0.0
     */
    private function getSearchParams()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        return $this->searchParams;
    }

    /**
     * Retorna o nome e campos do filtro
     * Return filter's names and fields
     *
     * @param string $filter_type
     * @return void
     * <AUTHOR> Revisão:
     * @version
     */
    private function getTheFilter($filter_type)
    {
        $filter_type = unserialize($filter_type);

        $filter_keys = array_keys($filter_type);
        $filter_fields = $filter_type[$filter_keys[0]];

        $final['filter_type'] = $filter_keys[0];
        $final['filter_fields'] = $filter_fields;
        $final['name_search'] = $filter_type['name_search'];

        return $final;
    }

    /**
     * Percorre o resultado e verifica a similaridade do criterio pesquisado
     *
     * @param array $results
     * @param string $criterio
     * @param integer $similarity
     * @return array
     * <AUTHOR> Hugo R. <<EMAIL>> - 2018-06-29
     * @version 1.0.0
     *          1.1.0 - alteração dos valores de validação para checar similarity
     */
    private function checkSimilarities($results, $section = '')
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        $hasSimilarity = [];
        $criterio = preg_replace('/"/iu', '', $this->name);

        $attr = 'nome';
        $mainNode = '';

        if ($section === 'advogados') {
            $mainNode = 'detalhes';
        }

        if ($section === 'diarios') {
            $criterio = Str::prepareString($criterio, true, true, false);
            $criterio = str_replace(' ', '\s*?', trim($criterio));
        }

        foreach ($results as $processo) {
            //Procurar o nome nos diários, pois tem processos que o crtiério é mencionado apenas no diário
            if ($section === 'diarios') {
                $diarios = $processo["detalhes"]["diarios"];
                if (empty($diarios)) {
                    continue;
                }

                $diarios = array_slice($diarios, 0, 10);
                foreach ($diarios as $diario) {
                    $snippet = strip_tags(Str::prepareString($diario["snippet"], true, true, false));
                    if (preg_match("#({$criterio})#i", $snippet)) {
                        $processo['similarity'] = 100;
                        $hasSimilarity[] = $processo;
                        break;
                    }
                }
                continue;
            }

            $check = Str::hasSimilarityValid(
                $processo,
                $criterio,
                $this->similarity,
                $section,
                $attr,
                true,
                $mainNode
            );

            if ($check['check'] === true) {
                $processo['similarity'] = $check['similarity'];
                $hasSimilarity[] = $processo;
            }
        }

        return $hasSimilarity;
    }

    private function getKeySimilarity()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        $keys = [];

        if (isset($this->param['partes'][0])) {
            $this->sections = $this->param['partes'];
        }

        foreach ($this->sections as $section) {
            switch ($section) {
                case 'ADVOGADOS_PARTE_ATIVA':
                case 'ADVOGADOS_PARTE_PASSIVA':
                    $keys[] = 'advogados';
                    break;
                case "PARTES_ATIVAS":
                case "PARTES_PASSIVAS":
                    $keys[] = 'partes';
                    break;
                case "DIARIOS_RECORTES":
                    $keys[] = 'diarios';
                    break;
                default:
                    $keys[] = 'partes';
                    $keys[] = 'advogados';
                    $keys[] = "diarios";
            }
        }

        return array_unique($keys);
    }
}
