<?php

namespace App\Crawler\InepIeb;

use App\Crawler\Spider;
use App\Factory\MongoDB;
use App\Helper\Document;
use Exception;

class InepIeb extends Spider
{
    private const INDEX_MONGODB = 'corporateName_document';
    private const LIMIT = 100;

    private $criterio;
    private $limit;
    private $cont = 0;
    private $codeYear;
    private $array;
    private $arrayFinal;
    private $testeCont = 0;
    private $manager;
    private $isDoc = false;

    private const ETAPACONVERTED = [
        'EI' => 'Educação Infantil',
        'EM1' => '1º ano Ensino Médio',
        'EM2' => '2º ano Ensino Médio',
        'EM3' => '3º ano Ensino Médio',
        'EM4' => '4º ano Ensino Médio',
        'EF1' => '1º ano Ensino Fundamental',
        'EF2' => '2º ano Ensino Fundamental',
        'EF3' => '3º ano Ensino Fundamental',
        'EF4' => '4º ano Ensino Fundamental',
        'EF5' => '5º ano Ensino Fundamental',
        'EF6' => '6º ano Ensino Fundamental',
        'EF7' => '7º ano Ensino Fundamental',
        'EF8' => '8º ano Ensino Fundamental',
        'EF9' => '9º ano Ensino Fundamental',
        'EJF' => 'EJA Ensino Fundamental',
        'EJM' => 'EJA Ensino Médio'
    ];


    public function start()
    {
        $this->manager = (new MongoDB())
            ->connectSources()
            ->setDb('common');

        return $this->getAndReturnResults();
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->criterio = trim($this->param['criterio']);
        $this->limit = $this->param['limit'] ?? self::LIMIT;
        if (empty($this->criterio)) {
            throw new Exception('Parâmetro Inválido', 1);
        }

        if (Document::validarCpfOuCnpj($this->criterio)) {
            $this->isDoc = true;
            $this->criterio = Document::removeMask($this->criterio);
        }
    }

    /** Busca dados da collection common.inep_ieb
     * @return array
     * <AUTHOR> Santos 30/12/21
     */
    private function getAndReturnResults()
    {
        $fields = [$this->isDoc ? 'cnpj' : 'nome'];

        $results = $this->manager->setCollection('inep_ieb')
        ->atlasSearch(
            $this->criterio,
            self::INDEX_MONGODB,
            $this->limit,
            $fields
        )->toArray();

        if (empty($results)) {
            throw new Exception('Nenhum registro encontrado', 2);
        }
        $results = json_decode(json_encode($results, true), true);
        return $this->parseAndGetResults($results);
    }

    /** Busca dados da collection common.inep_ieb_turma
     * @return array
     * <AUTHOR> Santos 30/12/21
     */
    private function parseAndGetResults($result)
    {
        $i = 0;
        $registryParse = [];
        foreach ($result as $key => $value) {
            $dataResult = $this->getMatricula($value['id']);

            foreach ($dataResult as $keyAlived => $valueNow) {
                $registryParse[$i][] = [
                    'ano' => $valueNow['_id']['ano'],
                    'etapa' => self::ETAPACONVERTED[$valueNow['_id']['etapa']],
                    'matriculas' => $valueNow['matriculas']
                ];
            }
            if (empty($dataResult)) {
                $registryParse[$i][] = [];
            }
            $i++;
        }
        return $this->finalParse($result, $registryParse);
    }


    /*Fonte veio do node com com o vetor com o indice por ano
    tornando a resposta uma matriz, esse tanto de for
    é para deixar a resposta igual, pq se mudar o front quebra
    os dossies antigos*/

    private function registryYear($year, $valueRegistry)
    {
        if ($this->cont == 0) {
            $this->codeYear = $year;
            $this->array[] = $valueRegistry;
        }
        if ($valueRegistry['ano'] == $this->codeYear && $this->cont != 0) {
            array_push($this->array, $valueRegistry);
        }

        if ($valueRegistry['ano'] != $this->codeYear) {
            $this->arrayFinal[$this->codeYear] = $this->array;
            $this->testeCont++;
            $this->array = [];
            $this->array[] = $valueRegistry;
        }

        if ($this->codeYear != $year) {
            $this->codeYear = $year;
        }

        if ($this->lastIndex) {
            $this->arrayFinal[$this->codeYear] = $this->array;
        }
        $this->cont++;
    }

    private function finalParse($result, $registryParse)
    {

        foreach ($result as $key => $value) {
            $this->arrayFinal = [];
            $this->array = [];
            $i = 0;
            $j = 1;
            $this->cont = 0;

            foreach ($registryParse[$key] as $keyRegistry => $valueRegistry) {
                if (empty($valueRegistry)) {
                    continue;
                }
                $this->lastIndex = false;
                if ($j == count($registryParse[$key])) {
                    $this->lastIndex = true;
                }
                $this->registryYear($valueRegistry['ano'], $valueRegistry);
                $i++;
                $j++;
            }
            $data[] = [
                'id' => $value['id'],
                'estado_id' => $value['estado_id'],
                'municipio_id' => $value['municipio_id'],
                'nome' => $value['nome'],
                'cnpj' => $value['cnpj'],
                'mantenedora' => $value['mantenedora'],
                'mantenedora_cnpj' => $value['mantenedora_cnpj'],
                'categoria' => $value['categoria'],
                'endereco' => $value['endereco'],
                'numero' => $value['endereco'],
                'complemento' => $value['complemento'],
                'bairro' => $value['bairro'],
                'uf' => $value['uf'],
                'cep' => $value['cep'],
                'telefone' => $value['telefone'],
                'fax' => $value['fax'],
                'email' => $value['email'],
                'situacao' => $value['situacao'],
                'dependencia_administrativa' => $value['dependencia_administrativa'],
                'tipo_atendimento' => $value['tipo_atendimento'],
                'zona' => $value['zona'],
                'matriculas' => $this->arrayFinal
            ];
        }

        return $data;
    }
    private function getMatricula($id)
    {
        $pipeline = [
                [
                    '$match' => [
                        '$and' => [
                            ['escola_id' => ['$eq' => $id]],
                            [
                                'etapa' => [
                                    '$in' => [
                                        'EF1',
                                        'EF2',
                                        'EF3',
                                        'EF4',
                                        'EF5',
                                        'EF6',
                                        'EF7',
                                        'EF8',
                                        'EF9',
                                        'EM1',
                                        'EM2',
                                        'EM3',
                                        'EM4',
                                        'EI',
                                        'EJF',
                                        'EJM'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                [
                    '$group' =>
                        [
                            '_id' => [
                                'ano' => '$ano',
                                'etapa' => '$etapa',
                            ],
                            'matriculas' => ['$sum' => '$numero_matriculas']
                        ]
                ],
                ['$sort' => ['_id.ano' => -1, '_id.etapa' => 1]]
            ];

        $dataResult = $this->manager->setCollection('inep_ieb_turma')
            ->query('aggregate', [$pipeline])->toArray();
        $dataResult = json_decode(json_encode($dataResult, true), true);

        return $dataResult;
    }
}
