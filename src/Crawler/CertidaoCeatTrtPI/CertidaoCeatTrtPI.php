<?php

namespace App\Crawler\CertidaoCeatTrtPI;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\ElasticsearchManager;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

class CertidaoCeatTrtPI extends Spider
{
    private const BASE_URL = 'https://siscle.trt22.jus.br/CEAT/index.xhtml';
    private const SITE_KEY = '6LdvJBgUAAAAAI8EmWh6Cpxa-2vxwkrimtX6gaPg';
    private const PATH_STATIC_S3 = S3_STATIC_URL;
    private const PATH_CERTIDAO_S3 = 'captura/certidao_ceat_trt_22_pi/';

    private $nome;
    private $cpfCnpj;
    private $tipo;
    private $elasticManager;
    public $headers = [
        "User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0",
    ];

    public function __construct($param, $auth)
    {
        parent::__construct($param, $auth);
        $this->elasticManager = new ElasticsearchManager();
    }

    public function start()
    {
        $retry = 3;
        while ($retry >= 0) {
            try {
                $this->setAlternativeProxy();
                $html = $this->getResponse(self::BASE_URL, $this->headers);
                preg_match('/ViewState:."\svalue="([\s\S].*?)"/isu', $html, $viewState);
                $this->makeRequest($viewState[1]);
                $file = $this->getPdf($viewState[1]);
                $data = $this->savePdfAndGetText($file);

                $text = $this->parseText($data['textPdf']);

                return [
                    'info' => $text,
                    'pdf' => $data['urlPdf']
                ];
            } catch (Exception) {
                if ($retry == 0) {
                    throw new Exception('Ocorreu um erro ao capturar dados da página', 3);
                }

                $retry--;
            }
        }
    }

    /**
     * @return void
     * <AUTHOR> Santos - 12 ago. 2022
     *
     * @param  string  $viewState
     *
     * @throws \Exception
     */
    private function makeRequest(string $viewState): void
    {
        $params = [
            "tabs:buscaCEAT" => "tabs:buscaCEAT",
            "tabs:buscaCEAT:tipoBusca" => $this->tipo,
            "tabs:buscaCEAT:cnpjcpf" => Document::formatCpfOrCnpj($this->cpfCnpj),
            "tabs:buscaCEAT:nomeReceita" => $this->nome,
            "tabs:buscaCEAT:btConsultar" => '',
            "javax.faces.ViewState" => $viewState
        ];

        $this->getResponse(self::BASE_URL, 'POST', $params, $this->headers);
    }

    private function getPdf(string $viewState): string
    {
        $captcha = $this->resolveRecaptcha();
        $params = [
            "tabs:buscaCEAT" => "tabs:buscaCEAT",
            "tabs:buscaCEAT:tipoBusca" => $this->tipo,
            "tabs:buscaCEAT:cnpjcpf" => Document::formatCpfOrCnpj($this->cpfCnpj),
            "tabs:buscaCEAT:nomeReceita" => $this->nome,
            "g-recaptcha-response" => $captcha,
            "tabs:buscaCEAT:btCertidaoPDF" => '',
            "javax.faces.ViewState" => $viewState
        ];

        return $this->getResponse(self::BASE_URL, 'POST', $params, $this->headers);
    }


    /**
     * @throws \Exception
     */
    private function savePdfAndGetText(string $file): array
    {
        $uniqid = md5(uniqid(rand(), true));
        $certificateName = "$uniqid.pdf";
        $certificateLocalPath = "/tmp/{$certificateName}";
        $certificateS3Path = self::PATH_CERTIDAO_S3 . $certificateName;
        $certificateUrl = self::PATH_STATIC_S3 . $certificateS3Path;

        file_put_contents($certificateLocalPath, $file);

        (new S3(new StaticUplexisBucket()))->save($certificateS3Path, $certificateLocalPath);

        $text = (new Pdf())->getTextFromPdf($certificateLocalPath, ['layout', 'nopgbrk']);

        return ["urlPdf" => $certificateUrl, "textPdf" => $text];
    }

    private function parseText(string $text): array
    {
        $patterns = [
            'situacao' => ['@CERTID.O\s(POSITIVA|NEGATIVA)@u'],
            'NumCertidao' => ['@Certid..\sn.\s(.*?)Expedi...@u'],
            'dataExpedicao' => ['@Expedi...:(.*)@u'],
            'descricao' => ['@(Certifica-se[\s\S]*?)\s+OBSERVA..ES@u'],
            'observacoes' => ['@OBSERVA..ES:([\s\S]*)@u']
        ];
        $data = Util::parseDados($patterns, $text);
        return array_map("utf8_decode", $data);
    }

    /**
     * @throws \Exception
     */
    private function resolveRecaptcha(): string
    {
        $retry = 3;
        do {
            if (!empty(self::SITE_KEY)) {
                return $this->solveReCaptcha(self::SITE_KEY, self::BASE_URL);
            }
            $retry--;
        } while ($retry > 0);

        throw new Exception("Erro ao localizar dados do captcha na página.", 3);
    }

    private function getQueryParams(string $document)
    {
        $isCpf = $this->tipo === 'cpf' ?? false;
        return [
            'index' => $isCpf ? 'spine_pf' : 'spine_pj',
            'type' => $isCpf ? 'pf' : 'pj',
            'body' => [
                'query' => [
                    'match' => [
                        $this->tipo => $document
                    ]
                ]
            ]
        ];
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->cpfCnpj = trim($this->param['cpf_cnpj']);
        $this->nome = trim($this->param['nome']);

        if (!Document::validarCpfOuCnpj($this->cpfCnpj)) {
            throw new Exception('Parâmetro CPF/CNPJ inválido', 1);
        }

        (Document::validarCpf($this->cpfCnpj)) ? $this->tipo = 'cpf' : $this->tipo = 'cnpj';
        if (empty($this->nome)) {
            $dados = $this->elasticManager->search($this->getQueryParams(Document::removeMask($this->cpfCnpj)));
            $dados = isset($dados['hits']['hits'][0])
                ? $dados['hits']['hits'][0]['_source']
                : null;

            $nome = $dados['nome'] ?? $dados['razao_social'];
            if (empty($nome)) {
                throw new Exception('Não foi possível recuperar o nome para busca!', 1);
            }

            $this->nome = $nome;
        }
    }
}
