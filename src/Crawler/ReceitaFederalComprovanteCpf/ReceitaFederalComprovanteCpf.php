<?php

namespace App\Crawler\ReceitaFederalComprovanteCpf;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Str;
use App\Helper\Util;
use App\Manager\ElasticsearchManager;
use Exception;

class ReceitaFederalComprovanteCpf extends Spider
{
    protected $serproKey = 'ijwwGUdz4xSi6a8lZcEeBzGnu9ca';
    protected $serproSecret = 'R0FU5f_95wfNGm6wa9uNWM6i5OYa';
    protected $serproTokenUrl = 'https://apigateway.serpro.gov.br/token';
    protected $serproUrl = 'https://apigateway.serpro.gov.br/consulta-cpf/v1/cpf/';

    private $document;

    protected function validateAndSetCrawlerAttributes()
    {
        if (isset($this->param['cpf']) && !empty($this->param['cpf']) && Document::validarCpf($this->param['cpf'])) {
            $this->document = preg_replace('/\D/isu', '', $this->param['cpf']);
        } else {
            throw new Exception('Documento inválido');
        }
    }

    protected function start()
    {
        switch ($this->param['fornecedor']) {
            case 'receita_federal_serpro':
                //$response = $this->receitaFederalSerpro();
                //break;
            case 'receita_federal_api':
                //$response = $this->receitaFederalApi();
                $response = $this->receitaFederal();
                break;

            default:
                $response = $this->receitaFederal();
                break;
        }

        if (empty($response['nome'])) {
            throw new Exception('Receita retornou todos os campos vazios favor reprocessar o lote', 3);
        }

        $this->updateSpine('receitaFederalPf', $response);

        return $response;
    }

    private function getDataDeNascimento()
    {
        $spine_pf = $this->searchElasticInfo($this->document);
        $spine_pf = $spine_pf['hits']['hits'][0]['_source'];

        if (empty($spine_pf)) {
            throw new Exception('DataNascimentoDivergente', 1);
        }

        if (empty($spine_pf['data_nascimento'])) {
            throw new Exception('DataNascimentoDivergente', 1);
        }

        return substr($spine_pf['data_nascimento'], 6, 2) . '/' .
            substr($spine_pf['data_nascimento'], 4, 2) . '/' .
            substr($spine_pf['data_nascimento'], 0, 4);
    }

    private function receitaFederalApi()
    {
        $this->setProxy();

        //Gerar Token
        $id = str_replace('.', '', uniqid(mt_rand(), true));
        $url = "https://movel01.receita.fazenda.gov.br/servicos-rfb/v2/Util/obterToken";
        $param = [
            'aplicativo' => 'pessoafisica',
            'idNuvem' => $id,
            'sandbox' => 'false',
            'so' => 'ios',
        ];
        $headers = [
            'Host: movel01.receita.fazenda.gov.br',
            'Connection: keep-alive',
            'Accept: application/json',
            'Accept-Language: fr-ca',
        ];

        $response = $this->getResponse($url, 'POST', json_encode($param), $headers);

        //Obter Token
        $url = "https://movel01.receita.fazenda.gov.br/servicos-rfb/v2/EnviarMensagemNuvem/recuperarMensagens";
        $param = [
            'idDispositivo' => $id,
        ];
        $token = null;

        $headers = [
            'Host: movel01.receita.fazenda.gov.br',
            'aplicativo: Pessoa F?sica',
            'Accept: */*',
            'versao_app: 4.3',
            'plataforma: iOS',
            'Accept-Language: fr-ca',
            'token: ' . $token,
            'versao: 10.2.1',
            'dispositivo: iPhone',
            'charset: utf-8',
            'Connection: keep-alive'
        ];

        $response = $this->getResponse($url, 'POST', $param, $headers);

        //Consulta
        $token = json_decode($response, true)[0]['token'];
        $tokenAuth = json_decode(json_decode($response, true)[0]['mensagemEnviada'], true)['aps']['tokenJWT'];

        $url = "https://movel01.receita.fazenda.gov.br:443/servicos-rfb/v2/IRPF/cpf/consultar";

        $param = [
            'cpf' => $this->param['cpf'],
            'dataNascimento' => $this->param['data_nascimento']
                ? str_replace('/', '', $this->param['data_nascimento'])
                : str_replace('/', '', $this->getDataDeNascimento()),
            'tokenAuth' => $tokenAuth,
        ];

        $headers = [
            'Host: movel01.receita.fazenda.gov.br',
            'aplicativo: Pessoa Física',
            'Accept: */*',
            'versao_app: 4.3',
            'plataforma: iOS',
            'Accept-Language: fr-ca',
            'token: ' . $token,
            'versao: 10.2.1',
            'dispositivo: iPhone',
            'idioma: pt',
            'Connection: keep-alive'
        ];

        $response = $this->getResponse($url, 'POST', json_encode($param), $headers);

        $response = json_decode($response, true);

        if ($response['codigoRetorno'] != '00') {
            throw new Exception($response['mensagemRetorno'], 1);
        }

        if (empty($response['nome'])) {
            throw new Exception('Erro: Não foi possível encontrar o nome na resposta da receita', 1);
        }

        return [
            'cpf' => $this->param['cpf'],
            'nome' => $response['nome'],
            'nascimento' => $response['dataNascimento'],
            'situacao' => $response['descSituacaoCadastral'],
            'inscricao' => $response['dataIsncricao'],
            'digito_verificador' => $response['digitoVerificador'],
            'obito' => $response['mensagemObito'] != null ? $response['mensagemObito'] : '',
            'hora' => $response['horaConsulta'],
            'data' => $response['dataConsulta'],
            'chave' => $response['codigoControle'],
        ];
    }

    private function receitaFederalSerpro()
    {
        /**
         * Buscar Token
         */
        $headers = array(
            'Authorization: Basic ' . base64_encode($this->serproKey . ':' . $this->serproSecret),
        );
        $params = array(
            'grant_type' => 'client_credentials',
        );
        $response = $this->getResponse($this->serproTokenUrl, 'POST', $params, $headers);
        $data = json_decode($response, true);
        if (isset($data['access_token']) && !empty($data['access_token'])) {
            $token = $data['access_token'];
        } else {
            throw new Exception('Erro ao buscar token de acesso à Serpro', 3);
        }
        /**
         * Buscar dados
         */
        $headers = array(
            'Accept: application/json',
            'Authorization: Bearer ' . $token,
        );

        $response = $this->getResponse($this->serproUrl . $this->document, 'GET', array(), $headers);
        $data = json_decode($response, true);

        if (!isset($data) || empty($data)) {
            throw new Exception('Erro ao buscar dados na Serpro', 3);
        } elseif (isset($data['message'])) {
            throw new Exception($data['message'], 3);
        } elseif (
            !isset($data['nome']) || empty($data['nome']) || !isset($data['nascimento']) || empty($data['nascimento'])
        ) {
            throw new Exception('Dados inválido recebidos da Serpro', 3);
        }

        return [
            'cpf' => $data['ni'],
            'nome' => $data['nome'],
            'nascimento' => preg_replace("/(\d{2})(\d{2})(\d{4})/isu", "$1/$2/$3", $data['nascimento']),
            'situacao' => $data['situacao']['descricao'],
            'inscricao' => '',
            'digito_verificador' => '',
            'obito' => (isset($data['obito']) && !empty($data['obito'])) ? $data['obito'] : '',
            'hora' => '',
            'data' => '',
            'chave' => '',
        ];
    }

    private function getSiteHeaders($method = 'GET', $cookies = [], $gResponse = '')
    {
        $options = [
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
            ]
        ];

        if ($method === 'POST') {
            $options['http'] = [
                'method'  => 'POST',
                'header' => 'Cookie: ' . implode(';', $cookies),
                'content' => http_build_query([
                    'idCheckedReCaptcha' => 'false',
                    'txtCPF' => Document::formatCpf($this->param['cpf']),
                    'txtDataNascimento' => $this->param['data_nascimento']
                        ? $this->param['data_nascimento']
                        : $this->getDataDeNascimento(),
                    'h-captcha-response' => $gResponse,
                    'Enviar' => 'Consultar'
                ])
            ];
        }

        return stream_context_create($options);
    }

    private function receitaFederal()
    {
        $url = "https://servicos.receita.fazenda.gov.br/Servicos/CPF/ConsultaSituacao/ConsultaPublica.asp";

        $response = Str::encoding(file_get_contents($url, false, $this->getSiteHeaders()));

        $cookies = [];

        foreach ($http_response_header as $hdr) {
            if (preg_match('/^Set-Cookie:\s*([^;]+)/', $hdr, $matches)) {
                $cookies[] = $matches[1];
            }
        }

        preg_match('/data-sitekey="(.*?)"/isu', $response, $match);

        if (empty($match[1])) {
            throw new Exception('Erro ao pegar a chave do catpcha', 3);
        }

        $gResponse = $this->solveHCaptcha($match[1], $url);

        $urlPost = "https://servicos.receita.fazenda.gov.br/Servicos/CPF/ConsultaSituacao/ConsultaPublicaExibir.asp";

        $response = file_get_contents(
            $urlPost,
            false,
            $this->getSiteHeaders('POST', $cookies, $gResponse)
        );

        if (
            preg_match(
                '#A.valida.*?o.anti-rob.*?.n.*?o.foi.realizada.corretamente#i',
                $response
            )
        ) {
            throw new Exception('Captcha invalido', 3);
        }

        if (preg_match('#Data\s*de\s*nascimento\s*informada.*?est.*?\s*divergente#i', $response)) {
            throw new Exception('DataNascimentoDivergente', 1);
        }

        if (
            preg_match(
                '#Informe\s*a\s*data\s*de\s*nascimento\s*do\s*titular\s*do\s*CPF\s*a\s*ser\s*consultado#i',
                $response
            )
        ) {
            throw new Exception('DataNascimentoDivergente', 1);
        }

        $patterns = [
            'cpf' => ['#CPF:\s*<b>(.*?)</b>#i', null],
            'nome' => ['#Nome:\s*<b>(.*?)</b>#i', null],
            'nascimento' => ['#Data\s*de\s*Nascimento:\s*<b>(.*?)</b>#i', null],
            'situacao' => ['#Situa.*?o\s*Cadastral:\s*<b>(.*?)</b>#i', null],
            'inscricao' => ['#Data\s*da\s*Inscri.*?o:\s*<b>(.*?)</b>#i', null],
            'digito_verificador' => ['#Digito\s*Verificador:\s*<b>(.*?)</b>#i', null],
            'obito' => ['#Ano\s*de\s*.*?bito:\s*<b>(.*?)</b>#i', null],
            'hora' => ['#Comprovante\s*emitido\s*.*s:\s*<b>(.*?)</b>#i', null],
            'data' => ['#do\s*dia\s*<b>(.*?)</b>#i', null],
            'chave' => ['#controle\s*do\s*comprovante:\s*<b>(.*?)<\/b>#i', null],
        ];

        return Util::parseDados($patterns, $response);
    }

    /**
     * Retorna o resultado da spine_pf ou pj do elastic
     *
     * <AUTHOR> Guilherme Sório
     * @param string $document
     * @return array
     */
    private function searchElasticInfo($document)
    {
        return (new ElasticsearchManager())->search($this->getQueryParams($document));
    }

    /**
     * Retorna a query para a busca no elastic search
     *
     * <AUTHOR> Guilherme Sório
     * @param string $document
     * @return array
     */
    private function getQueryParams($document)
    {
        return [
            'index' => 'spine_pf',
            'type' => 'pf',
            'body' => [
                'query' => [
                    'match' => [
                        'cpf' => $document
                    ]
                ]
            ]
        ];
    }
}
