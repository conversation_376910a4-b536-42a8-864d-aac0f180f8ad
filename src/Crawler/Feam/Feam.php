<?php

namespace App\Crawler\Feam;

use Exception;
use App\Helper\Str;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Factory\MongoDB;

class Feam extends Spider
{
    private const INDEX_MONGODB = 'name_document';
    private const LIMIT = 15;

    private $type;
    private $criteria = '';
    private $limit;

    protected function start()
    {
        $results = $this->getParamsByType();

        $this->checkResults($results);

        $result = [];
        foreach ($results as $value) {
            if ($this->type == 'nome' && (floatval($value['score']) < 2)) {
                continue;
            }

            $result[] = $value;
        }

        $this->checkResults($result);

        return $result;
    }

    public function checkResults($results)
    {
        if (empty($results)) {
            throw new Exception('Nada Encontrado', 2);
        }
    }

    private function getParamsByType()
    {
        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('common')
            ->setCollection('feam');

        $query = $this->setQuery($this->criteria);

        $results =  $manager->query(
            'aggregate',
            $query,
            null,
            null,
            true
        )->toArray();

        return $results;
    }

    protected function validateAndSetCrawlerAttributes()
    {
        $this->limit = $this->param['limite'] ?? self::LIMIT;

        if (empty($this->param['cnpj_nome'])) {
            throw new Exception('Parâmetro inválido');
        }

        $this->type = 'nome';

        if (Document::validarCnpj($this->param['cnpj_nome'])) {
            $this->type = 'doc';
        }

        $this->criteria = $this->param['cnpj_nome'];
    }

    private function setQuery(string $criteria)
    {
        $field = 'cnpj';

        if ($this->type == 'nome') {
            $field = 'responsavel';
        }

        $query[] = [
                '$search' => [
                    'index' => self::INDEX_MONGODB,
                    'compound' => [
                        'must' => [
                            [
                                "text" => [
                                    "query" => $criteria,
                                    "path" => $field
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        $query[] = ['$addFields' => ['score' => ['$meta' => 'searchScore']]];
        $query[] = ['$limit' => $this->limit];

        return $query;
    }
}
