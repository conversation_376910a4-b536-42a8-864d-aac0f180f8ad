<?php

namespace App\Crawler\ReceitaFederalPfSite;

use App\Crawler\ReceitaFederalPfGeneric\Models\ReceitaFederalPfGenericModel;
use App\Crawler\GenericInterface;
use App\Crawler\Spider;
use App\Manager\ColunaVertebralManager;
use App\Factory\PostgresDB;
use App\Helper\Document;
use App\Helper\Util;
use App\Helper\Date;
use Exception;

class ReceitaFederalPfSite extends Spider implements GenericInterface
{
    // private $url = 'https://servicos.receita.fazenda.gov.br/servicos/cpf/consultasituacao/ConsultaPublicaSonoro.asp';
    private const URL = 'https://servicos.receita.fazenda.gov.br/Servicos/CPF/'
        . 'ConsultaSituacao/ConsultaPublica.asp';
    private const URL_POST = 'https://servicos.receita.fazenda.gov.br/Servicos/CPF/'
        . 'ConsultaSituacao/ConsultaPublicaExibir.asp';

    protected function validateAndSetCrawlerAttributes()
    {
        $this->param['documento'] = preg_replace('/[^0-9]/isu', '', $this->param['documento']);

        if (!Document::validarCpf($this->param['documento'])) {
            throw new Exception("Critério de busca inválido.", 6);
        }

        if (empty($this->param['data_nascimento'])) {
            $this->param['data_nascimento'] = $this->getBirthdate();
        }

        if (!Date::validateDate($this->param['data_nascimento'])) {
            throw new Exception("Critério data de nascimento no formato errado.", 6);
        }
    }

    protected function start()
    {
        $retry = 3;

        while ($retry >= 0) {
            try {
                $data = $this->getData();

                return $data;
            } catch (Exception $e) {
                if ($e->getCode() == 6) {
                    throw new Exception($e->getMessage(), 6);
                }
                if ($retry == 0) {
                    throw new Exception($e->getMessage(), 3);
                }
            }
            $retry--;
        }

        return $data;
    }

    private function getData()
    {
        $this->setProxy();

        $headers = [
            "User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0",
            "Accept: */*",
            'Content-type: application/x-www-form-urlencoded',
            "Connection: keep-alive",
            "Upgrade-Insecure-Requests: 1",
            "Host: servicos.receita.fazenda.gov.br",
        ];

        $response = $this->getResponse(self::URL_POST, 'GET', [], $headers);

        $params = [
            'idCheckedReCaptcha' => 'false',
            'txtCPF' => Document::formatCpf($this->param['documento']),
            'txtDataNascimento' => $this->param['data_nascimento'],
            'h-captcha-response' => $this->solveHCaptcha('53be2ee7-5efc-494e-a3ba-c9258649c070', self::URL),
            'Enviar' => 'Consultar',
        ];

        array_push(
            $headers,
            "Origin: https://servicos.receita.fazenda.gov.br",
            "Referer: https://servicos.receita.fazenda.gov.br/Servicos/CPF/ConsultaSituacao/ConsultaPublica.asp"
        );

        $response = $this->getResponse(self::URL_POST, 'POST', $params);

        if (preg_match('#CPF\s*n.*?o\s*encontrado\s*na\s*base\s*de\s*dados\s*da\s*Receita\s*Federal#i', $response)) {
            throw new Exception('CPF não encontrado na base de dados da Receita Federal.', 6);
        }

        if (preg_match('#Data\s*de\s*nascimento\s*informada.*?est.*?\s*divergente#i', $response)) {
            throw new Exception('DataNascimentoDivergente', 1);
        }

        if (
            preg_match(
                '#Informe\s*a\s*data\s*de\s*nascimento\s*do\s*titular\s*do\s*CPF\s*a\s*ser\s*consultado#i',
                $response
            )
        ) {
            throw new Exception('DataNascimentoDivergente', 1);
        }

        if (str_contains($response, 'clConteudoRodape')) {
            $patterns = [
                'cpf' => ['#CPF:\s*<b>(.*?)</b>#i', null],
                'nome' => ['#Nome:\s*<b>(.*?)</b>#i', null],
                'nascimento' => ['#Data\s*de\s*Nascimento:\s*<b>(.*?)</b>#i', null],
                'situacao' => ['#Situa.*?o\s*Cadastral:\s*<b>(.*?)</b>#i', null],
                'inscricao' => ['#Data\s*da\s*Inscri.*?o:\s*<b>(.*?)</b>#i', null],
                'digito_verificador' => ['#Digito\s*Verificador:\s*<b>(.*?)</b>#i', null],
                'obito' => ['#Ano\s*de\s*.*?bito:\s*<b>(.*?)</b>#i', null],
                'hora' => ['#Comprovante\s*emitido\s*.*s:\s*<b>(.*?)</b>#i', null],
                'data' => ['#do\s*dia\s*<b>(.*?)</b>#i', null],
                'chave' => ['#controle\s*do\s*comprovante:\s*<b>(.*?)<\/b>#i', null],
            ];

            $data = Util::parseDados($patterns, $response);
            $data['site'] = true;

            if (empty($data['cpf'])) {
                throw new Exception("Erro ao buscar as informações na receita.", 3);
            }

            return $data;
        }

        throw new Exception('Erro ao capturar os dados', 3);
    }

    private function getBirthdate()
    {
        $results = [];

        $doc = $this->param['documento'];
        $db = new PostgresDB();
        $results =  $db->connectCaptura()
            ->select('data_nascimento')
            ->from('receita_federal.pf')
            ->where("cpf = '$doc'")
            ->execute()
            ->fetchAll();
        $db->disconnect();

        try {
            if (empty($results)) {
                // se não encontrar no postgre, busca no spine
                $elasticManager = new ColunaVertebralManager();
                try {
                    $results = $elasticManager->getSpinePf($this->param['documento']);
                } catch (Exception $e) {
                    if ($e->getCode() == 2 || $e->getCode() == 6) {
                        throw new Exception('DataNascimentoDivergente', 1);
                    }
                    throw new Exception($e->getMessage(), $e->getCode());
                }

                if (empty($results)) {
                    throw new Exception('DataNascimentoDivergente', 1);
                }

                if (empty($results['data_nascimento'])) {
                    throw new Exception('DataNascimentoDivergente', 1);
                }
            }
        } catch (Exception $e) {
            if ($e->getMessage() == 'DataNascimentoDivergente') {
                // Informa uma data aleatória para verificar se o cpf existe na receita
                $results[0]['data_nascimento'] = date("Ymd", strtotime(date("Y-m-d") . "-18 year"));
            } else {
                throw new Exception($e->getMessage(), $e->getCode());
            }
        }

        if (!empty($results[0])) {
            $dtNasc = str_replace("-", "", $results[0]['data_nascimento']);
        } else {
            $dtNasc = $results['data_nascimento'];
        }

        return  substr($dtNasc, 6, 2) . '/' .
            substr($dtNasc, 4, 2) . '/' .
            substr($dtNasc, 0, 4);
    }

    public function parseToGeneric(array $data, string $sourceName): ReceitaFederalPfGenericModel
    {
        $model = new ReceitaFederalPfGenericModel();

        $model->source = $sourceName;
        $model->cpf = $data['cpf'];
        $model->nome = $data['nome'];
        $model->nascimento = $data['nascimento'];
        $model->situacao = $data['situacao'];
        $model->inscricao = $data['inscricao'];
        $model->digito_verificador = $data['digito_verificador'];
        $model->obito = $data['obito'];
        $model->hora = $data['hora'];
        $model->data = $data['data'];
        $model->chave = $data['chave'];
        $model->cpf_nome = "{$data['cpf']}|{$data['nome']}";
        $model->nome_dataNascimento = "{$data['nome']}|{$data['nascimento']}";

        return $model;
    }
}
