<?php

namespace App\Crawler\AgencianetSecretariaFazendaDF;

use App\Crawler\AgencianetSecretariaFazendaDF\Models\AgencianetSecretariaFazendaDFModel;
use App\Crawler\Spider;
use App\Helper\Document;
use Exception;

class AgencianetSecretariaFazendaDF extends Spider
{
    private $cnpj;
    private $simpleResponse;
    private $cfdfResponse;
    private const CAPTCHA_URL = 'http://publica.agnet.fazenda.df.gov.br/Captcha/GeradorCaptchaHandler.ashx';
    private const SIMPLES_URL = 'http://publica.agnet.fazenda.df.gov.br/CFDF/ConsultaSimplesNacional';
    private const CFDF_URL = 'http://publica.agnet.fazenda.df.gov.br/CFDF/ImprimirDIF';

    public function start()
    {
        return $this->searchSimples();
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['cnpj'])) {
            throw new Exception('Parêmtro Inválido', 1);
        }

        if (!Document::validarCnpj($this->param['cnpj'])) {
            throw new Exception('CNPJ Inválido', 1);
        }

        $this->cnpj = Document::removeMask($this->param['cnpj']);
    }

    private function searchSimples()
    {
        $this->getImageAndBreakCaptcha(self::CAPTCHA_URL);
        $params = [
            'CNPJ' => $this->cnpj
        ];
        $headers = [
            'Captcha : ' . $this->captcha
        ];

        $this->simpleResponse = json_decode($this->getResponse(self::SIMPLES_URL, 'POST', $params, $headers));
        if (
            isset($this->simpleResponse->contribuinte->CfDf)
            && !empty($this->simpleResponse->contribuinte->CfDf)
        ) {
            $this->searchCfdf();
        }

        return $this->parseData();
    }

    private function searchCfdf()
    {
        $this->getImageAndBreakCaptcha(self::CAPTCHA_URL);
        $params = [
            'CFDF' => $this->cnpj
        ];
        $headers = [
            'Captcha : ' . $this->captcha
        ];

        $this->cfdfResponse = json_decode($this->getResponse(self::CFDF_URL, 'POST', $params, $headers));
    }

    private function parseData()
    {
        $agenciaNetModel = new AgencianetSecretariaFazendaDFModel();
        $agenciaNetModel->cnpj = $this->simpleResponse->contribuinte->CpfCnpj;
        $agenciaNetModel->cpfcnpj = $this->simpleResponse->contribuinte->CpfCnpj;
        $agenciaNetModel->nomeFantasia = $this->simpleResponse->contribuinte->NomeFantasia;
        $agenciaNetModel->razaoSocial = $this->simpleResponse->contribuinte->NomeRazaoSocial;
        $agenciaNetModel->endereco = $this->simpleResponse->contribuinte->Endereco;
        $agenciaNetModel->bairro = $this->simpleResponse->contribuinte->Bairro;
        $agenciaNetModel->cidade = $this->simpleResponse->contribuinte->Cidade;
        $agenciaNetModel->uf = $this->simpleResponse->contribuinte->Uf;
        $agenciaNetModel->cep = $this->simpleResponse->contribuinte->CEP;
        $agenciaNetModel->debito = $this->simpleResponse->simples->Debitos;
        $agenciaNetModel->pendenciasAtividadeEconomica = $this->simpleResponse->simples->AtividadeEconomica;
        $agenciaNetModel->pendenciasCadastrais = $this->simpleResponse->simples->PendenciasCadastrais;
        $agenciaNetModel->situacaoSimples = $this->simpleResponse->simples->Resultado;

        if (!empty($this->cfdfResponse)) {
            $agenciaNetModel->cfdf = $this->cfdfResponse->CfDf;
            $agenciaNetModel->dataInscricao = $this->cfdfResponse->DataConcessao;
            $agenciaNetModel->denominacaoSocial = $this->cfdfResponse->NomeRazaoSocial;
            $agenciaNetModel->naturezaJuridica = $this->cfdfResponse->TipoContribuinte;
            $agenciaNetModel->qualificacaoContribuente = $this->cfdfResponse->Qualificacao;
            $agenciaNetModel->fac = $this->cfdfResponse->NumeroFac;
            $agenciaNetModel->regimeTributacaoISS = $this->cfdfResponse->RegimeISS;
            $agenciaNetModel->faixaISS = $this->cfdfResponse->FaixaISS;
            $agenciaNetModel->dataISS = $this->convertDate($this->cfdfResponse->DataEnquadramentoISS);
            $agenciaNetModel->regimeTributacao = $this->cfdfResponse->RegimeISS;
            $agenciaNetModel->faixaICMS = $this->cfdfResponse->FaixaICMS;
            $agenciaNetModel->dataICMS = $this->convertDate($this->cfdfResponse->DataEnquadramentoICMS);
            $agenciaNetModel->atividadeEconomica = $this->cfdfResponse->DescricaoAtividadeISS;
            $agenciaNetModel->codigoAtividadeISS = $this->cfdfResponse->CodigoAtividadeISS;
            $agenciaNetModel->dataInicioAtividadeISS = $this->convertDate($this->cfdfResponse->DataInicioAtividadeISS);
            $agenciaNetModel->descricaoAtividade = $this->cfdfResponse->DescricaoAtividadeICMS;
            $agenciaNetModel->codigoAtividadeICMS = $this->cfdfResponse->CodigoAtividadeICMS;
            $agenciaNetModel->dataInicioAtividadeICMS =
                $this->convertDate($this->cfdfResponse->DataInicioAtividadeICMS);
            $agenciaNetModel->situacaoCadastral = $this->cfdfResponse->SituacaoCadastral;
            $agenciaNetModel->data = $this->convertDate($this->cfdfResponse->Data);
        }

        return $agenciaNetModel;
    }

    private function convertDate($date)
    {
        return isset($date) ? date('d/m/Y', strtotime($date)) : null;
    }
}
