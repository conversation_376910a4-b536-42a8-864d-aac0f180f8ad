<?php

namespace App\Crawler\AgencianetSecretariaFazendaDF\Models;

class AgencianetSecretariaFazendaDFModel
{
    public $cnpj;
    public $razaoSocial;
    public $debito;
    public $pendenciasCadastrais;
    public $pendenciasAtividadeEconomica;
    public $situacaoSimples;
    public $cfdf;
    public $cpfcnpj;
    public $dataInscricao;
    public $denominacaoSocial;
    public $nomeFantasia;
    public $naturezaJuridica;
    public $qualificacaoContribuente;
    public $fac;
    public $regimeTributacaoISS;
    public $faixaISS;
    public $dataISS;
    public $regimeTributacao;
    public $faixaICMS;
    public $dataICMS;
    public $atividadeEconomica;
    public $codigoAtividadeISS;
    public $dataInicioAtividadeISS;
    public $descricaoAtividade;
    public $codigoAtividadeICMS;
    public $dataInicioAtividadeICMS;
    public $endereco;
    public $bairro;
    public $cidade;
    public $uf;
    public $cep;
    public $situacaoCadastral;
    public $data;

    public function __get($name)
    {
        return $this->$name;
    }

    public function __set($name, $value)
    {
        return $this->$name = $value;
    }
}
