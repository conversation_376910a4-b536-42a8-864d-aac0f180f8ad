<?php

namespace App\Crawler\Crea;

use App\Crawler\Spider;
use App\Helper\Str;
use App\Helper\Document;
use Exception;

class Crea extends Spider
{
    private const BASE_URL = 'https://consultaprofissional.confea.org.br/';

    private $name = '';
    private $cpf = '';

    protected function validateAndSetCrawlerAttributes()
    {
        if (Document::validarCpf($this->param['criterio'])) {
            $this->cpf = Document::removeMask($this->param['criterio']);
            return false;
        }

        $this->name = $this->param['criterio'];
    }

    /**
     * Função para inserir parametros e chamar resposta
     * <AUTHOR>
     */
    protected function start()
    {
        $this->setAlternativeProxy();
        $html = $this->getResponse(self::BASE_URL);
        $html = Str::encoding($html);

        preg_match('/__VIEWSTATE" value="(.*?)"/', $html, $viewstate);
        preg_match('/VIEWSTATEGENERATOR" value="(.*?)\s\/>/m', $html, $generator);
        preg_match('/__EVENTVALIDATION" value="(.*?)"\s\/>/m', $html, $validation);

        $captcha = $this->resolveCaptcha($html);

        $params = array(
            '__VIEWSTATE' => trim($viewstate[1]),
            '__VIEWSTATEGENERATOR' => 'CA0B0334',
            '__EVENTVALIDATION' => trim($validation[1]),
            'ctl00$ContentPlaceHolder1$txtNome' => $this->name,
            'ctl00$ContentPlaceHolder1$txtCPF' => $this->cpf,
            'ctl00$ContentPlaceHolder1$txtRNP' => '',
            'ctl00$ContentPlaceHolder1$btnBuscar' => 'Localizar',
            'g-recaptcha-response' => $captcha
        );

        $response =  html_entity_decode($this->getResponse(self::BASE_URL, 'POST', $params), ENT_NOQUOTES, 'UTF-8');

        $this->checkResponse($response);

        $data = $this->getDados($response);

        return $data;
    }

    /**
     *Função que captura os dados do site (Crawler)
     *<AUTHOR> Alves
     *@param response
     *
     */

    private function getDados($response)
    {
        preg_match_all('/onclick="javascript\:open\(\'(.*?)\',/m', $response, $matche);

        foreach ($matche[1] as $key => $value) {
            $url = self::BASE_URL . $value;
            $html = $this->getResponse($url);
            $html = Str::encoding($html);
            //regex que captura os dados do engenheiro
            preg_match('/lblNome">(.*?)<\/span>/m', $html, $nome);
            preg_match('/lblRNP">(.*?)<\/span>/m', $html, $rnp);
            preg_match('/lblDataRegistro">(.*?)<\/span>/m', $html, $data);
            preg_match('/lblCrea">(.*?)<\/span>/m', $html, $crea);
            preg_match('/lblSituacao">(.*?)<\/span>/m', $html, $situacao);

            preg_match('/dlPos_lblNoRecord2">(.*?)<\/span>/m', $html, $pos);
            $pos = $pos[1];
            if (is_null($pos[1])) {
                if (preg_match('/id="dlPos"><span>(.*?)<\/span>.*?<span>(.*?)<\/span>/m', $html)) {
                    preg_match('/id="dlPos"><span>(.*?)<\/span>.*?<span>(.*?)<\/span>/m', $html, $posGraduacao);
                    if ($posGraduacao[2] === " ") {
                        $pos = trim($posGraduacao[1]);
                    } else {
                        $pos = trim($posGraduacao[1]) . ', ' . trim($posGraduacao[2]);
                    }
                }
            }

            preg_match(
                '/dlVistos">.*?>\s(<s.*?id="dlVistos_lblNoRecord3">)?' .
                    '(.*?)\s<.*?>(<br.*?n>)?(.*?)<.*?>(<br.*?n>)?(.*?)</m',
                $html,
                $vistos
            );

            //Pega mais de um visto, caso  exista.
            $vistos[2] = preg_replace('/<\/span>/m', '', $vistos[2]);
            if ($vistos[4] != " " && $vistos[6] == '' || $vistos[6] == " ") {
                $visto = $vistos[2] . ',' . $vistos[4];
            } elseif ($vistos[6] != "") {
                $visto = $vistos[2] . ', ' . trim($vistos[4]) . ',' . $vistos[6];
            } else {
                $visto = $vistos[2];
            }
            if (preg_match('/Nenhum visto encontrado./m', $visto)) {
                $visto = 'Nenhum visto encontrado.';
            }

            preg_match('/lTitulos"><span>\s(.*?)\s<\/span>(<b.*?n>)?(.*?)<\/span>/m', $html, $titulos);
            if ($titulos[3] != " ") {
                $titulo = $titulos[1] . ',' . $titulos[3];
            } else {
                $titulo = $titulos[1];
            }

            $engenheiro[] = [
                'nome' => $nome[1],
                'rnp' => $rnp[1],
                'data' => $data[1],
                'crea' => $crea[1],
                'situacao' => $situacao[1],
                'vistos' => trim($visto),
                'titulo' => trim($titulo),
                'pos' => $pos,
            ];
        }
        return $engenheiro;
    }

    private function resolveCaptcha($html)
    {
        preg_match('/data-sitekey="(.*?)"><\/div>/m', $html, $match);
        if (!empty($match[1])) {
            return $this->solveReCaptcha($match[1], self::BASE_URL, 5, 1);
        }

        throw new Exception("Erro ao localizar dados do captcha na página.", 1);
    }

    private function checkResponse($html)
    {
        if (preg_match('/Nenhum profissional do sistema Confea/m', $html)) {
            throw new Exception('Nenhum registro encontrado', 2);
        }

        if (preg_match('/Captcha incorreto./m', $html)) {
            throw new Exception('Erro ao quebrar o captcha', 3);
        }
    }
}
