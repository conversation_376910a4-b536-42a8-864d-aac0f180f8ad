<?php

namespace App\Crawler\CertidaoCeatTrtES;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

class CertidaoCeatTrtES extends Spider
{
    private const MAIN_URL = "https://pje.trt17.jus.br";
    private const BASE_URL = "/certidoes/trabalhista/emissao";
    private const API_URL = "/pje-certidoes-api/api/certidoes/trabalhistas";
    private const CAPTCHA_URL = "/pje-certidoes-api/api/propriedades";
    private const PATH_STATIC_S3 = S3_STATIC_URL;
    private const PATH_CERTIDAO_S3 = 'captura/certidao_ceat_trt17_es/';

    private $documento;
    private $nomeRazaoSocial;
    private $criterio;

    public function start()
    {
        try {
            $response = $this->makeRequest();
            $data = $this->savePdfAndGetText($response);
            $result = $this->parseText($data['textPdf']);

            return [
                'pdf' => $data['urlPdf'],
                'info' => $result
            ];
        } catch (Exception $e) {
            throw new Exception('Ocorreu um erro ao capturar dados da página. Tente reprocessar!', 3);
        }
    }

    /** @return mixed|void
     * <AUTHOR> Santos - 17 ago. 22
     * @throws \JsonException
     * @throws \Exception
     */
    private function makeRequest()
    {
        $retry = 3;
        do {
            $params = json_encode([
                                      "criterioDeEmissao" => $this->criterio,
                                      "nome" => $this->nomeRazaoSocial,
                                      "numeroDoDocumento" => $this->documento,
                                      "respostaDoCaptcha" => $this->resolveRecaptcha()
                                  ], JSON_THROW_ON_ERROR);

            $result = json_decode(
                $this->getResponse(self::MAIN_URL . self::API_URL . '/emissao', 'POST', $params),
                true,
                512,
                JSON_THROW_ON_ERROR
            );

            if ($result['codigo']) {
                return $this->getResponse(self::MAIN_URL . self::API_URL . "/{$result['codigo']}");
            }
            $retry--;
        } while ($retry > 0);
        throw new Exception('Não foi possível capturar as informações da página.', 6);
    }

    /**
     * @return array
     *
     * @param $data
     *
     * @throws \Exception
     */
    private function savePdfAndGetText(string $data)
    {
        $uniqid = md5(uniqid(rand(), true));
        $certificateName = "{$uniqid}.pdf";
        $certificateLocalPath = "/tmp/{$certificateName}";
        $certificateS3Path = self::PATH_CERTIDAO_S3 . $certificateName;
        $certificateUrl = self::PATH_STATIC_S3 . $certificateS3Path;

        $response = json_decode($data, true);

        (new Pdf())->saveHtmlToPdf(utf8_decode($response['conteudoHTML']), $certificateLocalPath);

        (new S3(new StaticUplexisBucket()))->save($certificateS3Path, $certificateLocalPath);

        $text = (new Pdf())->getTextFromPdf($certificateLocalPath, ['layout', 'nopgbrk']);

        return ["urlPdf" => $certificateUrl, "textPdf" => $text];
    }

    /**
     * @return array
     *
     * @param  string  $text
     *
     * @throws \Exception
     */
    private function parseText(string $text)
    {
        $patterns = [
            'codVerificacao' => ['@Código\sde\sverificação:(.*)@'],
            'emissao' => ['@emitida\sem\s(.*?).s@u'],
            'descricao' => ['@(Certifica-se[\s\S]*?)\s+Certifica-se@'],
            'observacoes' => ['@Observações:([\s\S]*?)\s+Certidão@']
        ];
        $data = Util::parseDados($patterns, $text);
        return array_map("utf8_decode", $data);
    }


    private function resolveRecaptcha()
    {
        $token = $this->getResponse(self::MAIN_URL . self::CAPTCHA_URL);
        preg_match('/"chaveDeSiteDoCaptcha":"([\s\S]*?)"/', $token, $keyCapctha);
        $retry = 3;
        do {
            if (!empty($keyCapctha[1])) {
                return $this->solveReCaptcha($keyCapctha[1], self::MAIN_URL . self::BASE_URL);
            }
            $retry--;
        } while ($retry > 0);
        throw new Exception("Erro ao localizar dados do captcha na página.", 3);
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->documento = trim($this->param['documento']);

        if (empty($this->documento)) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        if (Document::validarCpf($this->documento)) {
            $this->documento = Document::formatCpf($this->documento);
            $this->criterio = "CPF";
        } elseif (Document::validarCnpj($this->documento)) {
            $this->documento = Document::formatCnpj($this->documento);
            $this->documento = substr($this->documento, 0, 10);
            $this->criterio = "RAIZ_DE_CNPJ";
        } else {
            $this->nomeRazaoSocial = trim($this->documento);
            $this->criterio = "NOME";
        }
    }
}
