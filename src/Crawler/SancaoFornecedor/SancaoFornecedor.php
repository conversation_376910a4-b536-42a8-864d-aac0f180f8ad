<?php

namespace App\Crawler\SancaoFornecedor;

use App\Crawler\SancaoFornecedor\SancaoFornecedorResponse;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Util;
use Exception;

class SancaoFornecedor extends Spider
{
    private const URL_FORM = "https://www.bec.sp.gov.br/Sancoes_ui/aspx/ConsultaAdministrativaFornecedor.aspx";
    private const PARAM    = "documento";
    private const TAB_RESTRITIVAS = 0;
    private const TAB_MULTAS = 1;
    private const TAB_ADVERTENCIAS = 2;

    private $arrayTabs = array('Sanções Restritivas', 'Multas', 'Advertências');
    private $arrayTabsIds = array('sancoes_restritivas', 'multas', 'advertencias');
    private $documento = '';
    private $countLimit = 0;
    private $currentPage = 1;

    /**
     *  Iniciar a execução da fonte
     *
     *  <AUTHOR> Mesquita 10/04/2019
     *
     *  @version 1.0.0     *
     *
     *  @return Array
     *
     */
    protected function start()
    {
        $this->setProxy();

        return $this->getSanctionResponse();
    }

    /**
     *  Pega o resultado de uma aba de cada vez
     *
     *  <AUTHOR> Mesquita 10/04/2019
     *
     *  @version 1.0.0
     *
     *
     *  @return Array
     *
     */
    private function getSanctionResponse()
    {
        $responseResult = array();

        for ($i = 0; $i < count($this->arrayTabs); $i++) {
            //Resetar página e limit quando for outra aba
            $this->currentPage = 1;
            $this->countLimit = 0;
            $response = $this->getSanctions($i);

            if (is_array($response) && count($response) > 0) {
                $responseResult[$this->arrayTabsIds[$i]] = $response;
            }
        }

        if (!is_array($responseResult) || count($responseResult) == 0) {
            throw new Exception('Nada Encontrado', 2);
        }

        return $responseResult;
    }

    /**
     *  Pega a resposta de dados seguindo:
     *  - Pegar o html da busca
     *  - Selecionar o primeiro nome
     *  - Seleciona a tab correta
     *  - caso tenha mais de uma página e precisar, pagina e pega o html da nova página
     *  - Pega o detalhe das sanções
     *
     *  <AUTHOR> Mesquita 10/04/2019
     *
     *  @version 1.0.0
     *
     *  @param String $html
     *  @param Int $type
     *
     *  @return Array
     *
     */
    private function getSanctions($type)
    {
        $htmlList = $this->executeSearch();
        $htmlSelectedName = $this->executeSelectListNames($htmlList);
        $htmlSelectedTab = $this->getHtmlSanctionList($htmlSelectedName, $type);

        if ($this->currentPage > 1) {
            $htmlSelectedTab = $this->getHtmlNextPage($htmlSelectedTab, $type);
        }

        $response = $this->getSanctionsData($htmlSelectedTab, $type);

        if (!is_array($response) && $response != null) {
            throw new Exception('Erro ao obter dados de advertências', 1);
        }

        return $response;
    }

    /**
     *  Verifica o conteúdo da lista:
     *  - Saber se tem conteúdo na aba
     *  - Retirar o paginador
     *  - Verificar se tem mais de uma página
     *  - Pegar os dados ou paginar e pegar os dados de detalhe
     *
     *  <AUTHOR> Mesquita 10/04/2019
     *
     *  @version 1.0.0
     *
     *  @param String $html
     *  @param Int $type
     *
     *  @return Array
     *
     */
    private function getSanctionsData($html, $type)
    {
        $arrResult = $this->getArrayResults($html, $type);

        //Retirar o header
        unset($arrResult[0]);

        if (is_array($arrResult) && count($arrResult) == 1) {
            if (preg_match('@<b>Nenhum\sregistro\sencontrado</b>@is', $arrResult[1])) {
                return [];
            }
        }

        $countSanctions = count($arrResult);
        $lastSanction  = $arrResult[$countSanctions];
        $isNextPage = false;
        $countPages = 0;

        //se a última linha for o paginador deletar
        if (preg_match('/Page..\S+.\)/i', $lastSanction)) {
            $countPages = $this->getCountPages($arrResult[$countSanctions]);
            unset($arrResult[$countSanctions]);
        }

        $responseData = $this->getSanctionData($arrResult, $html, $type);

        //Caso ainda haja limite e tenha o que paginar, pagina e pega os dados da prósima
        //página até alcançar o limite ou acabar as páginas
        if (
            $countPages > $this->currentPage
            && $this->countLimit < $this->param['limitador']
        ) {
            $this->currentPage++;
            $responseDataPages = $this->getSanctions($type);
            $responseData = array_merge($responseData, $responseDataPages);
        }

        return $responseData;
    }

    /**
     *  Abre o detalhe de cada sanção e pega os dados
     *
     *  <AUTHOR> Mesquita 10/04/2019
     *
     *  @version 1.0.0
     *
     *  @param String $arrTab
     *  @param String $html
     *  @param Int $type
     *
     *  @return Array
     *
     */
    private function getSanctionData($arrTab, $html, $type)
    {
        $response = array();
        $htmlResponse = "";

        for ($i = 0; $i < count($arrTab); $i++) {
            $this->countLimit++;

            $htmlDetail = $this->getHtmlDetail($html, $i, $type);
            $result = SancaoFornecedorResponse::getResults($htmlDetail, $type);

            $result['tipo_sancao'] = $this->arrayTabs[$type];
            $response[] = $result;

            if ($this->countLimit == $this->param['limitador']) {
                break;
            }
        }

        return $response;
    }

    /**
     *  Pega o número de páginas que aparece no paginador
     *
     *  <AUTHOR> Mesquita 10/04/2019
     *
     *  @version 1.0.0
     *
     *  @param String $text
     *
     *  @return Int
     *
     */
    private function getCountPages($text)
    {
        $xPath = '//table/tr/td';
        $countPages = count(Util::queryXPath($text, $xPath));

        //Deixar 10 páginas
        if ($countPages > 10) {
            $countPages = 10;
        }

        return $countPages;
    }

    /**
     *  Pegar o html da tab correta
     *
     *  <AUTHOR> Mesquita 10/04/2019
     *
     *  @version 1.0.0
     *
     *  @param String $html
     *  @param Int $typeIndex
     *
     *  @return String
     *
     */
    private function getHtmlSanctionList($html, $typeIndex)
    {
        $params = $this->getPostParams($html, $this->documento);
        $params['ctl00$ContentPlaceHolder1$ToolkitScriptManager1'] =
            'ctl00$ContentPlaceHolder1$ToolkitScriptManager1|ctl00$ContentPlaceHolder1$TabsSancaos';
        $params['__EVENTTARGET'] = 'ctl00$ContentPlaceHolder1$TabsSancaos';
        $params['__EVENTARGUMENT'] = 'activeTabChanged:' . $typeIndex;

        return $this->getResponse(
            self::URL_FORM,
            'POST',
            $params
        );
    }

    /**
     *  Pega todas as linhas da tabela onde está a lista de sanções
     *
     *  <AUTHOR> Mesquita 10/04/2019
     *
     *  @version 1.0.0
     *
     *  @param String $html
     *  @param Int $type
     *
     *  @return String
     *
     */
    private function getArrayResults($html, $type)
    {
        $tableId = '';

        if ($type == self::TAB_RESTRITIVAS) {
            $tableId = 'TabSancaoIncluidas_gdvSancaosIncluidas';
        }

        if ($type == self::TAB_MULTAS) {
            $tableId = 'TabMultas_gdvSancaosMultas';
        }

        if ($type == self::TAB_ADVERTENCIAS) {
            $tableId = 'TabAdvertencias_gdvSancaosAdvertencias';
        }

        $xPath = '//*[@id="ctl00_ContentPlaceHolder1_TabsSancaos_' . $tableId . '"]/tr';
        return Util::queryXPath($html, $xPath);
    }

    /**
     *  Pega o html da próxima página caso haja paginação
     *
     *  <AUTHOR> Mesquita 10/04/2019
     *
     *  @version 1.0.0
     *
     *  @param String $html
     *  @param Int $type
     *
     *  @return String
     *
     */
    private function getHtmlNextPage($html, $type)
    {
        $postParams = $this->getPostParams($html, $this->param[self::PARAM]);

        if ($type == self::TAB_RESTRITIVAS) {
            $postParams['ctl00$ContentPlaceHolder1$ToolkitScriptManager1'] =
                'ctl00$ContentPlaceHolder1$ToolkitScriptManager1|ctl00$ContentPlaceHolder1$' .
                'TabsSancaos$TabSancaoIncluidas$gdvSancaosIncluidas';
            $postParams['__EVENTTARGET'] =
                'ctl00$ContentPlaceHolder1$TabsSancaos$TabSancaoIncluidas$gdvSancaosIncluidas';
        }

        if ($type == self::TAB_MULTAS) {
            $postParams['ctl00$ContentPlaceHolder1$ToolkitScriptManager1'] =
                'ctl00$ContentPlaceHolder1$ToolkitScriptManager1|ctl00$ContentPlaceHolder1$' .
                'TabsSancaos$TabMultas$gdvSancaosMultas';
            $postParams['__EVENTTARGET'] = 'ctl00$ContentPlaceHolder1$TabsSancaos$TabMultas$gdvSancaosMultas';
        }

        if ($type == self::TAB_ADVERTENCIAS) {
            $postParams['ctl00$ContentPlaceHolder1$ToolkitScriptManager1'] =
                'ctl00$ContentPlaceHolder1$ToolkitScriptManager1|ctl00$ContentPlaceHolder1$' .
                'TabsSancaos$TabAdvertencias$gdvSancaosAdvertencias';
            $postParams['__EVENTTARGET'] =
                'ctl00$ContentPlaceHolder1$TabsSancaos$TabAdvertencias$gdvSancaosAdvertencias';
        }

        $postParams['__EVENTARGUMENT'] = 'Page$' . $this->currentPage;

        return $this->getResponse(
            self::URL_FORM,
            'POST',
            $postParams
        );
    }

    /**
     *  Pega o html do detalhe da sanção
     *
     *  <AUTHOR> Mesquita 10/04/2019
     *
     *  @version 1.0.0
     *
     *  @param String $html
     *  @param Int $index
     *  @param Int $type
     *
     *  @return String
     *
     */
    private function getHtmlDetail($html, $index, $type)
    {
        $postParams = $this->getPostParams($html);

        if ($type == self::TAB_RESTRITIVAS) {
            $postParams['ctl00$ContentPlaceHolder1$ToolkitScriptManager1'] =
                'ctl00$ContentPlaceHolder1$ToolkitScriptManager1|ctl00$ContentPlaceHolder1$' .
                'TabsSancaos$TabSancaoIncluidas$gdvSancaosIncluidas';
            $postParams['__EVENTTARGET'] =
                'ctl00$ContentPlaceHolder1$TabsSancaos$TabSancaoIncluidas$gdvSancaosIncluidas';
            $postParams['__EVENTARGUMENT'] = 'Visualizar$' . $index;
        }

        if ($type == self::TAB_MULTAS) {
            $postParams['ctl00$ContentPlaceHolder1$ToolkitScriptManager1'] =
                'ctl00$ContentPlaceHolder1$ToolkitScriptManager1|ctl00$ContentPlaceHolder1$' .
                'TabsSancaos$TabMultas$gdvSancaosMultas';
            $postParams['__EVENTTARGET'] = 'ctl00$ContentPlaceHolder1$TabsSancaos$TabMultas$gdvSancaosMultas';
            $postParams['__EVENTARGUMENT'] = 'Visualizar$' . $index;
        }

        if ($type == self::TAB_ADVERTENCIAS) {
            $postParams['ctl00$ContentPlaceHolder1$ToolkitScriptManager1'] =
                'ctl00$ContentPlaceHolder1$ToolkitScriptManager1|ctl00$ContentPlaceHolder1$' .
                'TabsSancaos$TabAdvertencias$gdvSancaosAdvertencias';
            $postParams['__EVENTTARGET'] =
                'ctl00$ContentPlaceHolder1$TabsSancaos$TabAdvertencias$gdvSancaosAdvertencias';
            $postParams['__EVENTARGUMENT'] = 'Visualizar$' . $index;
        }

        return $this->getResponse(
            self::URL_FORM,
            'POST',
            $postParams
        );
    }

    /**
     *  Pega o html da página de sanções após escolher um nome da lista
     *
     *  <AUTHOR> Mesquita 10/04/2019
     *
     *  @version 1.0.0
     *
     *  @param String $html
     *
     *  @return String
     *
     */
    private function executeSelectListNames($html)
    {
        $postParams = $this->getPostParams($html, $this->param[self::PARAM]);

        $postParams['ctl00$ContentPlaceHolder1$ToolkitScriptManager1'] =
            'ctl00$ContentPlaceHolder1$ToolkitScriptManager1|ctl00$ContentPlaceHolder1$gdv' .
            'ConsultaAdm$ctl02$linkSelecionar';
        $postParams['__EVENTTARGET'] = 'ctl00$ContentPlaceHolder1$gdvConsultaAdm$ctl02$linkSelecionar';

        return $this->getResponse(
            self::URL_FORM,
            'POST',
            $postParams
        );
    }

    /**
     *  Pega o html da primeira página enviado o documento via queryString
     *
     *  <AUTHOR> Mesquita 10/04/2019
     *
     *  @version 1.0.0
     *
     *  @return String
     *
     */
    private function executeSearch()
    {
        return $this->getResponse(
            self::URL_FORM . '?CNPJ=' . $this->documento,
            'GEt'
        );
    }

    /**
     *  Valida parâmetros da fonte
     *
     *  <AUTHOR> Mesquita 10/04/2019
     *
     *  @version 1.0.0
     *
     *  @return String
     *
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (
            !isset($this->param[self::PARAM])
            || empty($this->param[self::PARAM])
            || !Document::validarCpfOuCnpj($this->param[self::PARAM])
        ) {
            throw new Exception("Critério não informado ou inválido.", 1);
        } else {
            $doc = preg_replace('/\D/isu', '', $this->param[self::PARAM]);
        }

        $this->documento = $doc;
    }

    /**
     *  Monta os parametros principais enviados em cada POST
     *
     *  <AUTHOR> Mesquita 10/04/2019
     *
     *  @version 1.0.0
     *
     *  @param String $html
     *
     *  @return Array
     *
     */
    private function getPostParams($html)
    {
        return [
            'ctl00$ContentPlaceHolder1$txtRazaoSocial' => '',
            'ctl00$ContentPlaceHolder1$txtCNPJCPF' => $this->param[self::PARAM],
            'ctl00$ContentPlaceHolder1$ddlOrdenarPor' => -1,
            '__VIEWSTATE' => $this->getViewState($html),
            '__VIEWSTATEGENERATOR' => $this->getViewStateGenerator($html),
            '__EVENTARGUMENT' => '',
            '__VIEWSTATEENCRYPTED' => '',
            '__SCROLLPOSITIONX' => 0,
            '__SCROLLPOSITIONY' => 0,
            '__ASYNCPOST' => true
        ];
    }

    /**
     *  Pegar ViewState no html
     *
     *  <AUTHOR> Mesquita 10/04/2019
     *
     *  @version 1.0.0
     *
     *  @param String $html
     *
     *  @return String
     *
     */
    private function getViewState($html)
    {
        preg_match('/<input.*id=.__VIEWSTATE.\s*.value=.(.*?).\s\/>/i', $html, $output);

        if (count($output) == 0 || !isset($output[1]) || empty($output[1])) {
            throw new Exception('Falha ao obter o parâmetro __VIEWSTATE do formuláro', 1);
        }
        return $output[1];
    }

    /**
     *  Pegar ViewStateGenerator no html
     *
     *  <AUTHOR> Mesquita 10/04/2019
     *
     *  @version 1.0.0
     *
     *  @param String $html
     *
     *  @return String
     *
     */
    private function getViewStateGenerator($html)
    {
        preg_match('/<input.*id=.__VIEWSTATEGENERATOR.\s*.value=.(.*?).\s\/>/i', $html, $output);

        if (count($output) == 0 || !isset($output[1]) || empty($output[1])) {
            throw new Exception('Falha ao obter o parâmetro __VIEWSTATEGENERATOR do formuláro', 1);
        }
        return $output[1];
    }
}
