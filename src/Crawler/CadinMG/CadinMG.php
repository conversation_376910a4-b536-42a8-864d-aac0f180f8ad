<?php

namespace App\Crawler\CadinMG;

use App\Crawler\Spider;
use App\Helper\Document;
use Exception;

class CadinMG extends Spider
{
    private const BASE_URL = 'http://consultapublica.fazenda.mg.gov.br/';
    private const REQUEST_URL = 'ConsultaPublicaCADIN/consultaSituacaoPublica.do';

    private string $cpf = '';
    private string $cnpj = '';

    public function start()
    {
        $result = $this->makeRequest();

        return $this->parseResult($result);
    }

    private function makeRequest(): string
    {
        $params = [
            "tipoDocumentoFiltro" => '',
            "numeroDocumentoFiltro" => '',
            "nomeInadimplente" => '',
            "mensagem" => '',
            "listaPTA" => '',
            "cpfPesqTB" => Document::removeMask($this->cpf),
            "cnpjPesqTB" => Document::removeMask($this->cnpj),
            "captchaValue" => $this->solveCaptcha()
        ];

        return $this->getResponse(self::BASE_URL . self::REQUEST_URL, 'POST', $params);
    }

    private function parseResult(string $html): array
    {

        $regexNome = '/<span\sclass="txtcpn"\s><b>Nome(.*).\s<\/b>(.*?)<\/span/';
        $regexDescricao = '/<span\sclass="texto">(.*?)<\/span>/';
        $regexProcessos = '/<span\sclass="txtcpn"\s><b>PTA(.*).\s<\/b>(.*?)<\/span/';

        preg_match($regexNome, $html, $nome);
        preg_match($regexDescricao, $html, $descricao);
        preg_match($regexProcessos, $html, $processos);

        $processosRelacionados = explode(', ', $processos[2]);

        $documento = empty($this->cpf) ? Document::formatCnpj($this->cnpj) : Document::formatCpf($this->cpf);
        return [
            'documento' => $documento,
            'nome' => $nome[2] ?? '-',
            'descricao' => utf8_encode(strip_tags($descricao[1])),
            'processos_relacionados' => empty($processos[2]) ? [] : $processosRelacionados,
        ];
    }


    private function solveCaptcha()
    {
        $captchaUrl = self::BASE_URL . '/ConsultaPublicaCADIN/captcha.jpg';
        $this->getImageAndBreakCaptcha($captchaUrl);
        return $this->captcha;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $documento = trim($this->param['cpf_cnpj']);

        if (empty($documento) || !Document::validarCpfOuCnpj($documento)) {
            throw new Exception('Parâmetro Inválido', 6);
        }
        if (Document::validarCnpj($documento)) {
            $this->cnpj = $documento;
        } else {
            $this->cpf = $documento;
        }
    }
}
