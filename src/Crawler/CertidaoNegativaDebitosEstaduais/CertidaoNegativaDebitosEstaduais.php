<?php

namespace App\Crawler\CertidaoNegativaDebitosEstaduais;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Manager\InfoSimplesManager;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

class CertidaoNegativaDebitosEstaduais extends Spider
{
    private const PATH_STATIC_S3 = S3_STATIC_URL;
    private const PATH_CERTIDAO_S3 = 'captura/certidao_negativa_debitos_estaduais/';

    private $cpfCnpj;
    private $cep;
    private $cpfSolicitante;
    private $uf;

    public function start()
    {
        $this->uniqid = md5(uniqid(rand(), true));
        $this->certificateName = "{$this->uniqid}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::PATH_CERTIDAO_S3 . $this->certificateName;
        $this->certificateUrl = self::PATH_STATIC_S3 . $this->certificateS3Path;

        $manager = new InfoSimplesManager();
        $data = $manager->searchCertidaoNegativaDebitos($this->cpfCnpj, $this->uf, $this->cep, $this->cpfSolicitante);
        $file = $data['receipt']['sites_urls'][0];

        $this->saveFile($file);

        if (!$data['data']['conseguiu_emitir_certidao_negativa']) {
            return $this->parseFailedIssue($data);
        }

        return $this->parseResult($data);
    }

    /** Salva a certidão e retorna os dados da API
     * @param $data
     * @return array
     * @throws Exception
     */
    private function parseResult($data)
    {
        return [
            'certidao_negativa' => true,
            'certidao_codigo' => $data['data']['certidao_codigo'],
            'emissao_data' => $data['data']['emissao_data'],
            'mensagem' => $data['data']['mensagem'],
            'pdf' => $this->certificateUrl
        ];
    }

    private function parseFailedIssue($data)
    {
        return [
            'certidao_negativa' => false,
            'mensagem' => $data['data']['mensagem'],
            'pdf' => $this->certificateUrl
        ];
    }

    /** Função que salva o arquivo pdf no S3
     * @param $file
     * @throws Exception
     */
    private function saveFile($file)
    {
        file_put_contents($this->certificateLocalPath, $this->getResponse($file));
        $type = pathinfo($file, PATHINFO_EXTENSION);
        // Regra para salvar o pdf da certidão do MG, pois o html tem erros de sintaxe
        if ($this->uf == 'MG') {
            $tmpDir = "/tmp/{$this->uniqid}.html"; //cria um novo arquivo html
            //remove as tags que causam erro no arquivo pdf
            $newFile = preg_replace('/<[\s\S]\/>/m', '', $this->getResponse($file));
            file_put_contents($tmpDir, $newFile); //salva o documento em pdf
            (new Pdf())->saveHtmlToPdf($tmpDir, $this->certificateLocalPath);
        } elseif ($type == 'html') {
            (new Pdf())->saveHtmlToPdf($this->getResponse($file), $this->certificateLocalPath);
        }

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
    }


    /** Método para validação de paramêtros obrigatórios para MG e SC
     * @param $uf
     * @throws Exception
     */
    private function validateAditionalParams($uf)
    {
        if ($uf == 'SC') {
            $this->cep = '';
            if (!Document::validarCpf($this->cpfSolicitante)) {
                throw new Exception('Parâmetro CPF obrigatório inválido', 6);
            }
        }
        if ($uf == 'MG') {
            $this->cpfSolicitante = '';
            if (!preg_match('/^[0-9]{5,5}([- ]?[0-9]{3,3})?$/', $this->cep)) {
                throw new Exception('Parâmetro CEP obrigatório inválido', 6);
            }
        }
    }


    public function validateAndSetCrawlerAttributes()
    {
        $this->cpfCnpj = trim($this->param['cpf_cnpj']);
        $this->ie = trim($this->param['ie']);
        $this->cep = trim($this->param['cep']);
        $this->cpfSolicitante = trim($this->param['cpf_solicitante']);
        $this->uf = trim($this->param['uf']);

        $this->cpfCnpj = preg_replace('/[^0-9]/isu', '', $this->param['cpf_cnpj']);

        if (empty($this->uf)) {
            throw new Exception('Parâmetro ou critério inválido', 6);
        }

        if (!Document::validarCpfOuCnpj($this->cpfCnpj)) {
            throw new Exception('Documento Inválido', 6);
        }

        if ($this->uf == 'MG' || ($this->uf == 'SC')) {
            $this->validateAditionalParams($this->uf);
        }
    }
}
