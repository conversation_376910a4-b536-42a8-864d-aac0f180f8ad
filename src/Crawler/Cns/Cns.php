<?php

namespace App\Crawler\Cns;

use App\Crawler\Spider;
use App\Helper\Util;
use App\Helper\Document;
use Exception;

class Cns extends Spider
{
    private $urlBase = 'https://portaldocidadao.saude.gov.br/portalcidadao/validaNumeroCNS.htm';

    private $urlCaptcha = 'https://www.google.com/recaptcha/api/challenge?k=6LfvJP0SAAAAAPIazk0P9y8SZN7KCJttSH5y-_x9';
    private $urlImg = 'https://www.google.com/recaptcha/api/image?c=';
    private $urlFormPost = 'https://portaldocidadao.saude.gov.br/portalcidadao/ValidarCNS.htm';

    private $dados = [];

    private $mapMonth = [
        'null',
        'Janeiro',
        'Fevereiro',
        'Março',
        'Abril',
        'Mai<PERSON>',
        '<PERSON><PERSON>',
        '<PERSON><PERSON>',
        'Agos<PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        'Dezemb<PERSON>'
    ];

    protected function validateAndSetCrawlerAttributes()
    {
        if (!isset($this->param['cpf']) or empty($this->param['cpf'])) {
            throw new Exception('Parâmetro inválido');
        }

        if (!isset($this->param['data_nascimento']) or empty($this->param['data_nascimento'])) {
            throw new Exception('Parâmetro data Nascimento inválido');
        }

        if (!Document::validarCpf($this->param['cpf'])) {
            throw new Exception('Parâmetro inválido');
        }

        $this->param['cpf'] = Document::formatCpf($this->param['cpf']);
    }

    protected function start()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $header = array(
            'User-Agent:Chrome/54.0.2840.59 Safari/537.36'
        );

        $walk = [$this->urlBase, $this->urlCaptcha];

        foreach ($walk as $key => $value) {
            $this->getDadosForm($this->getResponse($value, 'GET', null, $header));
        }

        $this->getImageAndBreakCaptcha($this->urlImg . $this->dados['challenge']);
        $this->loadDadosPost();

        return $this->finalParse($this->getResponse($this->urlFormPost, "POST", $this->dados, $header));
    }

    private function loadDadosPost()
    {
        $dados['numeroCartao'] = $this->param['cpf'];
        $dados['tipoBusca'] = 'CPF';
        $dados['numeroDefinitvo'] = '';
        $dados['municipioNascimentoCodigo'] = '355030';
        $dados['securityAnswer'] = $this->getSecurityAnswer($this->param['data_nascimento']);
        $dados['captcha_answer'] = $this->captcha;
        $dados['challenge'] = $this->urlImg . $this->dados['challenge'];

        $this->dados = $dados;
    }

    private function finalParse($html)
    {
        $patterns = [
            'cns' => array("#Sa.{1,8}de:\\s*(.*?)<\\/h3>#is", null),
            'nome' => array('#Nome:\s*(.*?)<#is', null),
            'data_nascimento' => array('#Nascimento:(.*?)<#is', null),
            'sexo' => array('#Sexo:(.*?)<#is', null),
        ];

        return Util::parseDados($patterns, $html);
    }

    private function getSecurityAnswer($dataNascimento)
    {
        list($dia, $mes, $ano) = explode("/", $dataNascimento);

        $string = $this->dados['securityAnswerLabel'];

        switch ($string) {
            case (preg_match('#ano#is', $string) ? true : false):
                return $ano;
                break;
            case (preg_match('#dia#is', $string) ? true : false):
                return $dia;
                break;
            case (preg_match('#m.{1,8}s#is', $string) ? true : false):
                return $this->mapMonth[intval($mes)];
                break;
        }
    }

    private function getDadosForm($html)
    {
        $patterns = array(
            'challenge' => array("#challenge\\s*:\\s*'(.*?)'#is", null),
            'securityAnswerLabel' => array('#<label[^>]*?securityAnswer[^>]*?>(.*?)<\/label>#is', null),
        );

        $dados = Util::parseDados($patterns, $html);

        foreach ($dados as $key => $value) {
            if (!empty($value)) {
                $this->dados[$key] = $value;
            }
        }
    }
}
