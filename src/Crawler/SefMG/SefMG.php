<?php

namespace App\Crawler\SefMG;

use Exception;
use App\Helper\Pdf;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\S3;
use App\Manager\S3\Bucket\StaticUplexisBucket;

class SefMG extends Spider
{
    protected $idenfication;

    private const BASE_URL = 'https://www2.fazenda.mg.gov.br';
    private const FIRST_PAGE = '/sol/ctrl/SOL/CDT/SERVICO_829?ACAO=INICIAR';
    private const INIT_FORM_ACTION = '/sol/ctrl/SOL/CDT/SERVICO_841';
    private const RESULT_FORM_ACTION = '/sol/ctrl/SOL/DIVATIV/DETALHE_001?ACAO=VISUALIZAR';
    private const FINAL_SUBMIT_PAGE = '/sol/ctrl/SOL/CDT/SERVICO_839';

    private const BAIRRO_DEFAULT = 'Bairro não indetificado';
    private const TIPO_LOGRADOURO_DEFAULT = '--';
    private const NOME_LOGRADOURO_DEFAULT = 'Logradouro não identificado';

    private const PSQ_ENDERECO = '/sol/ctrl/SOL/RETAGUAR/ENDERECO_INTERNET_PSQ';

    private const ERROR_CEP = 'O Endereço do contribuinte não pode ser encontrado pelo cep: ';

    // carregar e salvar htmls de apoio / facilita o teste
    private const PATH_LOCAL = '/var/www/captura-lambda/src/Crawler/SefMG/';

    private $protocolNumber = '';

    public $debug = false;

    private $reCaptchaToken = '';

    private $firstPageResponse = '';

    private $finalReturn = [];

    protected function validateAndSetCrawlerAttributes()
    {
        $this->log(__METHOD__);

        $this->param['cep'] = preg_replace('/[^\d]/', '', $this->param['cep']);
        $this->param['cpf_cnpj'] = preg_replace('/[^\d]/', '', $this->param['cpf_cnpj']);

        if (empty($this->param['cep'])) {
            throw new Exception("CEP é obrigatório");
        }

        if (empty($this->param['cpf_cnpj'])) {
            throw new Exception("Cpf/Cnpj é obrigatório");
        }

        if (!Document::validarCpfOuCnpj($this->param['cpf_cnpj'])) {
            throw new Exception("Invalid parameter: cpf/cnpj .");
        }

        $cep = preg_replace('/[^\d]/', '', $this->param['cep']);

        if (strlen($cep) != 8) {
            throw new Exception("CEP Inválido", 2);
        }

        $result = json_decode($this->getResponse('https://viacep.com.br/ws/' . $cep . '/json/'));

        if (!empty($result->erro)) {
            throw new Exception("CEP Inexistente");
        }

        $this->log('Validação OK');
    }

    /**
     * Starts the crawler operation.
     *
     */
    protected function start()
    {
        $this->log(__METHOD__);

        $this->setProxy();

        $this->test();

        $this->firstPageResponse = $this->getFirstPageResponse();

        $siteKey = $this->getCaptchaSiteKey();

        $this->handleCaptcha($siteKey);

        $formPageResponse = $this->submitInitForm();

        $this->validateFormPageResponse($formPageResponse);

        $addressPageResponse = $this->searchAddress();

        $regexErrorCep = '/O Endere.*o do Contribuinte n.*o pode ser encontrado/mi';

        if (preg_match($regexErrorCep, $addressPageResponse)) {
            throw new Exception(self::ERROR_CEP . $this->param['cep'], 2);
        }

        $this->finalSubmit($formPageResponse, $addressPageResponse);

        return $this->finalReturn;
    }

    private function validateFormPageResponse($formPageResponse)
    {
        if (!preg_match('/\(Clique aqui para informar endere.*o\)/mi', $formPageResponse)) {
            //
            if (preg_match('/id="MENSAGEM_ERRO"[\w\W]+?>For input string/m', $formPageResponse)) {
                // talvez
                throw new Exception("Erro no tipo de identificação", 3);
            }

            throw new Exception("Erro desconhecido", 3);
        }
    }

    private function test()
    {
        return false;

        exit;
    }

    private function searchAddress()
    {
        $this->log(__METHOD__);

        $requestParams = [
            'ACAO' => 'EXIBIRFLT',
            'unifwScrollTop' => 0,
            'unifwScrollLeft' => 0,
            'funcaoRetorno' => 'opener.mudarValor',
            'idMunicipio' => '',
            'nomeUF' => '',
            'cep' => $this->param['cep'],
            'sgUf' => '',
            'nomeMunicipio' => ''
        ];

        $url = self::BASE_URL . self::PSQ_ENDERECO;

        $response = $this->getResponse($url, 'POST', $requestParams);

        return $response;
    }

    private function getFirstPageResponse()
    {
        $response = $this->getResponse(self::BASE_URL . self::FIRST_PAGE);

        return $response;
    }

    /**
     * Solves the reCaptcha present in the initial page.
     * @version 1.0.0
     * <AUTHOR> Bonifácio
     * @return string -
     */
    private function getCaptchaSiteKey()
    {
        $this->log(__METHOD__);

        if (!preg_match("/sitekey=\"(.+)\"/", $this->firstPageResponse, $matches)) {
            throw new Exception("Could not extract reCaptcha sitekey from the page.", 3);
        }

        $this->log("Captcha Site Key: " . $matches[1]);

        return $matches[1];
    }

    /**
     *  Set the reCaptchaToken variable
     */
    private function handleCaptcha($siteKey)
    {
        $this->log(__METHOD__);

        try {
            $token = $this->solveReCaptcha($siteKey, self::BASE_URL . self::FIRST_PAGE);

            if (empty($token)) {
                throw new Exception('Não foi possível quebrar o captcha', 3);
            }

            $this->reCaptchaToken = $token;

            $this->log("Captcha Token: " . $this->reCaptchaToken);
        } catch (Exception $e) {
            $this->log('ERROR GET TOKEN');

            throw new Exception($this->errorWhere($e), 3);
        }
    }

    /**
     * Submits the initial form with the parameters and returns the resulting page.
     *
     */
    private function submitInitForm()
    {
        $this->log(__METHOD__);

        $requestParams = [
            'ACAO' => 'PESQUISAR',
            'tipoIdentificacao' => $this->getIdentificationType(),
            'numeroIdentificacao' => $this->getIdentification(),
            'g-recaptcha-response' => $this->reCaptchaToken
        ];

        $this->log($requestParams);

        $url = self::BASE_URL . self::INIT_FORM_ACTION;

        $regexQueryString = '/\/sol\/ctrl\/SOL\/CDT\/SERVICO_841(\?.*?)\'/mi';

        if (preg_match($regexQueryString, $this->firstPageResponse, $match)) {
            $url .= $match[1];
            $this->log('URL INIT FORM: ' . $url);
        }

        $initialResponse = $this->getResponse($url, 'POST', $requestParams);

        return $initialResponse;
    }

    /**
     * Retrieves the identification type.
     * @version 1.0.0
     * <AUTHOR> Bonifácio
     * @return string - The type of identification for this crawler.
     */
    private function getIdentificationType()
    {
        $this->log(__METHOD__);

        return strlen($this->getIdentification()) == 11 ? '4' : '3';
    }

    /**
     * Retrieves the idenfication parameter.
     * @version 1.0.0
     * <AUTHOR> Bonifácio
     * @return string - The identifcation without mask.
     */
    private function getIdentification()
    {
        $this->log(__METHOD__);

        return Document::removeMask($this->param['cpf_cnpj']);
    }

    private function log($obj)
    {
        if (!$this->debug) {
            return false;
        }

        if (is_string($obj) || is_numeric($obj)) {
            echo "\n$obj\n";
        } else {
            var_dump($obj);
        }
    }

    private function getAddressParams($addressSearchHtml)
    {
        $addressParams = [
            'solicitante.endereco.idMunicipio' => '',
            'solicitante.endereco.sgUf' => '',
            'solicitante.endereco.cep' => '',
            'solicitante.endereco.nomeUF' => '',
            'solicitante.endereco.nomeMunicipio' => '',
            'solicitante.endereco.nomeBairro' => '',
            'solicitante.endereco.complemento1' => '',
            'solicitante.endereco.tipoComplemento1.codigo' => '',
            'solicitante.endereco.tipoComplemento1.descricao' => '',
            'solicitante.endereco.tipoComplemento2.codigo' => '',
            'solicitante.endereco.tipoComplemento3.codigo' => '',
            'solicitante.endereco.tipoComplemento4.codigo' => '',
            'solicitante.endereco.tipoComplemento5.codigo' => '',
            'solicitante.endereco.tipoComplemento6.codigo' => '',
            'solicitante.endereco.tipoComplemento2.descricao' => '',
            'solicitante.endereco.complemento2' => '',
            'solicitante.endereco.tipoComplemento3.descricao' => '',
            'solicitante.endereco.complemento3' => '',
            'solicitante.endereco.tipoComplemento4.descricao' => '',
            'solicitante.endereco.complemento4' => '',
            'solicitante.endereco.tipoComplemento5.descricao' => '',
            'solicitante.endereco.complemento5' => '',
            'solicitante.endereco.tipoComplemento6.descricao' => '',
            'solicitante.endereco.complemento6' => '',

            'solicitante.endereco.complementoCEP' => '',

            'solicitante.endereco.nomePovoadoDistrito' => '',

            'solicitante.endereco.nomeTipoLogradouro' => '',
            'solicitante.endereco.nomeLogradouro' => '',
            'solicitante.endereco.numero' => '',
        ];

        //$addressSearchHtml = file_get_contents(self::PATH_LOCAL . 'psq-endereco.html');

        foreach ($addressParams as $key => $value) {
            $removedString = 'solicitante.endereco.';

            $str = str_replace($removedString, '', $key);
            $str = preg_replace('/\./m', '\\\\.', $str);

            // regex inputs e textarea
            $regex = '/<(input|textarea).*name="' . $str . '".*value="([\w\W]*?)"/mi';
            // regex selects
            $regexSelect = '/<select.*name="' . $str . '"[\w\W]+?value="(.*)".*?selected/mi';

            if (preg_match($regex, $addressSearchHtml, $match)) {
                $addressParams[$key] = $match[2];
            } elseif (preg_match($regexSelect, $addressSearchHtml, $match)) {
                $addressParams[$key] = $match[1];
            } else {
                $this->log('Dont match: ' . $regex);
                $this->log('Dont match: ' . $regexSelect);
                $addressParams[$key] = '';
            }
        }

        if (empty($addressParams['solicitante.endereco.nomeBairro'])) {
            $addressParams['solicitante.endereco.nomeBairro'] = self::BAIRRO_DEFAULT;
        }

        if (empty($addressParams['solicitante.endereco.nomeTipoLogradouro'])) {
            $addressParams['solicitante.endereco.nomeTipoLogradouro'] = self::TIPO_LOGRADOURO_DEFAULT;
        }

        if (empty($addressParams['solicitante.endereco.nomeLogradouro'])) {
            $addressParams['solicitante.endereco.nomeLogradouro'] = self::NOME_LOGRADOURO_DEFAULT;
        }


        return $addressParams;
    }

    private function getFormPageParams($formPageResponse)
    {
        $this->log(__METHOD__);

        $formPage = [
            'solicitante.identificacao.tipoIdentificacao' => '',
            'solicitante.identificacao.identificacao' => '',
            'CDTSolicitacao' => '',
            'solicitante.identificacao.identificacaoFormatada' => '',
            'solicitante.nome' => '',
            'txtComplemento' => '  ,  ,  , 
			 ,  ,  '

        ];

        //$formPageResponse = file_get_contents(self::PATH_LOCAL . 'first-submit-page.html');

        foreach ($formPage as $key => $param) {
            // escapando o ponto
            $formatKeyRegex = preg_replace('/\./m', '\\\\.', $key);

            $regex = '/<(input|textarea).*name="' . $formatKeyRegex . '".*value="([\w\W]*?)"/mi';

            if (preg_match($regex, $formPageResponse, $m)) {
                $formPage[$key] = $m[2];
            } else {
                $this->log('Dont match: ' . $regex);
                $formPage[$key] = '';
            }
        }

        return $formPage;
    }

    public function finalSubmit($formPageResponse, $addressPageResponse)
    {
        $this->log(__METHOD__);

        $arrayParams = [
            'ACAO' =>  'CONFNOVO',
            'unifwScrollTop' => '0',
            'unifwScrollLeft' => '0'
        ];

        $formPage = $this->getFormPageParams($formPageResponse);
        $addressParams = $this->getAddressParams($addressPageResponse);

        $finalParams = array_merge($arrayParams, $formPage, $addressParams);

        $this->log($finalParams);

        $finalUrl = self::BASE_URL . self::FINAL_SUBMIT_PAGE;

        $finalPageResponse = $this->getResponse($finalUrl, 'POST', $finalParams);

        if (!preg_match('/id="protocoloFormatado".*>([\d.-]+)/m', $finalPageResponse, $match)) {
            throw new Exception("Erro ao buscar o protocolo", 3);
        }

        $this->protocolNumber = $match[1];

        $this->finalReturn['protocolo'] = $this->protocolNumber;

        $this->printCertidoes($finalPageResponse);
    }

    private function printCertidoes($finalPageResponse)
    {
        $this->log(__METHOD__);

        $this->saveComprovanteProtocolo($finalPageResponse);

        $this->saveCertidao($finalPageResponse);
    }

    private function saveComprovanteProtocolo($finalPageResponse)
    {
        $this->log(__METHOD__);

        $regex = '/\'(\/sol\/ctrl\/SOL\/CDT\/DETALHE_865\?descServico.*?)&descServico/m';

        if (preg_match($regex, $finalPageResponse, $match)) {
            $url = self::BASE_URL . $match[1];

            $this->log($url);

            $comprovantePage = $this->getResponse($url);

            $comprovantePage = $this->adjustHtml($comprovantePage);

            $file = $this->savePDFS3($comprovantePage);

            $this->finalReturn['comprovante_certidao'] = $file;

            return true;
        }

        $errorMessage = 'Não foi possível salvar o comprovante do protocolo';
        throw new Exception($errorMessage, 3);
    }

    private function saveCertidao($finalPageResponse)
    {
        $this->log(__METHOD__);

        $regex = '/\'(\/sol\/ctrl\/SOL\/CDT\/DETALHE_746\?descServico.*?)&descServico/m';

        if (preg_match($regex, $finalPageResponse, $match)) {
            $url = self::BASE_URL . $match[1];

            $this->log($url);

            $certidaoPage = $this->getResponse($url);

            $certidaoPage = $this->adjustHtml($certidaoPage);

            $file = $this->savePDFS3($certidaoPage);

            $this->finalReturn['pdf'] = $file;

            return true;
        }

        $errorMessage = 'Não foi possível salvar certificado';
        throw new Exception($errorMessage, 3);
    }

    private function adjustHtml($html)
    {
        $search = '/sol/images/sicaf/brasao.gif';
        $replace = 'https://www2.fazenda.mg.gov.br/sol/images/sicaf/brasao.gif';

        $html = str_replace($search, $replace, $html);

        $replaceHead = '/(<head>[\w\W]+?<\/head>)/mi';
        $html = preg_replace($replaceHead, '', $html, 1);

        $replaceXmlTag = '/<\?xml version = \'1.0\' encoding = \'UTF-8\'\?>/mi';
        $html = preg_replace($replaceXmlTag, '', $html, 1);

        return $html;
    }

    private function savePDFS3($result)
    {
        $fileId = uniqid() . '.pdf';
        $filePath = '/tmp/SefMG' . $fileId;
        $s3Path = 'SefMG/' . $fileId;

        (new Pdf())->saveHtmlToPdf($result, $filePath);

        if ((new S3(new StaticUplexisBucket()))->save($s3Path, $filePath)) {
            return S3_STATIC_URL . $s3Path;
        }

        throw new Exception('Erro ao salvar o pdf', 6);
    }

    private function errorWhere($e)
    {
        $error = 'ERROR: ';
        $error .= $e->getMessage();
        $error .= ' - In ' . $e->getFile();
        $error .= ' - On line ' . $e->getLine();

        return $error;
    }
}
