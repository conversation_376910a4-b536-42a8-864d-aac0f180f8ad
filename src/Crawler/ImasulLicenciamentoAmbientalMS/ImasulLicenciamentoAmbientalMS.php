<?php

namespace App\Crawler\ImasulLicenciamentoAmbientalMS;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Util;
use Exception;

class ImasulLicenciamentoAmbientalMS extends Spider
{
    private const BASE_URL = 'http://cerberus.imasul.ms.gov.br:8005/LicencasConcedidas/';
    private $cpfCnpj = 0;
    private $nome = "";
    private $limit;
    private $sessionID;
    private $numPages = 1;
    private $countItemsfirstPage;
    private $count = 0;

    public function start()
    {
        $this->getSessionID();
        $this->makeRequest();
        return $this->data;
    }

    /**
     * Captura o sessionId do cookie da fonte para autenticação.
     * <AUTHOR>
     * @return void
     */
    private function getSessionID()
    {
        $ch = curl_init(self::BASE_URL . "login.jsf");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        curl_setopt($ch, CURLOPT_HEADER, 1);
        $result = curl_exec($ch);

        preg_match_all('/^Set-Cookie:\s*([^;]*)/mi', $result, $matches);
        $cookies = array();
        foreach ($matches[1] as $item) {
            parse_str($item, $cookie);
            $cookies = array_merge($cookies, $cookie);
        }

        $this->sessionID = $cookie['JSESSIONID'];
    }

    /**
     * Responsavel por executar todas as funções de request.
     * <AUTHOR> Pereira
     * @return void
     */
    private function makeRequest()
    {
        $this->login();
        $this->cadastroLicenca();

        for ($i = 1; $i <= $this->numPages; $i++) {
            $this->execData($i);
        }
    }

    /**
     * Responsavel pela requisição de login para autenticação.
     * <AUTHOR> Pereira
     * @return void
     */
    private function login()
    {

        $paramsLogin = [
            'login' => 'login',
            'login:usuario' => '',
            'login:senha' => '',
            'javax.faces.ViewState' => 'j_id1',
            'login:j_id_jsp_1664723547_21' => 'login:j_id_jsp_1664723547_21'
        ];

        $this->getResponse(
            self::BASE_URL . "login.jsf;jsessionid={$this->sessionID}",
            'POST',
            $paramsLogin
        );
    }

    /**
     * Responsavel pela requisição da tela que retorna os itens
       a partir do termo passado.
     * <AUTHOR> Pereira
     * @return int
     */
    private function cadastroLicenca()
    {
        $params = [
            'consultaLic:consultalicenca' => 'consultaLic:consultalicenca',
            'consultaLic:consultalicenca:licenca-numproc' => '',
            'consultaLic:consultalicenca:tipoprocesso' => 0,
            'consultaLic:consultalicenca:setor' => 0,
            'consultaLic:consultalicenca:subtipo' => 0,
            'consultaLic:consultalicenca:licenca-numero' => '',
            'consultaLic:consultalicenca:cliente' => $this->nome,
            'consultaLic:consultalicenca:cpf_cnpj' => $this->cpfCnpj,
            'consultaLic:consultalicenca:numeroSPI' => '',
            'consultaLic:consultalicenca:municipio' => '',
            'consultaLic:consultalicenca:consultar' => 'Consultar',
            'javax.faces.ViewState' => "j_id2",
        ];

        $results = $this->getResponse(
            self::BASE_URL . "cadastroLicenca.jsf;jsessionid={$this->sessionID}",
            'POST',
            $params
        );

        if (!preg_match('/dr-table-firstrow/', $results)) {
            throw new Exception('Sem resultados encontrados.', 2);
        }

        preg_match_all(
            '/<tr\sclass="dr-table-firstrow rich-table-firstrow\s">([\s\S]*?)<td/',
            $results,
            $quantidadeItens
        );

        preg_match_all(
            "/page':\s'([\d]*?)'/",
            $results,
            $paginas
        );

        if (!empty($paginas[0])) {
            $this->numPages = end($paginas[1]);
        }

        $this->countItemsfirstPage = count($quantidadeItens[1]);

        return $this->countItemsfirstPage;
    }

    /**
     * Responsavel por executar todas as páginas da requisição.
     * <AUTHOR> Pereira
     * @param int $count
     * @return void
     */
    private function execData($count)
    {
        $this->pageRequest($count);

        for ($i = 0; $i < $this->countItemsfirstPage; $i++) {
            if ($this->count == $this->limit) {
                break;
            }

            $this->licensaDetalhe();
            $this->count++;
        }
    }

    /**
     * Responsavel por trazer todos os itens de determinada página.
     * <AUTHOR> Pereira
     * @param int $count
     * @return int
     */
    private function pageRequest($count)
    {
        $params = [
            'AJAXREQUEST' => 'cadastroLic',
            'consultaLic:consultalicenca' => 'consultaLic:consultalicenca',
            'consultaLic:consultalicenca:tipoprocesso' => 0,
            'consultaLic:consultalicenca:setor' => 0,
            'consultaLic:consultalicenca:subtipo' => 0,
            'consultaLic:consultalicenca:cpf_cnpj' => $this->cpfCnpj,
            'consultaLic:consultalicenca:cliente' => $this->nome,
            'consultaLic:consultalicenca:municipio' => '',
            'javax.faces.ViewState' => "j_id3",
            'ajaxSingle' => 'consultaLic:consultalicenca:tabela:j_id_jsp_1886455766_104pc2',
            'consultaLic:consultalicenca:tabela:j_id_jsp_1886455766_104pc2' => "{$count}",
        ];

        $results = $this->getResponse(
            self::BASE_URL . "cadastroLicenca.jsf;jsessionid={$this->sessionID}",
            'POST',
            $params
        );

        if ($count == 1) {
            return true;
        }

        preg_match_all(
            '/<tr\sclass="dr-table-firstrow\srich-table-firstrow">([\s\S]*?)<td/',
            $results,
            $quantidadeItens
        );

        preg_match_all(
            "/page':\s'([\d]*?)'/",
            $results,
            $paginas
        );

        if (!empty($paginas[0])) {
            $this->numPages = end($paginas[1]);
        }

        $this->countItemsfirstPage = count($quantidadeItens[1]);

        return $this->countItemsfirstPage;
    }

    /**
     * Responsavel por trazer os detalhes dos itens.
     * <AUTHOR> Pereira
     * @return void
     */
    private function licensaDetalhe()
    {
        $params = [
            'AJAXREQUEST' => 'cadastroLic',
            'consultaLic:consultalicenca' => 'consultaLic:consultalicenca',
            'consultaLic:consultalicenca:tipoprocesso' => 0,
            'consultaLic:consultalicenca:setor' => 0,
            'consultaLic:consultalicenca:subtipo' => 0,
            'consultaLic:consultalicenca:cpf_cnpj' => $this->cpfCnpj,
            'consultaLic:consultalicenca:cliente' => $this->nome,
            'consultaLic:consultalicenca:municipio' => '',
            'javax.faces.ViewState' => "j_id3",
            "consultaLic:consultalicenca:tabela:{$this->count}:link" =>
            "consultaLic:consultalicenca:tabela:{$this->count}:link",
        ];

        $results = $this->getResponse(
            self::BASE_URL . "cadastroLicenca.jsf;jsessionid={$this->sessionID}",
            'POST',
            $params
        );

        $this->parseData($results, $this->count);
    }

    /**
     * Responsavel por capturar, tratar e estruturar os dados de cada item.
     * <AUTHOR> Pereira
     * @param string $parse
     * @param int $count
     * @return void
     */
    private function parseData($parse, $count)
    {
        $parse = utf8_decode($parse);

        preg_match_all('/<span class="mensagem" dir="ltr">([\s\S]*?)<\/span>/', $parse, $numeroLicencaDados);
        $numeroLicenca = $numeroLicencaDados[1][0] . $numeroLicencaDados[1][1];

        $pattern = [
            'numeroSPI' => ['@116pc2"\svalue="([\s\S]*?)"@'],
            'dataEmissao' => ['@118pc2"\svalue="([\s\S]*?)"@'],
            'dataValidade' => ['@120pc2"\svalue="([\s\S]*?)"@'],
            'cpfCnpj' => ['@122pc2"\svalue="([\s\S]*?)"@'],
            'numCerberus' => ['@124pc2"\svalue="([\s\S]*?)"@'],
            'tipoLicencaAutor' => ['@126pc2"\svalue="([\s\S]*?)"@'],
            'tipoEmpresaAtividade' => ['@128pc2"\svalue="([\s\S]*?)"@'],
            'municipio' => ['@130pc2"\svalue="([\s\S]*?)"@'],
            'empreendedor' => ['@132pc2"\svalue="([\s\S]*?)"@'],
            'objetoDaLicenca' => ['@134pc2"\svalue="([\s\S]*?)"@'],
            'endereco' => ['@136pc2"\svalue="([\s\S]*?)"@'],
        ];

        $this->data[] = Util::parseDados($pattern, $parse);
        $this->data[$count]['numeroLicenca'] = $numeroLicenca;
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty(trim($this->param['cpf_cnpj_name']))) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        $this->cpfCnpj = Document::removeMask(trim($this->param['cpf_cnpj_name']));

        if (!is_numeric($this->cpfCnpj)) {
            $this->nome = $this->param['cpf_cnpj_name'];
            $this->cpfCnpj = "";
        }

        $this->limit = $this->param['limit'];
        if (empty($this->limit)) {
            $this->limit = 20;
        }
    }
}
