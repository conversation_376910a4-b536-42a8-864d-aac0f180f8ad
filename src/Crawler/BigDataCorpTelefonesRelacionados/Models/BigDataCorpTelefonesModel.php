<?php

namespace App\Crawler\BigDataCorpTelefonesRelacionados\Models;

class BigDataCorpTelefonesModel
{
    public $Number;
    public $AreaCode;
    public $CountryCode;
    public $Complement;
    public $Type;
    public $PhoneTotalPassages;
    public $PhoneBadPassages;
    public $PhoneCrawlingPassages;
    public $PhoneValidationPassages;
    public $PhoneQueryPassages;
    public $PhoneMonthAveragePassages;
    public $PhoneNumberOfEntities;
    public $Priority;
    public $IsMain;
    public $IsRecent;
    public $IsActive;
    public $IsInDoNotCallList;
    public $CurrentCarrier;
    public $PlanType;
    public $FirstPassageDate;
    public $LastPassageDate;
    public $CreationDate;
    public $LastUpdateDate;
    public $PortabilityHistory;
    public $HasOptIn;

    public function __construct($dados)
    {
        foreach (array_keys(get_object_vars($this)) as $att) {
            $this->{$att} = !empty($dados->{$att}) ? $dados->{$att} : null;
        }
    }
}
