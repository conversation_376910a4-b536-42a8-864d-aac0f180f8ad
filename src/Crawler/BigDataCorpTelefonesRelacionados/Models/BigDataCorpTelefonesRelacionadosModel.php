<?php

namespace App\Crawler\BigDataCorpTelefonesRelacionados\Models;

class BigDataCorpTelefonesRelacionadosModel
{
    public $RELACAO;
    public $NOME;
    public $IDADE;
    public $DDD;
    public $TELEFONE;

    public function __construct($pessoa, $telefone)
    {
        $relationship = [
            'COWORKER' => 'COLEGA DE TRABALHO',
            'NEIGHBOR' => 'VIZINHO',
            'BROTHER' => 'IRMAO',
            'NEPHEW' => 'SOBRINHO',
            'MOTHER' => 'MAE',
            'SON' => 'FILHO',
            'HOUSEHOLD' => 'FAMILIAR',
            'GRANDSON' => 'NETO',
            'SPOUSE' => 'CONJUGE',
            'RELATIVE' => 'PARENTE',
            'GRANDPARENT' => 'AVO',
            'UNCLE' => 'TIO',
            'COUSIN' => 'PRIMO',
            'FATHER' => 'PAI',
            'RELATED' => 'RELACIONADO',
        ];

        $this->REL<PERSON>AO = isset($relationship[$pessoa->RelationshipType])
            ? $relationship[$pessoa->RelationshipType]
            : $pessoa->RelationshipType;
        $this->NOME = $pessoa->RelatedEntityName;
        $this->DDD = $telefone->AreaCode;
        $this->TELEFONE = $telefone->Number;
    }
}
