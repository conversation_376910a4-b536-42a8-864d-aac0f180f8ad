<?php

namespace App\Crawler\BigDataCorpTelefonesRelacionados;

use Exception;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\BigDataCorpManager;
use App\Crawler\BigDataCorpTelefonesRelacionados\Models\BigDataCorpTelefonesModel;
use App\Crawler\BigDataCorpTelefonesRelacionados\Models\BigDataCorpRelacoesPessoaisModel;
use App\Crawler\BigDataCorpTelefonesRelacionados\Models\BigDataCorpTelefonesRelacionadosModel;

class BigDataCorpTelefonesRelacionados extends Spider
{
    private $allDatasets;
    private $allData;

    protected function validateAndSetCrawlerAttributes()
    {
        if (isset($this->param['documento']) && Document::validarCpfOuCnpj($this->param['documento'])) {
            return true;
        }

        throw new Exception('Parâmetro inválido');
    }

    protected function start()
    {
        $this->config();

        $this->allData = $this->getDados($this->allDatasets);

        $relatedPeople = $this->getRelatedPeople();
        if (empty($relatedPeople)) {
            throw new Exception("Nenhum resultado encontrado!", 2);
        }

        $relatedPhones = $this->getRelatedPhones();
        if (empty($relatedPhones)) {
            throw new Exception("Não foi encontrado nenhum telefone!", 2);
        }

        $results = [];
        foreach ($relatedPeople as $related) {
            foreach ($relatedPhones as $phone) {
                print "\n{$phone->Type} ||| {$related->RelatedEntityTaxIdNumber}\n";
                if (false !== strpos($phone->Type, $related->RelatedEntityTaxIdNumber)) {
                    $results[] = new BigDataCorpTelefonesRelacionadosModel($related, $phone);
                }
            }
        }

        return ['aTelefones' => $results];
    }

    private function config()
    {
        $documento = $this->param['documento'];
        $this->allDatasets = 'related_people,related_people_phones';
    }

    public function getRelatedPhones()
    {
        $results = [];
        foreach ($this->allData->Result as $value) {
            foreach ($value->RelatedPeoplePhones as $relatedPhone) {
                $results[] = new BigDataCorpTelefonesModel($relatedPhone);
            }
        }

        return $results;
    }

    public function getRelatedPeople()
    {
        $results = [];
        foreach ($this->allData->Result as $value) {
            foreach ($value->RelatedPeople->PersonalRelationships as $personalRelationship) {
                $results[] = new BigDataCorpRelacoesPessoaisModel($personalRelationship);
            }
        }

        return $results;
    }

    private function getDados($url)
    {
        $bigDataManager = new BigDataCorpManager($this->idUser);
        $class = 'BigDataCorpTelefonesRelacionados';
        $result = $bigDataManager->getDataPersonApi($url, $this->param['documento'], $class);
        $data = json_decode($result);
        return $data;
    }
}
