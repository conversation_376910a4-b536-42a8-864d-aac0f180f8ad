<?php

namespace App\Crawler\CefHistoricoEmpregador;

use App\Crawler\Spider;
use Exception;
use Symfony\Component\DomCrawler\Crawler;
use App\Helper\Str;
use App\Helper\Document;

class CefHistoricoEmpregador extends Spider
{
    private const CONSULTA_URL = 'https://consulta-crf.caixa.gov.br/consultacrf/pages/consultaEmpregador.jsf';
    private const FORM_URL = 'https://consulta-crf.caixa.gov.br/consultacrf/pages/consultaRegularidade.jsf';
    private const MAX_EXCEPTIONS = 4;

    private const DATE_START = 1;
    private const DATE_END = 2;

    private $amountExceptions = 0;

    /**
     * Valida, configura e inicia o execução do script
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/01/2020
     *
     * @return array
     */
    protected function start()
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }

        $error = null;

        while ($this->amountExceptions < self::MAX_EXCEPTIONS) {
            try {
                //$this->setProxy();
                return $this->getSearchResult();
            } catch (Exception $e) {
                sleep(1);
                $error = $e->getMessage();
                $this->amountExceptions++;
                continue;
            }
        }
        $this->errorHandler($error);
    }

    /**
     * Retorna os dados parseados
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/01/2020
     *
     * @return array
     */
    private function getSearchResult()
    {
        $response = $this->getResponse(self::CONSULTA_URL);
        $this->resolveCaptcha($response);
        $html = $this->getFirstPage($response);
        return $this->parseData(Str::encoding($html));
    }

    /**
     * Validação do primeiro post
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/01/2020
     *
     * @return void
     */
    private function validateFirstPost($html)
    {
        if (preg_match('/captcha inv.*lido/i', $html)) {
            throw new Exception('Falha ao quebrar o captcha', 3);
        }

        if (preg_match('/<body.*?.Error/', $html)) {
            throw new Exception('Falha ao conectar com servidor', 3);
        }

        if (preg_match('/Empregador\s*n\W*o\s*encontrado/i', $html)) {
            throw new Exception('Empregador não encontrado', 2);
        }

        if (preg_match('/Empregador\s*n\W*o\s*cadastrado/i', $html)) {
            $this->amountExceptions = self::MAX_EXCEPTIONS;
            throw new Exception('Empregador não cadastrado', 2);
        }
    }

    /**
     * Trata as exceções ao término das tentativas de captura
     *
     * @version 1.0.0
     *
     * <AUTHOR> Medeiros 18/06/2021
     *
     * @return void
     */
    private function errorHandler($exceptionMessage)
    {
        switch ($exceptionMessage) {
            case 'Falha ao quebrar o captcha':
                throw new Exception('Falha ao quebrar o captcha', 3);
            case 'Falha ao conectar com servidor':
                throw new Exception('Falha ao conectar com servidor', 3);
            case 'Empregador não encontrado':
                throw new Exception('Empregador não encontrado', 2);
            case 'Empregador não cadastrado':
                throw new Exception('Empregador não cadastrado', 2);
            default:
                throw new Exception('Erro não identificado', 2);
        }
    }

    /**
     * Recupera o ViewState da página
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/01/2020
     *
     * @return string
     */
    private function getViewState($response)
    {
        $view = [];
        preg_match('/javax.faces.ViewState:0" value="(.*?)"/m', $response, $view);

        if (empty($view[1])) {
            throw new Exception('Falha ao pegar o viewstate');
        }

        return $view[1];
    }

    /**
     * Salva a imagem do captcha
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/01/2020
     *
     * @return void
     */
    private function getImageCaptcha($url)
    {
        $url = Str::encoding($url);
        preg_match('/<div class="captcha-imagem"> <img src="(.*?)"/m', $url, $captchaSrc);
        list($captchaPathInfo, $captchaImage) = explode(',', $captchaSrc[1]);
        file_put_contents($this->captcha_path, base64_decode($captchaImage));
    }

    /**
     * Resolve o captcha
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/01/2020
     *
     * @return void
     */
    private function resolveCaptcha($response)
    {
        $this->getImageCaptcha($response);
        $this->breakCaptcha();
    }

    /**
     * Pega o html do primeiro post
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/01/2020
     *
     * @return string
     */
    private function getFirstPage($html)
    {
        $params = [
            'AJAXREQUEST' => '_viewRoot',
            'mainForm:tipoEstabelecimento' => '1',
            'mainForm:txtInscricao1' => Document::removeMask($this->param['cnpj']),
            'mainForm:uf' => '',
            'mainForm:txtCaptcha' => $this->captcha,
            'mainForm' => 'mainForm',
            'autoScroll' => '',
            'javax.faces.ViewState' => $this->getViewState($html),
            'mainForm:btnConsultar' => 'mainForm:btnConsultar'
        ];

        $html = Str::encoding($this->getResponse(self::CONSULTA_URL, 'POST', $params));
        $this->validateFirstPost($html);
        return $html;
    }

    /**
     * Pega o html da página de Regularidade
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/01/2020
     *
     * @return string
     */
    private function getRegularity($html)
    {
        $params = [
            'AJAXREQUEST' => '_viewRoot',
            'mainForm:codAtivo' => '',
            'mainForm:listEmpFpas' => 'true',
            'mainForm:hidCodPessoa' => '0',
            'mainForm:hidCodigo' => '0',
            'mainForm:hidDescricao' => '',
            'mainForm' => 'mainForm',
            'autoScroll' => '',
            'javax.faces.ViewState' => $this->getViewState($html),
            'mainForm:j_id52' => 'mainForm:j_id52'
        ];

        $response = Str::encoding($this->getResponse(self::FORM_URL, 'POST', $params));
        if (preg_match('/Internal Server Error/m', $response)) {
            throw new Exception("Falha ao obter o certificado de regularidade", 1);
        }
        return $response;
    }

    /**
     * Pega o html da página de Histórico
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/01/2020
     *
     * @return string
     */
    private function getHistoric($html)
    {
        $params = [
            'AJAXREQUEST' => '_viewRoot',
            'mainForm:codAtivo' => '',
            'mainForm:listEmpFpas' => 'true',
            'mainForm:hidCodPessoa' => '0',
            'mainForm:hidCodigo' => '0',
            'mainForm:hidDescricao' => '',
            'mainForm' => 'mainForm',
            'autoScroll' => '',
            'javax.faces.ViewState' => $this->getViewState($html),
            'mainForm:j_id54' => 'mainForm:j_id54',
        ];

        $response = Str::encoding($this->getResponse(self::FORM_URL, 'POST', $params));
        if (preg_match('/Internal Server Error/m', $response)) {
            throw new Exception("Falha ao obter o histórico", 1);
        }

        return $response;
    }

    /**
     * Parse dos dados de histórico
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/01/2020
     *
     * @return array
     */
    private function parseHistoric($html)
    {
        $historic = [];

        $crawler = new Crawler($html);
        $crawler->filter('.rich-tablerow')->each(function (Crawler $table, $i) use (&$historic) {
            $historic[] = [
                'data_emissao' => $table->filter('td')->eq(0)->text(),
                'data_ini' => trim(explode('a', $table->filter('td')->eq(1)->text())[0]),
                'data_fim' => trim(explode('a', $table->filter('td')->eq(1)->text())[1]),
                'certificado' => $table->filter('td')->eq(2)->text(),
            ];
        });

        return $historic;
    }

    /**
     * Parse dos dados de endereço
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/01/2020
     *
     * @return array
     */
    private function parseAddress($htmlRegularidade)
    {
        $result = [];
        $endereco = strip_tags($this->parseItem('endereco', $htmlRegularidade));
        $arrEndereco = explode('/', $endereco);

        $result['logradouro'] = (isset($arrEndereco[0]) ? trim($arrEndereco[0]) : null);
        $result['bairro'] = (isset($arrEndereco[1]) ? trim($arrEndereco[1]) : null);
        $result['municipio'] = (isset($arrEndereco[2]) ? trim($arrEndereco[2]) : null);
        $result['uf'] = (isset($arrEndereco[3]) ? trim($arrEndereco[3]) : null);
        $result['cep'] = (isset($arrEndereco[4]) ? trim($arrEndereco[4]) : null);
        return $result;
    }

    /**
     * Parse das datas de validade
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/01/2020
     *
     * @return string
     */
    private function parseValidityDate($text, $type)
    {
        $text = explode('a', $text);

        if ($type == self::DATE_START) {
            return $text[0];
        }

        return isset($text[1]) ? trim($text[1]) : null;
    }

    /**
     * Parse da situação
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/01/2020
     *
     * @return string
     */
    private function parseSituation($text)
    {
        if (preg_match('/est...regular/i', $text)) {
            return 'Regular';
        }

        if (preg_match('/irregular/i', $text)) {
            return 'Irregular';
        }

        return null;
    }

    /**
     * Parse geral dos dados
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/01/2020
     *
     * @return array
     */
    private function parseData($searchHtml)
    {
        $htmlRegularidade = '';
        $htmlHistorico = '';
        $historic = null;

        if (preg_match('/certificado.de.regularidade.do.fgts/i', $searchHtml)) {
            $htmlRegularidade = $this->getRegularity($searchHtml);
        }

        if (preg_match('/hist..rico.do.empregador/i', $searchHtml)) {
            $htmlHistorico = $this->getHistoric($searchHtml);
            $historic = $this->parseHistoric($htmlHistorico);
        }

        $endereco = $this->parseAddress($htmlRegularidade);

        //Campos com null são da fonte antiga
        return [
            'cnpj' => $this->parseItem('cnpj', $searchHtml),
            'razao_social' => $this->parseItem('razao_social', $searchHtml),
            'nome_fantasia' => null,
            'pessoa_matriz' => null,
            'pessoa' => null,
            'end_logradouro' => $endereco['logradouro'],
            'end_numero' => null,
            'end_bairro' => $endereco['bairro'],
            'end_cep' => $endereco['cep'],
            'end_municipio' => $endereco['municipio'],
            'end_uf' => $endereco['uf'],
            'certificado' => $this->parseItem('certificado', $htmlRegularidade),
            'validade_ini' => $this->parseValidityDate(
                $this->parseItem('validade', $htmlRegularidade),
                self::DATE_START
            ),
            'validade_fim' => $this->parseValidityDate(
                $this->parseItem('validade', $htmlRegularidade),
                self::DATE_END
            ),
            'verificacao' => null,
            'liberacao' => null,
            'situacao' => $this->parseSituation($this->parseItem('situacao', $searchHtml)),
            'situacao_texto' => $this->parseItem('situacao', $searchHtml),
            'data_consulta' => date('d/m/Y'),
            'hora_consulta' => date('H:i:s'),
            'aHistorico' => $historic
        ];
    }

    /**
     * Parse dos itens
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/01/2020
     *
     * @return string
     */
    private function parseItem($item, $html)
    {
        $patterns = [
            'cnpj' => '#inscri....o:<\/label><img[^>]*><span[^>]*>([^>]*)?<\/span>#i',
            'razao_social' => '#raz..o.social:<\/label><img[^>]*><span[^>]*>([^>]*)?<\/span>#i',
            'endereco' => '#endere..o:<\/label><img[^>]*><span[^>]*>(.*?)<\/p>#i',
            'certificado' => '#certificado.n..mero:.*?<\/label><span[^>]*>([^>]*?)<\/span>#i',
            'validade' => '#validade:.?<img[^>]*>[^>]*?<\/label>([^>]*)<br.?\/>#i',
            'texto' => '#(a.caixa.econ..mica.federal.*?FGTS.\s?)<br\s\/>#i',
            'situacao' => '#feedback-text.>(.*?):\s<!--#i'
        ];

        $result = [];
        preg_match($patterns[$item], $html, $result);

        if (!empty($result[1])) {
            return $result[1];
        }
        return null;
    }

    /**
     * Valida os parametros
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/01/2020
     *
     * @return string
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }

        if (!isset($this->param['cnpj']) or empty($this->param['cnpj'])) {
            throw new Exception('Parâmetro inválido', 1);
        }

        if (!Document::validarCnpj($this->param['cnpj'])) {
            throw new Exception('Parâmetro inválido', 1);
        }
    }
}
