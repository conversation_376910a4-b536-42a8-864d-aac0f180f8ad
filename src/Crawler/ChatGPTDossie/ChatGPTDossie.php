<?php

namespace App\Crawler\ChatGPTDossie;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\ChatGPT\ChatGPTManager;
use Exception;

class ChatGPTDossie extends Spider
{
    public function start()
    {
        try {
            return (new ChatGPTManager())->sendDossie(
                $this->param['context']
            );
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty(trim($this->param['context']))) {
            throw new Exception('Parâmetro inválido', 1);
        }
    }
}
