<?php

namespace App\Crawler\BtgPactualPep;

use App\Crawler\Spider;
use App\Factory\MongoDB;
use App\Helper\Document;
use Exception;

class BtgPactualPep extends Spider
{
    private const INDEX_MONGODB = 'name_document';
    private const LIMIT = 50;

    public function start()
    {
        $results = $this->searchByNameOrCpf();
        return $results;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->param['criterio'] = trim($this->param['criterio']);

        if (empty($this->param['criterio'])) {
            throw new Exception('Parâmetro Inválido', 1);
        }

        if (Document::validarCpf($this->param['criterio'])) {
            $this->param['criterio'] = Document::removeMask($this->param['criterio']);
        }
    }

    private function searchByNameOrCpf()
    {
        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('common')
            ->setCollection('pep');
        $fields = ['nome','cpf'];
        $results = $manager
                    ->atlasSearch(
                        $this->param['criterio'],
                        self::INDEX_MONGODB,
                        self::LIMIT,
                        $fields
                    );

        return $this->parseResults($results);
    }

    private function parseResults($results)
    {
        $parsedResults = [];

        foreach ($results as $result) {
            $parsedResults[] = [
                'cpf' => $result['cpf'],
                'nome' => $result['nome'],
                'data_nascimento' => $result['data_nascimento'],
                'cargo' => $result['cargo'],
                'inicio_mandato' => $result['inicio_mandato'],
                'informacoes' => $result['informacoes'],
                'estado' => $result['uf'],
            ];
        }

        if (empty($parsedResults)) {
            throw new Exception('Nenhum resultado encontrado', 2);
        }

        return $parsedResults;
    }
}
