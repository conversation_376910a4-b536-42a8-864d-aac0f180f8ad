<?php

namespace App\Crawler\Cnes;

use App\Crawler\Spider;
use App\Factory\MongoDB;
use App\Helper\Document;
use App\Helper\Str;
use Exception;

class Cnes extends Spider
{
    private const INDEX_MONGODB = 'corporateName_document';
    private const LIMIT = 15;

    protected function start()
    {
        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('cnes')
            ->setCollection('cnes_estabelecimento');

        $fields = ['nome_razao_social','nome_fantasia'];

        if (Document::validarCpfOuCnpj($this->param['searchParams'])) {
            $fields = ['cpf','cnpj'];
        }

        $query = $this->setQuery($fields);

        $results =  json_decode(
            json_encode(
                $manager->query(
                    'aggregate',
                    $query,
                    null,
                    null,
                    true
                )
                ->toArray(),
                true
            ),
            true
        );

        if (empty($results)) {
            throw new Exception('Nenhum resultado encontrado!', 2);
        }

        foreach ($results as $result) {
            $response[] = $this->finalParse($result);
        }

        return $response;
    }

    protected function validateAndSetCrawlerAttributes()
    {
        $cnpj = $this->param['cpf_cnpj'] ??  '';
        $nome = $this->param['nome'] ??  '';

        $this->limit = !isset($this->param['limit']) ?
            self::LIMIT : intval($this->param['limit']);

        if (!empty($nome)) {
            //converter de minuscula para maiuscula para fazer a busca
            $this->param['searchParams'] = trim(strtoupper($nome));
            return;
        }

        $this->param['searchParams'] = $cnpj;
    }

    private function finalParse($result)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $identificacao = $this->getIdentificacao($result);
        $profissionaisSusNaoSus = $this->getProfissionais($result['profissionais']);
        $atendimentoPrestado = $this->getAtendimentoPrestado(
            $result['atendimentos'],
            $result['tipo_atendimento'],
            $result['convenio']
        );
        $equipamentos = $this->getEquipamentos(
            $result['equipamentos'],
            $result['equipamentos_descricao'],
        );
        $residuosRejeitos = $this->getResiduosRejeitos(
            $result['residuos_rejeitos'],
            $result['residuos_rejeitos_descricao']
        );
        $instalacoesFisicasAssistencia = $this->getInstalacoesFisicasAssistencia(
            $result['instalacoes'],
            $result['instalacoes_descricao'],
            $atendimentoPrestado
        );
        $servicoApoio = $this->getServicoApoio(
            $result['servico_apoio'],
            $result['servico_apoio_descricao']
        );
        $servicoEspecializado = $this->getServicoEspecializado(
            $result['rel_servico_especializado'],
            $result['servico_especializado'],
        );
        $servicoClassificacao = $this->getServicoClassificacao($servicoEspecializado);

        $dados = array_merge(
            $identificacao,
            $profissionaisSusNaoSus,
            $atendimentoPrestado,
            $equipamentos,
            $residuosRejeitos,
            $instalacoesFisicasAssistencia,
            $servicoApoio,
            $servicoEspecializado,
            $servicoClassificacao
        );

        return Str::encoding($dados);
    }

    private function getServicoClassificacao($servicosEspecializados)
    {
        $parsedData = [];

        foreach ($servicosEspecializados["servico_especializado"] as $servicoEspecializado) {
            $parsedData[] = [
                'cod'  => $servicoEspecializado['cod'],
                'servico'  => $servicoEspecializado['servico'],
                'caracteristica'  => $servicoEspecializado['servico_descricao']
            ];
        }
        return ['servico_classificacao' => $parsedData];
    }

    private function getServicoEspecializado(
        $relServicosEspecializados,
        $servicoEspecializado
    ) {
        if (!$relServicosEspecializados) {
            return ['servico_especializado' => []];
        }

        $manager = (new MongoDB())
                ->connectSources()
                ->setDb('cnes')
                ->setCollection('cnes_classificacao_servico');

        $caracterestica = [
            '1' => "PROPRIO",
            '2' => "TERCEIRIZADO"
        ];
        $parsedData = [];

        foreach ($relServicosEspecializados as $key => $relacaoServico) {
            foreach ($servicoEspecializado as $key => $servico) {
                if (
                    $servico['codigo'] ==
                    $relacaoServico['codigo_servico']
                ) {
                     $classificacao = $manager->query('find', [
                        [
                            'codigo' => $relacaoServico['codigo_classificacao'],
                            'codigo_servico_especializado' => $relacaoServico['codigo_servico']
                        ]
                     ])->toArray()[0];

                    $parsedData['servico_especializado'][] = [
                        'cod' => "{$classificacao['codigo_servico_especializado']}" .
                           " - " . "{$classificacao['codigo']}",
                        'servico' => $servico['descricao'],
                        'servico_descricao' => $classificacao['descricao'],
                        'caracteristica' => $caracterestica[
                            $relacaoServico['tipo_caracteristica']
                        ],
                        'Ambulatorial' => [
                            'SUS' => $relacaoServico['codigo_ambulatorial_sus'] == '1' ?
                                'SIM' : 'NÃO',
                            'NAO SUS' => $relacaoServico['codigo_ambulatorial'] == '1' ?
                                'SIM' : 'NÃO'
                        ],
                        'Hospitalar' => [
                            'SUS' => $relacaoServico['codigo_hospitalar_sus'] == '1' ?
                                'SIM' : 'NÃO',
                            'NAO SUS' => $relacaoServico['codigo_hospitalar'] == '1' ?
                                'SIM' : 'NÃO'
                        ],

                    ];
                    break;
                }
            }
        }

        return $parsedData;
    }

    private function getServicoApoio($servicosApoio, $servicosApoioDescricao)
    {
        if (!$servicosApoio) {
            return ['servico_apoio' => []];
        }

        $caracterestica = [
            '1' => "PROPRIO",
            '2' => "TERCEIRIZADO"
        ];

        $parsedData = [];
        foreach ($servicosApoio as $key => $servico) {
            foreach ($servicosApoioDescricao as $descricao) {
                if (
                    $descricao['codigo'] ==
                    $servico['codigo_servico_apoio']
                ) {
                    $parsedData['servico_apoio'][] = [
                        'cod' => $servico['codigo_servico_apoio'],
                        'servico' => $descricao['descricao_apoio'],
                        'caracteristica' => $caracterestica[$servico['codigo_caracteristica']]

                    ];
                    break;
                }
            }
        }

        return $parsedData;
    }

    private function getInstalacoesFisicasAssistencia(
        $instalacoes,
        $instalacoesDescricao,
        $atendimentoPrestado
    ) {

        if (!$instalacoes) {
            return ['instalacoes_fisicas_para_assistencia' => []];
        }

        $parsedData['instalacoes_fisicas_para_assistencia'] = [];
        foreach ($instalacoes as $instalacao) {
            foreach ($instalacoesDescricao as $descricao) {
                if (
                    $descricao['codigo_instalacao'] ==
                    $instalacao['codigo_instalacao']
                ) {
                    foreach ($atendimentoPrestado['atendimento_prestado'] as $key => $value) {
                        $data = [
                            'instalacoes' => [
                                $value['tipo_atendimento'] => [
                                    'descricao' => $descricao['descricao_instalacao'],
                                    'qtd_consultorio' => $instalacao['quantidade_instalacao'],
                                    'leitos_equipos' => $instalacao['numero_leitos']
                                ]
                            ]
                        ];
                        $parsedData['instalacoes_fisicas_para_assistencia'][] = $data['instalacoes'];
                    }
                    break;
                }
            }
        }

        return $parsedData;
    }

    private function getResiduosRejeitos($coletasRejeitos, $coletasRejeitosDescricao)
    {

        if (!$coletasRejeitos) {
            return ['residuos_rejeitos' => []];
        }

        $parsedData = [];

        foreach ($coletasRejeitos as $key => $Rejeito) {
            foreach ($coletasRejeitosDescricao as $descricao) {
                if (
                    $descricao['codigo_coleta_regeito'] ==
                    $Rejeito['codigo_coleta_rejeito']
                ) {
                    break;
                }
            }

            array_push($parsedData, $descricao['descricao_coleta_rejeito']);
        }

        return ['residuos_rejeitos' => $parsedData];
    }

    private function getEquipamentos($items, $equipamentosDescricao)
    {
        if (empty($items)) {
            return [
                'equipamentos' => [
                    'equipamentos_de_diagnostico_por_imagem' => [],
                    'equipamentos_de_infra-estrutura' => [],
                    'equipamentos_para_manutencao_da_vida' => []
                ]

            ];
        }

        foreach ($items as $key => $item) {
            foreach ($equipamentosDescricao as $descricao) {
                if (
                    $descricao['codigo'] ==
                    $item['codigo_equipamento']
                ) {
                    $item['nome_equipamento'] = $descricao['descricao_equipamento'];
                    break;
                }
            }
            $equipamentos[] = $item;
        }

        $arrEquipamentos = [

            '1' => 'equipamentos_de_diagnostico_por_imagem',
            '2' => 'equipamentos_de_infra-estrutura',
            '5' => 'equipamentos_para_manutencao_da_vida'

        ];

        $parsedData = [
            'equipamentos_de_diagnostico_por_imagem' => [],
            'equipamentos_de_infra-estrutura' => [],
            'equipamentos_para_manutencao_da_vida' => []
        ];

        foreach ($equipamentos as $equipamento) {
            if (key_exists($equipamento['codigo_equipamento'], $arrEquipamentos)) {
                array_push(
                    $parsedData[$arrEquipamentos[$equipamento['codigo_equipamento']]],
                    [
                        'equipamento' => $equipamento['nome_equipamento'],
                        'existente' => $equipamento['quantidade_existente'],
                        'em_uso' => $equipamento['quantidade_em_uso'],
                        'sus' => $equipamento['tipo_sus']
                    ]
                );
            }
        }

        return ['equipamentos' => $parsedData];
    }

    private function getAtendimentoPrestado($atendimentos, $tiposAtendimentos, $convenios)
    {
        $atendimentosPrestados = [];

        foreach ($atendimentos as $key => $atendimento) {
            $atendimentosPrestados[$key]['codigo_atendimento_prestado'] = $atendimento['codigo_atendimento_prestado'];
            foreach ($tiposAtendimentos as $tipo) {
                if (
                    $tipo['codigo_atendimento'] ==
                    $atendimento['codigo_atendimento_prestado']
                ) {
                    break;
                }
            }

            foreach ($convenios as $convenio) {
                if (
                    $convenio['codigo_convenio'] ==
                    $atendimento['codigo_convenio']
                ) {
                    break;
                }
            }

            $atendimentosPrestados[$key]['codigo_unidade'] = $atendimento['codigo_unidade'];
            $atendimentosPrestados[$key]['codigo_usuario'] = $atendimento['codigo_usuario'];
            $atendimentosPrestados[$key]['codigo_atendimento'] = $tipo['codigo_atendimento'];
            $atendimentosPrestados[$key]['codigo_convenio'] = $convenio['codigo_convenio'];
            $atendimentosPrestados[$key]['descricao_convenio'] = $convenio[
                'descricao_convenio'
            ];
            $atendimentosPrestados[$key]['descricao_atendimento'] = $tipo['descricao_atendimento'];
        }


        foreach ($atendimentosPrestados as $value) {
            $parsedData['atendimento_prestado'][] = [
                'tipo_atendimento' => $value['descricao_atendimento'],
                'convenio' => $value['descricao_convenio']
            ];
        }

        return $parsedData;
    }

    private function getProfissionais($professional)
    {

        $countSus = 0;
        $countNaoSus = 0;

        foreach ($professional as $value) {
            if ($value['sus_nao_sus'] == 'S') {
                $countSus++;
                continue;
            }

            $countNaoSus++;
        }

        return [
            'profissionais_sus' => [
                'medico' => 'Total',
                'qtd' =>  $countSus
            ],
            'profissionais_nao_sus' => [
                'medico' => 'Total',
                'qtd' =>  $countNaoSus
            ]
        ];
    }

    private function getIdentificacao($result)
    {
        $natJuridica = [
            '1000' => 'ADMINISTRAÇÃO PÚBLICA',
            '2000' => 'ENTIDADES EMPRESARIAIS',
            '3000' => 'ENTIDADES SEM FINS LUCRATIVOS',
            '4000' => 'ORGANIZAÇÕES INTERNACIONAIS/OUTRAS'
        ];

        $arrIdentificacao['identificacao'] = [
            'codigo_unidade' => $result['codigo_unidade'],
            'nome' => $result['nome'],
            'cnes' => $result['cnes'],
            'cnpj' => $result['cnpj'] == 'NULL' ? '' : $result['cnpj'],
            'cpf' => $result['cpf']  == 'NULL' ? '' : $result['cpf'],
            'nome_empresarial' => $result['nome_empresarial'],
            'logradouro' => $result['logradouro'],
            'numero' => $result['numero'],
            'complemento' => $result['complemento'],
            'bairro' => $result['bairro'],
            'cep' => $result['cep'],
            'ibge' => $result['ibge'],
            'municipio' => $result['municipio'],
            'uf' => $result['uf'],
            'tipo_unidade' => $result['tipo_unidade'],
            'gestao' => $result['gestao'],
            'dependencia' => $result['dependencia'],
            'natureza_juridica' => isset($natJuridica[$result['natureza_juridica']])
                ? $natJuridica[$result['natureza_juridica']]
                : '',
        ];

        return $arrIdentificacao;
    }

    private function setQuery($fields)
    {
        $query[] = [
            '$search' => [
                'index' => self::INDEX_MONGODB,
                'text' => [
                        'query' => $this->param['searchParams'],
                        'path' => $fields
                    ]
            ]
        ];

        $query[] = [
            '$lookup' => [
                'localField' => 'codigo_municipio',
                'from' => 'cnes_municios',
                'foreignField' => 'codigo_municipio',
                'as' => 'municipio'
            ]
        ];
        $query[] = [
            '$lookup' => [
                'localField' => 'codigo_unidade',
                'from' => 'cnes_profissional_sus_nao_sus',
                'foreignField' => 'codigo_unidade',
                'as' => 'profissionais'
            ]
        ];

        $query[] = [
            '$lookup' => [
                'localField' => 'codigo_unidade',
                'from' => 'cnes_atendimento_convenio',
                'foreignField' => 'codigo_unidade',
                'as' => 'atendimentos'
            ]
        ];

        $query[] = [
            '$lookup' => [
                'localField' => 'atendimentos.codigo_atendimento_prestado',
                'from' => 'cnes_descricao_atendimento_prestado',
                'foreignField' => 'codigo_atendimento',
                'as' => 'tipo_atendimento'
            ]
        ];

        $query[] = [
            '$lookup' => [
                'localField' => 'atendimentos.codigo_convenio',
                'from' => 'cnes_convenio',
                'foreignField' => 'codigo_convenio',
                'as' => 'convenio'
            ]
        ];

        $query[] = [
            '$lookup' => [
                'localField' => 'codigo_unidade',
                'from' => 'cnes_equipamento',
                'foreignField' => 'codigo_unidade',
                'as' => 'equipamentos'
            ]
        ];

        $query[] = [
            '$lookup' => [
                'localField' => 'equipamentos.codigo_equipamento',
                'from' => 'cnes_equipamento_descricao_composto',
                'foreignField' => 'codigo',
                'as' => 'equipamentos_descricao'
            ]
        ];

        $query[] = [
            '$lookup' => [
                'localField' => 'codigo_unidade',
                'from' => 'cnes_estabelecimento_coleta_seletiva_rejeito',
                'foreignField' => 'codigo_unidade',
                'as' => 'residuos_rejeitos'
            ]
        ];

        $query[] = [
            '$lookup' => [
                'localField' => 'residuos_rejeitos.codigo_coleta_rejeito',
                'from' => 'cnes_coleta_seletiva_rejeito',
                'foreignField' => 'codigo_coleta_regeito',
                'as' => 'residuos_rejeitos_descricao'
            ]
        ];


        $query[] = [
            '$lookup' => [
                'localField' => 'codigo_unidade',
                'from' => 'cnes_instalacao_fisica',
                'foreignField' => 'codigo_unidade',
                'as' => 'instalacoes'
            ]
        ];


        $query[] = [
            '$lookup' => [
                'localField' => 'instalacoes.codigo_instalacao',
                'from' => 'cnes_instalacao_fisica_descricao',
                'foreignField' => 'codigo_instalacao',
                'as' => 'instalacoes_descricao'
            ]
        ];


        $query[] = [
            '$lookup' => [
                'localField' => 'codigo_unidade',
                'from' => 'cnes_servico_apoio',
                'foreignField' => 'codigo_unidade',
                'as' => 'servico_apoio'
            ]
        ];


        $query[] = [
            '$lookup' => [
                'localField' => 'servico_apoio.codigo_servico_apoio',
                'from' => 'cnes_descricao_servico_apoio',
                'foreignField' => 'codigo',
                'as' => 'servico_apoio_descricao'
            ]
        ];

        $query[] = [
            '$lookup' => [
                'localField' => 'codigo_unidade',
                'from' => 'cnes_relacao_servico_especializado',
                'foreignField' => 'codigo_unidade',
                'as' => 'rel_servico_especializado'
            ]
        ];

        $query[] = [
            '$lookup' => [
                'localField' => 'rel_servico_especializado.codigo_servico',
                'from' => 'cnes_servico_especializado',
                'foreignField' => 'codigo',
                'as' => 'servico_especializado'
            ]
        ];

        $query[] = [
             '$project' => [
                'codigo_unidade' => '$codigo_unidade',
                'nome' => '$nome_fantasia',
                'cnes' => '$codigo_cnes',
                'cnpj' => '$cnpj',
                'cpf' => '$cpf',
                'nome_empresarial' => '$nome_razao_social',
                'logradouro' => '$rua',
                'numero' => '$numero_endereco',
                'complemento' => '$complemento',
                'bairro' => '$bairro',
                'cep' => '$cep',
                'ibge' => '$codigo_municipio',
                'uf' => [ '$first' => '$municipio.uf' ],
                'municipio' => [ '$first' => '$municipio.nome_municipio' ],
                'tipo_unidade' => '$tipo_unidade',
                'gestao' => '$gestao',
                'dependencia' => '$dependencia',
                'natureza_juridica' => '$codigo_natureza_juridica',
                'profissionais' => '$profissionais',
                'atendimentos' => '$atendimentos',
                'tipo_atendimento' => '$tipo_atendimento',
                'convenio' => '$convenio',
                'equipamentos' => '$equipamentos',
                'equipamentos_descricao' => '$equipamentos_descricao',
                'residuos_rejeitos' => '$residuos_rejeitos',
                'residuos_rejeitos_descricao' => '$residuos_rejeitos_descricao',
                'instalacoes' => '$instalacoes',
                'instalacoes_descricao' => '$instalacoes_descricao',
                'servico_apoio' => '$servico_apoio',
                'servico_apoio_descricao' => '$servico_apoio_descricao',
                'rel_servico_especializado' => '$rel_servico_especializado',
                'servico_especializado' => '$servico_especializado'

             ]

        ];

        $query[] = ['$limit' => $this->limit];
        return $query;
    }
}
