<?php

namespace App\Crawler\ListaEuropeia\Traits;

trait DataSetTrait
{
    public function getProps()
    {
        return array_keys(get_object_vars($this));
    }

    public function autoSet($row)
    {
        foreach ($this->getProps() as $prop) {
            if (isset($row[$prop])) {
                $this->{$prop} = $row[$prop];
            }
        }
    }

    public function toArray()
    {

        $data = [];

        foreach ($this->getProps() as $prop) {
            $data[$prop] = $this->{$prop};
        }

        return $data;
    }
}
