<?php

namespace App\Crawler\ListaEuropeia\vo;

use App\Crawler\ListaEuropeia\Traits\DataSetTrait;

class ListaEuropeiaAddress
{
    use DataSetTrait;

    /**
     * Representa a id_address
     *
     * @var string
     */
    private $id_address;

    /**
     * Representa a id_entity
     *
     * @var string
     */
    private $id_entity;

    /**
     * Representa a legal_basis
     *
     * @var string
     */
    private $legal_basis;

    /**
     * Representa a reg_date
     *
     * @var string
     */
    private $reg_date;

    /**
     * Representa a pdf_link
     *
     * @var string
     */
    private $pdf_link;

    /**
     * Representa a programme
     *
     * @var string
     */
    private $programme;

    /**
     * Representa a numero
     *
     * @var string
     */
    private $numero;

    /**
     * Representa a street
     *
     * @var string
     */
    private $street;

    /**
     * Representa a zipcode
     *
     * @var string
     */
    private $zipcode;

    /**
     * Representa a city
     *
     * @var string
     */
    private $city;

    /**
     * Representa a country
     *
     * @var string
     */
    private $country;

    /**
     * Representa a other
     *
     * @var string
     */
    private $other;

    /**
     * Representa a data_atualizacao
     *
     * @var string
     */
    private $data_atualizacao;

    public function getIdAddress()
    {
        return $this->id_address;
    }

    public function getIdEntity()
    {
        return $this->id_entity;
    }

    public function getLegalBasis()
    {
        return $this->legal_basis;
    }

    public function getRegDate()
    {
        return $this->reg_date;
    }

    public function getPdfLink()
    {
        return $this->pdf_link;
    }

    public function getProgramme()
    {
        return $this->programme;
    }

    public function getNumero()
    {
        return $this->numero;
    }

    public function getStreet()
    {
        return $this->street;
    }

    public function getZipcode()
    {
        return $this->zipcode;
    }

    public function getCity()
    {
        return $this->city;
    }

    public function getCountry()
    {
        return $this->country;
    }

    public function getOther()
    {
        return $this->other;
    }

    public function getDataAtualizacao()
    {
        return $this->data_atualizacao;
    }

    public function setIdAddress($v)
    {
        $this->id_address = $v;
    }

    public function setIdEntity($v)
    {
        $this->id_entity = $v;
    }

    public function setLegalBasis($v)
    {
        $this->legal_basis = $v;
    }

    public function setRegDate($v)
    {
        $this->reg_date = $v;
    }

    public function setPdfLink($v)
    {
        $this->pdf_link = $v;
    }

    public function setProgramme($v)
    {
        $this->programme = $v;
    }

    public function setNumero($v)
    {
        $this->numero = $v;
    }

    public function setStreet($v)
    {
        $this->street = $v;
    }

    public function setZipcode($v)
    {
        $this->zipcode = $v;
    }

    public function setCity($v)
    {
        $this->city = $v;
    }

    public function setCountry($v)
    {
        $this->country = $v;
    }

    public function setOther($v)
    {
        $this->other = $v;
    }

    public function setDataAtualizacao($v)
    {
        $this->data_atualizacao = $v;
    }

    public function autoSet($row)
    {
        $props = [
            'id_address',
            'id_entity',
            'legal_basis',
            'reg_date',
            'pdf_link',
            'programme',
            'numero',
            'street',
            'zipcode',
            'city',
            'country',
            'other',
            'data_atualizacao'
        ];

        foreach ($props as $prop) {
            $this->{$prop} = $row[$prop];
        }
    }
}
