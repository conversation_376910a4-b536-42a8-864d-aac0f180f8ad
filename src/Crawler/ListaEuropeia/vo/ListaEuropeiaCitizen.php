<?php

namespace App\Crawler\ListaEuropeia\vo;

use App\Crawler\ListaEuropeia\Traits\DataSetTrait;

class ListaEuropeiaCitizen
{
    use DataSetTrait;

    /**
     * Representa a id_citizen
     *
     * @var string
     */
    private $id_citizen;

    /**
     * Representa a id_entity
     *
     * @var string
     */
    private $id_entity;

    /**
     * Representa a legal_basis
     *
     * @var string
     */
    private $legal_basis;

    /**
     * Representa a reg_date
     *
     * @var string
     */
    private $reg_date;

    /**
     * Representa a pdf_link
     *
     * @var string
     */
    private $pdf_link;

    /**
     * Representa a programme
     *
     * @var string
     */
    private $programme;

    /**
     * Representa a country
     *
     * @var string
     */
    private $country;

    /**
     * Representa a data_atualizacao
     *
     * @var string
     */
    private $data_atualizacao;

    public function getIdCitizen()
    {
        return $this->id_citizen;
    }

    public function getIdEntity()
    {
        return $this->id_entity;
    }

    public function getLegalBasis()
    {
        return $this->legal_basis;
    }

    public function getRegDate()
    {
        return $this->reg_date;
    }

    public function getPdfLink()
    {
        return $this->pdf_link;
    }

    public function getProgramme()
    {
        return $this->programme;
    }

    public function getCountry()
    {
        return $this->country;
    }

    public function getDataAtualizacao()
    {
        return $this->data_atualizacao;
    }

    public function setIdCitizen($v)
    {
        $this->id_citizen = $v;
    }

    public function setIdEntity($v)
    {
        $this->id_entity = $v;
    }

    public function setLegalBasis($v)
    {
        $this->legal_basis = $v;
    }

    public function setRegDate($v)
    {
        $this->reg_date = $v;
    }

    public function setPdfLink($v)
    {
        $this->pdf_link = $v;
    }

    public function setProgramme($v)
    {
        $this->programme = $v;
    }

    public function setCountry($v)
    {
        $this->country = $v;
    }

    public function setDataAtualizacao($v)
    {
        $this->data_atualizacao = $v;
    }

    public function autoSet($row)
    {
        $props = [
            'id_citizen',
            'id_entity',
            'legal_basis',
            'reg_date',
            'pdf_link',
            'programme',
            'country',
            'data_atualizacao'
        ];

        foreach ($props as $prop) {
            $this->{$prop} = $row[$prop];
        }
    }
}
