<?php

namespace App\Crawler\ListaEuropeia\vo;

use App\Crawler\ListaEuropeia\Traits\DataSetTrait;

class ListaEuropeiaBirth
{
    use DataSetTrait;

    /**
     * Representa a id_birth
     *
     * @var string
     */
    private $id_birth;

    /**
     * Representa a id_entity
     *
     * @var string
     */
    private $id_entity;

    /**
     * Representa a legal_basis
     *
     * @var string
     */
    private $legal_basis;

    /**
     * Representa a reg_date
     *
     * @var string
     */
    private $reg_date;

    /**
     * Representa a pdf_link
     *
     * @var string
     */
    private $pdf_link;

    /**
     * Representa a programme
     *
     * @var string
     */
    private $programme;

    /**
     * Representa a data
     *
     * @var string
     */
    private $data;

    /**
     * Representa a place
     *
     * @var string
     */
    private $place;

    /**
     * Representa a country
     *
     * @var string
     */
    private $country;

    /**
     * Representa a data_atualizacao
     *
     * @var string
     */
    private $data_atualizacao;

    public function getIdBirth()
    {
        return $this->id_birth;
    }

    public function getIdEntity()
    {
        return $this->id_entity;
    }

    public function getLegalBasis()
    {
        return $this->legal_basis;
    }

    public function getRegDate()
    {
        return $this->reg_date;
    }

    public function getPdfLink()
    {
        return $this->pdf_link;
    }

    public function getProgramme()
    {
        return $this->programme;
    }

    public function getData()
    {
        return $this->data;
    }

    public function getPlace()
    {
        return $this->place;
    }

    public function getCountry()
    {
        return $this->country;
    }

    public function getDataAtualizacao()
    {
        return $this->data_atualizacao;
    }

    public function setIdBirth($v)
    {
        $this->id_birth = $v;
    }

    public function setIdEntity($v)
    {
        $this->id_entity = $v;
    }

    public function setLegalBasis($v)
    {
        $this->legal_basis = $v;
    }

    public function setRegDate($v)
    {
        $this->reg_date = $v;
    }

    public function setPdfLink($v)
    {
        $this->pdf_link = $v;
    }

    public function setProgramme($v)
    {
        $this->programme = $v;
    }

    public function setData($v)
    {
        $this->data = $v;
    }

    public function setPlace($v)
    {
        $this->place = $v;
    }

    public function setCountry($v)
    {
        $this->country = $v;
    }

    public function setDataAtualizacao($v)
    {
        $this->data_atualizacao = $v;
    }

    public function autoSet($row)
    {
        $props = [
            'id_birth',
            'id_entity',
            'legal_basis',
            'reg_date',
            'pdf_link',
            'programme',
            'data',
            'place',
            'country',
            'data_atualizacao'
        ];

        foreach ($props as $prop) {
            $this->{$prop} = $row[$prop];
        }
    }
}
