<?php

namespace App\Crawler\ListaEuropeia\vo;

use App\Crawler\ListaEuropeia\Traits\DataSetTrait;

class ListaEuropeiaName
{
    use DataSetTrait;

    /**
     * Representa a id_name
     *
     * @var string
     */
    private $id_name;

    /**
     * Representa a id_entity
     *
     * @var string
     */
    private $id_entity;

    /**
     * Representa a legal_basis
     *
     * @var string
     */
    private $legal_basis;

    /**
     * Representa a reg_date
     *
     * @var string
     */
    private $reg_date;

    /**
     * Representa a pdf_link
     *
     * @var string
     */
    private $pdf_link;

    /**
     * Representa a programme
     *
     * @var string
     */
    private $programme;

    /**
     * Representa a last_name
     *
     * @var string
     */
    private $last_name;

    /**
     * Representa a first_name
     *
     * @var string
     */
    private $first_name;

    /**
     * Representa a middle_name
     *
     * @var string
     */
    private $middle_name;

    /**
     * Representa a whole_name
     *
     * @var string
     */
    private $whole_name;

    /**
     * Representa a gender
     *
     * @var string
     */
    private $gender;

    /**
     * Representa a title
     *
     * @var string
     */
    private $title;

    /**
     * Representa a funcao
     *
     * @var string
     */
    private $funcao;

    /**
     * Representa a language
     *
     * @var string
     */
    private $language;

    /**
     * Representa a data_atualizacao
     *
     * @var string
     */
    private $data_atualizacao;

    public function getIdName()
    {
        return $this->id_name;
    }

    public function getIdEntity()
    {
        return $this->id_entity;
    }

    public function getLegalBasis()
    {
        return $this->legal_basis;
    }

    public function getRegDate()
    {
        return $this->reg_date;
    }

    public function getPdfLink()
    {
        return $this->pdf_link;
    }

    public function getProgramme()
    {
        return $this->programme;
    }

    public function getLastName()
    {
        return $this->last_name;
    }

    public function getFirstName()
    {
        return $this->first_name;
    }

    public function getMiddleName()
    {
        return $this->middle_name;
    }

    public function getWholeName()
    {
        return $this->whole_name;
    }

    public function getGender()
    {
        return $this->gender;
    }

    public function getTitle()
    {
        return $this->title;
    }

    public function getFuncao()
    {
        return $this->funcao;
    }

    public function getLanguage()
    {
        return $this->language;
    }

    public function getDataAtualizacao()
    {
        return $this->data_atualizacao;
    }

    public function setIdName($v)
    {
        $this->id_name = $v;
    }

    public function setIdEntity($v)
    {
        $this->id_entity = $v;
    }

    public function setLegalBasis($v)
    {
        $this->legal_basis = $v;
    }

    public function setRegDate($v)
    {
        $this->reg_date = $v;
    }

    public function setPdfLink($v)
    {
        $this->pdf_link = $v;
    }

    public function setProgramme($v)
    {
        $this->programme = $v;
    }

    public function setLastName($v)
    {
        $this->last_name = $v;
    }

    public function setFirstName($v)
    {
        $this->first_name = $v;
    }

    public function setMiddleName($v)
    {
        $this->middle_name = $v;
    }

    public function setWholeName($v)
    {
        $this->whole_name = $v;
    }

    public function setGender($v)
    {
        $this->gender = $v;
    }

    public function setTitle($v)
    {
        $this->title = $v;
    }

    public function setFuncao($v)
    {
        $this->funcao = $v;
    }

    public function setLanguage($v)
    {
        $this->language = $v;
    }

    public function setDataAtualizacao($v)
    {
        $this->data_atualizacao = $v;
    }

    public function autoSet($row)
    {
        $props = [
            'id_name',
            'id_entity',
            'legal_basis',
            'reg_date',
            'pdf_link',
            'programme',
            'last_name',
            'first_name',
            'middle_name',
            'whole_name',
            'gender',
            'title',
            'funcao',
            'language',
            'data_atualizacao'
        ];

        foreach ($props as $prop) {
            $this->{$prop} = $row[$prop];
        }
    }
}
