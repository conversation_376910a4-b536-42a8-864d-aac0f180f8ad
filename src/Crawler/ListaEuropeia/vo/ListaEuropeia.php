<?php

namespace App\Crawler\ListaEuropeia\vo;

use App\Crawler\ListaEuropeia\Traits\DataSetTrait;

class ListaEuropeia
{
    use DataSetTrait;

    /**
     * Representa a id_entity
     *
     * @var string
     */
    private $id_entity;

    /**
     * Representa a type
     *
     * @var string
     */
    private $type;

    /**
     * Representa a legal_basis
     *
     * @var string
     */
    private $legal_basis;

    /**
     * Representa a reg_date
     *
     * @var string
     */
    private $reg_date;

    /**
     * Representa a pdf_link
     *
     * @var string
     */
    private $pdf_link;

    /**
     * Representa a programme
     *
     * @var string
     */
    private $programme;

    /**
     * Representa a remarks
     *
     * @var string
     */
    private $remarks;

    /**
     * Representa a data_cadastro
     *
     * @var string
     */
    private $data_cadastro;

    /**
     * Representa a data_atualizacao
     *
     * @var string
     */
    private $data_atualizacao;

    /**
     * Representa a aName
     *
     * @var ListaEuropeiaName[]
     */
    private $aName;

    /**
     * Representa a aAddress
     *
     * @var ListaEuropeiaAddress[]
     */
    private $aAddress;

    /**
     * Representa a aBirth
     *
     * @var ListaEuropeiaBirth[]
     */
    private $aBirth;

    /**
     * Representa a aPassport
     *
     * @var ListaEuropeiaPassport[]
     */
    private $aPassport;

    /**
     * Representa a aCitizen
     *
     * @var ListaEuropeiaCitizen[]
     */
    private $aCitizen;

    public function __construct()
    {
        $this->aName = [];
        $this->aAddress = [];
        $this->aBirth = [];
        $this->aPassport = [];
        $this->aCitizen = [];
    }

    public function getIdEntity()
    {
        return $this->id_entity;
    }

    public function getType()
    {
        return $this->type;
    }

    public function getLegalBasis()
    {
        return $this->legal_basis;
    }

    public function getRegDate()
    {
        return $this->reg_date;
    }

    public function getPdfLink()
    {
        return $this->pdf_link;
    }

    public function getProgramme()
    {
        return $this->programme;
    }

    public function getRemarks()
    {
        return $this->remarks;
    }

    public function getDataCadastro()
    {
        return $this->data_cadastro;
    }

    public function getDataAtualizacao()
    {
        return $this->data_atualizacao;
    }

    public function getAName()
    {
        return $this->aName;
    }

    public function getAAddress()
    {
        return $this->aAddress;
    }

    public function getABirth()
    {
        return $this->aBirth;
    }

    public function getAPassport()
    {
        return $this->aPassport;
    }

    public function getACitizen()
    {
        return $this->aCitizen;
    }

    public function setIdEntity($v)
    {
        $this->id_entity = $v;
    }

    public function setType($v)
    {
        $this->type = $v;
    }

    public function setLegalBasis($v)
    {
        $this->legal_basis = $v;
    }

    public function setRegDate($v)
    {
        $this->reg_date = $v;
    }

    public function setPdfLink($v)
    {
        $this->pdf_link = $v;
    }

    public function setProgramme($v)
    {
        $this->programme = $v;
    }

    public function setRemarks($v)
    {
        $this->remarks = $v;
    }

    public function setDataCadastro($v)
    {
        $this->data_cadastro = $v;
    }

    public function setDataAtualizacao($v)
    {
        $this->data_atualizacao = $v;
    }

    public function setAName($v)
    {
        $this->aName = $v;
    }

    public function setAAddress($v)
    {
        $this->aAddress = $v;
    }

    public function setABirth($v)
    {
        $this->aBirth = $v;
    }

    public function setAPassport($v)
    {
        $this->aPassport = $v;
    }

    public function setACitizen($v)
    {
        $this->aCitizen = $v;
    }
}
