<?xml version="1.0" encoding="UTF-8"?>
<project name='lista_europeia'>
	<class name="ListaEuropeia">
		<prop name="id_entity" type="string" />
		<prop name="type" type="string" />
		<prop name="legal_basis" type="string" />
		<prop name="reg_date" type="string" />
		<prop name="pdf_link" type="string" />
		<prop name="programme" type="string" />
		<prop name="remarks" type="string" />
		<prop name="data_cadastro" type="string" />	
		<prop name="data_atualizacao" type="string" />
		<prop name="aName" type="ListaEuropeiaName[]" />
		<prop name="aAddress" type="ListaEuropeiaAddress[]" />
		<prop name="aBirth" type="ListaEuropeiaBirth[]" />
		<prop name="aPassport" type="ListaEuropeiaPassport[]" />
		<prop name="aCitizen" type="ListaEuropeiaCitizen[]" />
	</class>
	<class name="ListaEuropeiaName">
		<prop name="id_name" type="string" />
		<prop name="id_entity" type="string" />
		<prop name="legal_basis" type="string" />
		<prop name="reg_date" type="string" />
		<prop name="pdf_link" type="string" />
		<prop name="programme" type="string" />
		<prop name="last_name" type="string" />	
		<prop name="first_name" type="string" />
		<prop name="middle_name" type="string" />	
		<prop name="whole_name" type="string" />
		<prop name="gender" type="string" />	
		<prop name="title" type="string" />
		<prop name="funcao" type="string" />	
		<prop name="language" type="string" />
		<prop name="data_atualizacao" type="string" />
	</class>
	<class name="ListaEuropeiaAddress">
		<prop name="id_address" type="string" />
		<prop name="id_entity" type="string" />
		<prop name="legal_basis" type="string" />
		<prop name="reg_date" type="string" />
		<prop name="pdf_link" type="string" />
		<prop name="programme" type="string" />
		<prop name="numero" type="string" />	
		<prop name="street" type="string" />
		<prop name="zipcode" type="string" />
		<prop name="city" type="string" />
		<prop name="country" type="string" />
		<prop name="other" type="string" />
		<prop name="data_atualizacao" type="string" />
	</class>
	<class name="ListaEuropeiaBirth">
		<prop name="id_birth" type="string" />
		<prop name="id_entity" type="string" />
		<prop name="legal_basis" type="string" />
		<prop name="reg_date" type="string" />
		<prop name="pdf_link" type="string" />
		<prop name="programme" type="string" />
		<prop name="data" type="string" />	
		<prop name="place" type="string" />
		<prop name="country" type="string" />
		<prop name="data_atualizacao" type="string" />
	</class>
	<class name="ListaEuropeiaPassport">
		<prop name="id_passport" type="string" />
		<prop name="id_entity" type="string" />
		<prop name="legal_basis" type="string" />
		<prop name="reg_date" type="string" />
		<prop name="pdf_link" type="string" />
		<prop name="programme" type="string" />
		<prop name="numero" type="string" />
		<prop name="country" type="string" />
		<prop name="data_atualizacao" type="string" />
	</class>
		<class name="ListaEuropeiaCitizen">
		<prop name="id_citizen" type="string" />
		<prop name="id_entity" type="string" />
		<prop name="legal_basis" type="string" />
		<prop name="reg_date" type="string" />
		<prop name="pdf_link" type="string" />
		<prop name="programme" type="string" />
		<prop name="country" type="string" />
		<prop name="data_atualizacao" type="string" />
	</class>
</project>