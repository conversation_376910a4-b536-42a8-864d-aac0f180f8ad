<?php

namespace App\Crawler\ListaEuropeia;

use App\Factory\MongoDB;
use Exception;
use App\Crawler\Spider;

class ListaEuropeia extends Spider
{
    private const INDEX_MONGODB = [
        'xml_name' => 'search_name',
        'xmls' => 'id_entity'
    ];
    private const LIMIT = 50;

    private const COLLECTIONS = ['xml','xml_name', 'xml_address', 'xml_birth', 'xml_passport', 'xml_citizen'];

    private const ARRAY_NAMES = [
        'xml_name' => 'aName',
        'xml_address' => 'aAddress',
        'xml_birth' => 'aBirth',
        'xml_passport' => 'aPassport',
        'xml_citizen' => 'aCitizen'
    ];

    private const FIELDS = [
        'xml' => [
            'id_entity',
            'type',
            'legal_basis',
            'reg_date',
            'pdf_link',
            'programme',
            'remarks',
            'data_cadastro',
            'data_atualizacao',
        ],
        'xml_name' => [
            'id_name',
            'id_entity',
            'legal_basis',
            'reg_date',
            'pdf_link',
            'programme',
            'last_name',
            'first_name',
            'middle_name',
            'whole_name',
            'gender',
            'title',
            'funcao',
            'language',
            'data_atualizacao'
        ],
        'xml_address' => [
            'id_address',
            'id_entity',
            'legal_basis',
            'reg_date',
            'pdf_link',
            'programme',
            'numero',
            'street',
            'zipcode',
            'city',
            'country',
            'other',
            'data_atualizacao'
        ],
        'xml_birth' => [
            'id_birth',
            'id_entity',
            'legal_basis',
            'reg_date',
            'pdf_link',
            'programme',
            'data',
            'place',
            'country',
            'data_atualizacao'
        ],
        'xml_passport' => [
            'id_passport',
            'id_entity',
            'legal_basis',
            'reg_date',
            'pdf_link',
            'programme',
            'numero',
            'country',
            'data_atualizacao'
        ],
        'xml_citizen' => [
            'id_citizen',
            'id_entity',
            'legal_basis',
            'reg_date',
            'pdf_link',
            'programme',
            'country',
            'data_atualizacao'
        ],
    ];

    private string $criterio = '';

    protected function validateAndSetCrawlerAttributes()
    {
        $this->criterio = trim(trim($this->param['nome']));
        $this->criterio = preg_replace('/\\s+/isu', ' ', $this->criterio);

        if (empty($this->criterio)) {
            throw new Exception('Parâmetro de critério inválido', 1);
        }
    }

    protected function start()
    {
        $data = $this->getDados();
        if (empty($data)) {
            throw new Exception('Nenhum resultado encontrado', 2);
        }

        return $data;
    }

    private function getDados()
    {
        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('lista_europeia')
            ->setCollection('xml_name');

        $fields = ['last_name','first_name','middle_name','whole_name'];
        $query[] = [
            '$search' => [
                'index' => self::INDEX_MONGODB['xml_name'],
                'compound' => [
                    'must' => [
                        [
                            "phrase" => [
                                "query" => $this->criterio,
                                "path" => self::FIELDS['xml_name']
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $query[] = ['$limit' => self::LIMIT];

        $nameCursor =  json_decode(
            json_encode(
                $manager->query(
                    'aggregate',
                    $query,
                    null,
                    null,
                    true
                )
                ->toArray(),
                true
            ),
            true
        );

        $data = [];
        foreach ($nameCursor as $name) {
            $entry = [];
            foreach (self::COLLECTIONS as $collection) {
                $projection = [
                    '_id' => null
                ];

                foreach (self::FIELDS[$collection] as $field) {
                    $projection[$field] = 1;
                }

                $cursor = $manager->setCollection($collection)
                    ->query('find', [
                        [
                            'id_entity' => (string)$name['id_entity']
                        ],
                        [
                            'projection' => $projection
                        ]
                    ]);

                $entry[$collection] = [];
                foreach ($cursor as $document) {
                    $documentEntry = [];
                    foreach (self::FIELDS[$collection] as $field) {
                        $documentEntry[$field] = $document[$field];
                    }
                    $entry[$collection][] = $documentEntry;
                }
            }
            $parsedEntry = $this->parseEntry($entry);
            $data = [];
            if (!empty($parsedEntry)) {
                $data[] = $parsedEntry;
                /**
                 * A fonte original busca apenas um unico critério, não um array.
                 * Para fazer buscar um array retirar este break e o reset no fim da função
                 */
                break;
            }
        }

        return reset($data);
    }

    private function parseEntry($entry)
    {
        if (empty($entry['xml'])) {
            return null;
        }
        $parsedEntry = reset($entry['xml']);
        foreach ($entry as $key => $values) {
            if ($key == 'xml') {
                continue;
            }
            $parsedEntry[self::ARRAY_NAMES[$key]] = $values;
        }
        return $parsedEntry;
    }
}
