<?php

namespace App\Crawler\CertidaoCeatTrtBA;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Str;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\ColunaVertebralManager;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Carbon\Carbon;
use Exception;

class CertidaoCeatTrtBA extends Spider
{
    private const BASE_URL = "https://www.trt5.jus.br";
    private const FORM_URL = "/certidao-eletronica/1";
    private const LIST_URL = "/ceat/backend";
    private const RESULT_URL = "/exibe-relatorio";
    private const PATH_STATIC_S3 = S3_STATIC_URL;
    private const PATH_CERTIDAO_S3 = 'captura/certidao_ceat_trt5_ba/';

    private $url = self::BASE_URL . self::FORM_URL;
    private $cpfCnpj;
    private $formId;
    private $theme;
    private $theme_token;
    private $libraries;
    private $tipoPessoa;
    private $listaProcessos;
    private $nome = '';
    private $headers = ['User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0'];

    protected $captcha;

    public function start()
    {
        $retry = 7;
        while ($retry >= 0) {
            try {
                $this->setProxy();

                $html = $this->getResponse($this->url, 'GET', [], $this->headers);

                $this->getParams($html);

                $this->solveCaptcha($html);

                $this->startRequestSession();

                $this->loadList();

                $this->postRequest();

                return $this->getCertificate();
            } catch (Exception $e) {
                print PHP_EOL . 'ERRO:' . PHP_EOL;
                print_r($e->getMessage());
                print PHP_EOL . PHP_EOL;

                if ($retry == 0) {
                    throw new Exception($e->getMessage(), 2);
                }
                $retry--;

                curl_close($this->ch);
                $this->openCurl();
            }
        }
    }

    /**
     * Recupera o certificado
     *
     * <AUTHOR> Ricardo Vidal <<EMAIL>>
     *
     * @return array
     */
    public function getCertificate()
    {
        $certificate = $this->loadCertificate();

        $parsedContent = $this->parseCertificate($certificate);

        $pdfLink = $this->savePdf($certificate);

        $parsedContent['pdf'] = $pdfLink;

        return Str::toUtf8($parsedContent);
    }

    /**
     * Salva o PDF no S3 e retorna link
     *
     * <AUTHOR> Vidal <<EMAIL>>
     *
     * @param string $content
     *
     * @return string
     */
    public function savePdf($content)
    {
        $uniqid = md5(uniqid(rand(), true));
        $certificateName = "{$uniqid}.pdf";
        $certificateLocalPath = "/tmp/{$certificateName}";
        $certificateS3Path = self::PATH_CERTIDAO_S3 . $certificateName;
        $certificateUrl = self::PATH_STATIC_S3 . $certificateS3Path;

        ((new Pdf())->saveHtmlToPdf($content, $certificateLocalPath));

        (new S3(new StaticUplexisBucket()))->save($certificateS3Path, $certificateLocalPath);

        return $certificateUrl;
    }

    /**
     * Faz o parse do certificado
     *
     * <AUTHOR> Vidal <<EMAIL>>
     *
     * @param string $content
     *
     * @return array
     */
    public function parseCertificate($content)
    {
        $descricaoIfPositivaPattern = '/align="justify"\sstyle="color:rgb\(0, 0, 0\);'
            . '.[^>]*><[^>]*>\n?(.*?)<\/p>\s*<p/is';

        $patterns = [
            'tipo' => ['/CERTID.O\s(NEGATIVA|POSITIVA)/i', null],
            'numero' => ['/Certid.o\sn.*>(.*?)<\//i', null],
            'expedicao' => ['/Expedi..o.*>(.*?)<\//i', null],
            'codAutenticidade' => ['/autenticidade.*>(.*?)<\//i', null],
            'validade' => ['/V.lida at.*?g>(.*?)<\//i', null],
            'descricao' => ['/align="justify"\sstyle="color:rgb\(0, 0, 0\);.[^>]*><[^>]*>\n?(.*?)<\/p>\s*<p/is', null],
            'observacoes' => ['/OBSERVA..ES:<[^>]*>\s*(.*?)<\/p>/is', null],
        ];

        $result = Util::parseDados($patterns, $content, false, false);

        $result['descricao'] = strip_tags($result['descricao']);
        $result['observacoes'] = strip_tags($result['observacoes']);

        if ($result['tipo'] == null) {
            throw new \Exception('Não foi possível gerar a certidão', 3);
        }

        if ($result['tipo'] == 'POSITIVA') {
            preg_match_all($descricaoIfPositivaPattern, $content, $matches);
            $result['descricao'] = strip_tags($matches[1][0] . $matches[1][1]);
        }

        return $result;
    }

    /**
     * Requisição POST necessário para gerar o PDF em outro link
     *
     * <AUTHOR> Vidal <<EMAIL>>
     *
     * @return void
     */
    public function postRequest()
    {
        $params = [
            'grau_processos' => 1,
            'radios' => $this->tipoPessoa == 'F' ? 'CPF' : 'CNPJ',
            'documento' => $this->documento,
            'nomeReceita' => $this->nome,
            'certidaoProcessos' => $this->listaProcessos,
            'nomeConsulta' => '',
            'op' => 'Emitir',
            'form_build_id' => $this->formId,
            'form_id' => 'trt5_ceat_emissao_form',
            'recaptcha_response' => $this->captcha
        ];

        $headers = [
            'Referer: https://www.trt5.jus.br/certidao-eletronica/1',
            'User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
            'X-Requested-With' => 'XMLHTTPRequest'
        ];

        $result = $this->getResponse($this->url, 'POST', $params, $headers);

        $pattern = '/temporariamente indispon.*?veis/is';
        if (preg_match($pattern, $result)) {
            $msg = 'O Portal e alguns Sistemas do TRT5 estão temporariamente indisponíveis!';
            throw new \Exception($msg, 3);
        }

        if (!preg_match('/Emissão realizada com sucesso/is', $result)) {
            throw new \Exception('Emissão não realizada com sucesso!', 3);
        }
    }

    /**
     * Retorna certidão
     *
     * @return string
     */
    public function loadCertificate()
    {
        echo 'Aguardando 15 segundos...';
        sleep(15);
        $html = $this->getResponse(self::BASE_URL . self::RESULT_URL, 'GET', [], $this->headers);

        return utf8_decode($html);
    }

    /**
     * Carrega lista de processos
     *
     * <AUTHOR> Ricardo Vidal <<EMAIL>>
     *
     * @return void
     */
    public function loadList()
    {
        $params = [
            'DOCUMENTO' => $this->documento,
            'NOME' => $this->nome,
            'NOME_CONSULTA' => '',
            'NUM_PAGINA' => 1,
            'TAMANHO_PAGINA' => 2000,
            'GRAU' => 1,
            'ARQUIVADOS' => 'N'
        ];

        $headers = [
            'User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
            'X-Requested-With' => 'XMLHTTPRequest'
        ];

        $result = $this->getResponse(self::BASE_URL . self::LIST_URL, 'POST', $params, $headers);

        $this->listaProcessos = $result;
    }

    /**
     * Inicia a sessão
     *
     * <AUTHOR> Ricardo Vidal <<EMAIL>>
     *
     * @return void
     */
    public function startRequestSession()
    {
        $params = [
            'grau_processos' => 1,
            'radios' => $this->tipoPessoa == 'F' ? 'CPF' : 'CNPJ',
            'documento' => $this->documento,
            'nomeReceita' => $this->nome,
            'certidaoProcessos' => '',
            'nomeConsulta' => '',
            'form_build_id' => $this->formId,
            'form_id' => 'trt5_ceat_emissao_form',
            'recaptcha_response' => $this->captcha,
            '_triggering_element_name' => 'op',
            '_triggering_element_value' => 'Verificar',
            '_drupal_ajax' => '1',
            'ajax_page_state[theme]' => $this->theme,
            'ajax_page_state[theme_token]' => $this->theme_token,
            'ajax_page_state[libraries]' => $this->libraries
        ];

        $headers = [
            'Referer: https://www.trt5.jus.br/certidao-eletronica/1',
            'User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
        ];

        $url = $this->url . '?ajax_form=1&_wrapper_format=drupal_ajax';
        $html = $this->getResponse($url, 'POST', $params, $headers);

        $result = json_decode($html, true);

        if (preg_match('/Captcha Expirado ou Inv\\\u00e1lido/isu', $html)) {
            throw new \Exception('Captcha Expirado ou Inválido. Tente novamente.', 3);
        }

        if (empty($result)) {
            throw new \Exception('Não foi possível prosseguir com a requisição', 3);
        }
    }

    /**
     * Recupera alguns parâmetros necessários
     *
     * <AUTHOR> Leandro Borges <<EMAIL>>
     *
     */
    public function getParams($html)
    {
        $pattern = '/name=\"form_build_id\".value=\"(.*?)\".\/>/is';

        if (!preg_match($pattern, $html, $formId)) {
            throw new \Exception('Não foi possível recuperar o form id!', 3);
        }

        if (!preg_match('/theme":"(.*?)"/is', $html, $theme)) {
            throw new \Exception('Não foi possível recuperar o theme!', 3);
        }

        if (!preg_match('/theme_token":"(.*?)"/is', $html, $theme_token)) {
            throw new \Exception('Não foi possível recuperar o theme_token!', 3);
        }

        if (!preg_match('/libraries":"(.*?)"/is', $html, $libraries)) {
            throw new \Exception('Não foi possível recuperar as libraries!', 3);
        }

        $this->formId = $formId[1];
        $this->theme = $theme[1];
        $this->theme_token = $theme_token[1];
        $this->libraries = $libraries[1];
    }

    /**
     * Recupera sitekey e resolve o captcha
     *
     * <AUTHOR> Ricardo Vidal <<EMAIL>>
     *
     * @param string $html
     *
     * @return array
     */
    public function solveCaptcha($html)
    {
        $pattern = '/js\?render=(.*?)"/is';

        if (!preg_match($pattern, $html, $matches)) {
            throw new \Exception('Não foi possível recuperar o sitekey', 3);
        }

        $this->captcha = $this->solveReCaptcha($matches[1], $this->url);
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->cpfCnpj = trim($this->param['cpf_cnpj']);
        $unmaskedDocument = Document::removeMask(trim($this->param['cpf_cnpj']));

        if (!Document::validarCpfOuCnpj($this->cpfCnpj)) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        if (Document::validarCpf($this->cpfCnpj)) {
            $this->documento = Document::formatCpf($this->cpfCnpj);
            $this->tipoPessoa = 'F';
            $spinePf = (object) (new ColunaVertebralManager())->getSpinePf($unmaskedDocument);
            $this->nome = $spinePf->nome;
        } elseif (Document::validarCnpj($this->cpfCnpj)) {
            $this->documento = Document::formatCnpj($this->cpfCnpj);
            $this->tipoPessoa = 'J';
            $spinePJ = (object) (new ColunaVertebralManager())->getSpinePJ($unmaskedDocument);
            $this->nome = $spinePJ->razao_social;
        }
    }

    /**
     * Método adicionado para forçar renovação de conexão
     *
     * <AUTHOR> Leandro Borges <<EMAIL>>
     */
    private function openCurl()
    {
        $uniqid = md5(uniqid(rand(), true));
        $this->cookie = '/tmp/cookie_' . $uniqid . '.txt';
        $this->ch = curl_init();

        curl_setopt($this->ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($this->ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($this->ch, CURLOPT_COOKIESESSION, 1);
        curl_setopt($this->ch, CURLOPT_COOKIEJAR, $this->cookie);
        curl_setopt($this->ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($this->ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($this->ch, CURLOPT_TIMEOUT, 600);
    }
}
