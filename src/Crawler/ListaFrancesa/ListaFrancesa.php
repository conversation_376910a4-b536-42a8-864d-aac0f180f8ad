<?php

namespace App\Crawler\ListaFrancesa;

use Exception;
use App\Crawler\Spider;
use App\Factory\MongoDB;

class ListaFrancesa extends Spider
{
    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['nome'])) {
            throw new Exception('Parâmetro de criterio inválido', 1);
        }
        if (empty($this->param['limit'])) {
            $this->param['limit'] = 100;
        }
        $this->param['nome'] = trim($this->param['nome']);
    }

    protected function start()
    {
        $results = $this->getResults();
        $data = $this->parseResults($results);
        return $data;
    }

    private function getResults()
    {
        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('common')
            ->setCollection('lista_francesa');

        $results = $manager->textSearch($this->param['nome'], $this->param['limit']);

        return $results;
    }

    private function parseResults($results)
    {
        $data = [];
        foreach ($results as $result) {
            $data[] = [
                'id' => $result['id'],
                'nome' => $result['nome'],
                'sobrenome' => $result['sobrenome'],
                'nome_comspleto' => $result['nome_completo'],
                'alias' => $result['alias'],
                'data_nascimento' => $result['data_nascimento'],
                'local_nascimento' => $result['local_nascimento'],
                'informacoes' => $result['informacoes'],
                'tipo' => $result['tipo'],
                'sancao' => $result['sancao'],
                'fundamento' => $result['fundamento'],
                'decreto' => $result['decreto'],
                'link' => $result['link'],
                'data_inclusao' => $result['data_inclusao'],
                'resumo' => $result['resumo']
            ];
        }
        return $data;
    }
}
