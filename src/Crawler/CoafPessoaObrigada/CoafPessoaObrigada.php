<?php

namespace App\Crawler\CoafPessoaObrigada;

use App\Crawler\CoafPessoaObrigada\vo\CoafPessoaObrigada as VoCoafPessoaObrigada;
use App\Crawler\Spider;
use Exception;

class CoafPessoaObrigada extends Spider
{
    protected function start()
    {
        // busca pagina em questao
        $url = 'https://www1.fazenda.gov.br/siscoaf/portugues/ConsultaPessoaObrigada.asp';
        $data = array(
            "CPFCNPJ" => $this->param['cnpj'],
            "Submit" => "Ok"
        );
        $result = $this->getResponse($url, 'POST', $data, ['Referer' => $url]);
        try {
            return $this->getDados($result);
        } catch (Exception $e) {
            throw $e;
        }
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['cnpj'])) {
            throw new Exception('CNPJ obrigatório');
        }
    }

    public function getDados($result)
    {
        $pattern = '@<p><H3> Empresa\s*n.*?o\s*cadastrada\s*<H3></p>@i';
        $int = preg_match($pattern, $result, $matches);
        if (count($matches) > 0) {
            throw new Exception("Empresa não cadastrada no COAF!", 2);
        }

        // procura dados da empresa
        $o = new VoCoafPessoaObrigada();
        $patterns = array(
            'cnpj'            => '@<tr>\s*<td\b[^>]*?><b>CPF/CNPJ:</b></td>\s*<td\b[^>]*?>(.*?)</td>\s*</tr>@i',
            'nome'            => '@<tr>\s*<td\b[^>]*?><b>Nome\s*empresarial:</b></td>\s*<td\b[^>]*?>' .
                '(.*?)</td>\s*</tr>@i',
            'segmento'        => '@<tr>\s*<td\b[^>]*?><b>Segmento:</b></td>\s*<td\b[^>]*?>(.*?)</td>\s*</tr>@is',
            'situacao'        => '@<tr>\s*<td\b[^>]*?><b>Situa.*?o:</b></td>\s*<td\b[^>]*?>(.*?)</td>\s*</tr>@is',
            'data_consulta'    => '@<tr>\s*<td\b[^>]*?><b>Data\s*da\s*Consulta:</b></td>\s*<td\b[^>]*?>' .
                '(.*?)</td>\s*</tr>@i'
        );

        foreach ($patterns as $c => $p) {
            $int = preg_match($p, $result, $matches);
            if (count($matches) == 0) {
                throw new Exception("Spider Broken at ${c}", 1);
            }
            eval("\$o->set_${c}(utf8_encode(trim(\$matches[1])));");
        }

        return $o;
    }
}
