<?php

namespace App\Crawler\CoafPessoaObrigada\vo;

class CoafPessoaObrigada
{
    /**
     * Representa a cnpj
     *
     * @var string
     */
    private $cnpj;

    /**
     * Representa a nome
     *
     * @var string
     */
    private $nome;

    /**
     * Representa a segmento
     *
     * @var string
     */
    private $segmento;

    /**
     * Representa a situacao
     *
     * @var string
     */
    private $situacao;

    /**
     * Representa a data_consulta
     *
     * @var string
     */
    private $data_consulta;

    public function __construct($cnpj = null, $nome = null, $segmento = null, $situacao = null, $data_consulta = null)
    {
        $this->cnpj = $cnpj;
        $this->nome = $nome;
        $this->segmento = $segmento;
        $this->situacao = $situacao;
        $this->data_consulta = $data_consulta;
    }

    public function getCnpj()
    {
        return $this->cnpj;
    }

    public function getNome()
    {
        return $this->nome;
    }

    public function getSegmento()
    {
        return $this->segmento;
    }

    public function getSituacao()
    {
        return $this->situacao;
    }

    public function getDataConsulta()
    {
        return $this->data_consulta;
    }

    public function setCnpj($v)
    {
        $this->cnpj = $v;
    }

    public function setNome($v)
    {
        $this->nome = $v;
    }

    public function setSegmento($v)
    {
        $this->segmento = $v;
    }

    public function setSituacao($v)
    {
        $this->situacao = $v;
    }

    public function setDataConsulta($v)
    {
        $this->data_consulta = $v;
    }
}
