<?php

namespace App\Crawler\BsmPad;

use App\Crawler\Spider;
use App\Helper\Util;
use Exception;

class BsmPad extends Spider
{
    private const BASE_URL = 'https://www.bsmsupervisao.com.br';
    private const BASE_URL_REQUEST = self::BASE_URL . '/PAD/Mais?';
    private $name = "";
    private $limit = 0;
    private $data = [];

    public function start()
    {
        $urlsDetalhes = $this->makeRequest();
        for ($i = 0; $i < count($urlsDetalhes); $i++) {
            $this->parseData(self::BASE_URL . $urlsDetalhes[$i], $i);

            if (!empty($this->limit) && $this->limit - 1 == $i) {
                return $this->data;
            }
        }

        return $this->data;
    }

    private function makeRequest()
    {
        $params = [
            'p' => 1,
            'mobile' => 1,
            'nroPad' => '',
            'origem' => '',
            'acusado' => $this->name,
            'tipoAcusado' => '',
            'infracao' => '',
            'dtinicio' => '',
            'dttermino' => '',
            'status' => '',
            'tevePropostaTc' => '',
            'penalidadeAplicada' => '',
        ];

        $params = http_build_query($params);

        $result = $this->getResponse(self::BASE_URL_REQUEST . $params);

        if (preg_match('/Não há processos que atendam os requisitos/', $result)) {
            throw new Exception("Nenhum Resultado Encontrado", 2);
        }

        preg_match_all('/href="([\s\S]*?)"/', $result, $result);
        return $result[1];
    }

    private function parseData($url, $index)
    {
        $html = $this->getResponse($url);
        $basePattern = '//*[@id="main"]/section/div/div/table/tbody/tr/td';

        $this->data[$index]['numProcesso'] = Util::queryXPath($html, '//*[@id="main"]/section/div/div/h3')[0];
        $this->data[$index]['origem'] = Util::queryXPath($html, $basePattern . '[1]')[0];
        $this->data[$index]['data_instauracao'] = Util::queryXPath($html, $basePattern . '[2]')[0];
        $this->data[$index]['data_encerramento'] = Util::queryXPath($html, $basePattern . '[3]')[0];
        $this->data[$index]['rito'] = Util::queryXPath($html, $basePattern . '[4]')[0];
        $this->data[$index]['relator_primeira_instancia'] = Util::queryXPath($html, $basePattern . '[5]')[0];
        $this->data[$index]['concelheiro_segunda_instancia'] = Util::queryXPath($html, $basePattern . '[6]')[0];
        $this->data[$index]['concelheiro_terceira_instancia'] = Util::queryXPath($html, $basePattern . '[7]')[0];
        $this->data[$index]['relator_segunda_instancia'] = Util::queryXPath($html, $basePattern . '[8]')[0];

        $element = Util::queryXPath($html, "//div[@class='item-pda-left']");

        $basePatternLeft = "//div[@class='item-pda-left']";
        $basePatternRight = "//div[@class='item-pda-right']";
        $basePatternTable = "//div[@class='item-pda-table']";

        for ($i = 1; $i <= count($element); $i++) {
            $left = Util::queryXPath($html, $basePatternLeft . "[{$i}]/div/div/ul/li[2]/p[1]");
            $right = Util::queryXPath($html, $basePatternRight . "[{$i}]/div/div/ul/li[2]/p[1]");

            $this->data[$index]['defendentes'][$i]['defendente'] = $left[0];
            $this->data[$index]['defendentes'][$i]['tipo_defendente'] = $left[1];
            $this->data[$index]['defendentes'][$i]['status'] = $left[2];

            $this->data[$index]['defendentes'][$i]['teve_proposta_tc'] = $right[0];
            $this->data[$index]['defendentes'][$i]['valor_tc'] = $right[1];

            $countTable = Util::queryXPath($html, $basePatternTable . "[{$i}]/table/tbody/tr");

            for ($tableIndex = 1; $tableIndex <= count($countTable); $tableIndex++) {
                $table = Util::queryXPath($html, $basePatternTable . "[{$i}]/table/tbody/tr[{$tableIndex}]/td");

                $nomeInfracao = str_replace('amp;', '', $table[0]);

                $this->data[$index]['defendentes'][$i]['infracoes'][$tableIndex]['infracao'] = $nomeInfracao;
                $this->data[$index]['defendentes'][$i]['infracoes'][$tableIndex]['artigo_inciso'] = $table[1];
                $this->data[$index]['defendentes'][$i]['infracoes'][$tableIndex]['periodo_infracoes'] = $table[2];
                $this->data[$index]['defendentes'][$i]['infracoes'][$tableIndex]['resultado_julgamento'] = $table[3];
                $this->data[$index]['defendentes'][$i]['infracoes'][$tableIndex]['valor_pena'] = $table[4];
                $this->data[$index]['defendentes'][$i]['infracoes'][$tableIndex]['indice_aglutinador'] = $table[5];
            }
        }

        return $this->data;
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty(trim($this->param['name']))) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        $this->limit = $this->param['limit'];
        $this->name = $this->param['name'];
    }
}
