<?php

namespace App\Crawler\CertidaoDebitosCnd;

use App\Crawler\Spider;
use App\Helper\Document;
use GuzzleHttp\Client;
use GuzzleHttp\Cookie\CookieJar;
use App\Helper\Pdf;
use App\Manager\S3\S3;
use App\Manager\S3\Bucket\StaticUplexisBucket;

class CertidaoDebitosCnd extends Spider
{
    private const URL = 'https://www4.sefaz.pb.gov.br/atf/dia/DIAf_EmitirCertidaoDebito.do';
    private const BASE_URL = 'https://www4.sefaz.pb.gov.br/';
    private const COOKIE_URL = 'atf/seg/SEGf_AcessarFuncao.jsp?cdFuncao=DIA_060&idSERVirtual=S&h=';
    private const COOKIE_URL2 = 'https://www.sefaz.pb.gov.br/ser/servirtual/credenciamento/info';
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const CERTIDAO_S3_PATH = 'captura/certidao_debitos_cnd/';
    private string $documento = '';
    private string $sitekey = '6LffsL8fAAAAAA4KmvPP97ZQp9vlVWmncaqsvj57';

    public function start(): array
    {
        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::CERTIDAO_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;
        return $this->getCertidaoDebitosCndResponse();
    }

    public function validateAndSetCrawlerAttributes(): void
    {
        if (Document::validarCnpj($this->param['documento'])) {
            print_r('é cnpj');
            $this->documento = Document::removeMask($this->param['documento']);
        } else {
            throw new Exception("CNPJ inválido!", 6);
        }
    }

    private function getCertidaoDebitosCndResponse(): array
    {
        $captcha = $this->solveRecaptchaV3($this->sitekey, 'certidao', self::URL);
        $response = $this->makeRequest($captcha);
        if (preg_match('/(<!DOCTYPE html>)/', $response)) {
            return [
                "msg" => "Não foi possível atender a sua solicitação. Favor comparecer a repartição fiscal.",
                "type" => "positiva",
            ];
        }
        $results = $this->savePdfAndReturnText($response);

        return $this->parseResult($results);
    }

    private function makeRequest(string $captcha): string
    {
        $client = new Client();
        $data = array(
            'hidAcao' => 'gerar',
            'hidHistorico' => '-1',
            'hidLimpar' => 'true',
            'hidMostrarCamposCPF' => 'false',
            'rdbTpDocumento' => '2',
            'edtNrDocumento' => $this->documento,
            'g-recaptcha-response' => $captcha,
        );
        $cookie = $client->get(self::URL)->getHeader('Set-Cookie');
        $headers = array(
            'Accept-Encoding: gzip, deflate, br',
            'Accept-Language: pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3',
            'Connection: keep-alive',
            'Content-Type: application/x-www-form-urlencoded',
            'Host: www4.sefaz.pb.gov.br',
            'Origin: https://www4.sefaz.pb.gov.br',
            'Sec-Fetch-Dest: iframe',
            'Sec-Fetch-Mode: navigate',
            'Sec-Fetch-Site: same-origin',
            'Sec-Fetch-User: ?1',
            'Upgrade-Insecure-Requests: 1',
            'User-Agent: Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/115.0'
        );

        $jar = new cookieJar();

        $cookie = $client->get(self::BASE_URL . self::COOKIE_URL . self::COOKIE_URL2)->getHeader('Set-Cookie');
        $cookiePattern = '/JSESSIONID=(.*?);/';
        preg_match($cookiePattern, $cookie[0], $matches);
        $cookie1 = new \GuzzleHttp\Cookie\SetCookie([
            'Name'     => 'JSESSIONID',
            'Value'    => $matches[1],
            'Domain'   => 'www4.sefaz.pb.gov.br',
        ]);

        $jar->setCookie($cookie1);
        $response = $client->post(self::URL, [
            'headers' => $headers,
            'form_params' => $data,
            'cookies' => $jar,
        ])->getBody()->getContents();

        return $response;
    }

    private function savePdfAndReturnText(string $pdf): string
    {
        file_put_contents($this->certificateLocalPath, $pdf);

        $text = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'layout',
            'nopgbrk'
        ]);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        $this->pdf = $this->certificateUrl;

        return $text;
    }

    private function parseResult(string $result): array
    {
        $result = preg_replace('/\s+/', ' ', $result);

        $codigoPattern = '/CÓDIGO:\s(.*?)\s/';
        $dataEmissaoPattern = '/Emitida no dia\s(.*?\sàs\s.*?)\s/';
        $nomeEmpresarialPattern = '/Nome\sEmpresarial:\s(.*?)\sEndereço/';
        $conteudoPattern = '/(.*)/';

        preg_match($codigoPattern, $result, $codigoMatches);
        preg_match($dataEmissaoPattern, $result, $dataEmissaoMatches);
        preg_match($nomeEmpresarialPattern, $result, $nomeEmpresarialMatches);
        preg_match($conteudoPattern, $result, $conteudoMatches);

        $parsedData = [
            'codigo' => $codigoMatches[1] ?? '',
            'data_emissao' => $dataEmissaoMatches[1] ?? '',
            'nome_empresarial' => $nomeEmpresarialMatches[1] ?? '',
            'conteudo' => $conteudoMatches[1] ?? '',
            'type' => 'negativa',
            'pdf' => $this->pdf,
        ];

        return $parsedData;
    }
}
