<?php

namespace App\Crawler\CertidaoNegativaES;

use Exception;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\Bucket\StaticUplexisBucket;

class CertidaoNegativaES extends Spider
{
    private const MAIN_URL = 'https://sistemas.tjes.jus.br/certidaonegativa/sistemas/certidao/CERTIDAOPESQUISA.cfm';
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const CERTIDAO_S3_PATH = 'captura/certidao_negativa_es/';



    protected function start()
    {
        $uniqd = md5(uniqid(rand(), true));

        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::CERTIDAO_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;

        $response = $this->consulta();
        $this->saveHtmlToPdf($response);
        $result = Str::encoding($response);
        $result = utf8_decode($result);
        $result = preg_replace('/<\/ol>.*/m', '', $result);
        $this->saveHtmlToPdf($result);
        $data = [
            'pdf' => self::S3_STATIC_PATH . self::CERTIDAO_S3_PATH . $this->certificateName
        ];

        return $data;
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if (Document::validarCpfOuCnpj($this->param['cpf'])) {
            $this->param['cpf'] = Document::formatCpfOrCnpj($this->param['cpf']);
        } else {
            throw new Exception('CPF inválido', 1);
        }
    }

    private function consulta()
    {
        $params = [
            'btnSolicitar' => 'Solicitar',
            'cbEstadoCivil' => 0,
            'cbEstadoCivilAux' => '',
            'cbInstancia' => 1,
            'cbMunicipio' => 0,
            'cbMunicipioInfo' => 0,
            'cbMunicipioInfoAux' => '',
            'cbNacionalidade' => 0,
            'cbNacionalidadeAux' => '',
            'cbNatureza' => 11,
            'cbNaturezaAux' => 'Recuperação Judicial e Extrajudicial (Falência e Concordata)',
            'edBairro' => '',
            'edCep' => '',
            'edCnpj' => '',
            'edComplemento' => '',
            'edCpf' => $this->param['cpf'],
            'edCtpsNumero' => '',
            'edCtpsSerie' => '',
            'edEmail' => '',
            'edMae' => '',
            'edNascimento' => '',
            'edNome' => $this->param['nome'],
            'edNumero' => '',
            'edPai' => '',
            'edProfissao' => '',
            'edRg' => '',
            'edRua' => '',
            'edTelCelular' => '',
            'edTelFixo' => '',
            'edTitEleitor' => '',
            'rbPessoa' => 'F',
            'valorPessoa' => 'F'
        ];

        $headers = [
            'Content-Type' => 'text/html;charset=UTF-8',
            'Transfer-Encoding' => 'chunked',
            'Vary' => 'Accept-Encoding'
        ];
        $response = $this->getResponse(self::MAIN_URL, 'POST', $params, $headers);
        return $response;
    }

    private function saveHtmlToPdf($html)
    {
        if (!(new Pdf())->saveHtmlToPdf($html, $this->certificateLocalPath)) {
            throw new Exception('PDF nao criado', 1);
        }

        (new S3(new StaticUplexisBucket()))->save(
            self::CERTIDAO_S3_PATH . "{$this->certificateName}",
            $this->certificateLocalPath
        );
    }
}
