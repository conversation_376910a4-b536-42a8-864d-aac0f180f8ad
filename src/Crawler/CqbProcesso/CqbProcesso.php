<?php

namespace App\Crawler\CqbProcesso;

use App\Crawler\Spider;
use App\Helper\Str;
use Exception;

/**
 * Classe da fonte () CQB Processo
 *
 * <AUTHOR> Mesquita 28/01/2020
 */
class CqbProcesso extends Spider
{
    private const MAIN_API_URL = 'http://ctnbio-webapps.mctic.gov.br/ctnbio-portlet/webapps/api/';
    private const SEARCH_API_URL = 'cqb/consultar-processo?instituicao=';
    private const PUBLICATIONS_API_URL = 'publicacao/listar?processo=';
    private const DOU_API_URL = 'publicacao/dou/';

    /**
     * Iniciar a pesquisa da fonte
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 30/01/2020
     *
     * @return array
     */
    protected function start()
    {
        $this->setProxy();

        $searchs = $this->getSearchResult();

        $result = [];

        foreach ($searchs as $search) {
            $search['publicacoes'] = $this->getPublications($search['processo']);
            $result[] = $search;
        }

        return $result;
    }

    /**
     * Retorna array com os processos encontrados
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 30/01/2020
     *
     * @return array
     */
    private function getSearchResult()
    {
        $searchUrl = self::MAIN_API_URL . self::SEARCH_API_URL . urlencode($this->param['name']);

        $result = json_decode($this->getResponse($searchUrl), true);

        if (empty($result)) {
            throw new Exception('Nada encontrado', 2);
        }

        return array_shift($result);
    }

    /**
     * Retorna as publicações de cada processo
     *
     * @param string $process - Número do processo
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 30/01/2020
     *
     * @return array
     */
    private function getPublications($process)
    {
        $publicationUrl = self::MAIN_API_URL . self::PUBLICATIONS_API_URL . urlencode($process);

        $publications = array_shift(
            json_decode($this->getResponse($publicationUrl), true)
        );

        $result = [];

        foreach ($publications as $publication) {
            $publication['dou'] = $this->getDOU($publication['codigoObjeto']);
            $result[] = $publication;
        }

        return $result;
    }

    /**
     * Retorna detalhes da publicação
     *
     * @param string $objectCode - Código do objeto que vio no array de publicação
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 30/01/2020
     *
     * @return array
     */
    private function getDOU($objectCode)
    {
        if ($objectCode === 0) {
            return null;
        }

        $publicationUrl = self::MAIN_API_URL . self::DOU_API_URL . $objectCode;

        return array_shift(
            json_decode($this->getResponse($publicationUrl), true)
        );
    }

    /**
     * Valida e trata o critério
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 30/01/2020
     *
     * @return array
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (!isset($this->param['name']) || empty($this->param['name'])) {
            throw new Exception('Parâmetro de critério invalido', 1);
        }

        $this->param['name'] = trim(Str::removeSiglas($this->param['name'], true, false));
    }
}
