<?php

namespace App\Crawler\CertidaoCeatTrtPR;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Carbon\Carbon;
use Exception;

class CertidaoCeatTrtPR extends Spider
{
    private const BASE_URL = "https://pje.trt12.jus.br";
    private const API_PROPRIEDADES = '/pje-certidoes-api/api/propriedades';
    private const API_EMISSAO = "/pje-certidoes-api/api/certidoes/trabalhistas/emissao";
    private const API_PDF = "/pje-certidoes-api/api/certidoes/trabalhistas/";
    private const PATH_STATIC_S3 = S3_STATIC_URL;
    private const PATH_CERTIDAO_S3 = 'captura/certidao_ceat_trt12_pr/';

    private $cpfCnpj;
    private $cpf = '';
    private $cnpj = '';
    private $tipo = '';
    private $headers = [
        "User-Agent: Mozilla/5.0 (X11; Linux x86_64)" .
            " AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ];

    public function start()
    {
        $maxRetries = 3;
        $retryDelay = 1000;
        $retryCount = 0;

        while ($retryCount < $maxRetries) {
            try {
                $siteKey = $this->getSiteKey();
                $captcha = $this->solveReCaptcha($siteKey, self::BASE_URL);
                $params = $this->getSearchParams($captcha);

                $certidao = $this->getResponse(
                    self::BASE_URL . self::API_EMISSAO,
                    'POST',
                    json_encode($params),
                    $this->headers
                );
                $certidaoData = json_decode($certidao, true);

                if (!array_key_exists('codigo', $certidaoData)) {
                    throw new Exception('Erro ao obter código da certidão');
                }

                $codigoCertidao = $certidaoData['codigo'];

                $pdf = $this->getResponse(
                    self::BASE_URL . self::API_PDF . $codigoCertidao,
                    'GET',
                    null,
                    $this->headers
                );
                $pdfData = json_decode($pdf, true);

                if (!array_key_exists('conteudoHTML', $pdfData)) {
                    throw new Exception('Erro ao obter conteúdo HTML do PDF');
                }

                $conteudoCertidao = $pdfData['conteudoHTML'];
                $data = $this->savePdfAndReturnText($conteudoCertidao);
                $info = $this->parseText($data['text']);

                return ['pdf' => $data['pdf'], 'info' => $info];
            } catch (Exception $e) {
                $retryCount++;
                if ($retryCount == $maxRetries) {
                    throw new Exception(
                        'Erro ao processar certidão após ' . $maxRetries . ' tentativas: ' . $e->getMessage()
                    );
                }
                usleep($retryDelay * 1000);
            }
        }
    }

    private function getSiteKey()
    {
        $propriedades = $this->getResponse(self::BASE_URL . self::API_PROPRIEDADES, 'GET', null, $this->headers);
        $data = json_decode($propriedades, true);
        return $data['chaveDeSiteDoCaptcha'];
    }

    private function getSearchParams($captcha)
    {
        if (!empty($this->cpf)) {
            $this->tipo = 'CPF';
            return [
                'criterioDeEmissao' => 'CPF',
                'nome' => '',
                'numeroDoDocumento' => $this->cpf,
                'respostaDoCaptcha' => $captcha
            ];
        } else {
            $this->tipo = 'Raiz';
            $raizCnpj = substr($this->cnpj, 0, strpos($this->cnpj, "/"));
            return [
                'criterioDeEmissao' => 'RAIZ_DE_CNPJ',
                'nome' => '',
                'numeroDoDocumento' => $raizCnpj,
                'respostaDoCaptcha' => $captcha
            ];
        }
    }

    private function savePdfAndReturnText($html)
    {
        try {
            $uniqid = md5(uniqid(rand(), true));
            $certificateName = "{$uniqid}.pdf";
            $certificateLocalPath = "/tmp/{$certificateName}";
            $certificateS3Path = self::PATH_CERTIDAO_S3 . $certificateName;
            $certificateUrl = self::PATH_STATIC_S3 . $certificateS3Path;

            (new Pdf())->saveHtmlToPdf(utf8_decode($html), $certificateLocalPath);

            $text = (new Pdf())->getTextFromPdf($certificateLocalPath, [
                'layout',
                'nopgbrk'
            ]);

            if (file_exists($certificateLocalPath)) {
                $s3 = new S3(new StaticUplexisBucket());
                if ($s3->save($certificateS3Path, $certificateLocalPath)) {
                    return ['pdf' => $certificateUrl, 'text' => $text];
                }
            }

            throw new Exception('Erro ao salvar o pdf');
        } catch (Exception $e) {
            throw new Exception('Erro ao salvar o pdf: ' . $e->getMessage());
        }
    }

    private function parseText($text)
    {
        $patterns = [
            'expedicao' => ['@Certidão\semitida\sem\s([\d\/]+\sàs\s[\d:]+)@'],
            'codAutenticidade' => ['@Código\sde\sverificação:\s([0-9.]+)@'],
            'descricao' => ['@(Certifica-se[\s\S]*?)+' . $this-> tipo . '@'],
            'observacoes' => ['@Observações:\\n([\s\S]*?)Certidão@']
        ];

        $data = Util::parseDados($patterns, $text, true);
        $data = array_map("utf8_decode", $data);
        $data['statusCertidao'] = 'NEGATIVA';
        $data['expedicao'] = Carbon::createFromFormat('d/m/Y \à\s H:i', $data['expedicao'])->format('d/m/Y H:i:s');
        if (!preg_match('/NÃO CONSTAM/', $text)) {
            $data['statusCertidao'] = 'POSITIVA';
            $data['processos'] = $this->listProcessos($text);
        }

        return $data;
    }

    private function listProcessos($text)
    {
        $patterns = [
            '/Poder\sJudiciário\sFederal\\n/',
            '/Tribunal\sRegional\sdo\sTrabalho\sda\s12ª\sRegião\\n/',
            '/Certidão\sEletrônica\sde\sAções\sTrabalhistas\\n/',
            '/(Observações[\s\S]*?)Certidão[\s\S]*?(emitida em \d{2}\/\d{2}\/\d{4} às \d{2}:\d{2})/',
            '/(Certifica-se[\s\S]*?)+\\n\s*\\n/',
            '/(' . $this-> tipo . '[\s\S]*?)+\\n\s*\\n/',
            '/Código\sde\sverificação:\s([0-9.]+)/',
        ];
        $result = preg_replace($patterns, '', $text);

        preg_match_all('/\n\s*(.*?)\n/', $result, $matches);
        $result = $matches[1];
        $list_processos = array();
        for ($i = 0; $i < count($result); $i += 2) {
            $vara = trim($result[$i]);
            $processo = trim($result[$i + 1]);
            $list_processos[] = "$vara - $processo";
        }
        return array_filter($list_processos);
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->cpfCnpj = trim($this->param['cpf_cnpj']);
        $unmaskedDocument = Document::removeMask(trim($this->param['cpf_cnpj']));

        if (!Document::validarCpfOuCnpj($this->cpfCnpj)) {
            throw new Exception('Parâmetro Inválido');
        }

        if (Document::validarCpf($this->cpfCnpj)) {
            $this->cpf = Document::formatCpf($this->cpfCnpj);
        } elseif (Document::validarCnpj($this->cpfCnpj)) {
            $this->cnpj = Document::formatCnpj($this->cpfCnpj);
        }
    }
}
