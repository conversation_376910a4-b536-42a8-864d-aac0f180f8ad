<?php

namespace App\Crawler\CertidaoCeatTrtPE;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;
use App\Manager\ColunaVertebralManager;

class CertidaoCeatTrtPE extends Spider
{
    private const BASE_URL = 'https://pje.trt6.jus.br/pje-certidoes-api/api';
    private const URL_REQUEST = '/propriedades';
    private const URL_CAPTCHA = "/certidoes/trabalhistas/emissao";
    private const URL_POST_REQUEST = "/certidoes/trabalhistas/emissao";
    private const URL_CERTIFICATE = '/certidoes/trabalhistas/';
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const CERTIDAO_S3_PATH = 'captura/certidao_ceat_trt6_pe/';
    private $cpfCnpj = '';
    private $gCaptcha;

    public function start()
    {
        $this->setProxy();
        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::CERTIDAO_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;
        $json = $this->makeRequest();
        $this->gCaptcha = $this->resolveRecaptcha($json);
        $pdf = $this->makeRequestCertificate();
        $text = $this->savePdfAndReturnText($pdf);
        $data = $this->parseData($text);
        $data['pdf'] = $this->pdf;
        $data['name'] = $this->getNameOfCriteria($this->cpfCnpj);
        return $data;
    }

    /**
     * Requisição para recuperar nome do critério.
     * @autho Pedro Medeiros
     * @return string
     */
    private function getNameOfCriteria($document)
    {
        $colunaVertebralManager = new ColunaVertebralManager();

        $formatedDocument = str_replace([".", "-", "/"], "", $document);

        if (strlen($this->cpfCnpj) > 14) {
            $data = $colunaVertebralManager->getSpinePj($formatedDocument);
            return $data['razao_social'];
        } else {
            $data = $colunaVertebralManager->getSpinePf($formatedDocument);
            return $data['nome'];
        }
    }

    /**
     * Primeira requisição para capturar os dados.
     * <AUTHOR> Pereira
     * @autho Pedro Medeiros - setando proxy e alterando o retorno para json
     * @return $html
     */
    private function makeRequest()
    {
        $this->setProxy();
        $res = $this->getResponse(self::BASE_URL . self::URL_REQUEST, 'GET');
        return json_decode($res);
    }

    /**
     * Resolve o captcha e retorna o token.
     * <AUTHOR> Pereira
     * <AUTHOR> Medeiros - Link do captcha e argumento da função alterados.
     * @return string
     */
    private function resolveRecaptcha($json)
    {
        if (!empty($json)) {
            $urlCaptcha = self::BASE_URL . self::URL_CAPTCHA;
            $res = $this->solveReCaptcha($json->chaveDeSiteDoCaptcha, $urlCaptcha, 'GET');
            return $res;
        }

        throw new Exception("Erro ao localizar dados do captcha na página.", 3);
    }

    /**
     * Captura a certidão da requisição em pdf.
     * <AUTHOR> Pereira
     * <AUTHOR> Medeiros Mudanças nos parametros para a emissão do certificado
     * @return $pdf
     */
    private function makeRequestCertificate()
    {
        $url = self::BASE_URL . self::URL_POST_REQUEST;
        $pdfUrl = self::BASE_URL . self::URL_CERTIFICATE;
        $typeSearch = "CPF";
        $document = $this->cpfCnpj;

        if (strlen($this->cpfCnpj) > 14) {
            $typeSearch = "RAIZ_DE_CNPJ";
            $document = substr($this->cpfCnpj, 0, 10);
        }

        $params = [
            'criterioDeEmissao' => $typeSearch,
            'numeroDoDocumento' => $document,
            'respostaDoCaptcha' => $this->gCaptcha
        ];

        $result = json_decode($this->getResponse($url, 'POST', json_encode($params)));

        $pdfResult = $this->getResponse($pdfUrl . $result->codigo, 'GET');

        $pdf = json_decode($pdfResult);

        $pdf = $pdf->conteudoHTML;

        return $pdf;
    }

    /**
     * Salva o pdf capturado na amazom temporariamente.
     * <AUTHOR> Pereira
     * <AUTHOR> html recebido em pdf
     * @param string $pdf
     * @return $text
     */
    private function savePdfAndReturnText($html)
    {
        (new Pdf())->saveHtmlToPdf(utf8_decode($html), $this->certificateLocalPath);

        $text = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'layout',
            'nopgbrk'
        ]);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);

        $this->pdf = $this->certificateUrl;

        return $text;
    }

    /**
     * Faz o parse dos dados.
     * <AUTHOR> Pereira
     * @param string $text
     * @return $data
     */
    private function parseData($text)
    {
        $text = str_replace(["\n", "\r"], "", $text);

        $patterns = [
            'document' => ['@[CPF|CNPJ]\s*pesquisado:([\s\.\d\-]*)@'],
            'codAutenticidade' => ['@C\W+digo\s*de\s*verifica\W+o:([\d\s\.]*)@'],
            'expedicao' => ['@emitida\s*em\s*([\d\/]*)@'],
            'situacao' => ['@(Certifica-se[\w\W\s\S\n\b]* responsabilidade.)@'],
            'descricao' => ['@Observa\W*es:([\W\w\s\S\d]*)emitida@'],
        ];

        $data = Util::parseDados($patterns, $text);
        $data = array_map("utf8_decode", $data);
        $data['validade'] =  date('d/m/Y', strtotime('+30 days'));
        $data['status'] = 'NEGATIVA';
        if (preg_match('/\d\sações/', $text)) {
            preg_match('/Rela[\s\S]*?citante\.\n+([\s\S]*?)\n+\s+Cert/', $text, $descAcoes);
            $acoes = preg_split('/\s|\n/', $descAcoes[1]);

            $data['acoes'] = $acoes;
            $data['status'] = 'POSITIVA';
        }
        return $data;
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCpfOuCnpj($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro Inválido.', 6);
        }

        $this->cpfCnpj = Document::formatCpfOrCnpj($this->param['cpf_cnpj']);
    }
}
