<?php

namespace App\Crawler\ReceitaFederalSimplesNacional;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Str;
use App\Helper\Util;
use Exception;

class ReceitaFederalSimplesNacional extends Spider
{
    private const RETRY = 5;

    private $mainUrl = 'https://consopt.www8.receita.fazenda.gov.br/consultaoptantes';
    private $infoUrl = 'https://consopt.www8.receita.fazenda.gov.br/consultaoptantes/Home/AjaxMaisInfo';
    private $urlReceita = 'http://www8.receita.fazenda.gov.br/SimplesNacional/aplicacoes.aspx?id=21';

    private $data = [];

    protected function validateAndSetCrawlerAttributes()
    {
        $this->param['cnpj'] = preg_replace('/[^0-9]/isu', '', $this->param['cnpj']);

        if (!Document::validarCnpj($this->param['cnpj'])) {
            throw new Exception("Critério de busca inválido.", 6);
        }
    }

    protected function start()
    {
        $this->search();

        $this->updateSpine('ReceitaFederalSimplesNacional', $this->data);

        return $this->data;
    }

    private function resolveCaptcha($html)
    {
        preg_match(
            '/class=\"[\w\s\-]+h-captcha\"[\w\s\-\=\"]+sitekey=\"([\w\s\-]+)\"/is',
            $html,
            $match
        );
        if (isset($match[1]) && !empty($match[1])) {
            return $this->solveHCaptchaWithCapMonster($match[1], $this->mainUrl);
        }
        throw new Exception("Erro ao localizar dados do captcha na página.", 1);
    }


    private function search()
    {
        $this->setCurlOpt([
            CURLOPT_SSL_VERIFYPEER => 0,
        ]);

        for ($i = 1; $i < self::RETRY; $i++) {
            $this->setAlternativeProxy();

            $this->getResponse($this->urlReceita);
            $html = $this->getResponse($this->mainUrl);
            $captcha = $this->resolveCaptcha($html);

            preg_match(
                '/name="__RequestVerificationToken"\s*type="hidden"\s*value="(.*?)"/isu',
                $html,
                $postMatch
            );

            if (empty($postMatch[1])) {
                throw new Exception("Erro ao buscar informações do formulário", 3);
            }

            $params = [
                'Cnpj' => $this->param['cnpj'],
                'h-captcha-response' => $captcha,
                '__RequestVerificationToken' => $postMatch[1],
            ];

            $postHtml = html_entity_decode($this->getResponse($this->mainUrl, 'POST', $params, [
                'Upgrade-Insecure-Requests' => 1,
                'Referer' => $this->mainUrl . '/Home/ConsultarCnpj?vc=' . $this->param['cnpj']
            ]));

            if (preg_match('/CNPJ\s*:\s*<span[^>]*?>\s*(.*?)\s*<\/span>/isu', $postHtml)) {
                break;
            }
        }

        $this->verifyErrors($postHtml);

        $this->parseData($postHtml);

        $infosHtml = html_entity_decode($this->getResponse($this->infoUrl));

        $this->parseInfos($infosHtml);

        //NÃO ACHEI NADA COM ESSES CAMPOS
        $this->data['eventosFuturos'] = [];
        $this->data['eventosFuturosSimei'] = [];
        $this->data['aAgendamento'] = [];
    }

    private function verifyErrors($html)
    {
        if (preg_match('/CNPJ\s*n.o\s*encontrado\s*na\s*base/isu', $html, $match)) {
            throw new Exception("Nenhum resultado encontrado.", 2);
        }

        if (preg_match('/<h5\s*class="text-danger"\s*>\s*(.*?)\s*<\/h5>/isu', $html, $match)) {
            throw new Exception($match[1], 3);
        }

        if (preg_match('/<div\s*class="validation-summary-errors"\s*>(.*?)<\/div>/isu', $html, $match)) {
            $message = strip_tags($match[1]);

            throw new Exception($message, 3);
        }
    }

    private function parseData($html)
    {
        $patterns = array(
            'cnpj' => ['/CNPJ\s*:\s*<span[^>]*?>\s*(.*?)\s*<\/span>/isu', null],
            'razao_social' => [
                '/Nome\s*Empresarial\s*:\s*<span[^>]*?>\s*(.*?)\s*<\/span>/isu', null
            ],
            'situacao_simples' => [
                '/Situa..o\s*no\s*Simples\s*Nacional\s*:\s*<span[^>]*?>(.*?)\s*pelo\s*Simples\sNacional/isu',
                null
            ],
            'data_situacao_simples' => ['/pelo\s*Simples\s*Nacional\s*desde\s*(.*?)\s*<\/span>/isu', null],
            'situacao_simei' => ['/Situa..o\s*no\s*SIMEI\s*:\s*<span[^>]*?>(.*?)\s*no\s*SIMEI/isu', null],
            'data_situacao_simei' => ['/Enquadrado\s*no\s*SIMEI\s*desde\s*(.*?)\s*<\/span>/isu', null],
        );

        $data = Str::encoding(Util::parseDados($patterns, $html));

        $data['situacao_simei'] = str_replace(
            ['NÃO enquadrado', 'Enquadrado'],
            ['NÃO optante', 'Optante'],
            $data['situacao_simei']
        );

        $this->data = $data;
    }

    private function parseInfos($html)
    {
        if (
            preg_match(
                '/<span>\s*Op..es\s*pelo\s*Simples\s*Nacional\s*em\s*Per.odos\s*Anteriores' .
                    '\s*:\s*<\/span>\s*<table[^>]*?>(.*?)<\/table>/isu',
                $html,
                $table
            )
        ) {
            $this->data['aOpcaoSimples'] = [];

            preg_match_all(
                '/<tr>\s*<td>\s*(.*?)\s*<\/td>\s*<td>\s*(.*?)\s*<\/td>\s*<td>\s*(.*?)\s*<\/td>\s*<\/tr>/isu',
                $table[1],
                $rows,
                PREG_SET_ORDER
            );

            foreach ($rows as $value) {
                $this->data['aOpcaoSimples'][] = [
                    'data_inicial' => $value[1],
                    'data_final' => $value[2],
                    'detalhamento' => $value[3],
                ];
            }
        }

        if (
            preg_match(
                '/<span>\s*Enquadramentos\s*no\s*SIMEI\s*em\s*Per.odos\s*Anteriores' .
                    '\s*:\s*<\/span>\s*<table[^>]*?>(.*?)<\/table>/isu',
                $html,
                $table
            )
        ) {
            $this->data['aOpcaoSimei'] = [];

            preg_match_all(
                '/<tr>\s*<td>\s*(.*?)\s*<\/td>\s*<td>\s*(.*?)\s*<\/td>\s*<td>\s*(.*?)\s*<\/td>\s*<\/tr>/isu',
                $table[1],
                $rows,
                PREG_SET_ORDER
            );

            foreach ($rows as $value) {
                $this->data['aOpcaoSimei'][] = [
                    'data_inicial' => $value[1],
                    'data_final' => $value[2],
                    'detalhamento' => $value[3],
                ];
            }
        }
    }
}
