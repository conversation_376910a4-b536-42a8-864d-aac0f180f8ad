<?php

namespace App\Crawler\ReceitaFederalPfInfoSimples;

use App\Crawler\ReceitaFederalPfGeneric\Models\ReceitaFederalPfGenericModel;
use App\Crawler\GenericInterface;
use App\Crawler\Spider;
use App\Manager\ColunaVertebralManager;
use App\Manager\DynamoManager;
use App\Factory\PostgresDB;
use App\Manager\InfoSimplesManager;
use App\Helper\Document;
use App\Helper\Date;
use Carbon\Carbon;
use Exception;

class ReceitaFederalPfInfoSimples extends Spider implements GenericInterface
{
    protected function validateAndSetCrawlerAttributes()
    {
        $this->param['documento'] = preg_replace('/[^0-9]/isu', '', $this->param['documento']);

        if (!Document::validarCpf($this->param['documento'])) {
            throw new Exception("Critério de busca inválido.", 6);
        }

        if (empty($this->param['data_nascimento'])) {
            $this->param['data_nascimento'] = $this->getBirthdate();
        }

        if (!Date::validateDate($this->param['data_nascimento'])) {
            throw new Exception("Critério data de nascimento no formato errado.", 6);
        }
    }

    protected function start()
    {
        $manager = new InfoSimplesManager();

        $data = $manager->searchReceitaPF(
            $this->param['documento'],
            Carbon::createFromFormat('d/m/Y', $this->param['data_nascimento'])->format('Y-m-d')
        );

        return $data['data'];
    }
    /*
    private function getBirthdate()
    {
        $dynamoManager = new DynamoManager(false);

        $spine_pf = $dynamoManager->getItem(
            'spine_pf',
            ['cpf' => (string) "{$this->param['documento']}"]
        );

        if (empty($spine_pf)) {
            throw new Exception('DataNascimentoDivergente', 1);
        }

        if (empty($spine_pf['data_nascimento'])) {
            throw new Exception('DataNascimentoDivergente', 1);
        }

        return substr($spine_pf['data_nascimento'], 6, 2) . '/' .
            substr($spine_pf['data_nascimento'], 4, 2) . '/' .
            substr($spine_pf['data_nascimento'], 0, 4);
    }
    */
    private function getBirthdate()
    {
        $doc = $this->param['documento'];
        $db = new PostgresDB();
        $results =  $db->connectCaptura()
            ->select('data_nascimento')
            ->from('receita_federal.pf')
            ->where("cpf = '$doc'")
            ->execute()
            ->fetchAll();
        $db->disconnect();

        // se não encontrar no postgre, busca no spine
        if (empty($results)) {
            $elasticManager = new ColunaVertebralManager();
            try {
                $results = $elasticManager->getSpinePf($this->param['documento']);
            } catch (Exception $e) {
                if ($e->getCode() == 2) {
                    throw new Exception('DataNascimentoDivergente', 1);
                }
                throw new Exception($e->getMessage(), $e->getCode());
            }

            if (empty($results)) {
                throw new Exception('DataNascimentoDivergente', 1);
            }

            if (empty($results['data_nascimento'])) {
                throw new Exception('DataNascimentoDivergente', 1);
            }
        }

        if (!empty($results[0])) {
            $dtNasc = str_replace("-", "", $results[0]['data_nascimento']);
        } else {
            $dtNasc = $results['data_nascimento'];
        }

        return  substr($dtNasc, 6, 2) . '/' .
            substr($dtNasc, 4, 2) . '/' .
            substr($dtNasc, 0, 4);
    }

    public function parseToGeneric(array $data, string $sourceName): ReceitaFederalPfGenericModel
    {
        $model = new ReceitaFederalPfGenericModel();

        $dataHora = explode(' ', $data['consulta_datahora']);

        $model->source = $sourceName;
        $model->cpf = $data['cpf'];
        $model->nome = $data['nome'];
        $model->nascimento = $data['data_nascimento'];
        $model->situacao = $data['situacao_cadastral'];
        $model->inscricao = $data['data_inscricao'];
        $model->digito_verificador = $data['consulta_digito_verificador'];
        $model->obito = $data['ano_obito'];
        $model->hora = $dataHora[1];
        $model->data = $dataHora[0];
        $model->chave = $data['consulta_comprovante'];
        $model->cpf_nome = "{$data['cpf']}|{$data['nome']}";
        $model->nome_dataNascimento = "{$data['nome']}|{$data['data_nascimento']}";

        return $model;
    }
}
