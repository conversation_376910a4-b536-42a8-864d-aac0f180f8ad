<?php

namespace App\Crawler\MpfCertidaoNegativa;

use Exception;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\Bucket\StaticUplexisBucket;

/**
 *  Classe da fonte (276) MPF Certidão Negativa
 *
 *  <AUTHOR> Mesquita - 20/08/2019
 */
class MpfCertidaoNegativa extends Spider
{
    //O captcha só aparece com o clique, então uso o token dele para quebrar sem o clique
    private const CAPTCHA_TOKEN = '6LeUowITAAAAAOIiAB441SS3EF77AS4ZuK0LFsaH';
    private const MAIN_URL = 'https://aplicativos.mpf.mp.br/ouvidoria/';
    private const ISSUE_CERTIFICATE_URL = 'rest/v1/publico/certidao/emitir';
    private const DOWNLOAD_URL = 'rest/v1/publico/certidao/download/';

    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const MFP_S3_PATH = 'captura/mpf_certidao_negativa/';

    private $certificateName;
    private $certificateLocalPath;
    private $certificateS3Path;
    private $certificateUrl;
    private $document;
    private $type;

    /**
     *  Valida e retorna os parametros
     *
     *  <AUTHOR> Mesquita - 20/08/2019
     *
     *  @version 1.0.0
     *
     *  @return array
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (!Document::validarCpfOuCnpj($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }

        $this->param['cpf_cnpj'] = preg_replace('#\D#isu', '', $this->param['cpf_cnpj']);
        $this->document = $this->param['cpf_cnpj'];

        $this->type = Document::validarCpf($this->param['cpf_cnpj']) ? 'F' : 'J';
    }

    /**
     *  Execução da fonte (276) MPF Certidão Negativa
     *
     *  <AUTHOR> Mesquita - 20/08/2019
     *
     *  @version 1.0.0
     *
     *  @return array
     */
    protected function start()
    {
        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::MFP_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;

        $this->setAlternativeProxy();
        $downloadHash = $this->issueCertificate();
        $this->downloadCertificate($downloadHash);
        return $this->parseData();
    }

    /**
     *  Quabra o captcha, emiti a certidão e retorna o hash para download
     *
     *  <AUTHOR> Mesquita - 20/08/2019
     *
     *  @version 1.0.0
     *
     *  @return string
     */
    private function issueCertificate()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $captcha  = $this->resolveCaptcha();
        $queryString = "?documento={$this->document}&recaptcha={$captcha}&tipoPessoa={$this->type}";
        $response = $this->getResponse(self::MAIN_URL . self::ISSUE_CERTIFICATE_URL . $queryString);

        // Caso venha constando como inexistente/inválida, vai retornar como sucesso. Não se trata de um erro,
        // mas a api retorna assim. O documento não é inválido por que é validado antes de chegar aqui.
        if (preg_match('#CPF\/CNPJ inexistente\/inv.*?lido#i', $response)) {
            throw new Exception('Documento não localizado na base do MPF', 2);
        }

        if (!preg_match('#gerada.com.sucesso#i', $response)) {
            throw new Exception('Falha ao gerar a certidão :', 3);
        }

        $response = json_decode($response, true);
        return $response['data'];
    }

    /**
     *  Baixa e salva o PDF na pasta temporária
     *
     *  @param $hash
     *
     *  <AUTHOR> Mesquita - 20/08/2019
     *
     *  @version 1.0.0
     *
     *  @return string
     */
    private function downloadCertificate($hash)
    {
        $responsePdf = $this->getResponse(
            self::MAIN_URL . self::DOWNLOAD_URL . $hash
        );

        file_put_contents($this->certificateLocalPath, $responsePdf);
    }

    /**
     *  Parse dos dados e salva PDF no S3
     *
     *  <AUTHOR> Mesquita - 20/08/2019
     *
     *  @version 1.0.0
     *
     *  @return array
     */
    private function parseData()
    {
        $pdfText = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'layout'
        ]);
        $pdfText = $this->cleanText(utf8_decode($pdfText));

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);

        $arrPatterns = [
            'documento' => ['/[cpf|cnpj]\sn.\s(.*)?\)/isU', null],
            'nome' => ['/contra\s?(.*)?\s?\(/isU', null],
            'conteudo' => ['/(CERTIFICAMOS,.*?[cpf|cnpj]\sn(.*)?\))/isU', null],
            'data_emissao' => [
                '/emitida.gratuitamente.pela.internet.em:.(\d{2}\/\d{2}\/\d{4}\s*?\d{2}:\d{2})/isU',
                null
            ],
            'data_atualizacao' => [
                '/atualiza.*?.*?o.8?do.*?banco.*?de.dados:.*?(\d{2}\/\d{2}\/\d{4}\s*?\d{2}:\d{2})/isU',
                null
            ],
            'selo_digital' => ['/selo\s*?digital\s*?de\s?seguran.*?a:(.*)?\ssala/isU', null]
        ];

        $result = Util::parseDados($arrPatterns, $pdfText);
        $result['pdf'] = $this->certificateUrl;
        return $result;
    }

    /**
     *  Resolve o captcha
     *
     *  <AUTHOR> Mesquita - 20/08/2019
     *
     *  @version 1.0.0
     *
     *  @return string
     */
    private function resolveCaptcha()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $captcha =  $this->solveReCaptcha(
            self::CAPTCHA_TOKEN,
            self::MAIN_URL . self::ISSUE_CERTIFICATE_URL,
            3,
            10
        );

        if ($captcha == 'CAPCHA_NOT_READY' || empty($captcha)) {
            throw new Exception("Erro ao recuperar o captcha.", 3);
        }

        return $captcha;
    }

    /**
     *  Limpa quebras da string
     *
     *  @param string $text
     *
     *  <AUTHOR> Mesquita - 20/08/2019
     *
     *  @version 1.0.0
     *
     *  @return string
     */
    public static function cleanText($text)
    {
        $text = preg_replace('/\n/', " ", $text);
        $text = preg_replace('/\s{2,}/', " ", $text);
        return preg_replace('/\t/', " ", $text);
    }
}
