<?php

namespace App\Crawler\DistribuicaoAcoesFederais;

use Exception;
use App\Helper\Pdf;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use GuzzleHttp\Client;

class DistribuicaoAcoesFederais extends Spider
{
    private const PDF_URL = 'http://web.trf3.jus.br/certidao/Certidao/GerarPdf/';
    private const URL = 'http://web.trf3.jus.br/certidao/Certidao/Solicitar';
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const CERTIDAO_S3_PATH = 'captura/certidao_distribuicao_acoes_federais/';
    private const RETRY = 5;

    private $documento = '';
    private $tipo = '';
    private $retries = 0;


    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->param['cpf'] != '') {
            $this->documento = $this->param['cpf'];
        } else {
            $this->documento = $this->param['cnpj'];
        }

        $this->nome = strtoupper($this->param['nome']);

        if (Document::validarCpf($this->documento)) {
            $this->tipo = 'Fisica';
        } elseif (Document::validarCnpj($this->documento)) {
            $this->tipo = 'Juridica';
        } else {
            throw new Exception("Documento inválido", 3);
        }
    }

    protected function start()
    {
        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::CERTIDAO_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;
        $data = $this->sendPostRequest();
        return $data;
    }

    private function sendPostRequest()
    {
        if ($this->debug) {
            echo PHP_EOL . __METHOD__ . PHP_EOL;
        }

        $this->setCurlOpt([
            CURLOPT_ENCODING => '',
            CURLOPT_TIMEOUT => 60
        ]);

        while ($this->retries < self::RETRY) {
            try {
                $this->setProxy();

                $data = [
                    'TipoCertidao' => '4',
                    'abrangencia' => '2',
                    'NomeConcurso' => '',
                    'Nome' => $this->param['nome'],
                    'NomeSocial' => '',
                    'TipoPessoa' => $this->tipo,
                    'CpfCnpj' => $this->documento,
                    'captcha' => $this->resolveRecaptcha()
                ];

                $response = $this->getResponse(self::URL, 'POST', $data, [
                    'Accept: */*',
                    'Content-Type: application/x-www-form-urlencoded',
                    'Accept-Encoding: gzip, deflate, br',
                    'Referer: ' . self::URL,
                    'Origin: http://web.trf3.jus.br',
                    'Host: web.trf3.jus.br',
                    'Connection: Keep-Alive'
                ]);

                $errorResponse = '/Ocorreu um evento inesperado./m';

                if (preg_match($errorResponse, $response)) {
                    throw new Exception("Erro no site do TRF, por favor tente mais tarde!", 2);
                }

                $pdf = $this->getPdf($response);

                if (preg_match('/PDF-1\../', $pdf)) {
                    return $this->savePdfAndReturnText($pdf);
                }

                throw new Exception('Não foi possível emitir o PDF.', 1);
            } catch (Exception $e) {
                $this->retries++;

                if (
                    $e->getCode() == 2
                    || $this->retries >= self::RETRY
                ) {
                    throw $e;
                }
                sleep(1);
            }
        }
    }

    private function savePdfAndReturnText($pdf)
    {
        if ($this->debug) {
            echo PHP_EOL . __METHOD__ . PHP_EOL;
        }

        file_put_contents($this->certificateLocalPath, $pdf);

        if (filesize($this->certificateLocalPath) == 0) {
            throw new Exception('Não foi possível gravar o PDF.', 1);
        }

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        $this->pdf = $this->certificateUrl;
        $data = [
            'pdf' => $this->pdf
        ];
        return $data;
    }

    private function getPdf($html)
    {
        if ($this->debug) {
            echo PHP_EOL . __METHOD__ . PHP_EOL;
        }

        $pdfPattern = '/ertidao\/Certidao\/GerarPdf\/(.*?)"/m';
        preg_match($pdfPattern, $html, $hashPdf);
        $pdfLink = self::PDF_URL . $hashPdf[1];
        $pdfresponse = $this->getResponse($pdfLink);
        return $pdfresponse;
    }

    private function resolveRecaptcha()
    {
        if ($this->debug) {
            echo PHP_EOL . __METHOD__ . PHP_EOL;
        }

        $html = $this->getResponse(self::URL);

        if (!preg_match('/grecaptcha\.execute\(\'(.*?)\',\s{\saction:\s\'(.*?)\' /', $html, $matches)) {
            throw new Exception('Não foi possível recuperar o sitekey', 3);
        }

        return $this->solveReCaptchaV3($matches[1], $matches[2], self::URL);
    }
}
