<?php

namespace App\Crawler\Homonimos;

use App\Crawler\Spider;
use GuzzleHttp\Client;
use App\Helpers\StringHelper;

class Homonimos extends Spider
{
    private $marshaler;
    protected function start(): array
    {
        $response = $this->getHomonimos();
        $data = [
            'totalHomonimos' => $response
        ];
        return $data;
    }

    protected function validateAndSetCrawlerAttributes(): void
    {
        if (empty($this->param['nome'])) {
            throw new Exception('Parâmetro Nome obrigatório', 1);
        }
    }

    private function getHomonimos()
    {
        $nome = strtoupper($this->param['nome']);
        $this->client = new Client([
            'base_uri'  =>  CAPTURA . "spine/",
            'timeout'   =>  60,
            'connect_timeout'   =>  15,
            'http_errors'   =>  false
        ]);


        $count = 0;
        $homonimos = json_decode($this->client->get('search.php', ['query' => [
            'field'     =>  'individuos',
            'homonimos' => '1',
            'query'     => mb_strtoupper($nome)
        ]])->getBody()->getContents());

        if ($homonimos->suggestions) {
            $count = count($homonimos->suggestions);
        }

        if ($count == 1) {
            return $count = 0;
        }

        return $count;
    }
}
