<?php

namespace App\Crawler\CertidaoCnatTrtPR;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\S3\S3;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use Exception;

class CertidaoCnatTrtPR extends Spider
{
    private const BASE_URL = "https://pje.trt9.jus.br";
    private const REQUEST_URL = "/certidoes/trabalhista/emissao";
    private const API_URL = "/pje-certidoes-api/api";
    private const PATH_STATIC_S3 = S3_STATIC_URL;
    private const PATH_CERTIDAO_S3 = 'captura/certidao_cnat_trt9_pr/';

    private $documento = ' ';
    private $nomeRazaoSocial = ' ';
    private $criterio = ' ';

    public function start()
    {
        $data = $this->makeRequest();
        $data = $this->savePdfAndGetText($data);
        $result = $this->parseText($data['textPdf']);

        return [
            'pdf' => $data['urlPdf'],
            'info' => $result
        ];
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->documento = trim($this->param['documento']);

        if (empty($this->documento)) {
            throw new Exception('Parâmetro Inválido', 1);
        }

        if (Document::validarCpf($this->documento)) {
            $this->documento = Document::formatCpf($this->documento);
            $this->criterio = "CPF";
        } elseif (Document::validarCnpj($this->documento)) {
            $this->documento = Document::formatCnpj($this->documento);
            $this->documento = substr($this->documento, 0, 10);
            $this->criterio = "RAIZ_DE_CNPJ";
        } else {
            $this->nomeRazaoSocial = trim($this->documento);
            $this->criterio = "NOME";
        }
    }


    /** Função que faz requisição e retorna o pdf
     * @return mixed
     * @throws Exception
     */
    private function makeRequest()
    {
        $retry = 3;
        while ($retry >= 0) {
            try {
                $params = json_encode([
                                          "criterioDeEmissao" => $this->criterio,
                                          "nome" => $this->nomeRazaoSocial,
                                          "numeroDoDocumento" => $this->documento,
                                          "respostaDoCaptcha" => $this->resolveRecaptcha()
                                      ]);


                $result = json_decode(
                    $this->getResponse(
                        self::BASE_URL . self::API_URL . '/certidoes/trabalhistas/emissao',
                        'POST',
                        $params
                    ),
                    true
                );

                if ($result['codigo']) {
                    return $this->getResponse(
                        self::BASE_URL . self::API_URL . "/certidoes/trabalhistas/{$result['codigo']}"
                    );
                }
            } catch (Exception) {
                if ($retry == 0) {
                    throw new Exception('Não foi possível capturar as informações da página.', 6);
                }
                $retry--;
            }
        }
    }


    /** Função que salva o pdf e retorna o texto do arquivo
     * @param $file
     * @return array
     * @throws Exception
     */
    private function savePdfAndGetText($text)
    {
        $uniqid = md5(uniqid(rand(), true));
        $certificateName = "{$uniqid}.pdf";
        $certificateLocalPath = "/tmp/{$certificateName}";
        $certificateS3Path = self::PATH_CERTIDAO_S3 . $certificateName;
        $certificateUrl = self::PATH_STATIC_S3 . $certificateS3Path;

        $response = json_decode($text, true);

        (new Pdf())->saveHtmlToPdf(utf8_decode($response['conteudoHTML']), $certificateLocalPath);

        (new S3(new StaticUplexisBucket()))->save($certificateS3Path, $certificateLocalPath);

        $text = (new Pdf())->getTextFromPdf($certificateLocalPath, ['layout', 'nopgbrk']);

        return ["urlPdf" => $certificateUrl, "textPdf" => $text];
    }

    /** Função que faz o parse do texto do PDF
     * @param $text
     * @return array
     */
    private function parseText($text)
    {
        $patterns = [
            'codVerificacao' => ['@Código\sde\sverificação:(.*)@'],
            'emissao' => ['@Certidão\semitida\sem\s(.*)@'],
            'descricao' => ['@(Certifica-se[\s\S]*?\s+responsabilidade.)@'],
            'observacoes' => ['@Observações:([\s\S]*?)\s+Certidão@']
        ];

        $data = Util::parseDados($patterns, $text);
        $data = array_map("utf8_decode", $data);

        if (!preg_match('/N.O\sCONSTAM/isu', $text, $matches)) {
            preg_match_all('/responsabilidade.\n([\s\S]*?\s+)Observa..es/isu', $text, $processos);
            $patterns = array("/((Nome\scompleto|CPF|Raiz\sdo\sCNPJ)+\spesquisado:(.*)+\\n)/", "/(Nomes.*)+\\n/");
            $listaProcessos = preg_replace($patterns, "", $processos[1]);
            $data['processos'] = trim($listaProcessos[0]);
        }

        return $data;
    }


    /** Função que faz quebra do captcha
     * @throws Exception
     */
    private function resolveRecaptcha()
    {
        $captchaToken = json_decode($this->getResponse(self::BASE_URL . self::API_URL . '/propriedades'));

        $retry = 3;
        do {
            if (!empty($captchaToken->chaveDeSiteDoCaptcha)) {
                return $this->solveReCaptcha($captchaToken->chaveDeSiteDoCaptcha, self::BASE_URL . self::REQUEST_URL);
            }
            $retry--;
        } while ($retry > 0);
        throw new Exception("Erro ao localizar dados do captcha na página.", 3);
    }
}
