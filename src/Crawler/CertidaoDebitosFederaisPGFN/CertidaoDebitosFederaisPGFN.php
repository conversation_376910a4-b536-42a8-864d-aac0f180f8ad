<?php

namespace App\Crawler\CertidaoDebitosFederaisPGFN;

use Exception;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Helper\Util;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Exception\PdfException;
use App\Manager\S3\Bucket\StaticUplexisBucket;

/**
 *  Classe da fonte (51)PGFN - Certidão de Débitos Relativos a Créditos Tributários Federais e à Dívida Ativa da União
 *
 *  <AUTHOR> Mesquita - 11/10/2019
 *  <AUTHOR> - 16/05/2022 -
 *      Subi alterações que efetuei em Janeiro/2022. Não subi antes por que a fonte era em Node.
 */
class CertidaoDebitosFederaisPGFN extends Spider
{
    private const BASE_URL = 'https://servicos.receita.fazenda.gov.br';
    private const HOST_URL = 'solucoes.receita.fazenda.gov.br';
    private const URL = self::BASE_URL . '/Servicos/certidaointernet/<type>/EmitirPGFN';
    private const RETRY = 5;
    private const RETRY_SITE_ERRORS = 5;
    private const DEFAULT_PARSE = 1;
    private const INSUFFICIENT_DATA_PARSE = 2;
    private const TYPE_CPF = 'PF';
    private const TYPE_CNPJ = 'PJ';

    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const MFP_S3_PATH = 'captura/pgfn_certicao_debitos_federais/';

    private $certificateName;
    private $certificateLocalPath;
    private $certificateS3Path;
    private $certificateUrl;
    private $pdf = null;
    private $retrySiteErrors = 0;
    private $retries = 0;

    private $document;
    private $type;

    /**
     *  Valida e retorna os parametros
     *
     *  <AUTHOR> Mesquita - 11/10/2019
     *
     *  @version 1.0.0
     *
     *  @version 1.1.0 - Ricardo Vidal - 17/01/2022 - Validação PF ou PJ
     *
     *  @return array
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (!Document::validarCpfOuCnpj($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }

        $this->param['cpf_cnpj'] = preg_replace('#\D#isu', '', $this->param['cpf_cnpj']);
        $this->document = $this->param['cpf_cnpj'];

        $this->type =  Document::validarCpf($this->param['cpf_cnpj']) ? self::TYPE_CPF : self::TYPE_CNPJ;
    }

    /**
     * Fonte (51) PGFN - Certidão de Débitos Relativos a Créditos Tributários Federais e à Dívida Ativa da União
     *
     *  <AUTHOR> Mesquita - 11/10/2019
     *
     *  @version 1.0.0
     *
     *  @version 1.1.0 - Ricardo Vidal - 17/01/2022 - Adicionei loop e troquei proxy
     *
     *  @return array
     */
    protected function start()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }
        try {
            $uniqd = md5(uniqid(rand(), true));
            $this->certificateName = "{$uniqd}.pdf";
            $this->certificateLocalPath = "/tmp/{$this->certificateName}";
            $this->certificateS3Path = self::MFP_S3_PATH . $this->certificateName;
            $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;
            $this->url = str_replace('<type>', $this->type, self::URL);
            $this->setAlternativeProxy();
            return $this->issueCertificate();
        } catch (Exception $e) {
            $this->retries++;

            if (
                $e->getCode() == 2
                || $this->retries >= self::RETRY
            ) {
                throw new PdfException($e->getMessage(), $e->getCode(), $this->pdf);
            }
            $this->start();
        }
    }

    /**
     *  Iniciar emissão da certidão
     *
     *  <AUTHOR> Mesquita - 11/10/2019
     *
     *  @version 1.0.0
     *
     *  @return array
     */
    private function issueCertificate()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }
        return $this->postDocument();
    }

    /**
     *  Resolver o captcha
     *
     *  <AUTHOR> Mesquita - 11/10/2019
     *
     *  @version 1.0.0
     *
     *  @version 1.1.0 - Ricardo Vidal - 17/01/2022 - Troca para hcaptcha
     *
     *  @return string
     */
    private function resolveCaptcha()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $html = $this->getResponseWithRetry($this->url);

        if (!preg_match('/data-sitekey=\'(.*?)\'/isu', $html, $matches)) {
            throw new Exception('Não foi possível recuperar o sitekey', 3);
        }
        $this->captcha = $this->solveHCaptcha($matches[1], 'https://' . self::HOST_URL);
    }


    /**
     *  Enviar post de emissão da certidão
     *
     *  <AUTHOR> Mesquita - 11/10/2019
     *
     *  @version 1.0.0
     *
     *  @version 1.1.0 - Ricardo Vidal - 17/01/2022 - Ajuste de parâmetros e verificação de resultado
     *
     *  @return array
     */
    private function postDocument()
    {
        $this->resolveCaptcha();

        $params = [
            'NI' => $this->document,
            'h-captcha-response' => $this->captcha
        ];

        $response = $this->getResponseWithRetry($this->url . '/Verificar', 'POST', $params);

        if (preg_match('/deve.ser.emitida.para.o.cnpj.da.matriz/is', $response)) {
            $this->saveHtmlToPdf($response);
            throw new Exception('A certidão deve ser emitida para o CNPJ da matriz: ' . $this->document, 2);
        }

        if (preg_match("/suspensa pela Secretaria da Receita Federal do Brasil/", $response)) {
            throw new Exception("Inscrição suspensa pela Secretaria da Receita Federal do Brasil", 6);
        }

        return $this->getCertificate();
    }

        /**
     *  Checa e retorna o parse resultado
     *
     *  <AUTHOR> Vidal - 17/01/2022 - Verificando se o retorno é um PDF e adicionei verificação de parse
     *
     *  @return array
     */
    private function checkAndParseResult($result)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (preg_match('/Proxy.Error/i', $result)) {
            if ($this->retrySiteErrors >= self::RETRY_SITE_ERRORS) {
                throw new Exception('Erro de proxy', 3);
            }

            $this->retrySiteErrors++;
            return $this->issueCertificate();
        }

        //Certidão emitida, fazer o parse
        if (preg_match('/PDF\-1\.4/is', $result)) {
            return $this->parseData($result, self::DEFAULT_PARSE);
        }

        if (
            preg_match(
                '/insuficientes/i',
                $result
            )
        ) {
            $this->saveHtmlToPdf($result);
            return $this->parseData($result, self::INSUFFICIENT_DATA_PARSE);
        }

        if ($this->retrySiteErrors >= self::RETRY_SITE_ERRORS) {
            throw new Exception('Houve um erro com o conteúdo da certidão', 3);
        }

        $this->retrySiteErrors++;
        return $this->issueCertificate();
    }

    /**
     *  Retorna certificado
     *
     *  <AUTHOR> Vidal - 17/01/2022 - Emite certificado
     *
     *  @version 1.0.0
     */
    public function getCertificate()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $url = $this->url . "/Emitir?Ni=$this->document";

        $response = function () use ($url) {
            $this->setAlternativeProxy();
            $content = $this->getResponseWithRetry($url);
            while (preg_match('/em.processamento/i', $content)) {
                // Aguardar 15 segundos para emissão do certificado
                echo 'Aguardando 15 segundos para a emissão do certificado' . PHP_EOL;
                sleep(15);

                $content = $this->getResponseWithRetry($url);
            }

            if (preg_match('/mensagem/i', $content)) {
                echo 'O site retornou uma mensagem, aguardando' . PHP_EOL;

                //Recuperar o ID da mensagem
                ['Id' => $id,] = json_decode($content, true);
                sleep(1);
                $content = $this->getResponseWithRetry($this->url . "/ResultadoEmissao/$id");
            }

            return $content;
        };

        return $this->checkAndParseResult($response());
    }

    /**
     *  Converter HTML para PDF e salvar no S3
     *
     *  <AUTHOR> Mesquita - 11/10/2019
     *
     *  @version 1.0.0
     */
    private function saveHtmlToPdf($response)
    {
        (new Pdf())->saveHtmlToPdf($response, $this->certificateLocalPath);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        $this->pdf = $this->certificateUrl;
    }

    /**
     *  Salvar o PDF no s3 e retornar texto
     *
     *  <AUTHOR> Vidal - 17/01/2022
     *
     *  @version 1.0.0
     *
     *  @return string
     */
    private function savePdfAndReturnText($pdf)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        file_put_contents($this->certificateLocalPath, $pdf);
        $text = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'layout',
            'nopgbrk'
        ]);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        $this->pdf = $this->certificateUrl;

        return $text;
    }

    /**
     *  Parse dos dados e salva PDF no S3
     *
     *  <AUTHOR> Vidal - 17/01/2022 - Ajuste para verificar o tipo de parse
     *
     *  @version 1.0.0
     *
     *  @return array
     */
    private function parseData($result, $type)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if ($type == self::DEFAULT_PARSE) {
            $text = $this->savePdfAndReturnText($result);
        } else {
            $text = htmlspecialchars_decode($this->cleanText($result));
        }

        $result = $this->getParseDataToResponse($text, $type);
        $result['conteudo'] = $this->cleanText(strip_tags($result['conteudo']));
        $result['pdf'] = $this->certificateUrl;
        return $result;
    }

    /**
     *  Limpa quebras da string
     *
     *  @param string $text
     *
     *  <AUTHOR> Vidal- 17/01/2022
     *
     *  @version 1.0.0
     *
     *  @return string
     */
    public static function cleanText($text)
    {
        $text = preg_replace('/\n/', " ", $text);
        $text = preg_replace('/\s{2,}/', " ", $text);
        $text = str_replace('&nbsp;', '', $text);
        return preg_replace('/\t/', " ", $text);
    }

    /**
     *  Parse dos dados de acordo com a resposta
     *
     *  <AUTHOR> Vidal - 17/01/2022 - Ajuste para fazer o parse do texto do PDF.
     *
     *  @version 1.0.0
     *
     *  @return array
     */
    private function getParseDataToResponse($result, $type)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $result = utf8_decode($result);
        $arrPatterns = [
            'documento' => ['/[cnpj|cpf]:([^>]*)?Ressal/is', null],
            'nome' => ['/nome:(.*?)\n[cpf\/cnpj]/i', null],
            'conteudo' => ['/(Ressalvado.*invalidar..este.documento.)/is', null],
            'data_emissao' => ['/emitida..s.*do.dia.(.*?).<hora/is', null],
            'data_validade' => ['/v.lida.at..(.*?).C.digo.de.controle/is', null],
            'codigo_controle' => ['/C.digo.de.controle.da.certid.o:\s(.*?)Qualquer.rasura/is', null]
        ];

        if ($type == self::INSUFFICIENT_DATA_PARSE) {
            $arrPatterns['conteudo'] = ['/(as\sinforma..es\sdispon.veis.*?atendimento.*?\.)/is', null];
        }

        return Util::parseDados($arrPatterns, $result);
    }

    /**
     * Repete a requisição até obter resultado. As vezes retorna página em branco por causa de bloqueio
     *
     * <AUTHOR> Vidal - 01/04/2022
     * <AUTHOR> VIdal - 16/05/2022 - Add header host para outra url
     *
     * @param string $url
     * @param string $method
     * @param array $param
     * @param array $headers
     *
     * return string
     */
    private function getResponseWithRetry(
        $url,
        $method = 'GET',
        $param = [],
        $headers = []
    ) {
        $retries = 0;
        $patternEmpty = '/empty.reply/i';
        $patternRejected = '/request.rejected/i';
        $requestRejectError = 'Requisição bloqueada pelo site';

        if (empty($headers)) {
            $headers = [
                'Host: ' . self::HOST_URL
            ];
        }

        while ($retries < self::RETRY) {
            try {
                $error = null;
                $this->setAlternativeProxy();
                $response = $this->getResponse($url, $method, $param, $headers);
                if (preg_match($patternRejected, $response)) {
                    throw new Exception($requestRejectError, 3);
                }
                break;
            } catch (\Exception $e) {
                $error = $e;
                if (
                    !preg_match($patternEmpty, $e->getMessage())
                    && $e->getMessage() != $requestRejectError
                ) {
                    break;
                }
                echo 'A requisição retornou uma página vazia ou rejeitada por bloqueio. Repetindo...' . PHP_EOL;
                $retries++;
            }
        }

        if ($error) {
            throw $error;
        }

        return $response;
    }
}
