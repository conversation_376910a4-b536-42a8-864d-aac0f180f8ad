<?php

namespace App\Crawler\CefFgts;

class Certificate
{
    public function certificate($result)
    {

        $html = "<!DOCTYPE>
        <html xmlns='http://www.w3.org/1999/xhtml'>
        <head>

            <meta charset='utf-8' /> 
            <meta name='author' content='RQ_AOA' /> 
            <meta name='description' content='Caixa Prototype' /> 
            <meta name='viewport' content='width=device-width, initial-scale=1.0' />
            <meta http-equiv='X-UA-Compatible' content='IE=edge' />
            
            <title>
            Consulta Regularidade do Empregador</title>
            <link class='component' href='/consultacrf/a4j/s/3_3_2.SR1org/richfaces/renderkit/html/css" .
            "/basic_classes.xcss/DATB/eAELXT5DOhSIAQ!sA18_.jsf' rel='stylesheet' type='text/css' />" .
            "<link class='component' href='/consultacrf/a4j/s/3_3_2.SR1org/richfaces/renderkit/html/css/" .
            "extended_classes.xcss/DATB/eAELXT5DOhSIAQ!sA18_.jsf' media='rich-extended-skinning' " .
            "rel='stylesheet' type='text/css' /><script src='/consultacrf/a4j/g/3_3_2.SR1org.ajax4jsf." .
            "javascript.AjaxScript.jsf' type='text/javascript'></script><script src='/consultacrf/a4j/" .
            "g/3_3_2.SR1org/ajax4jsf/javascript/scripts/form.js.jsf' type='text/javascript'></script>" .
            "<script type='text/javascript'>window.RICH_FACES_EXTENDED_SKINNING_ON=true;</script><script " .
            "src='/consultacrf/a4j/g/3_3_2.SR1org/richfaces/renderkit/html/scripts/skinning.js.jsf' " .
            "type='text/javascript'></script><style>
                form {
                    padding: 2em 13%;
                }
                .submit-d {
                    position: relative;
                    display: inline-block;
                    height: 2.65em;
                    min-width: 6em;
                    width: 100%;
                    border-width: 1px;
                    border-style: solid;
                    padding: 0 1.5em;
                    border-radius: 2px;
                    box-shadow: 0px 2px 0 rgba(31,42,71,.1);
                    font-family: 'FuturaWeb', sans-serif;
                    font-size: 1.125em;
                    line-height: 1;
                    outline: none;
                }
                
                a.submit-d {
                    line-height: 2.8rem;
                    cursor: pointer;
                }
                
                a.submit-small {
                    line-height: 2.6rem;
                }
                
                a.submit-d:hover {
                    color: #fff;
                }
                
                .submit-d:focus,
                .submit-selected {
                    box-shadow: 5px 5px 5px rgba(0,0,0,.15) inset; 
                }
                
                .submit-non-fluid {
                    width: auto;
                }
                
                .submit-left {
                    float:left;
                }
                
                .submit-disabled {
                    border-color: #a5aab5 !important;
                    background: #fff;
                    background: -webkit-linear-gradient(top,#fff 0%, #e9e9ec 100%) !important;
                    background: linear-gradient(to bottom,#fff 0%, #e9e9ec 100%) !important;
                    color: #4c556c !important;
                    opacity: .4 !important;
                }
                
                .submit-small {
                    font-size: 1em;
                }
                
                .submit-big {
                    font-size: 1.2em;
                }
                
                .submit-white {
                    border-color: #a5aab5;
                    background: -webkit-linear-gradient(top,#fff 0%, #e9e9ec 100%);
                    background: linear-gradient(to bottom,#fff 0%, #e9e9ec 100%);
                    color: #006bae;
                }
                
                .submit-d.submit-transparent {
                    background: transparent;
                    border-color: transparent;
                    box-shadow: none;
                }
                
                .submit-white:hover {
                    border-color: #8c909a;
                    background: -webkit-linear-gradient(top,#fff 0%, #f4f4f6 100%);
                    background: linear-gradient(to bottom,#fff 0%, #f4f4f6 100%);
                }
                
                .submit-white:focus,
                .submit-white.submit-selected {
                    background: #dcdddf;
                    border-top-color: #4c556c;
                    color: #005d98;
                }
                
                .submit-blue {
                    border-color: #1f2a47;
                    background: -webkit-linear-gradient(top,#058ce0 0%, #047ecb 100%);
                    background: linear-gradient(to bottom,#058ce0 0%, #047ecb 100%);
                    color: #fff;
                }
                
                .submit-blue:hover {
                    border-color: #171d2f;
                    background: -webkit-linear-gradient(top,#119af0 0%, #0f8cda 100%);
                    background: linear-gradient(to bottom,#119af0 0%, #0f8cda 100%);
                }
                
                .submit-blue:focus,
                .submit-blue.submit-selected {
                    background: #0b6daa;
                    border-top-color: #000;
                    color: #fff;
                }
                
                .submit-orange {    
                    background: -webkit-linear-gradient(top,#fda917 0%, #fc8f01 100%); 
                    background: linear-gradient(to bottom,#fda917 0%, #fc8f01 100%); 
                    border-color: #9f6705;
                    color: #fff;    
                }
                
                .submit-orange:hover {
                    border-color: #6c4105;
                    background: -webkit-linear-gradient(top,#ffb32d 0%, #ff9a00 100%);
                    background: linear-gradient(to bottom,#ffb32d 0%, #ff9a00 100%);
                }
                
                .submit-orange:focus,
                .submit-orange.submit-selected {
                    background: #dd790d;
                    border-top-color: #462d09;
                    color: #fff;
                }
                        
            </style>
            

        <body>
        <!-- bloco principal --><span id='mainOutputPanel'>
                    <!-- &lt;div class='form-wrapper'&gt; -->
                    <div class='form-wrapper'><form id='mainForm' name='mainForm' method='post' " .
            "action='/consultacrf/pages/impressao.jsf'>
                        <!-- &lt;rich:panel id='mainPanel'&gt; -->
            
            <br /><br />    
                <table width='100%' cellspacing='0' cellpadding='0' class='txtcentral' style='border: " .
            "1px solid black;' align='center'>
                    <tr>
                        <td>
                        <table WIDTH='100%' CELLSPACING='0' CELLPADDING='0' class='txtcentral'>
                            <tr>
                                <td WIDTH='40%'></td>
                                <td WIDTH='10%'></td>
                                <td></td>
                            </tr>
                            <TR>
                                <TD align='left' style='padding: 15px;'><img border='0' src='" . S3_STATIC_URL .
                                "CefFgts/assets/caixa.jpg' width='180' height='44' /></TD>
                            </TR>
                            <TR><TD colspan='2' style='padding: 15px;'>
                                <span style='font-size: 13pt' align='center'><strong>Certificado de " .
            "Regularidade do FGTS - CRF</strong></span>
                            </TD></TR>
                        </table>
                        <table WIDTH='100%' CELLSPACING='0' CELLPADDING='0' class='txtcentral'>
                            <TR><TD style='padding: 15px;' colspan='2'></TD></TR>
                            <TR><TD colspan='2'></TD></TR>
                            <TR>
                                <TD width='22%'><font style='font-family:Verdana;font-size:10pt;padding: 15px;'>
                                    <strong>
                                    Inscrição:
                                    </strong>
                                    </font>
                                </TD>
                                <TD>
                                    <font style=' font-family: Verdana;font-size:8pt'>
                                    	<span class='valor'>{$result['inscricao']}</span> 
                                    </font>
                                </TD>
                            </TR>
                            <TR>
                                <TD width='22%' valign='top'>
                                <font style=' font-family: Verdana;font-size:10pt;padding: 15px;'>
                                <strong>Razão Social:</strong>
                                </font>
                                </TD>
                                <TD>
                                    <font style=' font-family: Verdana;font-size:8pt'>
                                    	<span class='valor'>{$result['razao_social']}</span> 
                                    </font>
                                </TD>
                            </TR>
                            <!-- &lt;TR&gt;
                                &lt;h:panelGroup rendered='false'&gt;
                                &lt;TD  width='22%' &gt;
                                    &lt;font style=' font-family: Verdana;font-size:10pt'&gt;
                                    &lt;strong&gt;Nome Fantasia:&lt;/strong&gt;&lt;/font&gt;    
                                &lt;/TD&gt;
                                &lt;TD&gt;
                                    &lt;font style=' font-family: Verdana;font-size:8pt'&gt;
                                    &lt;h:outputText styleClass='valor' value='' /&gt;
                                    &lt;/font&gt;
                                &lt;/TD&gt;
                                &lt;/h:panelGroup&gt; 
                            &lt;/TR&gt; -->
                        
                            <TR>
                            <TD width='22%' valign='top'>
                            <font style=' font-family: Verdana;font-size:10pt;padding: 15px;'>" .
            "<strong>Endereço:</strong></font>
                            </TD>
                                <TD>
                                <font style=' font-family: Verdana;font-size:8pt'>
                                	<span class='valor'>{$result['endereco']}</span> 
                                </font>
                                </TD>
                            </TR>
                            <TR><TD colspan='2'></TD></TR>
                            <TR><TD colspan='2'></TD></TR>
                            <TR>
                                <TD colspan='2' style='text-align: justify;padding: 15px;'>
                                <br />
                                <font style=' font-family: Verdana;font-size:10pt'>
                                	{$result['texto_informativo']}
                                </font>
                                </TD>
                            </TR>
                            <TR><TD colspan='2'></TD></TR>
                            <TR><TD colspan='2'></TD></TR>
                            <TR>
                                <TD style='text-align: justify;padding: 15px;' colspan='2'>
                                
                                <font style=' font-family: Verdana;font-size:10pt'>O presente Certificado " .
            "não servirá de prova contra cobrança de quaisquer débitos referentes
                                a contribuições e/ou encargos devidos, decorrentes das obrigações com o FGTS.</font>
                                </TD>
                            </TR>
                            <TR>
                                <TD colspan='2' style='padding:15px;'>
                                <font style=' font-family: Verdana;font-size:10pt'>
                                	<strong>Validade:</strong>
                                	<span class='valor'>{$result['periodo_validade']}</span>
                                </font>
                                <br /><br /><font style=' font-family: Verdana;font-size:10pt'><strong>" .
            "Certificação Número: </strong>
                                	<span class='valor'>{$result['certidao_numero']}</span>
                                </font>
                                </TD>
                            </TR>
                            
                            <TR>
                                <TD colspan='2' style='padding:15px;'><font style='font-family:Verdana;" .
            "font-size:10pt;'><span class='valor'>Informação obtida em  </span>
                                	<span class='valor'>{$result['data_hora_consulta']}</span>
                                </font></TD>
                            </TR>
                            <TR>
                                <TD style='text-align:justify;padding:15px;' colspan='2'>
                                <font style=' font-family: Verdana;font-size:10pt'>
                                	{$result['certificado_utilizacao']}
                                </font>
                                </TD>
                                </TR>
                        </table>
                        </td>
                        </tr>
                    </table>
                    </form>
                    </div></span>
          </body>
        </html>";

        return $html;
    }
}
