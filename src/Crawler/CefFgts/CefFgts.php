<?php

namespace App\Crawler\CefFgts;

use Exception;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;

class CefFgts extends Spider
{
    private const CSS = S3_STATIC_URL . 'CefFgts/assets/certificado.css';
    private const IMAGEM = S3_STATIC_URL . 'CefFgts/assets/caixa.jpg';
    private const CONSULTA_URL = 'https://consulta-crf.caixa.gov.br/consultacrf/pages/consultaEmpregador.jsf';
    private const FORM_URL = 'https://consulta-crf.caixa.gov.br/consultacrf/pages/consultaRegularidade.jsf';
    private const CERTIFICADO_URL = 'https://consulta-crf.caixa.gov.br/consultacrf/pages/FgeCfSImprimirCrf.jsf';

    private $pdf = '';
    public $headers = '';
    public $headersPost = '';

    protected function start()
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }

        $retry = 10;
        while ($retry >= 0) {
            try {
                $this->loadsInitialData();
                $result = $this->getDadosPost();
                if (empty($result)) {
                    throw new Exception('Nenhum resultado encontrado!', 3);
                }
                return $result;
            } catch (Exception $e) {
                if ($this->debug) {
                    print(PHP_EOL . $e->getMessage() . PHP_EOL);
                }

                if ($e->getCode() != 3) {
                    throw new Exception($e->getMessage(), $e->getCode());
                }

                if ($retry == 0) {
                    throw new Exception($e->getMessage(), 2);
                }

                curl_close($this->ch);
                $this->openCurl();

                $retry--;

                sleep(10);
            }
        }
    }

    public function loadsInitialData()
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }

        $this->setProxy();

        $this->headers = [
            "Accept: */*",
            "Accept-Encoding: gzip, deflate, br",
            "Accept-Language: pt-BR,pt;q=0.9",
            "Upgrade-Insecure-Requests: 1",
            "User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0"
        ];

        $this->headersPost = [
            "Accept: */*",
            "Accept-Language: pt-BR,pt;q=0.9",
            "Origin: https://consulta-crf.caixa.gov.br",
            "Referer: https://consulta-crf.caixa.gov.br/consultacrf/pages/consultaEmpregador.jsf",
            "User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0"
        ];
    }

    /**
     *
     * <AUTHOR> Alves
     * Data: 24/06/2019
     *
     */
    private function getDadosPost()
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }

        $response = $this->getResponse(self::CONSULTA_URL, [], $this->headers);

        $this->checkErros($response);

        preg_match('/javax.faces.ViewState:0" value="(.*?)"/m', $response, $view);

        $params = [
            'AJAXREQUEST' => '_viewRoot',
            'mainForm:tipoEstabelecimento' => '1',
            'mainForm:txtInscricao1' => Document::removeMask($this->param['cnpj']),
            'mainForm:uf' => '',
            'mainForm:txtCaptcha' => $this->resolveCaptcha($response),
            'mainForm' => 'mainForm',
            'autoScroll' => '',
            'javax.faces.ViewState' => $view[1],
            'mainForm:btnConsultar' => 'mainForm:btnConsultar',
        ];

        $html = $this->getResponse(self::CONSULTA_URL, 'POST', $params, $this->headersPost);

        if (preg_match('/captcha inv.*lido/i', $html)) {
            throw new Exception('Falha ao quebrar o captcha', 3);
        }

        if (preg_match('/<body.*?.Error/', $html)) {
            throw new Exception('Falha ao conectar com servidor', 3);
        }

        return $this->getDadosFgts(Str::encoding($html));
    }

    private function checkErros($html)
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }

        if (preg_match('/403\sForbidden/is', $html)) {
            throw new Exception("Erro: 403 Forbidden", 3);
        }

        if (preg_match('/Estamos\sdetectando\scomportamento\smalicioso/is', $html)) {
            throw new Exception("Estamos detectando comportamento malicioso no acesso.", 3);
        }
    }

    private function getImageCaptcha($url)
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }
        $url = Str::encoding($url);
        preg_match('/<div class="captcha-imagem"> <img src="(.*?)"/m', $url, $captchaSrc);
        list($captchaPathInfo, $captchaImage) = explode(',', $captchaSrc[1]);
        file_put_contents($this->captcha_path, base64_decode($captchaImage));
    }

    private function resolveCaptcha($response)
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }
        $this->getImageCaptcha($response);
        $this->breakCaptcha();
        return $this->captcha;
    }

    private function getDadosFgts($html)
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }

        if (preg_match('/não\s+são\s+suficientes/is', $html)) {
            throw new Exception(
                "As informações disponíveis não são suficientes para a comprovação " .
                    "automática da regularidade do empregador perante o FGTS",
                2
            );
        }

        if (!preg_match('/Certificado de Regularidade do FGTS - CRF/i', $html)) {
            $this->checkErros($html);
            throw new Exception("Nada consta", 3);
        }

        if (preg_match('/Empregador não cadastrado. /m', $html)) {
            throw new Exception('Empregador não cadastrado', 2);
        }

        preg_match_all('/<span class="valor">(.*?)<\/span>\s<br \/>/', $html, $razao_inscricao);

        if (!isset($razao_inscricao[1][0]) && $razao_inscricao[1][1]) {
            throw new Exception("Falha ao obter razão social", 3);
        }

        $inscricao = $razao_inscricao[1][0];
        $razaoSocial = $razao_inscricao[1][1];

        preg_match('/javax.faces.ViewState:0" value="(.*?)"/m', $html, $javaxRegex);
        preg_match('/Obtenha\so<a href="#"\sid="(.*?)"/m', $html, $formId);

        $regularidadeHtml = $this->getRegularity($javaxRegex[1], $formId[1]);

        $endereco = '';
        $informativo = '';
        if (preg_match_all('/<span class="valor">(.*?)<\/span>/m', $regularidadeHtml)) {
            preg_match('/<div class="wrapper">\s<p>\s(A.*?)\s<\/p>/m', $regularidadeHtml, $informativo);
            preg_match(
                '/<p>.*?Endereço:(.*?)<\/p>/',
                $regularidadeHtml,
                $htmlEndereco
            );

            preg_match_all(
                '/<span class="valor">(.*?)<\//',
                $htmlEndereco[1],
                $enderecos
            );

            foreach ($enderecos[1] as $value) {
                if (empty($value)) {
                    continue;
                }

                $endereco .= $value;
            }

            preg_match('/javax.faces.ViewState:0" value="(.*?)"/m', $regularidadeHtml, $javaxRegularidades);
            $informativo = $informativo[1];
        }

        $historicoHtml = $this->getHistorical($javaxRegex[1], $formId[1]);

        $crf = '';
        $dataValidade = '';
        $foundValidity = preg_match('/width="2"\s\/>\s<\/label>(.*?)\s<br/m', $historicoHtml, $validade);
        if ($foundValidity) {
            $dataValidade = $validade[1];
            preg_match('/<\/label><span class="valor">(.*?)<\//m', $historicoHtml, $crf);
        }

        $numCertidao = (!empty($crf)) ? $crf[1] : null;

        $certificadoUtilizacao = '';
        $dataHora = '';
        if ($crf != '') {
            $certificadoHtml = $this->getCertificate($javaxRegularidades[1]);

            preg_match(
                '/dding:15px;" colspan="2"> <font style=" font-family: Verdana;font-size:10pt">(.*?)<\/font>/m',
                $certificadoHtml,
                $certificadoUtilizacao
            );
            $certificadoUtilizacao = array_pop($certificadoUtilizacao);

            preg_match('/ida em <\/span><span class="valor">(.*?)<\/span>/m', $certificadoHtml, $dataHora);
            $dataHora = array_pop($dataHora);
        }

        if (empty($razaoSocial)) {
            throw new Exception("Falha ao obter razão social", 3);
        }

        /*
        caso não tenha sido possivel encontrar as informações só com o cnpj
        o site exibe um informativo que deve ser pego
         */
        if (
            empty($informativo)
            && preg_match('/feedback-text">([\s\S]+?)<\/span>/', $html, $informativo)
        ) {
            $informativo = array_pop($informativo);
            $informativo = trim(strip_tags($informativo));
        }

        $dados = [
            'inscricao' => $inscricao,
            'razao_social' => $razaoSocial,
            'endereco' => $endereco,
            'texto_informativo' => $informativo,
            'periodo_validade' => $dataValidade,
            'certidao_numero' => $numCertidao,
            'certificado_utilizacao' => $certificadoUtilizacao,
            'data_hora_consulta' => $dataHora,
        ];

        $this->pdf = $this->savePDF($this->parseDataToCertificate($dados));

        return [
            'certificate' => $dados,
            'pdf' => $this->pdf,
        ];
    }

    private function getRegularity($javaxViewState, $formId)
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }
        $params = [
            'AJAXREQUEST' => '_viewRoot',
            'mainForm:codAtivo' => '',
            'mainForm:listEmpFpas' => 'true',
            'mainForm:hidCodPessoa' => '0',
            'mainForm:hidCodigo' => '0',
            'mainForm:hidDescricao' => '',
            'mainForm' => 'mainForm',
            'autoScroll' => '',
            'javax.faces.ViewState' => $javaxViewState,
            $formId => $formId,
        ];

        $regularityHtml = Str::encoding($this->getResponse(self::FORM_URL, 'POST', $params, $this->headersPost));

        if (preg_match('/Internal Server Error/m', $regularityHtml)) {
            throw new Exception("Falha ao obter regularidade", 3);
        }

        return $regularityHtml;
    }

    private function getHistorical($javaxViewState, $formId)
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }
        $params = [
            'AJAXREQUEST' => '_viewRoot',
            'mainForm:codAtivo' => '',
            'mainForm:listEmpFpas' => 'true',
            'mainForm:hidCodPessoa' => '0',
            'mainForm:hidCodigo' => '0',
            'mainForm:hidDescricao' => '',
            'mainForm' => 'mainForm',
            'autoScroll' => '',
            'javax.faces.ViewState' => $javaxViewState,
            $formId => $formId,
        ];

        $historicalHtml = Str::encoding($this->getResponse(self::FORM_URL, 'POST', $params, $this->headersPost));

        if (preg_match('/Internal Server Error/m', $historicalHtml)) {
            throw new Exception("Falha ao obter o histórico", 3);
        }

        return $historicalHtml;
    }

    private function getCertificate($javaxViewState)
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }
        $params = [
            'AJAXREQUEST' => '_viewRoot',
            'mainForm' => 'mainForm',
            'autoScroll' => '',
            'javax.faces.ViewState' => $javaxViewState,
            'mainForm:btnVisualizar' => 'mainForm:btnVisualizar',
        ];

        $certificateHtml = $this->getResponse(self::CERTIFICADO_URL, 'POST', $params, $this->headersPost);
        $certificateHtml = Str::encoding($certificateHtml);

        if (preg_match('/Internal Server Error/m', $certificateHtml)) {
            throw new Exception("Falha ao obter certificado", 3);
        }

        $certificateHtml = preg_replace('/<input id="mainForm:btImprimir4".*?>/', '', $certificateHtml);
        $certificateHtml = preg_replace('/<input id="mainForm:btVoltar3.*?>/', '', $certificateHtml);

        return $certificateHtml;
    }

    protected function sanitizeCertificate($certificadoHtml)
    {
        $attachedFiles = [
            '/\/consultacrf\/a4j\/s\/3_3_2\.SR1org\/richfaces\/renderkit\/html\/css\/basic_classes\.xcss\/DATB\/' .
                'eAELXT5DOhSIAQ!sA18_\.jsf/',
            '/\.\.\/estaticos\/img\/caixa\.gif/',
            '/<IMG/',
            '/(<\?xml\sversion="1\.0)(.*)(dtd"\s>\s)/',
            '/(<td\sWIDTH="15%")(.*)(class="submit-d\ssubmit-blue\ssubmit-small"\s\/>\s<\/td>)/',
            '/(<td\sWIDTH="15%")(.*)(onclick="Javascript:Imprimir\(\)"\s\/> <\/td>)/',
        ];

        $replace = [
            self::CSS,
            self::IMAGEM,
            '<img',
            '',
            '',
            '',
        ];

        foreach ($attachedFiles as $key => $pattern) {
            if (preg_replace($pattern, $replace[$key], $certificadoHtml)) {
                $certificadoHtml = preg_replace($pattern, $replace[$key], $certificadoHtml);
            }
        }

        return \ForceUTF8\Encoding::toUTF8(Str::removerAcentos($certificadoHtml));
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }

        if (!isset($this->param['cnpj']) or empty($this->param['cnpj'])) {
            throw new Exception('Parâmetro inválido', 6);
        }

        if (!Document::validarCnpj($this->param['cnpj'])) {
            throw new Exception('Parâmetro inválido', 6);
        }
    }

    private function savePDF($result)
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }
        $fileId = uniqid() . '.pdf';
        $filePath = "/tmp/CefFgts" . $fileId;
        $s3Path = 'CefFgts/' . $fileId;

        (new Pdf())->saveHtmlToPdf($result, $filePath);

        if (file_exists($filePath)) {
            if ((new S3(new StaticUplexisBucket()))->save($s3Path, $filePath)) {
                return S3_STATIC_URL . $s3Path;
            }
        }
        throw new Exception('Erro ao salvar o pdf', 3);
    }

    private function parseDataToCertificate($result)
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }
        $cert = '';
        $cert = new Certificate();
        return $cert->certificate($result);
    }

    /**
     * Método adicionado para forçar renovação de conexão
     *
     * <AUTHOR> Vidal - 10/02/2022
     */
    private function openCurl()
    {
        $uniqid = md5(uniqid(rand(), true));
        $this->cookie = '/tmp/cookie_' . $uniqid . '.txt';
        $this->ch = curl_init();

        curl_setopt($this->ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($this->ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($this->ch, CURLOPT_COOKIESESSION, 1);
        curl_setopt($this->ch, CURLOPT_COOKIEJAR, $this->cookie);
        curl_setopt($this->ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($this->ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($this->ch, CURLOPT_TIMEOUT, 600);
    }
}
