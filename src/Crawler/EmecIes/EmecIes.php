<?php

namespace App\Crawler\EmecIes;

use App\Crawler\Spider;
use App\Factory\MongoDB;
use App\Helper\Document;
use Exception;

class EmecIes extends Spider
{
    private const INDEX_MONGODB = 'maintainer';
    private const LIMIT = 5;
    private $fields = [];

    private $criteria;

    public function start()
    {
        $result = $this->searchData();

        if (empty($result)) {
            throw new Exception("Nenhum Resultado Encontrado", 2);
        }

        return $result;
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (Document::validarCpfOuCnpj($this->param['criterio'])) {
            $this->criteria[] = Document::removeMask($this->param['criterio']);
            $this->criteria[] = Document::formatCnpj($this->param['criterio']);
            $this->fields = ['mantenedora_cnpj','cnpj'];
        } else {
            $this->criteria = $this->param['criterio'];
            $this->fields = ['mantenedora','nome'];
        }
    }

    private function searchData()
    {
        $result = $this->setQuery();
        foreach ($result as $value) {
            $arrayCursos = [];
            $cursos = $values['cursos'] ?? [];

            foreach ($cursos as $curso) {
                $arrayCursos[] = $this->parseItem($curso);
            }

            $arrayAtos = [];
            $atos = $value['atos'] ?? [];

            foreach ($atos as $ato) {
                $arrayAtos[] = $this->parseItem($ato);
            }

            $arrayHistoricos = [];
            $historicos = $values['historicos'] ?? [];

            foreach ($historicos as $historico) {
                $arrayHistoricos[] = $this->parseItem($historico);
            }

            $data[] = [
                'id' => $value['id'],
                'nome' => $value['nome'],
                'sigla' => $value['sigla'],
                'cnpj' => $value['cnpj'],
                'mantenedora_id' => $value['mantenedora_id'],
                'mantenedora' => $value['mantenedora'],
                'mantenedora_cnpj' => $value['mantenedora_cnpj'],
                'endereco' => $value['endereco'],
                'municipio' => $value['municipio'],
                'bairro' => $value['bairro'],
                'uf' => $value['uf'],
                'cep' => $value['cep'],
                'telefone' => $value['telefone'],
                'email' => $value['email'],
                'site' => $value['site'],
                'organizacao_academica' => $value['organizacao_academica'],
                'tipo_credenciamento' => $value['tipo_credenciamento'],
                'categoria' => $value['categoria'],
                'categoria_administrativa' => $value['categoria_administrativa'],
                'natureza_juridica' => $value['natureza_juridica'],
                'representante_legal' => $value['representante_legal'],
                'situacao' => $value['situacao'],
                'situacao_descricao' => $value['situacao_descricao'],
                'status' => $value['status'],
                'cursos' => $arrayCursos,
                'atos_regulatorios' => $arrayAtos,
                'historicos' => $arrayHistoricos,
            ];
        }

        return $data;
    }

    /** Parse do item do array
     */
    private function parseItem($data)
    {
        $result = [];
        foreach ($data as $key => $value) {
            if ($key == '_id') {
                /** Converte o objeto ID em string */
                $id = json_decode(json_encode($value, true), true);
                $result['id'] = $id['$oid'];
            } else {
                $result[$key] = $value;
            }
        }
        return $result;
    }

    private function setQuery()
    {
        $limit = $this->param['limit'] ?? self::LIMIT;
        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('emec')
            ->setCollection('emec_ies');

        $query[] = [
            '$search' => [
                'index' => self::INDEX_MONGODB,
                'compound' => [
                    'must' => [
                        [
                            "phrase" => [
                                "query" => $this->criteria,
                                "path" => $this->fields
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $query[] = ['$limit' => $limit];

        $query[] = [
            '$lookup' => [
                'localField' => 'id',
                'from' => 'emec_ies_curso',
                'foreignField' => 'ies_id',
                'as' => 'cursos'
            ]
        ];

        $query[] = [
            '$lookup' => [
                'localField' => 'id',
                'from' => 'emec_ies_ato_regulatorio',
                'foreignField' => 'ies_id',
                'as' => 'atos'
            ]
        ];

        $query[] = [
            '$lookup' => [
                'localField' => 'id',
                'from' => 'emec_ies_historico',
                'foreignField' => 'ies_id',
                'as' => 'historicos'
            ]
        ];
        $query[] = ['$sort' => ['historicos.ano' => -1]];

        $data = json_decode(
            json_encode(
                $manager
                ->query(
                    'aggregate',
                    $query,
                    null,
                    null,
                    true
                )->toArray(),
                true
            ),
            true
        );

        return $data;
    }
}
