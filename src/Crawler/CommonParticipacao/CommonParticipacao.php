<?php

namespace App\Crawler\CommonParticipacao;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\DynamoManager;
use Carbon\Carbon;
use App\Crawler\CommonParticipacao\Models\ParticipacaoPartnersModel;
use App\Crawler\CommonParticipacao\Models\ParticipacaoModel;
use Exception;

/**
 * Classe para pegar participação por CPF ou CNPJ (Utilizado no Sugeridor)
 *
 * @version 1.0.0
 *
 * <AUTHOR> Mesquita 18/09/2020
 *
 * @return
 */
class CommonParticipacao extends Spider
{
    private $document;
    private $dynamoManager;
    private $year;

    /**
     *  Inicia pesquisa da fonte Common Participacao
     *
     *  <AUTHOR> Mesquita 18/09/2020
     *
     *  @version 1.0.0
     */
    protected function start()
    {
        $this->dynamoManager = new DynamoManager($this->debug);

        return $this->parseParticipacao();
    }

    /**
     *  Valida e seta os parametros
     *
     *  <AUTHOR> 18/09/2020
     *
     *  @version 1.0.0
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCpfOuCnpj($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }

        $this->year = $this->param['ano'];

        if (empty($this->year)) {
            $this->year = Carbon::now()->format('Y');
        }

        $this->document = Document::removeMask($this->param['cpf_cnpj']);
    }

    /**
     *  Retorna os dados de participação parseados
     *
     *  <AUTHOR> Mesquita 18/09/2020
     *
     *  @version 1.0.0
     */
    private function parseParticipacao()
    {
        $participacaoData = $this->getSpinePjDocumentoSocio($this->year);

        if (empty($participacaoData)) {
            throw new Exception('Nenhum dado encontrado!', 2);
        }

        $partners = [];
        $result =  [];

        foreach ($participacaoData as $item) {
            $participacao = new ParticipacaoModel();

            $spinePj = $this->getSpinePJ($item['cnpj']);

            $participacao->cnpj = $item['cnpj'];
            $participacao->razao_social = $spinePj['razao_social'];
            $participacao->data_consulta = $item['data_alt'] ? $this->formatDate($item['data_alt']) : null;

            if (count($participacaoData) <= 10) {
                $this->tryToCatchFromOldYears($item);
            }

            $partners = new ParticipacaoPartnersModel();
            $partners->cpf_cnpj = $item['documento_socio'];
            $partners->nome = $item['nome'] ?? null;
            $partners->entrada = $item['data_entrada'] ? $this->formatDate($item['data_entrada']) : null;
            $partners->qualificacao = $item['qualificacao'] ? $item['qualificacao'] : null;
            $partners->participacao = $item['participacao'] ? $this->formatStake($item['participacao']) : null;

            $participacao->setPartners($partners);

            $result[] = $participacao;
        }

        return $result;
    }

    /**
     *  Tenta pegar os dados data de entrada, qualificação e participação de anos anteriores
     *
     *  <AUTHOR> Mesquita 18/09/2020
     *
     *  @param array &$part
     *  @param int $yearEnd
     *
     *  @version 1.0.0
     */
    private function tryToCatchFromOldYears(&$part, $yearEnd = 2013)
    {
        for ($i = $this->year - 1; $i >= $yearEnd; $i--) {
            if (!empty($part['data_entrada']) && !empty($part['qualificacao']) && !empty($part['participacao'])) {
                return;
            }

            try {
                $spineTmp = $this->getSpinePjDocumentoSocio((string)$i);

                foreach ($spineTmp as $tmp) {
                    if ($part['documento_socio'] == $tmp['documento_socio']) {
                        if (empty($part['data_entrada'])) {
                            $part['data_entrada'] = !empty($tmp['data_entrada']) ? $tmp['data_entrada'] : null;
                        }

                        if (empty($part['qualificacao'])) {
                            $part['qualificacao'] = !empty($tmp['qualificacao']) ? $tmp['qualificacao'] : null;
                        }

                        if (empty($part['participacao'])) {
                            $part['participacao'] = !empty($tmp['participacao']) ? $tmp['participacao'] : null;
                        }
                    }
                }
            } catch (\Exception $e) {
            }
        }
    }

    /**
     *  Formata data
     *
     *  <AUTHOR> Mesquita 18/09/2020
     *
     *  @param string $date
     *
     *  @version 1.0.0
     */
    private function formatDate(string $date): string
    {
        return Carbon::createFromFormat('Ymd', $date)->format('d/m/Y');
    }

    /**
     *  Formata Participação
     *
     *  <AUTHOR> Mesquita 18/09/2020
     *
     *  @param string $stake
     *
     *  @version 1.0.0
     */
    private function formatStake(string $stake): string
    {
        if (!empty($stake)) {
            $pattern = "/([0-9]+)(?:(\.0+[1-9]|\.[1-9])0+|\.0+(?![1-9]))/isu";

            return preg_replace($pattern, "$1$2", number_format(floatval($stake), 2, '.', ''));
        }

        return null;
    }

    /**
     *  Retorna SpinePjQsa por ano e documento do socio
     *
     *  <AUTHOR> Mesquita 18/09/2020
     *
     *  @param string $ano
     *
     *  @version 1.0.0
     */
    private function getSpinePjDocumentoSocio(string $ano): array
    {
        $index = 'documento_socio-ano-index';
        $conditions = 'documento_socio = :documento_socio and ano = :ano';
        $expression = [
            ':documento_socio' => $this->document,
            ':ano' => $ano
        ];

        return $this->dynamoManager
            ->getQuery('spine_pj_qsa', $conditions, $expression, $index);
    }

    /**
     *  Retorna SpinePj por cnpj
     *
     *  <AUTHOR> Mesquita 18/09/2020
     *
     *  @param string $cnpj
     *
     *  @version 1.0.0
     */
    private function getSpinePJ(string $cnpj): array
    {
        return $this->dynamoManager->getItem('spine_pj', [
            'cnpj' => (string)$cnpj
        ]);
    }

    /**
     *  Retorna SpinePf por cpf
     *
     *  <AUTHOR> Mesquita 18/09/2020
     *
     *  @param string $ano
     *
     *  @version 1.0.0
     */
    private function getSpinePF(string $cpf)
    {
        return $this->dynamoManager->getItem('spine_pf', [
            'cpf' => (string)$cpf
        ]);
    }
}
