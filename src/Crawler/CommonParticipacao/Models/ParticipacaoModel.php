<?php

namespace App\Crawler\CommonParticipacao\Models;

use App\Crawler\CommonParticipacao\Models\ParticipacaoPartnersModel;
use Exception;

class ParticipacaoModel
{
    public $cnpj;
    public $razao_social;
    public $data_consulta;
    public $aSocio;

    public function __set($name, $value)
    {
        if (property_exists($this, $name)) {
            return $this->$name = $value;
        }

        throw new Exception("Propriedade set '{$name}' não existe", 3);
    }

    public function __get($name)
    {
        if (property_exists($this, $name)) {
            return $this->$name;
        }
        throw new Exception("Propriedade get '{$name}'  não existe", 3);
    }

    public function setPartners(ParticipacaoPartnersModel $partners)
    {
        $this->aSocio[] = $partners;
    }
}
