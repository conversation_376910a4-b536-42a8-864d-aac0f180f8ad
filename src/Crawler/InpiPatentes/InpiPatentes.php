<?php

namespace App\Crawler\InpiPatentes;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Util;
use Exception;

/**
 * Classe utilizada no processo de captura de informações de Patentes cadastradas no Inpi
 *
 * @version 2.0
 * <AUTHOR> Andrade - 23/07/2018 - Adicionada a busca por nome como critério.
 *
 */
class InpiPatentes extends Spider
{
    /**
     * Constante URL base necessário para realizar a requisição
     *
     * @var string
     */
    private $baseURL = 'https://busca.inpi.gov.br';

    /**
     * URL necessária para obter os cookies necessários para a página de login,
     * embora não seja efetuado o login é um metodo necessário para continuar
     * o processo de busca, realizando qualquer requisição no sistema.
     *
     * @var string
     */
    private $menuURL = 'https://busca.inpi.gov.br/pePI/servlet/LoginController?action=login';
    /**
     * URL onde é informado os valores da requisição, que só pode ser obtidos depois ter
     * sido gerado os cookies da página de login, as informações são passadas através
     * de um formulário e tratadas para que a requisição seja efetuada corretamente,
     * com os parâmetros informados.
     *
     * @var string
     */
    private $formURL = 'https://busca.inpi.gov.br/pePI/jsp/patentes/PatenteSearchBasico.jsp';
    /**
     * URL onde é recebido os dados da requisição após serem tratados e distinguidos que tipo
     * de requisição está sendo realizada, e devolver os dados requisitados
     *
     * @var string
     */
    private $formPostURL = 'https://busca.inpi.gov.br/pePI/servlet/PatenteServletController';
    /**
     * Variável para debugar a fonte em caso de erro
     *
     * @var boolean
     */

    /**
     * Função que realiza validação dos parâmetros principais da busca
     * descobrindo qual é o metodo de entrada da busca e diferenciando entre
     * Nome, CPF ou CNPJ através de um regex
     *
     * @version 1.1.3
     * <AUTHOR> Ribeiro - 23/07/2018 - Método para validar e retornar os critérios de busca;
     * <AUTHOR> Andrade - 23/07/2018 - Adicionada validação para buscas com nome como critério;
     * <AUTHOR> Andrade - 23/07/2018 - Método agora retorna os parâmetros como array;
     * <AUTHOR> Andrade - 24/07/2018 - Alterado o código para o padrão de codificação;
     * <AUTHOR> Andrade - 25/07/2018 - Alterada a condição de existência do critério
     *                                     que estava retornando false quando nome era informado;
     * <AUTHOR> Costa - 13/08/2018 - Adicionado apenas comentários as funções já existente.
     * <AUTHOR> Vidal - 09/09/2020 - Ajustado tratamento do criterio quando for documento;
     * <AUTHOR> Vidal - 08/10/2020 - Adicionando aspas caso o criterio seja um nome para
     *                                      fazer a busca exata.
     *
     * @return array
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (empty($this->param['nome_cpf_cnpj'])) {
            throw new Exception('Nenhum parâmetro informado!');
        }

        $this->criterion = $this->param['nome_cpf_cnpj'];
        $this->type = Document::validarCpfOuCnpj($this->criterion) ? "document" : "name";

        if ($this->type == 'document') {
            $this->criterion = preg_replace('/[^0-9]/', '', $this->criterion);
        } else {
            $this->criterion = '"' . $this->criterion . '"';
        }

        if (!empty($this->param['limit'])) {
            $this->limit = $this->param['limit'];
        }
    }

    /**
     * Função que da ínicio aos metodos de busca passando os parâmetros informados
     * para realizar a requisição
     *
     * @version 1.2.1
     * <AUTHOR> Andrade - 26/02/2017 - Função que captura das informações de Patentes cadastradas no Inpi;
     * <AUTHOR> Andrade - 24/07/2018 - Passada a função para o PSR-2;
     * <AUTHOR> Andrade - 02/07/2018 - Criada a função getAndParseResults para melhorar a legibilidade deste método;
     * <AUTHOR> Costa - 13/08/2018 - Adicionado apenas comentários as funções já existente.
     *
     * @return array
     */
    protected function start()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $this->initialRequests();

        $parameters = $this->getRequestParameters();

        $links = $this->checkResponse(
            $this->getResponse(
                $this->formPostURL,
                'POST',
                $parameters,
                [
                    'Referer' => $this->formURL,
                    'Upgrade-Insecure-Requests' => 1
                ]
            )
        );

        $result = $this->getAndParseResults($links);

        return $result;
    }

    /**
     * Função necessária para iniciar as requisições, sendo "setadas" através do proxy alternativo
     * para que não ocorra erros de blacklist e incapacite que as informações corretas
     * sejam trazidas de forma segura.
     *
     * @version 1.0.0
     * <AUTHOR> Andrade - 26/02/2016 - Método que realiza as requisições para montar os cookies
     *                                       das próximas requisições;
     * <AUTHOR> Costa - 13/08/2018 - Adicionado apenas comentários as funções já existente.
     *
     */
    private function initialRequests()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }
        $this->setAlternativeProxy();

        if (!($this->getResponse($this->menuURL) && $this->getResponse($this->formURL))) {
            throw new Exception("Não foi possível comunicar-se com a base de dados do Inpi!");
        }
    }


    /**
     * Função necessária para buscar e analizar os endereços dentro da $baseURL removendo
     * e gerando um array através de uma analise "final"
     *
     * @version 1.0.0
     * <AUTHOR> Andrade - 26/02/2016 - Método que obtém as informações de uma patente
     *                                       dado o resultado da consulta;
     * <AUTHOR> Costa - 13/08/2018 - Adicionado apenas comentários as funções já existente.
     * <AUTHOR> Vidal - 19/05/2021 - Adicionando try catch caso a requisição atinja o tempo
     *                                       limite do curl.
     *
     * @return array
     */
    private function getAndParseResults($links)
    {
        $responses = [];

        $this->setCurlOpt(array(
            CURLOPT_USERAGENT => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) ' .
                'Chrome/74.0.3729.108 Safari/537.36',
            CURLOPT_TIMEOUT => 40
        ));

        foreach ($links as $key => $link) {
            // parse dos registros encontrados
            $link = str_replace(' ', '%20', $link);
            $link = str_replace('"', '%22', $link);
            $link = $this->baseURL . $link;

            try {
                $rs = $this->getResponse($link);
            } catch (Exception $e) {
                echo 'Site demorou muito para responder. Pulando...' . PHP_EOL;
                continue;
            }

            $responses[] = $this->finalParse($rs);

            if ($key >= $this->limit) {
                break;
            }
        }

        return $responses;
    }

    /**
     * Função utilizada para checar se há ou não informações contradas pela requisição
     * recebendo valores em forma de array através da variável $html
     *
     * @version 1.0.1
     * <AUTHOR> Andrade - 26/02/2017 - Método para checar se existem ocorrências para a busca realizada;
     * <AUTHOR> Costa - 13/08/2018 - Adicionado apenas comentários as funções já existente
     *
     * @param string $html
     *
     * @return array
     */
    public function checkResponse($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (preg_match('#Nenhum\s*resultado\s*foi\s*encontrado#is', $html)) {
            throw new Exception('Nada Encontrado', 2);
        }

        return $this->getLinkDetails($html);
    }

    /**
     * Função é utilizada para resgatar informações passadas através da variável $html e
     * através de um regex é retirado apenas as informações relevantes da tabela de exibição
     * das patentes publicadas
     *
     * @version 1.0.2
     * <AUTHOR> Andrade - 26/02/2017 - Método para montar resposta da captura
     *                                       com todos os dados das patentes encontradas;
     * <AUTHOR> Andrade - 24/07/2018 - Método passado para o padrão de codificação;
     * <AUTHOR> Costa - 13/08/2018 - Adicionado apenas comentários as funções já existente.
     *
     * @param array $html
     *
     * @return array
     */
    private function finalParse($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $nationalPatentDepositRequest = $this->parseNationalPatentDepositRequest($html);

        $divs = Util::queryXPath($html, '//div[@class="accordions"]', true);

        foreach ($divs as $key => $div) {
            if (preg_match('#<font\s*class="titulo">peti.{1,16}<\/font>#is', $div)) {
                $petitions = $this->parsePetition($div);
            }

            if (preg_match('#<font\s*class="titulo">Publica.{1,16}es<\/font>#is', $div)) {
                $publications = $this->parsePublication($div);
            }
        }

        $patterns = [
            'data_atualizacao' => ['#<font[^>]*?>.*dados\s*atualizados\s*at.{1,8}\s*(.*?)<\/b>#is', null],
            'num_revista' => ['#Revista:\s*<b>\s*(.*?)\s*<\/b>#is', null],
        ];

        $content = $this->parseData($patterns, $html);
        $content['DepositoPedidoNacionalPatente'] = $nationalPatentDepositRequest;
        $content['Peticoes'] = $petitions;
        $content['Publicacoes'] = $publications;

        return $content;
    }

    /**
     * Essa função recolhe através de um regex as informações retiradas dentro de um "accordion"
     * que separa as informações dentro de uma tabela, tratando o parâmetro $html e o transformado
     * em array para poder retorna os valores encontrados.
     *
     * @version 1.0.2
     * <AUTHOR> Andrade - 26/02/2017 - Método utilizado para obter as informações de uma dada patente;
     * <AUTHOR> Andrade - 24/07/2018 - Método passado para o padrão de codificação;
     * <AUTHOR> Costa - 13/08/2018 - Adicionado apenas comentários as funções já existente.
     *
     * @param array $html
     *
     * @return array
     */
    private function parsePublication($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $trs = Util::queryXPath($html, '//div[@class="accordion-content"]/table/tbody/tr', true);

        $response = [];

        foreach ($trs as $key => $tr) {
            list(
                $rpi,
                $rpiDate,
                $dispatch,
                $img1,
                $img2,
                $dispatchComplements
            ) = Util::queryXPath($tr, '//td[@align][@width]', true);


            $response[] = [
                'rpi' => trim(strip_tags($rpi)),
                'data_rpi' => trim(strip_tags($rpiDate)),
                'despacho' => $this->getDispatchDescription($dispatch),
                'img1' => trim(strip_tags($img1)),
                'img2' => trim(strip_tags($img2)),
                'complemento_do_despacho' => trim(strip_tags($dispatchComplements))
            ];
        }

        return $response;
    }

    /**
     * Função utilizada para capturar e fazer um split das informações passadas através
     * do parâmetro $html devolvendo em forma de array
     *
     * @version 1.0.2
     * <AUTHOR> Ribeiro - 26/02/2017 - Método para obter id e descrição do despacho de uma dada patente;
     * <AUTHOR> Andrade - 24/07/2018 - Método passado para o padrão de codificação;
     * <AUTHOR> Costa - 13/08/2018 - Adicionado apenas comentários as funções já existente
     *
     * @param string $html - Paramêtro que recebe o html da página
     *
     * @return array
     */
    private function getDispatchDescription($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $text = Util::queryXPath($html, '//table/tbody/tr[2]/td/font');

        list($id, $desc) = preg_split("#<\\/b*>#is", $text[0]);

        return [
            'id' => strip_tags($id),
            'desc' => strip_tags($desc),
        ];
    }

    /**
     * Função utilizada para recuperar informaçãos sobre o banco, a data e o valor utilizado
     * para a criação da patente, através de um regex passado pelo parâmetro $html retornando
     * como um array
     *
     * @version 1.0.2
     * <AUTHOR> Ribeiro - 26/02/2017 - Método para obter os dados de pagamento
     *                                       de uma dada petição de uma dada patente;
     * <AUTHOR> Andrade - 24/07/2018 - Método passado para o padrão de codificação;
     * <AUTHOR> Costa - 13/08/2018 - Adicionado apenas comentários as funções já existente
     *
     * @param string $html - Paramêtro que recebe o html da página
     *
     * @return array
     */
    private function getBankInformations($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $text = Util::queryXPath($html, '//table/tbody/tr[2]/td/font');

        list($bank, $date, $value) = preg_split("#<br\\/*>#is", $text[0]);

        return [
            'banco' => trim($bank),
            'data' => trim($date),
            'valor' => trim($value)
        ];
    }

    /**
     * Função criada para capturar a descrição do tipo de serviço que se relaciona a patente
     * através de um regex passando pelo parâmetro $html e retornando como um array
     *
     * @version 1.0.2
     * <AUTHOR> Ribeiro - 26/02/2017 - Método para obter os dados de um serviço de uma dada patente;
     * <AUTHOR> Andrade - 24/07/2018 - Método passado para o padrão de codificação;
     * <AUTHOR> Costa - 13/08/2018 - Adicionado apenas comentários as funções já existente
     *
     * @param string $html - Paramêtro que recebe o html da página
     *
     * @return array
     */
    private function getServiceDescription($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $text = Util::queryXPath($html, '//table/tbody/tr[2]/td/font');

        list($id, $desc) = preg_split("#<\\/b>#is", $text[0]);

        return [
            'id' => strip_tags($id),
            'desc' => trim(strip_tags($desc)),
        ];
    }

    /**
     * Função criada para analizar as petições relacionado com os parametros informados
     * passando um regex nos parâmetros informados através de $html,
     * para retornar como um array
     *
     * @version 1.0.2
     * <AUTHOR> Ribeiro - 26/02/2017 - Método para obter os dados de uma dada petição;
     * <AUTHOR> Andrade - 24/07/2018 - Método passado para o padrão de codificação;
     * <AUTHOR> Costa - 13/08/2018 - Adicionado apenas comentários as funções já existente
     *
     * @param string $html - Paramêtro que recebe o html da página
     *
     * @return array
     */
    private function parsePetition($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $trs = Util::queryXPath($html, '//div[@class="accordion-content"]/table/tbody/tr', true);

        $response = [];

        foreach ($trs as $key => $tr) {
            list(
                $service,
                $pgo,
                $protocol,
                $depositDate,
                $images1,
                $images2,
                $images3,
                $client,
                $delivery,
                $date
            ) = Util::queryXPath($tr, '//td[@align="center"][@width]', false);

            if (empty($protocol)) {
                continue;
            }

            $response[] = [
                'pgo' => $this->getBankInformations($pgo),
                'protocolo' => trim(strip_tags($protocol)),
                'data_deposito' => trim(strip_tags($depositDate)),
                'imagens1' => trim(strip_tags($images1)),
                'imagens2' => trim(strip_tags($images2)),
                'imagens3' => trim(strip_tags($images3)),
                'servico' => $this->getServiceDescription($service),
                'cliente' => trim(strip_tags($client)),
                'delivery' => trim(strip_tags($delivery)),
                'data' => trim(strip_tags($date)),
            ];
        }

        return $response;
    }

    /**
     * Função que analisa e mostras as solicitações de patentes através das informações
     * passadas pelo parâmetro $html
     *
     * @version 1.0.2
     * <AUTHOR> Ribeiro - 26/02/2017 - Método para obter os dados de uma requisição de depósito
     *                                       de uma dada patente;
     * <AUTHOR> Andrade - 24/07/2018 - Método passado para o padrão de codificação;
     * <AUTHOR> Costa - 13/08/2018 - Adicionado apenas comentários as funções já existente
     *
     * @param string $html - Paramêtro que recebe o html da página
     *
     * @return array
     */
    private function parseNationalPatentDepositRequest($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $trs = Util::queryXPath($html, '//*[@id="principal"]/table[2]/tr', true);
        unset($trs[0]);

        $response = [];
        foreach ($trs as $key => $tr) {
            $tds[$key] = Util::queryXPath($tr, '//td');

            list($id, $desc) = Util::queryXPath($tds[$key][0], '//font');
            unset($tds[$key][0]);

            $response[] = [
                'id' => trim(strip_tags($id)),
                'desc' => trim(strip_tags($desc)),
                'conteudo ' => trim(strip_tags(implode(' ', $tds[$key]))),
            ];
        }

        return $response;
    }

    /**
     * Obtem os dados passado pelo parametro e retonando como um array
     *
     * @version 1.1.2
     * <AUTHOR> Ribeiro - 26/02/2017 - Método que monta os parâmetros da requisição;
     * <AUTHOR> Andrade - 23/08/2018 - Adicionada condição para passar o critério de busca
     *                                     para o campo NomeDepositante;
     * <AUTHOR> Andrade - 24/07/2018 - Método passado para o padrão de codificação;
     * <AUTHOR> Costa - 13/08/2018 - Adicionado apenas comentários as funções já existente
     *
     * @param int $type - Tipo (Pessoa Física ou Juridica)
     * @param string $nameCpfCnpj - Dado Recebido, sendo nome, cpf ou cnpj
     * @param string $cpfCnpj - parametro de cpf ou cpnj
     *
     * @return array
     */
    private function getRequestParameters()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $parameters['NumPedido'] = '';
        $parameters['NumGru'] = '';
        $parameters['NumProtocolo'] = '';
        $parameters['FormaPesquisa'] = 'todasPalavras';
        $parameters['ExpressaoPesquisa'] = $this->criterion;
        $parameters['RegisterPerPage'] = '20';
        $parameters['botao'] = '+pesquisar+%BB+';
        $parameters['Action'] = 'SearchBasico';

        if ($this->type == 'document') {
            $parameters['Coluna'] = 'CpfCnpjDepositante';
        } else {
            $parameters['Coluna'] = 'NomeDepositante';
        }
        return $parameters;
    }

    /**
     * Função criada para recuperar os dados através do parâmetro $html e retornando através
     * da variável $link
     *
     * @version 1.0.2
     * <AUTHOR> Ribeiro - 26/02/2017 - Método para parsear os dados das patentes;
     * <AUTHOR> Andrade - 24/07/2018 - Método passado para o padrão de codificação;
     * <AUTHOR> Costa - 13/08/2018 - Adicionado apenas comentários as funções já existente
     *
     * @param string $html
     *
     * @return array
     */
    public function getLinkDetails($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }
        if (preg_match_all('#Service Temporarily Unavailable#is', $html)) {
            throw new Exception("O serviço de busca do Inpi está temporariamente indisponível!");
        }
        if (!preg_match_all("#href='(.*?)'\s*class='visitado'#is", $html, $links)) {
            throw new Exception('Erro ao obter links {' . __METHOD__ . '}', 3);
        }

        return $links[1];
    }

    /**
     * Função criada para "parsear" os dados informados pelo parârametro $patterns
     * e retornando as informações recuperadas como array para a variavel $data
     * através do parametro $result
     *
     * @version 1.0.2
     * <AUTHOR> Ribeiro - 26/02/2017 - Método para capturar dados padrões em um html;
     * <AUTHOR> Andrade - 24/07/2018 - Método passado para o padrão de codificação;
     * <AUTHOR> Costa - 13/08/2018 - Adicionado apenas comentários as funções já existente
     *
     * @param array $patterns
     * @param array $result
     *
     * @return array
     */
    private function parseData($patterns, $result)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $data = [];
        $aError = [];

        foreach ($patterns as $desc => $aRegExp) {
            foreach ($aRegExp as $sRegExp) {
                if ($sRegExp == null) {
                    $data[$desc] = null;
                    unset($aError[$desc]);
                } elseif (preg_match($sRegExp, $result, $matches)) {
                    $data[$desc] = trim(html_entity_decode($matches[1]));
                    unset($aError[$desc]);
                    break;
                } else {
                    $aError[$desc] = $desc;
                }
            }
        }

        return $data;
    }
}
