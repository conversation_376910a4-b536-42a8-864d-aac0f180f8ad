<?php

namespace App\Crawler\BankScore;

/**
 * Value Object do BankScore, para facilitar validações de informações retornadas pela fonte
 *
 * @version 1.0.0 - Criação
 * <AUTHOR> <<EMAIL>>
 *
 */
class BankScoreValueObject
{
    private $score = null;
    private $identificacaoPesquisado = null;
    private $situacaoSecretariaReceitaFederal = '';
    private $registroObito = '';
    private $classeSocial = null;
    private $informacoesAdicionais = [];
    private $ocupacao = '';
    private $rendaPresumida = '';
    private $escolaridade = '';
    private $relacaoProdutosServicosCotadosUltimos12Meses = [];
    private $participacaoEmpresarial = [];
    private $relacaoTelefonesVinculados = [];
    private $relacaoEmailsVinculados = [];
    private $relacaoEnderecosVinculados = [];
    private $analisePessoasEmpresasVinculados = [];
    private $historicoConsultasAnteriores = [];

    public function to()
    {
        return (array) (get_object_vars($this));
    }

    public function __set($name, $value)
    {
        // por padrão todos os valores vem como array da fonte
        if (is_array($value)) {
            // Eu removo o campo de cpf que não será usado em outros lugares.
            if (isset($value['cpf']) && $name != "identificacaoPesquisado") {
                unset($value['cpf']);
            }

            //Tratamento especifico para quando um valor que deve necessariamente ser um array
            if (is_array($this->{$name}) && !isset($value[0]) && !empty($value)) {
                $value = [$value];
            }

            //Essa regra só deve valer para os atributos que não sejam listas
            if (!is_array($this->{$name}) && count($value) == 1) {
                $value = array_shift($value);
            }

            // transforma o array nomeado dessa chave em uma lista simples de valores
            if ($name == "relacaoProdutosServicosCotadosUltimos12Meses") {
                $value = array_filter(array_column($value, "produtosServicosPreferidos"));
            }
        }

        $this->{$name} = $value;
    }

    public function __toString()
    {
        return json_encode(get_object_vars($this));
    }
}
