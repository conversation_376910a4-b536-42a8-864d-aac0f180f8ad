<?php

namespace App\Crawler\BankScore;

use App\Crawler\Spider;
use Symfony\Component\DomCrawler\Crawler;
use App\Helper\Str;
use App\Helper\Document;
use Exception;

/**
 * Fonte BankScore, fonte privada com consulta cpf, cnpj
 *
 * @version 1.0.0 - Criação
 * <AUTHOR> <<EMAIL>>
 *
 */
class BankScore extends Spider
{
    private const ANTIGO_BANKSCORE = 'http://antigo.bankscore.com.br/';
    private const URL_LOGIN = self::ANTIGO_BANKSCORE . 'bankscore/sistema/Login';
    private const URL_LOGIN_POST = self::ANTIGO_BANKSCORE . 'bankscore/sistema/users/login';
    private const URL_FORM = self::ANTIGO_BANKSCORE . 'bankscore/sistema/credito/ConsultasPessoas/localizadorFull';
    private const URL_FORM_POST = self::ANTIG<PERSON>_BANKSCORE . 'bankscore/sistema/credito/pessoas/localizadorFull';

    private $headers = [
        'User-Agent: Mozilla/5.0 (X11; Linux x86_64)' .
            ' AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.106 Safari/537.36'
    ];

    /**
     * Inicia o processamento da fonte
     *
     * @version 1.0.0
     * <AUTHOR> Macedo <<EMAIL>>
     *
     * @return array resultado da pesquisa
     *
     */
    protected function start(): array
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $this->login();

        $pageResult = $this->getPageResult();
        $response = $this->parse($pageResult);

        if (empty($results)) {
            throw new Exception('Nada encontrado', 2);
        }

        return $response;
    }

    /**
     * Resolve o recaptcha presente na pagina de login da fonte
     *
     * @version 1.0.0 - Criação
     * <AUTHOR> Macedo <<EMAIL>>
     *
     * @return string código do captcha
     *
     */
    private function resolveCaptcha(): string
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $loginPage = $this->getResponse(self::URL_LOGIN);

        $crawler = new Crawler($loginPage);
        list($recaptcha) = $crawler->filter('.g-recaptcha')
            ->first()
            ->extract(['data-sitekey']);

        if (empty($recaptcha)) {
            throw new Exception('Erro ao localizar os dados de captcha da pagina', 3);
        }

        $captcha = $this->solveReCaptcha($recaptcha, self::URL_LOGIN, 5, 7);

        if (empty($captcha)) {
            throw new Exception('Erro ao resolver recaptcha', 100);
        }

        return $captcha;
    }

    /**
     * Realiza o login na fonte, através do login e senha do usuario, e o código do recaptcha
     *
     * @version 1.0.0 - Criação
     * <AUTHOR> Macedo <<EMAIL>>
     *
     */
    private function login()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $fields = [
            'data[User][login]' => $this->auth['usuario'],
            'data[User][senha]' => $this->auth['senha'],
            'g-recaptcha-response' => $this->resolveCaptcha(),
        ];

        $response = $this->getResponse(
            self::URL_LOGIN_POST,
            'POST',
            $fields,
            array_merge($this->headers, ['Referer: ' . self::URL_LOGIN])
        );

        if (preg_match('/o\sip.+/i', $response)) {
            throw new Exception(
                'Erro ao tentar se logar no sistema. É necessario verificar a liberação do IP na Bank Score.',
                3
            );
        }

        if (
            preg_match('#<div\s.*id="flashMessage"\s*class="message".*?(.*?)<\/div>#is', $response, $error)
        ) {
            throw new Exception($error[1], 3);
        }

        if (preg_match('/Usuário\sou\ssenha\sinválida,\stente\snovamente/', $response)) {
            throw new Exception('Usuario ou senha inválidos', 6);
        }

        return $response;
    }

    /**
     * Pega o html do resultado da busca no site
     *
     * @version 1.0.0 - Criação
     * <AUTHOR> Macedo <<EMAIL>>
     *
     * @return string html da pagina de busca
     *
     */
    private function getPageResult(): string
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }
        $fields = [
            'valor_pesquisado' => $this->param['cpf'],
            'campo' => $this->type,
        ];

        $response = $this->getResponse(
            self::URL_FORM_POST,
            'POST',
            $fields,
            array_merge($this->headers, ['Referer: ' . self::URL_FORM])
        );

        if (preg_match('/REGISTRO\sN.O\sLOCALIZADO/i', $response)) {
            throw new Exception('Nada encontrado', 2);
        }

        return $response;
    }

    /**
     * Realiza o parse do html do resultado da fonte
     *
     * @version 1.0.0 - Criação
     * <AUTHOR> Macedo <<EMAIL>>
     *
     * @param  string $html string do resultado da fonte
     *
     * @return array resultado formatado da fonte
     *
     */
    private function parse(string $html): array
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $valueObject = new BankScoreValueObject();

        //Filtra pelas tabelas
        $crawler = new Crawler($html);
        $crawler->filter('table.table-2')->each(function (Crawler $table, $i) use ($valueObject) {
            //pega titulo da tabela
            list($key) = $table->filter('caption')
                ->extract(['_text']);
            $key = Str::camelCase($key);

            //verifica a forma como as informações foram dispostas na tabela
            $th = $table->filter('th');
            $body = ($th->count() > 0)
                ? $this->parseTableWithHeaders($table)
                : $this->parseTableWithoutHeaders($table);

            $valueObject->{$key} = $body;
        });

        return $valueObject->to();
    }

    /**
     * Realiza o parse das tabelas que possuem tag th no resultado da fonte
     *
     * @version 1.0.0 - Criação
     * <AUTHOR> Macedo <<EMAIL>>
     *
     * @param Crawler $table Crawler do symfony
     *
     * @return array resultado formatado da tabela
     *
     */
    private function parseTableWithHeaders(Crawler $table): array
    {
        //formata os campos que serão keys do array
        $th = $table->filter('th');
        $keys = array_map(function ($element) {
            return Str::camelCase($element);
        }, $th->extract(['_text']));

        //extrai as linhas e formata sua extrutura
        $body = $table->filter('tbody tr')->each(function (Crawler $tableLine, $i) use ($keys) {
            $content = $tableLine->filter('td')
                ->extract(['_text']);

            return array_combine($keys, $content);
        });

        if (count($body) == 1) {
            $body = $body[0];
        }

        return $body;
    }

    /**
     * Realiza o parse das tableas que possuem como cabeçalho tags td com tag b dentro
     *
     * @version 1.0.0 - Criação
     * <AUTHOR> Macedo <<EMAIL>>
     *
     * @param Crawler $table Crawler do symfony
     *
     * @return array resultado formatado da tabela
     *
     */
    private function parseTableWithoutHeaders(Crawler $table): array
    {
        $body = [];

        $table->filter('tbody tr')->each(function (Crawler $tableLine, $i) use (&$body) {
            //pega a informação dos campos da tabela
            $content = $tableLine->evaluate('td[not(b)]')->extract(['_text']);

            //pega o titulo
            $keys = array_map(function ($element) {
                return Str::camelCase($element);
            }, $tableLine->filter('td > b')->extract(['_text']));

            $body = array_merge($body, array_combine($keys, $content));
        });

        return $body;
    }

    /**
     * Valida os inputs de autenticação e parametros de busca
     *
     * @version 1.0.0 - Criação
     * <AUTHOR> Macedo <<EMAIL>>
     *
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->auth['usuario']) || empty($this->auth['senha'])) {
            throw new Exception('Usuario ou senha inválidos', 6);
        }

        if (empty($this->param['cpf'])) {
            throw new Exception('Parâmetro inválido', 1);
        }

        $this->param['cpf'] = preg_replace('/\D/', '', $this->param['cpf']);
        if (Document::validarCpf($this->param['cpf'])) {
            $this->type = 'cpf';
            return;
        }

        throw new Exception('Parâmetro inválido', 1);
    }
}
