<?php

namespace App\Crawler\AnvisaFarmaciaDrogaria;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Str;
use App\Helper\Util;
use Exception;

class AnvisaFarmaciaDrogaria extends Spider
{
    private const URL = 'https://consultas.anvisa.gov.br';
    private const API = '/api/empresa/';
    private const DETAILS = self::URL . '/api/documento/tecnico/';

    protected function validateAndSetCrawlerAttributes()
    {
        $this->cnpj = Document::removeMask($this->param['cnpj']);

        if (!Document::validarCnpj($this->cnpj)) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }
    }

    /**
     * Metodo responsável por iniciar o crawler
     *
     * <AUTHOR> <PERSON> <<EMAIL>>
     * @return array
     */
    protected function start()
    {
        $this->setProxy();

        try {
            $result = $this->searchByCnpj();

            $processes = $this->getProcesses($result['content']);
        } catch (Exception $exception) {
            throw new Exception("Erro ao consultar processos", 3);
        }

        if (count($processes) <= 0) {
            throw new Exception('Não foram encontrados processos para o critério informado', 2);
        }

        return $this->parseData($processes[0]);
    }

    /**
     * Metodo responsável por fazer a requisição e recuperar detalhes do cnpj
     * Atualização: Método responsável por chamar API e recuperar os processos
     * sem os detalhes - 01/12/2022
     *
     * <AUTHOR> Vidal <<EMAIL>>
     * <AUTHOR> Medeiros <<EMAIL>> - 01/12/2022
     * @return array
     */
    private function searchByCnpj()
    {
        $body = [
            'count' => '10',
            'filter[cnpj]' => $this->cnpj,
            'page' => '1'
        ];

        $headers = [
            'Authorization: Guest'
        ];

        $result = $this->getResponse(
            self::URL . self::API . 'funcionamento?' . http_build_query($body),
            'GET',
            [],
            $headers
        );

        return json_decode($result, true);
    }

    /**
     * Método resposável por retornar lista de processos completos
     *
     * <AUTHOR> Medeiros <<EMAIL>> - 01/12/2022
     * @return array
     */
    public function getProcesses($processes)
    {
        $allProcesses = [];

        foreach ($processes as $process) {
            $allProcesses[] = $this->getProcessGeneralDetails($process['numeroProcesso']);
        }

        return $allProcesses;
    }

    /**
     * Método resposável por retornar os detalhes iniciais de cada processo encontrado
     *
     * <AUTHOR> Medeiros <<EMAIL>> - 01/12/2022
     * @return array
     */
    public function getProcessGeneralDetails($processNumber)
    {
        sleep(2);
        $headers = [
            'Authorization: Guest'
        ];

        $result = $this->getResponse(
            self::URL . self::API . 'funcionamento/' . $processNumber,
            'GET',
            [],
            $headers
        );

        return json_decode($result, true);
    }

    /**
     * Metodo responsável por fazer o parse do resultado final
     * Atualização - 05/12/2022 - <<EMAIL>>
     *
     * <AUTHOR> Vidal <<EMAIL>>
     * <AUTHOR> Medeiros <<EMAIL>>
     *
     * @param array $process
     * @return array
     */
    private function parseData($process)
    {
        $parsedProcesses = [
            'cnpj' => $process['cnpj'],
            'nome_fantasia' => $process['nomeFantasia'],
            'razao_social' => $process['razaoSocial'],
            'responsavel_legal' => strtoupper($process['representantesLegais'][0]),
            'responsavel_tecnico' => strtoupper($process['representantesTecnicos'][0]),
            'situacao_cadastral' => empty($process['ativa']) ? 'IRREGULAR' : 'REGULAR',
            'telefone' => $process['endereco']['telefone'],
            'bairro' => $process['endereco']['bairro'],
            'cep' => $process['endereco']['cep'],
            'data_autorizacao' => $this->parseStringDate($process['dataAutorizacao']),
            'data_consulta' => date('d/m/y'),
            'data_referencia' => '',
            'email' => empty($process['enderecoInternet']) ? 'Não informado' : $process['enderecoInternet'],
            'endereco' => $process['endereco']['rua'],
            'municipio' => $process['endereco']['bairro'],
            'numero_autorizacao' => $process['autorizacao'],
            'processo' => $process['numeroProcesso'],
            'aPeriodoAFE' => []
        ];

        return $parsedProcesses;
    }

    /**
     * Metodo responsável por formatar datas vindas como string
     *
     * <AUTHOR> Medeiros <<EMAIL>>
     *
     * @param string $date
     * @param string $dateFormat
     * @return string
     */
    public function parseStringDate($date, $dateFormat = 'd/m/y')
    {
        return date('d/m/y', strtotime($date));
    }
}
