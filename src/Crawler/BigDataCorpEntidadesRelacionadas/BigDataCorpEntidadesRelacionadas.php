<?php

namespace App\Crawler\BigDataCorpEntidadesRelacionadas;

use Exception;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\BigDataCorpManager;
use App\Manager\ColunaVertebralManager;
use App\Crawler\BigDataCorpEntidadesRelacionadas\ElasticPessoasRelacionadas;
use App\Crawler\BigDataCorpEntidadesRelacionadas\Models\BigDataCorpEnderecosModel;
use App\Crawler\BigDataCorpEntidadesRelacionadas\Models\BigDataCorpRelacoesPessoaisModel;
use App\Crawler\BigDataCorpEntidadesRelacionadas\Models\BigDataCorpParticipacaoEmpresasModel;
use App\Crawler\BigDataCorpEntidadesRelacionadas\Models\BigDataCorpEntidadesRelacionadasModel;

class BigDataCorpEntidadesRelacionadas extends Spider
{
    private $documento;
    private $urlParticipacao;
    private $urlRelacionamentoPessoal;
    private $urlEnderecosRelacionamentos;
    private $urlRelacaoCnpj;
    private $limit = '';

    protected function start()
    {
        $this->config();

        if (Document::validarCpf($this->documento)) {
            return $this->getEntidadesRelacionadasCpf();
        }

        return $this->getEntidadesRelacionadasCnpj();
    }

    private function config()
    {
        $this->documento = $this->param['documento'];
        $this->urlParticipacao =  'business_relationships';
        $this->urlRelacionamentoPessoal =  'related_people';
        $this->urlEnderecosRelacionamentos = 'related_people_addresses';
        $this->urlRelacaoCnpj = 'relationships';
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if (!empty($this->param['limit'])) {
            $this->limit = ".limit(" . $this->param['limit'] . ")";
        }

        if (
            isset($this->param['documento'])
            && Document::validarCpfOuCnpj($this->param['documento'])
        ) {
            return true;
        }

        throw new Exception('Parâmetro inválido');
    }

    private function getEntidadesRelacionadasCpf()
    {
        $relacionamentoPessoal = $this->getRelacionamentoPessoal();
        $relacionamentoEconomico = $this->getParticipacaoEmpresas();

        if (empty($relacionamentoPessoal) && empty($relacionamentoEconomico)) {
            throw new Exception("Nenhum resultado encontrado!", 2);
        }

        $enderecosRelacionados = $this->getEnderecosRelacionados();

        $relacionados = [];
        foreach ($relacionamentoPessoal as $pessoa) {
            $find = false;
            foreach ($enderecosRelacionados as $endereco) {
                if (false !== strpos($endereco->Type, $pessoa->RelatedEntityTaxIdNumber)) {
                    $relacionados[] = new BigDataCorpEntidadesRelacionadasModel($pessoa, $endereco);
                    $find = true;
                    break;
                }
            }

            if (!$find) {
                $relacionados[] = new BigDataCorpEntidadesRelacionadasModel($pessoa, []);
            }
        }
        return json_decode(
            json_encode([
                'aPessoas_Ligadas' => $relacionados, 'aEmpresas_Ligadas' => $relacionamentoEconomico
            ]),
            true
        );
    }

    private function getEntidadesRelacionadasCnpj()
    {
        $dados = $this->getDados($this->urlRelacaoCnpj);
        if (empty($dados->Result[0]->Relationships->Relationships)) {
            throw new Exception("Nenhum resultado encontrado!", 2);
        }

        $results = ['aPessoas_Ligadas' => [], 'aEmpresas_Ligadas' => []];

        foreach ($dados->Result[0]->Relationships->Relationships as $relationship) {
            if (!$this->possuiDocumentoEmpresarialValido($relationship)) {
                continue;
            }

            if (Document::validarCpf($relationship->RelatedEntityTaxIdNumber)) {
                $aPessoas_Ligadas = new BigDataCorpEntidadesRelacionadasModel(
                    $relationship,
                    (object) []
                );
                if (empty($aPessoas_Ligadas->NOME)) {
                    $aPessoas_Ligadas->NOME = $this->getNome($aPessoas_Ligadas->DOCUMENTO);
                }
                $results['aPessoas_Ligadas'][] = $aPessoas_Ligadas;
            } else {
                $results['aEmpresas_Ligadas'][] = new BigDataCorpParticipacaoEmpresasModel($relationship);
            }
        }

        return json_decode(json_encode($results), true);
    }

    public function getNome($cpf)
    {
        if (Document::validarCpf($cpf)) {
            $spider = new ColunaVertebralManager();
            $response = $spider->getSpinePf($cpf);

            return $response['nome'];
        }

        return '';
    }

    private function possuiDocumentoEmpresarialValido($entidade)
    {
        $documentoEmpresarial = str_replace('0', '', (string) $entidade->RelatedEntityTaxIdNumber);
        if (empty($documentoEmpresarial)) {
            return false;
        }

        return true;
    }

    public function getEnderecosRelacionados()
    {
        $dados = $this->getDados($this->urlEnderecosRelacionamentos);
        $results = [];
        foreach ($dados->Result as $value) {
            foreach ($value->RelatedPeopleAddresses as $relatedPeopleAddress) {
                $results[] = new BigDataCorpEnderecosModel($relatedPeopleAddress);
            }
        }

        return $results;
    }

    public function getParticipacaoEmpresas()
    {
        $dados = $this->getDados($this->urlParticipacao);
        $results = [];
        foreach ($dados->Result as $value) {
            foreach ($value->BusinessRelationships->BusinessRelationships as $BusinessRelationship) {
                if (empty($BusinessRelationship->RelatedEntityTaxIdNumber)) {
                    continue;
                }

                if (empty($BusinessRelationship->RelatedEntityName)) {
                    continue;
                }

                $results[] = new BigDataCorpParticipacaoEmpresasModel($BusinessRelationship);
            }
        }

        return $results;
    }

    public function getRelacionamentoPessoal()
    {
        $dados = $this->getDados($this->urlRelacionamentoPessoal);
        $results = [];
        foreach ($dados->Result as $value) {
            foreach ($value->RelatedPeople->PersonalRelationships as $personalRelationship) {
                if (empty($personalRelationship->RelatedEntityTaxIdNumber)) {
                    continue;
                }

                $results[] = new BigDataCorpRelacoesPessoaisModel($personalRelationship);
            }
        }

        if (empty($results)) {
            return (new ElasticPessoasRelacionadas())->getPessoasRelacionadas(
                $this->param['documento']
            );
        }

        return $results;
    }

    private function getDados($dataset)
    {
        $bigDataManager = new BigDataCorpManager($this->idUser);
        $class = "BigDataCorpEntidadesRelacionadas";
        if (Document::validarCpf($this->documento)) {
            $result = $bigDataManager->getDataPersonApi($dataset, $this->param['documento'], $class, $this->limit);
        } else {
            $result = $bigDataManager->getDataCompaniesApi($dataset, $this->param['documento'], $class, $this->limit);
        }

        $data = json_decode($result);
        return $data;
    }
}
