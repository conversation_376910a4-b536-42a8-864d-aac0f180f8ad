<?php

namespace App\Crawler\BigDataCorpEntidadesRelacionadas\Models;

use DateTime;
use Exception;

class BigDataCorpEntidadesRelacionadasModel
{
    public $DOCUMENTO;
    public $NOME;
    public $IDADE;
    public $ENDERECO;
    public $BAIRRO;
    public $CIDADE;
    public $ESTADO;
    public $CEP;
    public $PARENTESCO;

    public function __construct($pessoa, $endereco)
    {
        if ($pessoa->RelationshipType == 'QSA') {
            $pessoa->RelationshipType = $pessoa->RelationshipName;
        }

        $this->DOCUMENTO = $pessoa->RelatedEntityTaxIdNumber;
        $this->NOME = $pessoa->RelatedEntityName;
        $this->IDADE = '';
        $this->PARENTESCO = $this->translateRelationship($pessoa->RelationshipType ?? '');
        $this->ENDERECO = trim("{$endereco->Typology} {$endereco->AddressMain} {$endereco->Number}");
        $this->BAIRRO = $endereco->Neighborhood ?? '';
        $this->CIDADE = $endereco->City ?? '';
        $this->ESTADO = $endereco->State ?? '';
        $this->CEP = $endereco->ZipCode ?? '';
        $this->ULTIMA_ATUALIZACAO = $this->getDateFormated($pessoa->LastUpdateDate);
        $this->NIVEL = $this->translateRelationshipLevel($pessoa->RelationshipLevel ?? '');
    }

    private function translateRelationship($relationship)
    {
        if (empty($relationship)) {
            return '';
        }

        $relationship = strtoupper($relationship);
        $kindOfRelationships = [
            'COWORKER' => 'COLEGA DE TRABALHO',
            'NEIGHBOR' => 'VIZINHO',
            'BROTHER' => 'IRMAO',
            'NEPHEW' => 'SOBRINHO',
            'MOTHER' => 'MAE',
            'SON' => 'FILHO',
            'HOUSEHOLD' => 'FAMILIAR',
            'GRANDSON' => 'NETO',
            'SPOUSE' => 'CONJUGE',
            'RELATIVE' => 'PARENTE',
            'GRANDPARENT' => 'AVO',
            'UNCLE' => 'TIO',
            'COUSIN' => 'PRIMO',
            'FATHER' => 'PAI',
            'RELATED' => 'RELACIONADO',
            'EMPLOYMENT' => 'EMPREGADO',
            'EMPLOYEE' => 'EMPREGADO',
            'PARTNER' => 'SOCIO',
            'OWNERSHIP' => 'EMPRESA',
            'COLLEGECLASS' => 'COLEGA DE UNIVERSIDADE'
        ];

        if (!isset($kindOfRelationships[$relationship])) {
            return $relationship;
        }

        return $kindOfRelationships[$relationship];
    }

    private function getDateFormated($iso8601DateTime)
    {
        try {
            $date = DateTime::createFromFormat(DateTime::ISO8601, $iso8601DateTime);

            if (empty($date)) {
                return '-';
            }

            return $date->format('d/m/Y');
        } catch (Exception $e) {
            return '-';
        }
    }

    private function translateRelationshipLevel($relationship)
    {

        if (empty($relationship) || $relationship == '') {
            return '';
        }

        $relationship = strtoupper($relationship);
        $kindOfRelationships = [
            'DIRECT' => 'DIRETA',
            'INDIRECT' => 'INDIRETA'
        ];

        return $kindOfRelationships[$relationship] ?? $relationship;
    }
}
