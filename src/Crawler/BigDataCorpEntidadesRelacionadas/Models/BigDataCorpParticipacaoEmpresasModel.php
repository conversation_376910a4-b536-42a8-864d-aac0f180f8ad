<?php

namespace App\Crawler\BigDataCorpEntidadesRelacionadas\Models;

class BigDataCorpParticipacaoEmpresasModel
{
    public $DOCUMENTO;
    public $NOME;
    public $PARENTESCO;
    public $PAIS;
    public $NIVEL;
    public $TIPO;
    public $DATA_INICIO;
    public $DATA_FINAL;

    public $ULTIMA_ATUALIZACAO;

    public function __construct($dados)
    {
        $relationship = [
            'EMPLOYMENT' => 'EMPREGADO',
            'PARTNER' => 'SOCIO',
            'OWNERSHIP' => 'EMPRESA',
            'EMPLOYEE' => 'EMPREGADO',
            'OWNER' => 'PROPRIETÁRIO',
            'LEGAL REPRESENTATIVE' => 'REPRESENTANTE LEGAL'
        ];

        // precisa adicionar outros tipos aqui
        $levelOptions = [
            'DIRECT' => 'DIRETA',
            'INDIRECT' => 'INDIRETA',
            'SECOND-LEVEL' => 'INDIRETA'
        ];

        if (!empty($dados->RelationshipName)) {
            $dados->RelationshipType = $dados->RelationshipName;
        }

        $this->DOCUMENTO = $dados->RelatedEntityTaxIdNumber;

        $this->NOME = $dados->RelatedEntityName;
        $this->PARENTESCO = isset($relationship[strtoupper($dados->RelationshipType)])
            ? $relationship[strtoupper($dados->RelationshipType)] : $dados->RelationshipType;

        $this->PAIS = $dados->RelatedEntityTaxIdCountry;

        $this->NIVEL = isset($levelOptions[strtoupper($dados->RelationshipLevel)])
            ? $levelOptions[strtoupper($dados->RelationshipLevel)] : $dados->RelationshipLevel;

        $this->TIPO = $dados->RelatedEntityTaxIdType;

        $this->DATA_INICIO = $this->getDateFormated($dados->RelationshipStartDate);

        if (!empty($dados->RelationshipEndDate)) {
            $this->DATA_FINAL = $this->getDateFormated($dados->RelationshipEndDate);
        }

        $this->ULTIMA_ATUALIZACAO = $this->getDateFormated($dados->LastUpdateDate);
    }

    private function getDateFormated($iso8601DateTime)
    {

        try {
            $date = \DateTime::createFromFormat(\DateTime::ISO8601, $iso8601DateTime);

            if (gettype($date) == 'boolean') {
                return '-';
            }

            return $date->format('d/m/Y');
        } catch (\Exception $e) {
            return '-';
        }
    }
}
