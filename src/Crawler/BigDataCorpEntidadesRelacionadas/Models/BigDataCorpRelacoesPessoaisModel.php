<?php

namespace App\Crawler\BigDataCorpEntidadesRelacionadas\Models;

class BigDataCorpRelacoesPessoaisModel
{
    public $RelatedEntityTaxIdNumber;
    public $RelatedEntityTaxIdType;
    public $RelatedEntityTaxIdCountry;
    public $RelatedEntityName;
    public $RelationshipType;
    public $RelationshipLevel;
    public $RelationshipStartDate;
    public $RelationshipEndDate;
    public $CreationDate;
    public $LastUpdateDate;

    public function __construct($dados)
    {
        foreach (array_keys(get_object_vars($this)) as $att) {
            $this->{$att} = !empty($dados->{$att}) ? $dados->{$att} : null;
        }
    }
}
