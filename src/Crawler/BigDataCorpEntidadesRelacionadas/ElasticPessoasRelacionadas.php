<?php

namespace App\Crawler\BigDataCorpEntidadesRelacionadas;

use App\Crawler\BigDataCorpEntidadesRelacionadas\Models\BigDataCorpRelacoesPessoaisModel;
use App\Manager\ElasticsearchManager;

/**
 * Classe para procurar o critério na base do elastic e verificar se temos os dados da mãe
 *
 * @version 1.0.0
 *
 * <AUTHOR> Mesquita 06/04/2020
 *
 */
class ElasticPessoasRelacionadas
{
    /**
     * Retorna dados da mãe do critério informado
     *
     * @param string $criterio
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/04/2020
     *
     * @return array
     */
    public function getPessoasRelacionadas($criterio)
    {
        $nomeMae = $this->getNomeMae($criterio);

        if (empty($nomeMae)) {
            return [];
        }

        return $this->getDadosMae($nomeMae);
    }

    /**
     * Pega os dados da mãe no elastic e retorna o parse
     *
     * @param string $nomeMae
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/04/2020
     *
     * @return array
     */
    private function getDadosMae($nomeMae)
    {
        $result = (new ElasticsearchManager())->search($this->getMaeParams($nomeMae));

        $result = isset($result['hits']['hits']) ? $result['hits']['hits'] : [];

        //só pega do elastic quando vier apenas 1 nome para mãe
        if (empty($result) || count($result) > 1) {
            return [];
        }

        return $this->parseResultBigDataCorp($result[0]['_source']);
    }

    /**
     * Parse do resultado para ficar igual ao parse dos dados do BigDataCorp
     *
     * @param array $data
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/04/2020
     *
     * @return array
     */
    private function parseResultBigDataCorp($data)
    {
        $data = [
            "RelatedEntityTaxIdNumber" => $data['cpf'],
            "RelatedEntityTaxIdType" => "CPF",
            "RelatedEntityTaxIdCountry" => null,
            "RelatedEntityName" => $data['nome'],
            "RelationshipType" => "MOTHER",
            "RelationshipLevel" => "DIRECT",
            "RelationshipStartDate" => null,
            "RelationshipEndDate" => null,
            "CreationDate" => null,
            "LastUpdateDate" => null
        ];

        $result[] = new BigDataCorpRelacoesPessoaisModel((object) $data);
        return $result;
    }

    /**
     * Busca os dados do critério no Elastic para pegar o nome da mãe
     *
     * @param string $criterio
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/04/2020
     *
     * @return string
     */
    private function getNomeMae($criterio)
    {
        $result = (new ElasticsearchManager())->search($this->getCriterioParams($criterio));
        $result = isset($result['hits']['hits']) ? $result['hits']['hits'] : [];

        if (empty($result)) {
            return '';
        }

        return $result[0]['_source']['mae'];
    }

    /**
     * Monta query para pegar os dados do critério
     *
     * @param string $criterio
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/04/2020
     *
     * @return array
     */
    private function getCriterioParams($criterio)
    {
        return [
            'index' => 'spine_pf',
            'type'  => 'pf',
            'body' => [
                'query' => [
                    'match' => [
                        'cpf' => $criterio
                    ]
                ]
            ]
        ];
    }

    /**
     * Monta query para pegar os dados da mãe docritério
     *
     * @param string $nomeMae
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 06/04/2020
     *
     * @return array
     */
    private function getMaeParams($nomeMae)
    {
        return  [
            'index' => 'spine_pf',
            'type'  => 'pf',
            'body' => [
                "query" => [
                    "bool" => [
                        "must" => [
                            "query_string" => [
                                "default_field" => "nome",
                                "query" => "\"{$nomeMae}\"",
                                "minimum_should_match" => "100%"
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }
}
