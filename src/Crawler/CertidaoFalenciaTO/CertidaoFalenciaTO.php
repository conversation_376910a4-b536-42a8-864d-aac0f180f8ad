<?php

namespace App\Crawler\CertidaoFalenciaTO;

use Exception;
use App\Helper\Pdf;
use App\Helper\Document;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Manager\ColunaVertebralManager;
use App\Manager\S3\Bucket\StaticUplexisBucket;

class CertidaoFalenciaTO extends Spider
{
    private const MAIN_URL = 'https://eproc1.tjto.jus.br/eprocV2_prod_1grau/';
    private const PRINCIPAL_URL = 'externo_controlador.php?acao=cj_online&acao_origem=&acao_retorno=cj_online';
    private const PDF_URL = 'externo_controlador.php?acao=visualizar_pdf_certidao_judicial&idCertidao=';
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const CERTIDAO_FALENCIA_TO_S3_PATH = 'captura/certidao_falencia_to/';

    private $cpf = '';
    private $pdf = '';

    protected function validateAndSetCrawlerAttributes()
    {
        if (Document::validarCpf($this->param['cpf'])) {
            $this->cpf = Document::removeMask($this->param['cpf']);
        } else {
            throw new Exception("CPF inválido!", 6);
        }

        // Buscar nome do criterio
        $this->nome = $this->getCriteriaName();
    }

    protected function start()
    {
        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::CERTIDAO_FALENCIA_TO_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;

        $pdf = $this->getResults();
        $text = $this->savePdfAndReturnText($pdf);
        $data = [
            'text' => $text,
            'pdf' => $this->pdf
        ];
        return $data;
    }

    private function getResults()
    {
        $params = [
           'hdnInfraTipoPagina' => 1,
           'sbmNovo' => 'Consultar',
           'txtCpfCnpj' => $this->cpf,
           'competencia' => 4,
           'txtNumeroDaj' => '',
           'txtSerieDaj' => '',
           'txtStrParte' => $this->nome
        ];

        $response = $this->getResponse(self::MAIN_URL . self::PRINCIPAL_URL, 'POST', $params);
        $pdfLinkregex = '/_judicial&idCertidao=(.*?)\',/m';
        preg_match($pdfLinkregex, $response, $num);

        $pdf = $this->getResponse(self::MAIN_URL . self::PDF_URL . $num[1]);

        return $pdf;
    }

    private function savePdfAndReturnText($pdf)
    {
        file_put_contents($this->certificateLocalPath, $pdf);

        $text = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'layout',
            'nopgbrk'
        ]);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        $this->pdf = $this->certificateUrl;
        return $text;
    }

    /**
     * Recupera o nome da pessoa física através do documento
     *
     * @return string
     */
    private function getCriteriaName()
    {
        $params = [
            'retry' => '1',
            'source' => 'DadosCadastraisPf',
            'param' => [
                'cpf' => $this->cpf,
            ],
        ];

        $response = (object) (new ColunaVertebralManager())->getSpinePf(json_encode($params));
        return $response->nome;
    }
}
