<?php

namespace App\Crawler\CertidaoCeatTrtBASegundoGrau;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Str;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

class CertidaoCeatTrtBASegundoGrau extends Spider
{
    private const BASE_URL = "https://www.trt5.jus.br";
    private const FORM_URL = "/certidao-eletronica/2";
    private const LIST_URL = "/ceat/backend";
    private const RESULT_URL = "/exibe-relatorio";
    private const PATH_STATIC_S3 = S3_STATIC_URL;
    private const PATH_CERTIDAO_S3 = 'captura/certidao_ceat_trt5_ba_segundo_grau/';

    private $url = self::BASE_URL . self::FORM_URL;
    private $cnpj;
    private $formId;
    private $theme;
    private $theme_token;
    private $libraries;
    private $tipoPessoa;
    private $listaProcessos;
    private $nome = '';
    private $headers = [
        'User-Agent: Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/115.0',
    ];

    protected $captcha;

    public function start()
    {
        $retry = 3;
        while ($retry >= 0) {
            try {
                $this->setProxy();

                $html = $this->getResponse($this->url, 'GET', [], $this->headers);

                $this->getParams($html);

                $this->solveCaptcha($html);

                $this->startRequestSession();

                $this->loadList();

                $this->postRequest();

                return $this->getCertificate();
            } catch (Exception $e) {
                print PHP_EOL . 'ERRO:' . PHP_EOL;
                print_r($e->getMessage());
                print PHP_EOL . PHP_EOL;

                if ($retry == 0) {
                    throw new Exception($e->getMessage(), 2);
                }
                $retry--;

                curl_close($this->ch);
                $this->openCurl();
            }
        }
    }

    public function getCertificate()
    {
        $certificate = $this->loadCertificate();

        $parsedContent = $this->parseCertificate($certificate);

        $pdfLink = $this->savePdf($certificate);

        $parsedContent['pdf'] = $pdfLink;

        return Str::toUtf8($parsedContent);
    }

    public function savePdf($content)
    {
        $uniqid = md5(uniqid(rand(), true));
        $certificateName = "{$uniqid}.pdf";
        $certificateLocalPath = "/tmp/{$certificateName}";
        $certificateS3Path = self::PATH_CERTIDAO_S3 . $certificateName;
        $certificateUrl = self::PATH_STATIC_S3 . $certificateS3Path;

        ((new Pdf())->saveHtmlToPdf($content, $certificateLocalPath));

        (new S3(new StaticUplexisBucket()))->save($certificateS3Path, $certificateLocalPath);

        return $certificateUrl;
    }

    public function parseCertificate($content)
    {
        $descricaoIfPositivaPattern = '/align="justify"\sstyle="color:rgb\(0, 0, 0\);'
            . '.[^>]*><[^>]*>\n?(.*?)<\/p>\s*<p/is';

        $patterns = [
            'tipo' => ['/CERTID.O\s(NEGATIVA|POSITIVA)/i', null],
            'numero' => ['/Certid.o\sn.*>(.*?)<\//i', null],
            'expedicao' => ['/Expedi..o.*>(.*?)<\//i', null],
            'codAutenticidade' => ['/autenticidade.*>(.*?)<\//i', null],
            'validade' => ['/V.lida at.*?g>(.*?)<\//i', null],
            'descricao' => ['/align="justify"\sstyle="color:rgb\(0, 0, 0\);.[^>]*><[^>]*>\n?(.*?)<\/p>\s*<p/is', null],
            'observacoes' => ['/OBSERVA..ES:<[^>]*>\s*(.*?)<\/p>/is', null],
        ];

        $result = Util::parseDados($patterns, $content, false, false);

        $result['descricao'] = str_replace('&nbsp;', ' ', strip_tags($result['descricao']));
        $result['observacoes'] = strip_tags($result['observacoes']);

        if ($result['tipo'] == null) {
            throw new \Exception('Não foi possível gerar a certidão', 3);
        }

        if ($result['tipo'] == 'POSITIVA') {
            preg_match_all($descricaoIfPositivaPattern, $content, $matches);
            $result['descricao'] = str_replace('&nbsp;', ' ', strip_tags($matches[1][0] . $matches[1][1]));
        }

        return $result;
    }

    public function postRequest()
    {
        $params = [
            'grau_processos' => 2,
            'radios' => 'CNPJ',
            'documento' => $this->documento,
            'nomeReceita' => $this->nome,
            'certidaoProcessos' => $this->listaProcessos,
            'nomeConsulta' => '',
            'op' => 'Emitir',
            'form_build_id' => $this->formId,
            'form_id' => 'trt5_ceat_emissao_form',
            'recaptcha_response' => $this->captcha
        ];

        $headers = [
            'Referer: https://www.trt5.jus.br/certidao-eletronica/2',
            'User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
            'X-Requested-With' => 'XMLHTTPRequest'
        ];

        $result = $this->getResponse($this->url, 'POST', $params, $headers);

        $pattern = '/temporariamente indispon.*?veis/is';
        if (preg_match($pattern, $result)) {
            $msg = 'O Portal e alguns Sistemas do TRT5 estão temporariamente indisponíveis!';
            throw new \Exception($msg, 3);
        }

        if (!preg_match('/Emissão realizada com sucesso/is', $result)) {
            throw new \Exception('Emissão não realizada com sucesso!', 3);
        }
    }

    public function loadCertificate()
    {
        echo 'Aguardando 15 segundos...';
        sleep(15);
        $html = $this->getResponse(self::BASE_URL . self::RESULT_URL, 'GET', [], $this->headers);

        return utf8_decode($html);
    }

    public function loadList()
    {
        $params = [
            'DOCUMENTO' => $this->documento,
            'NOME' => $this->nome,
            'NOME_CONSULTA' => '',
            'NUM_PAGINA' => 1,
            'TAMANHO_PAGINA' => 2000,
            'GRAU' => 2,
            'ARQUIVADOS' => 'N'
        ];

        $headers = [
            'User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
            'X-Requested-With' => 'XMLHTTPRequest'
        ];

        $result = $this->getResponse(self::BASE_URL . self::LIST_URL, 'POST', $params, $headers);

        $this->listaProcessos = $result;
    }

    public function startRequestSession()
    {
        $params = [
            'grau_processos' => 2,
            'radios' => 'CNPJ',
            'documento' => $this->documento,
            'nomeReceita' => '',
            'certidaoProcessos' => '',
            'nomeConsulta' => '',
            'form_build_id' => $this->formId,
            'form_id' => 'trt5_ceat_emissao_form',
            'recaptcha_response' => $this->captcha,
            '_triggering_element_name' => 'op',
            '_triggering_element_value' => 'Verificar',
            '_drupal_ajax' => '1',
            'ajax_page_state[theme]' => $this->theme,
            'ajax_page_state[theme_token]' => $this->theme_token,
            'ajax_page_state[libraries]' => $this->libraries
        ];

        $headers = [
            'Authority: www.trt5.jus.br',
            'Origin: https://www.trt5.jus.br',
            'Referer: https://www.trt5.jus.br/certidao-eletronica/2',
            'User-Agent: Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/115.0',
            'X-Requested-With: XMLHttpRequest'
        ];

        $url = $this->url . '?ajax_form=1&_wrapper_format=drupal_ajax';
        $html = $this->getResponse($url, 'POST', $params, $headers);
        $nomePattern = '/nomeReceita.*?0022\svalue.*?22(.*?)..0022/';
        $result = json_decode($html, true);

        if (preg_match('/Captcha Expirado ou Inv\\\u00e1lido/isu', $html)) {
            throw new \Exception('Captcha Expirado ou Inválido. Tente novamente.', 3);
        }

        preg_match($nomePattern, $html, $matches);
        $this->nome = $matches[1];

        if (empty($result)) {
            throw new \Exception('Não foi possível prosseguir com a requisição', 3);
        }
    }

    public function getParams($html)
    {
        $pattern = '/name=\"form_build_id\".value=\"(.*?)\"/is';

        if (!preg_match($pattern, $html, $formId)) {
            throw new \Exception('Não foi possível recuperar o form id!', 3);
        }

        if (!preg_match('/theme":"(.*?)"/is', $html, $theme)) {
            throw new \Exception('Não foi possível recuperar o theme!', 3);
        }

        if (!preg_match('/theme_token":"(.*?)"/is', $html, $theme_token)) {
            throw new \Exception('Não foi possível recuperar o theme_token!', 3);
        }

        if (!preg_match('/libraries":"(.*?)"/is', $html, $libraries)) {
            throw new \Exception('Não foi possível recuperar as libraries!', 3);
        }

        $this->formId = $formId[1];
        $this->theme = $theme[1];
        $this->theme_token = $theme_token[1];
        $this->libraries = $libraries[1];
    }

    public function solveCaptcha($html)
    {
        $pattern = '/js\?render=(.*?)"/is';

        if (!preg_match($pattern, $html, $matches)) {
            throw new \Exception('Não foi possível recuperar o sitekey', 3);
        }

        $this->captcha = $this->solveReCaptchaV3($matches[1], 'certidao', $this->url);
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->cnpj = trim($this->param['cnpj']);
        // $unmaskedDocument = Document::removeMask(trim($this->param['cnpj']));

        if (!Document::validarCpfOuCnpj($this->cnpj)) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        $this->documento = Document::formatCnpj($this->cnpj);
        $this->tipoPessoa = 'J';
    }

    private function openCurl()
    {
        $uniqid = md5(uniqid(rand(), true));
        $this->cookie = '/tmp/cookie_' . $uniqid . '.txt';
        $this->ch = curl_init();

        curl_setopt($this->ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($this->ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($this->ch, CURLOPT_COOKIESESSION, 1);
        curl_setopt($this->ch, CURLOPT_COOKIEJAR, $this->cookie);
        curl_setopt($this->ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($this->ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($this->ch, CURLOPT_TIMEOUT, 600);
    }
}
