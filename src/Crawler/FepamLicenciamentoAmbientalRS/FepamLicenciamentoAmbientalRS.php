<?php

namespace App\Crawler\FepamLicenciamentoAmbientalRS;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Util;
use Exception;

class FepamLicenciamentoAmbientalRS extends Spider
{
    private const BASE_URL_RAZAO_SOCIAL = 'http://ww3.fepam.rs.gov.br/licenciamento/area3/listaRazao.asp?';
    private const BASE_URL_CPF_CNPJ = 'http://ww3.fepam.rs.gov.br/licenciamento/area3/lista1.asp?';

    private $object = [];
    private $indexStart = 1;
    private $indexEnd = 10;
    private $countEmpreendedor = 0;
    private $limit;

    public function start()
    {
        return $this->object;
    }

    /**
     * Captura a quantidade de páginas existentes da requisição.
     * <AUTHOR>
     * @param string $name
     * @return void
     */
    private function getNumberPage($name)
    {
        $params = [
            'area' => 3,
            'buscar' => 2,
            'tipoBusca' => 'municipio',
            'municipio' => 9999999,
            'razao' => $name,
            'ramo' => ""
        ];

        $params = http_build_query($params);
        $result = $this->getResponse(self::BASE_URL_RAZAO_SOCIAL . $params);

        preg_match('/1\s+de\s+(\d+)/', $result, $totalPagina);

        $this->execData($totalPagina[1], $name);
    }

    /**
     * Executa todas as funções para cada página existente na requisição.
     * <AUTHOR> Pereira
     * @param number $totalPagina
     * @param string $name
     * @return void
     */
    private function execData($totalPagina, $name)
    {
        for ($i = 1; $i <= $totalPagina; $i++) {
            $this->makeRequestName($name);
        }
    }

    /**
     * Faz a requisição da página a partir do Nome passado.
     * <AUTHOR> Pereira
     * @param string $name
     * @return void
     */
    private function makeRequestName($name)
    {
        $params = [
            'area' => 3,
            'buscar' => 2,
            'tipoBusca' => 'municipio',
            'municipio' => 9999999,
            'razao' => $name,
            'ramo' => ""
        ];

        $paramsPost = [
            'IndiceIni' => $this->indexStart,
            'IndiceFim' => $this->indexEnd
        ];

        $params = http_build_query($params);
        $result = $this->getResponse(self::BASE_URL_RAZAO_SOCIAL . $params, 'POST', $paramsPost);

        if (strpos($result, " 0 empreendedores")) {
            throw new Exception('Sem resultados encontrados.', 2);
        }

        $this->indexStart = $this->indexEnd + 1;
        $this->indexEnd = $this->indexStart + 9;

        $this->parseDataName($result);
    }

    /**
     * Faz a requisição da página a partir do CPF/CNPJ passado.
     * <AUTHOR> Pereira
     * @param string $cpfCnpj
     * @return void
     */
    private function makeRequestCpfCnpj($cpfCnpj, $itemEmpreendedor = null)
    {

        if ($itemEmpreendedor) {
            preg_match('/CNPJ:<\/li>[\s\S]*?([0-9]*)<\/li>/', $itemEmpreendedor, $cpfCnpj);
            $cpfCnpj = $cpfCnpj[1];
        }

        $paramsGet = [
            'buscar' => 2,
            'tipoBusca' => 'cpfcnpj',
            'cpfcnpj' => $cpfCnpj,
        ];

        $paramsGet = http_build_query($paramsGet);

        $result = $this->getResponse(self::BASE_URL_CPF_CNPJ . $paramsGet);

        if (
            strpos($result, "Object required")
            || strpos($result, "ADODB.Field")
            || preg_match('/Erro\s*\d+\s*\W*\D+/mi', $result)
        ) {
            throw new Exception('Sem resultados encontrados.', 2);
        }

        $this->parseData($result, $itemEmpreendedor);
    }

    /**
     * Captura e estrutura os dados no array a partir do parametro de CPF/CNPJ ou dos itens a partir do Nome.
     * <AUTHOR> Pereira
     * @param string $data
     * @return void
     */
    private function parseData($data, $itemEmpreendedor = null)
    {
        $countItems = 0;
        $countProcesso = 0;

        preg_match_all('/(<div class="(area01|item)">[\s\S]*?<\/div>)/', $data, $items);

        $pattern = [
            'nomeRazaSocial' => ['@align="middle"\/>&nbsp;([\s\S]*?)<\/h3>@'],
            'cpfCnpj' => ['@Doc.\s+Estrangeiro:<\/b>\s+&nbsp;\s+([\s\S]*?)<\/p>@', null],
        ];

        $this->object[$this->countEmpreendedor] = Util::parseDados($pattern, $data);

        if ($itemEmpreendedor) {
            $pattern = [
                'nomeRazaSocial' => ["@<a\s+href='lista1.asp[\s\S]*?'\s+>([\s\S]*?)<\/a>@"],
                'cpfCnpj' => ['@CPF\/CNPJ:<\/li><li class="dir"\s+>([\s\S]*?)<\/li>@'],
                'codEmpreendedor' => ['@Empreendedor:<\/b>\s+&nbsp;\s+([\s\S]*?)<\/p>@'],
                'endereco' => ['@Endere\D+o:<\/li><li\s+class="dir">([\s\S]*?)<\/li>@'],
                'MunEmpreendedor' => ['@Empreendedor:\s<\/li><li\s+class="dir">([\s\S]*?)<\/li>@'],
                'bairro' => ['@Bairro:<\/li><li\s+class="dir">([\s\S]*?)<\/li>@'],
                'munEmpreendimento' => ['@Empreendimento:<\/li><li\s+class="dir">([\s\S]*?)<\/li>@'],
                'ultimaLicenca' => ['@Licen\D+a:<\/li><li\s+class="dir">([\s\S]*?)<\/li>@']
            ];

            $this->object[$this->countEmpreendedor] = Util::parseDados($pattern, $itemEmpreendedor);
        }

        preg_match('/Empreendedor:<\/b>\s+&nbsp;\s+([\s\S]*?)<\/p>/', $data, $codEmpreendedor);

        $this->object[$this->countEmpreendedor]['codEmpreendedor'] = $codEmpreendedor[1];

        foreach ($items[1] as $item) {
            if (strpos($item, 'item')) {
                $countItems++;
                $pattern = [
                    'nomeProcesso' => ['@<img src="[\s\S]*?"\s+alt=""\s+\/><p>([\s\S]*?)<\/p>@'],
                    'ativDetalhes' => ['@Atividade\s+detalhe:<\/li><li[\s\S]*?>([\s\S]*?)<\/li>@'],
                    'porte' => ['@Porte:<\/li><li[\s\S]*?>([\s\S]*?)<\/li>@', null],
                    'potencialPoluidor' => ['@Potencial\s+Poluidor:<\/li><li[\s\S]*?>([\s\S]*?)<\/li>@'],
                    'endereco' => ['@Endere\Do\s[\s\S]*?:<\/li><li\s+class="dir">([\s\S]*?)<\/li>@'],
                    'MunEmpreendimento' => ['@Munic\D+pio\sdo\sEmpreendimento:<\/li><li[\s\S]*?>([\s\S]*?)<\/li>@'],
                ];

                $this->object[$this->countEmpreendedor]['itens'][$countItems] = Util::parseDados($pattern, $item);
            } else {
                $countProcesso++;

                $pattern = [
                    'codProcesso' => ['@Processo:<\/b>[\s\S]*?\s+([\s\S]*?)<\/p>@'],
                    'dataEntrada' => ['@Data\s+de\s+entrada:<\/b>[\s\S]*?\s+([\s\S]*?)<\/p>@'],
                    'situacao' => ['@Situa\D\Do:<\/b>&nbsp;([\s\S]*?)<\/p>@'],
                    'enviado' => ['@Enviado:<\/b>&nbsp;([\s\S]*?)<br>@'],
                    'setor' => ['@Setor:<\/b>&nbsp;([\s\S]*?)<\/p>@'],
                    'assunto' => ['@Assunto:<\/b>&nbsp;([\s\S]*?)<\/p>@'],
                ];

                $this->object[$this->countEmpreendedor]['itens'][$countItems]['processos'][$countProcesso]
                = Util::parseDados($pattern, $item);

                if (strpos($item, 'Documento Associado')) {
                    $pattern = [
                        'codDocAssociado' => ['@Documento\sAssociado:<\/b>[\s\S]*?\s+([\s\S]*?)<\/p>@'],
                        'tipoDoc' => ['@Tipo\s+de\s+Documento:<\/b>[\s\S]*?\s+([\s\S]*?)<\/p>@'],
                        'situacaoDoc' => ['@Situa\D\Do:<\/b>[\s\S]*?.gif"\s+alt="([\s\S]*?)"@'],
                        'vigencia' => ['@Vig\D+ncia:<\/b>[\s\S]*?\s+([\s\S]*?)<\/p>@'],
                        'linkDoc' => ['@<a href="([\s\S]*?)"@'],
                    ];

                    $this->object
                    [$this->countEmpreendedor]
                    ['itens'][$countItems]
                    ['processos'][$countProcesso]
                    ['documento'] = Util::parseDados($pattern, $item);

                    $vigencia = $this->object
                    [$this->countEmpreendedor]
                    ['itens'][$countItems]
                    ['processos'][$countProcesso]
                    ['documento']['vigencia'];

                    $vigencia = str_replace('&nbsp;', ' ', $vigencia);

                    $this->object
                    [$this->countEmpreendedor]
                    ['itens'][$countItems]
                    ['processos'][$countProcesso]
                    ['documento']['vigencia'] = $vigencia;
                }
            }
        }
        $this->countEmpreendedor++;
    }

    /**
     * Captura as divs de cada empreendedor.
     * <AUTHOR> Pereira
     * @param string $data
     * @return void
     */
    private function parseDataName($data)
    {
        $cpfCnpj = null;
        preg_match_all('/<div class="item">([\s\S]*?)<\/div>/', $data, $items);
        foreach ($items[1] as $item) {
            if (count($this->object) < $this->limit) {
                $this->makeRequestCpfCnpj($cpfCnpj, $item);
            }
        }
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->limit = $this->param['limit'];

        if (empty($this->limit)) {
            $this->limit = 10;
        }

        if (empty($this->param['cpf_cnpj_name'])) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        if (!is_numeric(Document::removeMask(trim($this->param['cpf_cnpj_name'])))) {
            $this->getNumberPage($this->param['cpf_cnpj_name']);
        } else {
            $this->makeRequestCpfCnpj(Document::removeMask(trim($this->param['cpf_cnpj_name'])));
        }
    }
}
