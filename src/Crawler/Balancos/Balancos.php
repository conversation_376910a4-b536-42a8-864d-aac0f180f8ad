<?php

namespace App\Crawler\Balancos;

use Exception;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\Bucket\UpminerBucket;
use App\Manager\S3\Bucket\StaticUplexisBucket;

class Balancos extends Spider
{
    private const MAIN_URL = 'https://www.balancos.com.br';
    private const LOGAR_URL = 'https://www.balancos.com.br/login/logar';
    private const BUSCA_URL = 'https://www.balancos.com.br/home/<USER>';
    private const PDF_URL = 'https://www.balancos.com.br/pdf/{tipo}/{id}';

    private const USER = 'uplexis';
    private const PASS = 'UpLexis@2021#BP';

    private const S3_FILE_PATH = 'balanco_patrimonial/{cnpj}/{year}/{file_name}.pdf';

    private $cnpj;
    private $pdfData;

    protected function start()
    {
        try {
            $this->login();

            $this->search();

            if ($this->hasPdfOnS3()) {
                return $this->returnFromS3();
            }

            $results = $this->downloadAllPdfs();
        } catch (\Throwable $th) {
            throw $th;
        } finally {
            $this->logout();
        }

        return $results;
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['cnpj'])) {
            throw new Exception('Critério não informado');
        }

        if (!Document::validarCnpj($this->param['cnpj'])) {
            throw new Exception("Critério informado não é um CNPJ válido");
        }
        $this->cnpj = preg_replace('/\W/', '', $this->param['cnpj']);
    }

    /**
     * Valida se existe pdfs no s3
     *
     * @return boolean
     */
    private function hasPdfOnS3()
    {
        $s3 = new S3(new UpminerBucket());
        foreach ($this->pdfData as $year => $link) {
            try {
                $s3Path = str_replace(
                    [
                        '{cnpj}',
                        '{year}',
                        '{file_name}'
                    ],
                    [
                        $this->cnpj,
                        $year,
                        "{$this->cnpj}_{$year}"
                    ],
                    self::S3_FILE_PATH
                );

                if (empty($s3->get($s3Path))) {
                    throw new Exception("Arquivo vazio");
                }
            } catch (Exception $e) {
                return false;
            }
        }

        return true;
    }

    /**
     * Retorna os dados do s3 (sem ter que baixar, pois eles já existem no s3)
     *
     * @return array*/
    private function returnFromS3()
    {
        $response = [];
        foreach ($this->pdfData as $year => $link) {
            $s3Path = str_replace(
                [
                    '{cnpj}',
                    '{year}',
                    '{file_name}'
                ],
                [
                    $this->cnpj,
                    $year,
                    "{$this->cnpj}_{$year}"
                ],
                self::S3_FILE_PATH
            );
            $response['documents'][] = [
                'bucket' => 'upminer',
                'path' => $s3Path,
                'year' => $year
            ];
        }
        return $response;
    }

    /**
     * Baixa as páginas dos pdfs
     *
     * @return array
     */
    private function downloadAllPdfs()
    {
        $listPdf = [];
        foreach ($this->pdfData as $year => $link) {
            $path = $this->downloadPdf($link, $year);
            $listPdf['documents'][] = [
                'bucket' => 'upminer',
                'path' => $this->sendPdfS3($path, $year),
                'year' => $year
            ];
        }

        return $listPdf;
    }

    private function downloadPdf($link, $year)
    {
        $this->setCurlOpt([
            CURLOPT_RETURNTRANSFER => 1,
            CURLOPT_VERBOSE => 1,
            CURLOPT_HEADER => 1
        ]);

        $path = "/tmp/balancos-{$this->cnpj}-${year}-" . uniqid() . ".pdf";
        $content = $this->getResponse($link);
        file_put_contents($path, $content);

        return $path;
    }

    /**
     * Envia os pdfs baixados para o s3
     *
     * @param string $date
     * @return string
     */
    private function sendPdfS3($path, $year)
    {
        $s3Path = str_replace(
            [
                '{cnpj}',
                '{year}',
                '{file_name}'
            ],
            [
                $this->cnpj,
                $year,
                "{$this->cnpj}_{$year}"
            ],
            self::S3_FILE_PATH
        );

        (new S3(new UpminerBucket()))->save($s3Path, $path);

        return $s3Path;
    }

    private function login()
    {
        $this->getResponse(self::MAIN_URL);
        $html = $this->getResponse(self::LOGAR_URL, 'POST', [
            'login' => self::USER,
            'senha' => self::PASS
        ]);

        if (!preg_match('/class="logado"/isu', $html)) {
            throw new Exception('Não foi possível acessar o site.');
        }

        if (
            preg_match('/Voc.\s*n.o\s*possui\s*cr.ditos/is', $html)
            || preg_match('/Sua\s*assinatura\s*j.\s*expirou/is', $html)
        ) {
            throw new Exception('Sem créditos para executar a consulta.');
        }
    }

    private function logout()
    {
        $url = 'https://www.balancos.com.br/login/logout';
        $response = $this->getResponse($url);

        return $response;
    }

    private function search()
    {
        $html = $this->getResponse(self::BUSCA_URL, 'POST', [
            'doenca' => 2,
            'busca' => \App\Helper\Document::formatCnpj($this->cnpj),
            'x' => 16,
            'y' => 7
        ]);

        if (!$this->hasResult($html)) {
            throw new Exception('Não foi encontrado dados para o critério consultado', 2);
        }

        $companyUrl = $this->getCompanyUrl($html);

        $this->pdfData = $this->getYears($companyUrl);
    }

    private function getYears($companyUrl)
    {
        $html = $this->getResponse($companyUrl);

        preg_match_all(
            '/<a\shref="https:\/\/www.balancos.com.br\/pdf\/(\w+)\/(\d+)"\starget="_blank">(\d{4})<\/a>/is',
            $html,
            $matches,
            PREG_SET_ORDER
        );

        if (empty($matches[0])) {
            throw new Exception('Erro ao buscar os pdfs dos anos');
        }

        $result = [];
        foreach ($matches as $key => $match) {
            $result[$match[3]] = str_replace(
                [
                    '{tipo}',
                    '{id}'
                ],
                [
                    $match[1],
                    $match[2]
                ],
                self::PDF_URL
            );
        }

        return $result;
    }

    private function hasResult($html)
    {
        if (preg_match('/foram\sencontrados\s\<strong\>0\<\/strong\>\sresultados\./isU', $html)) {
            return false;
        } elseif (preg_match('/Não\s*existem\s*balanços\s*anuais\s*cadastrados/ui', $html)) {
            return false;
        }
        return true;
    }

    private function getCompanyUrl($html)
    {
        preg_match('/\/(\w*)\/interna\/(\d*)/is', $html, $match);
        if (empty($match[1]) || empty($match[2])) {
            throw new Exception('Não foi possível encontrar o link da empresa');
        }
        return self::MAIN_URL . $match[0];
    }
}
