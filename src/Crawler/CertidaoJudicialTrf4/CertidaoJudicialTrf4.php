<?php

namespace App\Crawler\CertidaoJudicialTrf4;

use App\Crawler\CertidaoJudicialTrf4\Models\CertidaoJudicialTrf4Model;
use App\Crawler\CertidaoJudicialTrf4\Models\NotIssueCertificateTrf4Model;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Helper\Util;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

class CertidaoJudicialTrf4 extends Spider
{
    private const HOST_NAME = "https://www2.trf4.jus.br";
    private const MAIN_URL = self::HOST_NAME . "/trf4/processos/certidao/index.php";
    private const PROCESSA_CERTIDAO_URL = self::HOST_NAME . '/trf4/processos/certidao/proc_processa_certidao.php?';
    private const EMISSAO_CERTIDAO_URL = self::HOST_NAME . '/trf4/processos/certidao_balcao/certidao_emite_cjf.php?';

    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const CERTIDAO_TRF4_S3_PATH = 'captura/certidao_trf4/';

    private $certificateName;
    private $certificatePath;
    private $certificatePathS3;

    private $razaoSocial;
    private $document;
    private $certificateType;

    public function validateAndSetCrawlerAttributes()
    {
        $this->certificateType = $this->param['tipo_certidao'];
        $this->document = Document::removeMask($this->param['criterio']);

        /* Tipos de certidões: M — Certidão Judicial Cível;
                               N — Certidão Judicial Criminal;
                               O — Certidão Judicial para Fins Eleitorais */
        $arrayTypes = array('O', 'M', 'N');

        if (empty($this->document)) {
            throw new Exception('É obrigatório o CPF/CNPJ!', 1);
        }

        if (empty($this->certificateType)) {
            throw new Exception('É necessário informar o Tipo da Certidão', 1);
        }

        if (!in_array($this->certificateType, $arrayTypes, true)) {
            throw new Exception('Tipo de Certidão Inválido', 1);
        }
    }

    public function start()
    {
        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificatePath = "/tmp/{$this->certificateName}";
        $this->certificatePathS3 = self::S3_STATIC_PATH . self::CERTIDAO_TRF4_S3_PATH . $this->certificateName;

        $retry = 3;
        do {
            try {
                $this->setAlternativeProxy();

                $exception = null;

                $html = $this->getResponse(self::MAIN_URL);

                $key = $this->getSitekey($html);
                $captcha = $this->solveReCaptcha($key, self::MAIN_URL);

                $params = [
                    'status_emissao' => 0,
                    'string_tipo_cert' => $this->certificateType,
                    'string_cpf' => $this->document,
                    'g-recaptcha-response' => $captcha
                ];

                $resProcessaCertidao = $this->getResponse(self::PROCESSA_CERTIDAO_URL . http_build_query($params));

                if (preg_match('/<b>ATEN\W+O:/mi', $resProcessaCertidao, $outSemEmissao)) {
                    $data = $this->parseDataNotCertificate($resProcessaCertidao);
                    return $this->parseNotCertificate($data);
                }

                if (!preg_match('/num_contro_certid=([\d]+)/mi', $resProcessaCertidao, $outputNumeroCertidao)) {
                    throw new Exception('Não foi possível recuperar o id da certidão', 3);
                }

                if (!preg_match("/nom_parte_judici=(.*?)'/is", $resProcessaCertidao, $outputNomeJudicial)) {
                    throw new Exception('Não foi possível recuperar a razão social', 3);
                }

                $numeroCertidao = $outputNumeroCertidao[1];
                $this->razaoSocial = $outputNomeJudicial[1];

                $certidaoParams =
                    'num_contro_certid=' . $numeroCertidao . '&' .
                    'num_cpf_cgc_judici=' . $this->document . '&' .
                    'nom_parte_judici=' . $this->razaoSocial;

                $urlCertidaoEmissao = self::EMISSAO_CERTIDAO_URL . $certidaoParams;
                $res = $this->getResponse($urlCertidaoEmissao);
                file_put_contents($this->certificatePath, $res);

                $text = (new Pdf())->getTextFromPdf($this->certificatePath, [
                    'layout',
                    'nopgbrk'
                ]);

                $this->savePdf();

                return $this->parseDados($text);
            } catch (Exception $e) {
                $exception = $e->getMessage();
                $retry--;
                continue;
            }
        } while ($retry > 0);

        if ($exception) {
            throw new Exception($exception, 3);
        }
    }


    private function parseDataNotCertificate($text): NotIssueCertificateTrf4Model
    {
        $text = Str::encoding($text);
        $patterns = [
            "not_certificate" => [
                '/divConteudo.*?>(.*?.\([\w\W]+\)<\/p>)/mi',
                null
            ]
        ];

        $data = Util::parseDados($patterns, $text);

        if ($data['not_certificate'] == null) {
            if (strlen($this->document) > 11 && $this->param['tipo_certidao'] == 'O') {
                throw new Exception("Não é possível emitir a certidão ELEITORAL para CNPJ", 1);
            }

            $patterns = [
                "not_certificate" => [
                    '/(<p>\s+<b>ATEN\W+O:.*.<\/p>)/mi',
                    null
                ]
            ];

            $data = Util::parseDados($patterns, $text);
        }

        $notIssueCertificateTrf4Model = new NotIssueCertificateTrf4Model();


        if (strlen($this->document) > 11 && $this->param['tipo_certidao'] == '0') {
            throw new Exception("Não é possível emitir a certidão ELEITORAL para CNPJ", 1);
        }

        foreach ($data as $key => $value) {
            $notIssueCertificateTrf4Model->$key .= utf8_decode($value);
        }

        return $notIssueCertificateTrf4Model;
    }

    private function parseDados($text): CertidaoJudicialTrf4Model
    {
        $patterns = [
            "data_emissao" => ['/emitida\sem:\s([\/\d]+)/im', null],
            "hora_emissao" => ['/emitida\sem:\s[\/\d]+\s.*?\s([:\d]+)/im', null],
            "texto" => ['/(Certificamos.*?)Observações:/is', null],
            "numero_controle" => ['/NÚMERO\sDE\sCONTROLE:\s+(\d+)/is', null],
            "codigo_validacao" => ['/CÓDIGO\sDE\sVALIDAÇÃO:\s+(\d+)/s', null]
        ];

        $data = Util::parseDados($patterns, $text);
        $data['descricao'] = utf8_decode($data['texto']);
        $data['razao_social'] = urldecode($this->razaoSocial);
        $data['documento'] = Document::formatCpfOrCnpj($this->document);
        $data['pdf'] = $this->certificatePathS3;

        $certidaoTRF4Model = new CertidaoJudicialTrf4Model();

        foreach ($data as $key => $value) {
            $certidaoTRF4Model->$key = $value;
        }
        return $certidaoTRF4Model;
    }

    private function savePdf(): void
    {
        (new S3(new StaticUplexisBucket()))->save(
            self::CERTIDAO_TRF4_S3_PATH . $this->certificateName,
            $this->certificatePath
        );
    }

    private function getSiteKey(string $html): string
    {
        if (!preg_match('/data-sitekey=\"(.*?)\"/is', $html, $matches)) {
            throw new Exception('Não foi possível recuperar o sitekey', 3);
        }

        return $matches[1];
    }

    private function parseNotCertificate($value)
    {
        $patterns = [
            "razao_social" => ['/física\/jurídica:<br><br><b>(.*)\s-\s+CPF\//im', null]
        ];

        (new Pdf())->saveHtmlToPdf(utf8_decode($value->not_certificate), $this->certificatePath);

        $this->savePdf();

        $data = Util::parseDados($patterns, $value->not_certificate);
        $data['descricao'] = $value->not_certificate;
        $data['documento'] = Document::formatCpfOrCnpj($this->document);
        $data['pdf'] = $this->certificatePathS3;

        return $data;
    }
}
