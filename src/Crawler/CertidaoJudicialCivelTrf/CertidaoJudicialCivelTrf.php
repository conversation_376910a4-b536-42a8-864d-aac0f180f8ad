<?php

namespace App\Crawler\CertidaoJudicialCivelTRF;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

class CertidaoJudicialCivelTrf extends Spider
{
    private const BASE_URL = 'https://sistemas.trf1.jus.br/certidao/';
    private const POST_URL = 'https://sistemas.trf1.jus.br/pgp-api/api/certidao';
    private const PATH_STATIC_S3 = S3_STATIC_URL;
    private const PATH_CERTIDAO_S3 = 'captura/certidao_judicial_civel_trf/';
    private const TYPE_PJ = 'PJS';
    private const TYPE_PF = 'PFS';
    public const RETRY = 5;

    private $certificateName;
    private $certificateLocalPath;
    private $certificateS3Path;
    private $certificateUrl;
    private $document;
    private $type;

    public function start()
    {
        $retries = self::RETRY;

        $uniqid = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqid}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::PATH_CERTIDAO_S3 . $this->certificateName;
        $this->certificateUrl = self::PATH_STATIC_S3 . $this->certificateS3Path;

        while ($retries >= 0) {
            try {
                $sitekey = '6Le8WeUUAAAAAEQ0sNuNgYdqVmklhQCSEKigDDDT';

                $captcha = $this->solveReCaptchaV3($sitekey, 'processarCertidao', self::BASE_URL);

                $response = $this->getResponse(
                    self::POST_URL,
                    'POST',
                    json_encode($this->getPostParams(), JSON_THROW_ON_ERROR),
                    [
                        'x-token:' . $captcha
                    ]
                );

                // Emitiu Negativa
                if (preg_match('/PDF/', $response)) {
                    $data = $this->savePdfAndGetText($response);
                    $text = $this->parseResult($data);
                    return [
                        'pdf' => $this->certificateUrl,
                        'info' => $text
                    ];
                }

                // Positiva
                if (preg_match('/documento/', $response)) {
                    $this->getMensagemPositiva();
                    $text = $this->getInfo();
                    return [
                        'pdf' => $this->certificateUrl,
                        'info' => $text
                    ];
                }
            } catch (Exception $e) {
                if ($retries == 0) {
                    throw new Exception('Não foi possível capturar as informações da página.', 6);
                }
                $retries--;
            }
        }
    }

    public function getPostParams()
    {
        return [
            "spec" => [
                "@type" => $this->type,
                "tipo" => [
                    "id" => 1,
                    "descricao" => "Cível",
                ],
                "secoes" => [
                    [
                        "nome" => "TRIBUNAL REGIONAL FEDERAL DA 1ª REGIÃO",
                        "sigla" => "TRF1",
                        "grau" => 2,
                        "codigoSecaoSubsecao" => 100,
                        "uf" => "TRF",
                    ],
                ],
                "requerente" => [
                    "documento" => $this->document,
                    "isPessoaFisica" => $this->type == self::TYPE_PF ? true : false,
                ],
            ],
        ];
    }

    public function getSitekey()
    {
        $html = $this->getResponse(self::BASE_URL);

        if (!preg_match('/src=\"(main\-es2015..*?)\"/is', $html, $matches)) {
            throw new Exception('Não foi possível recuperar as informações necessárias', 3);
        }

        $response = $this->getResponse(self::BASE_URL . $matches[1]);

        if (!preg_match('/provide:g.b,useValue:\"(.*?)\"/is', $response, $matches)) {
            throw new Exception('Não foi possível recuperar o sitekey', 3);
        }

        return $matches[1];
    }

    public function getMensagemPositiva()
    {
        // O site usa Angular, e com curl não é possível carregar o Javascript
        $html = '
            <h1><strong>ATENÇÃO</strong></h1>
            <h2>
                Essa fonte somente emite certidão negativa, e para emitir a certidão negativa não pode constar nada 
                em nome do critério alvo, assim se constar algo é considerado um positivo onde é necessário 
                preencher um requerimento no  <a href="https://sistemas.trf1.jus.br/certidao/#/solicitacao">site</a>.
            </h2>
        ';

        (new Pdf())->saveHtmlToPdf(utf8_decode($html), $this->certificateLocalPath);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
    }

    private function getInfo()
    {
        $data['numCertidao'] = '';
        $data['codValidacao'] = '';
        $data['observacoes'] = '';
        $data['descricao'] = '
                ATENÇÃO: 
            
                Essa fonte somente emite certidão negativa, e para emitir a certidão negativa não pode constar nada 
                em nome do critério alvo, assim se constar algo é considerado um positivo onde é necessário 
                preencher um requerimento no site.
            ';

        return $data;
    }


    private function parseResult($text)
    {
        $patterns = [
            'numCertidao' => ['@Certidão:([\s\S].*)@'],
            'expedicao' => ['@Expedição:([\s\S].*)@'],
            'codValidacao' => ['@Código\sde\sValidação:([\s\S].*)@'],
            'descricao' => ['@(CERTIFICAMOS[\s\S]*?)+Certidão\semitida@'],
            'observacoes' => ['@Observações:\\n([\s\S]*)Certidão:@']
        ];

        $data = Util::parseDados($patterns, $text);
        return array_map("utf8_decode", $data);
    }

    private function savePdfAndGetText($file)
    {
        file_put_contents($this->certificateLocalPath, $file);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);

        return (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'layout',
            'nopgbrk'
        ]);
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if (Document::validarCpf($this->param['documento'])) {
            $this->type = self::TYPE_PF;
        }

        if (Document::validarCnpj($this->param['documento'])) {
            $this->type = self::TYPE_PJ;
        }

        if ($this->type == null) {
            throw new Exception("Critério de busca inválido.", 6);
        }

        $this->document = Document::formatCpfOrCnpj($this->param['documento']);
    }
}
