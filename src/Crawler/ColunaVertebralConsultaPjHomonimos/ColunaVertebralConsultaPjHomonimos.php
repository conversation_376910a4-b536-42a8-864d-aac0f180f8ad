<?php

namespace App\Crawler\ColunaVertebralConsultaPjHomonimos;

use App\Crawler\Spider;
use App\Manager\ColunaVertebralManager;
use Exception;

class ColunaVertebralConsultaPjHomonimos extends Spider
{
    private $compamy;
    private $page;
    private $count;
    private $onlyAmount;

    /**
     *  Inicia pesquisa da fonte Coluna Vertebral Consulta PJ Homonimos
     *
     *  <AUTHOR> Mesquita 05/02/2021
     *
     *  @version 1.0.0
     */
    protected function start()
    {
        $results = (new ColunaVertebralManager())->getHomonym(
            $this->compamy,
            $this->page,
            $this->count
        );

        if (!$this->onlyAmount) {
            return $results;
        }

        $companies = [];
        foreach ($results['hits']['hits'] as $hit) {
            $companies[$hit['_source']['razao_social'] . ' - ' . $hit['_source']['cnpj']] = $hit['_source'];
        }

        ksort($companies);
        return count($companies);
    }

    /**
     *  Valida e seta os parametros
     *
     *  <AUTHOR> <PERSON>squi<PERSON> 05/02/2021
     *
     *  @version 1.0.0
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['compamy'])) {
            throw new Exception("Razão social não informada!", 3);
        }

        $this->compamy = $this->param['compamy'];
        $this->page = $this->param['page'];
        $this->count = $this->param['count'];
        $this->onlyAmount = $this->param['onlyAmount'];

        if (empty($this->page)) {
            $this->page = 0;
        }

        if (empty($this->count)) {
            $this->count = 0;
        }

        if (empty($this->onlyAmount)) {
            $this->onlyAmount = false;
        }
    }
}
