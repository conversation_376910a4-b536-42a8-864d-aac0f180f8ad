<?php

namespace App\Crawler\CertidaoNegativaRSPF;

use Exception;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\Bucket\StaticUplexisBucket;

class CertidaoNegativaRSPF extends Spider
{
    private const POST_URL = 'https://www.tjrs.jus.br/proc/alvara/gera_alvara.php';
    private const PDF_LINK = 'https://www.tjrs.jus.br/proc/alvara/alvara.php?identificador=';
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const CERTIDAO_S3_PATH = 'captura/certidao_negativa_rs/';

    protected function start()
    {
        $uniqd = md5(uniqid(rand(), true));

        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::CERTIDAO_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;

        $pdf = $this->consulta();
        $pdflink = $this->savePdf($pdf);
        $data = [
            'pdf' => $pdflink
        ];
        return $data;
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCpf($this->param['cpf'])) {
            throw new Exception('Documento inválido', 1);
        }
    }

    //Fonte é uma certidão, concordado com o CS
    //Apenas parametros obrigatórios serão incluidos
    private function consulta()
    {
        $erroPattern = '/pode ser fornecido por meio/m';
        $params = [
            'tipoDocumento' => 7,
            'Municipio' => '',
            'tipoPessoa' => 'F',
            'nome' => $this->param['nome'],
            'sexo' => $this->param['sexo'],
            'cpf' => $this->param['cpf'],
            'cnpj' => '',
            'nomeMae' => $this->param['nomeMae'],
            'nomePai' => '',
            'dataNascimento' => $this->param['dataNascimento'],
            'nacionalidade' => 1,
            'estadoCivil' => $this->param['estadoCivil'],
            'rg' => $this->param['rg'],
            'orgaoExpedidor' => $this->param['orgaoExpedidor'],
            'ufRg' => $this->param['ufRg'],
            'endereco' => $this->param['endereco']
        ];

        $this->setCurlOpt([CURLOPT_TIMEOUT => 240000]);
        $this->setAlternativeProxy();

        $response = $this->getResponse(self::POST_URL, 'POST', $params);
        if (preg_match($erroPattern, $response)) {
            throw new Exception('Não foi possível pesquisar a certidão', 6);
        }
        $tokenPattern = '/mostraAlvara\(\'(.*?)\',/m';
        preg_match($tokenPattern, $response, $token);
        $pdfLink = self::PDF_LINK . $token[1] . '&t=7';
        $pdf = $this->getResponse($pdfLink);
        return $pdf;
    }

    private function savePdf($pdf)
    {
        file_put_contents($this->certificateLocalPath, $pdf);
        (new S3(new StaticUplexisBucket()))->save(
            $this->certificateS3Path,
            $this->certificateLocalPath
        );
        $link = $this->certificateUrl;
        return $link;
    }
}
