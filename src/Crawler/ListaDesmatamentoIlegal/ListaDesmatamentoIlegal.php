<?php

namespace App\Crawler\ListaDesmatamentoIlegal;

use App\Crawler\Spider;
use App\Factory\MongoDB;
use App\Helper\Document;
use Exception;

class ListaDesmatamentoIlegal extends Spider
{
    private const INDEX_MONGODB = 'corporateName_document';
    private const LIMIT = 100;

    private $criterio;
    private $limit;

    public function start()
    {
        $result = $this->searchData();

        if (empty($result)) {
            throw new Exception("A pesquisa não encontrou nenhum dado correspondente.", 2);
        }

        return $result;
    }

    /** Busca dados na base
     * @return array
     * <AUTHOR> - 6 jul. 22
     */
    public function searchData()
    {
        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('common')
            ->setCollection('lista_desmatamento_ilegal');
        $fields = ['documento_proprietario','razao_social'];

        $result = $manager->atlasSearch(
            $this->criterio,
            self::INDEX_MONGODB,
            $this->limit,
            $fields,
            'phrase'
        );

        foreach ($result as $value) {
            $array[] = $this->parseValue($value);
        }

        return $array;
    }


    private function parseValue($value)
    {
        $proprietarios = explode(',', $value['documento_proprietario']);

        foreach ($proprietarios as $prop) {
            $doc_proprietario = preg_replace('/[^0-9]/', '', $prop);
            $nome_proprietario = preg_replace('/[^a-zA-Z]+$/i', ' ', $prop);
            $array_proprietarios[] = [
                'nome' => rtrim($nome_proprietario),
                'documento' => Document::formatCpfOrCnpj($doc_proprietario)
            ];
        }

        $id = json_decode(json_encode($value['_id'], true), true);

        return [
            'id' => $id['$oid'],
            "num_recibo" => $value['num_recibo'],
            "situacao_car" => $value['situacao_car'],
            "municipios" => $value['municipios'],
            "area_car" => $value['area_car'] . ' ha',
            "razao_social" => $value['razao_social'],
            "proprietarios" => $array_proprietarios,
            "info_desmatamento" => $this->parseInfo($value['info_desmatamento'])
        ];
    }

    private function parseInfo($data)
    {
        foreach ($data as $value) {
            $result[] = [
                "ano_desmatamento" => $value['ano_desmatamento'],
                "area_desmatamento" => $value['area_desmatamento'] . ' ha',
                "latitude" => $value['latitude'],
                "longitude" => $value['longitude'],
                "fonte" => $value['fonte'],
                "proc_punitivo_car" => $value['proc_punitivo_car'],
                "municipio_car" => $value['municipio_car']
            ];
        }

        return $result;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->limit = $this->param['limit'] ?? self::LIMIT;
        $this->criterio = trim($this->param['criterio']);

        if (empty($this->criterio)) {
            throw new Exception('Parâmetro Inválido', 1);
        }

        if (Document::validarCpfOuCnpj($this->criterio)) {
            $this->criterio = Document::formatCpfOrCnpj($this->criterio);
        }
    }
}
