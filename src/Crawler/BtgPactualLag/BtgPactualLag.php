<?php

namespace App\Crawler\BtgPactualLag;

use App\Crawler\BtgPactualLag\Models\BtgPactualLagModel;
use App\Crawler\Spider;
use App\Factory\MongoDB;
use Exception;

class BtgPactualLag extends Spider
{
    private const INDEX_MONGODB = 'name';
    private const LIMIT = 50;

    private $name = '';

    public function start()
    {
        $data = $this->searchByName();

        if (!$data) {
            throw new Exception('Nada encontrado!', 2);
        }

        return $data;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->name = trim($this->param['nome']);

        if (empty($this->name)) {
            throw new Exception('Parâmetro Inválido', 1);
        }
    }

    private function searchByName()
    {
        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('btgpactual')
            ->setCollection('lag_base');
        $query = $this->setQuery();

        $result = $manager->query(
            'aggregate',
            $query,
            null,
            null,
            true
        );

        return $this->parseResults($result);
    }

    private function setQuery()
    {
        $query[] = [
            '$search' => [
                'index' => self::INDEX_MONGODB,
                'compound' => [
                    'must' => [
                        [
                            "text" => [
                            "query" => "\"{$this->name}\"",
                            "path" => "nome"
                            ]
                        ]
                    ]
                ]
            ]
        ];
        $query[] = [
           '$project' => [
                '_id' => 0,
                'b' => '$$ROOT'
           ]
        ];
        $query[] = [
            '$lookup' => [
                'localField' => 'b.id_cap',
                'from' => 'lag_siglas',
                'foreignField' => 'id',
                'as' => 's_cap'
            ]
        ];
        $query[] = [
            '$lookup' => [
                'localField' => 'b.id_orgao',
                'from' => 'lag_siglas',
                'foreignField' => 'id',
                'as' => 's_orgao'
            ]
        ];
        $query[] = [
            '$lookup' => [
                'localField' => 'b.id_cargo',
                'from' => 'lag_siglas',
                'foreignField' => 'id',
                'as' => 's_cargo'
            ]
        ];
        $query[] = [
            '$project' => [
                'nome' => '$b.nome',
                'cap' => [
                    '$first' => '$s_cap.descricao'
                ],
                'orgao' => [
                    '$first' => '$s_orgao.descricao'
                ],
                'cargo' => [
                    '$first' => '$s_cargo.descricao'
                ],
                'dt_aniversario' => '$b.dt_aniversario',
                'ano' => '$b.dt_cadastro',
                'status' => '$b.status',
                '_id' => 0,
                'aproximacao' => [
                    '$meta' => "searchScore"
                ]
            ]
        ];

        $query[] = ['$limit' => self::LIMIT];

        return $query;
    }

    private function parseResults($results)
    {
        $btgPactualArray = [];

        foreach ($results as $result) {
            $btgModel = new BtgPactualLagModel();
            $btgModel->nome = $result['nome'];
            $btgModel->cap_orgao = $this->parseCapOrgao($result['cap'], $result['orgao']);
            $btgModel->cargo = $result['cargo'];
            $btgModel->dt_aniversario = $result['dt_aniversario'];
            $btgModel->anos_ocorrencia = substr($result['ano'], 0, 4);
            $btgModel->aproximacao = number_format($result['aproximacao'], 2, '.');
            $btgPactualArray[] = $btgModel;
        }

        return $btgPactualArray;
    }

    private function parseCapOrgao($cap, $orgao)
    {
        return trim(preg_replace('#\s{2,}#is', ' ', $cap) . ' >> ' .
            trim(preg_replace('#\s{2,}#is', ' ', $orgao)));
    }
}
