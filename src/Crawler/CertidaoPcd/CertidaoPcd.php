<?php

namespace App\Crawler\CertidaoPcd;

use Exception;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Helper\Document;
use App\Manager\S3\S3;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Crawler\Spider;

class CertidaoPcd extends Spider
{
    private const BASE_URL = 'https://certidoes.sit.trabalho.gov.br';
    private const MAIN_URL = '/pcdreab';
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const MTE_CERTIDAO_DEBITO_S3_PATH = 'captura/certidao_pcd/';
    private $certificateUrl = '';
    private $storedCookies = '';

    /**
     * Execução da fonte certidao pcd
     *
     * @version 1.0.0
     *
     * <AUTHOR> - 18/11/2019
     *
     * @version 1.1.0 - <PERSON> - 30/05/2022 -
     *      Retirei requisição de detalhes da certidão já que a fonte passou retornar o PDF diretamente
     *
     * @version 2.0.0 - <PERSON> - 27/12/2023 -
     *      Refiz a fonte para adequar a nova forma de emitir através de nova url.
     *
     * @return array
     */
    protected function start()
    {
        $this->initializeCertificatePaths();

        $params = $this->getParams(3);

        $result = $this->generateCertificate($params, 10);

        $this->cleanUpTemporaryFiles();

        return $result;
    }

    protected function getParams($retryCount)
    {
        try {
            $this->setProxy();
            $this->configureCurlOptions();
            $html = $this->getInitialHtml();
            $csrfToken = $this->getCsrfToken($html);
            $cookies = $this->getStoredCookies();
            if (empty($cookies)) {
                $cookies = $this->extractCookies($html);
                $this->setStoredCookies($cookies);
            }
            $captcha = $this->resolveReCaptcha($html);
            return compact('html', 'csrfToken', 'cookies', 'captcha');
        } catch (\Exception $e) {
            if ($retryCount > 0) {
                $this->handleRetry($e, $retryCount);
                return $this->getParams($retryCount - 1);
            } else {
                throw $e;
            }
        }
    }

    protected function generateCertificate($params, $retryCount)
    {
        try {
            $this->setProxy();
            $pdf = $this->getCertificatePdf($params['csrfToken'], $params['captcha'], $params['cookies']);
            $this->savePdf($pdf);
            return $this->parseData();
        } catch (\Exception $e) {
            if ($retryCount > 0) {
                $this->handleRetry($e, $retryCount);
                $newParams = $this->getParams(3);
                return $this->generateCertificate($newParams, $retryCount - 1);
            } else {
                throw $e;
            }
        }
    }

    protected function setStoredCookies($cookies)
    {
        $this->storedCookies = $cookies;
    }

    protected function getStoredCookies()
    {
        return $this->storedCookies ?? '';
    }

    private function initializeCertificatePaths()
    {
        $uniqid = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqid}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::MTE_CERTIDAO_DEBITO_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;
    }

    private function configureCurlOptions()
    {
        $this->setCurlOpt([
            CURLOPT_RETURNTRANSFER => 1,
            CURLOPT_HEADER => 1
        ]);
    }

    private function getInitialHtml()
    {
        return $this->getResponse(
            self::BASE_URL . self::MAIN_URL,
            'GET',
            [],
            [
                'Upgrade-Insecure-Requests: 1',
                'Referer: ' . self::BASE_URL . self::MAIN_URL,
                'Origin:' . self::BASE_URL,
                'User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
            ]
        );
    }

    private function extractCookies($html)
    {
        preg_match_all('/^Set-Cookie:\s*([^;]*)/mi', $html, $matches);
        $cookies = [];

        foreach ($matches[1] as $item) {
            parse_str($item, $cookie);
            $cookies = array_merge($cookies, $cookie);
        }

        if (empty($cookies)) {
            throw new \Exception('Não foram encontrados os cookies.');
        }

        return $cookies;
    }

    private function handleRetry(\Exception $e, &$retry)
    {
        $retry--;

        if ($retry == 0) {
            throw new Exception($e->getMessage(), 3);
        }
    }

    private function savePdf($pdf)
    {
        file_put_contents($this->certificateLocalPath, $pdf);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
    }

    private function resolveReCaptcha($html)
    {
        preg_match(
            '/var recaptcha3SiteKey\s*=\s*["\'](.*?)["\']\s*;/is',
            $html,
            $captchaMatch
        );

        if (empty($captchaMatch[1])) {
            throw new Exception("Erro ao localizar dados do captcha na página.", 3);
        }

        $captcha = $this->solveReCaptchaV3($captchaMatch[1], 'submit', self::BASE_URL . self::MAIN_URL);

        return $captcha;
    }


    private function getCertificatePdf($csrfToken, $captcha, $cookies)
    {
        $postParams = [
            '_csrf' => $csrfToken,
            'cnpjForm' => Document::formatCnpj($this->param['cnpj']),
            'form-id' => 'emitir',
            'token' => $captcha,
        ];

        $headers = [
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,' .
                'image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
            'Cache-Control: max-age=0',
            'Connection: keep-alive',
            'Content-Type: application/x-www-form-urlencoded',
            'Cookie: _ga_35SM2848R8=GS1.1.1703592121.2.0.1703592121.0.0.0; _gid=GA1.3.392272639.1703592343;' .
                '_ga=GA1.1.963152196.1703267636; _ga_Z5L30R9TCE=GS1.1.1703592342.1.1.1703592357.0.0.0;' .
                'JSESSIONID=' . $cookies['JSESSIONID'],
            'Upgrade-Insecure-Requests: 1',
            'Origin:' . self::BASE_URL,
            'Referer: ' . self::BASE_URL . self::MAIN_URL,
            'Sec-Fetch-Dest: document',
            'Sec-Fetch-Mode: navigate',
            'Sec-Fetch-Site: same-origin',
            'Sec-Fetch-User: ?1',
            'User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
            'sec-ch-ua: "Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile: ?0',
            'sec-ch-ua-platform: "Linux"'
        ];

        $response = $this->getResponse(self::BASE_URL . self::MAIN_URL, 'POST', $postParams, $headers);

        if (preg_match('/403 Forbidden/', $response)) {
            throw new Exception("Erro: Acesso negado (403 Forbidden)");
        }

        if (!preg_match('/PDF/', $response)) {
            throw new Exception("Erro: Arquivo não é um PDF!");
        }

        return $response;
    }

    private function getCsrfToken($html)
    {
        $inputCsrf = '/<input[^>]*\s+type="hidden"[^>]*\s+name="_csrf"[^>]*\s+value="([^"]*)"[^>]*>/';

        if (preg_match($inputCsrf, $html, $output)) {
            return $output[1];
        }

        throw new Exception('Erro ao retornar capturar o csrf token.', 3);
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCnpj($this->param['cnpj'])) {
            throw new Exception('CNPJ inválido', 3);
        }
    }

    /**
     * Função para parsear o conteudo do pdf
     * <AUTHOR> Silva - Criação da Função
     * Data 18/11/2019
     * <AUTHOR> Miguel - 27/12/2023 - Alteração no conteúdo do pdf
     */
    private function parseData()
    {
        $result = (new Pdf())->getTextFromPdf($this->certificateLocalPath, ['layout']);
        $resultEncoding = Str::encoding($result);

        preg_match('/EMPREGADOR:\s(.*?)\s*CNPJ/m', $result, $nome);
        preg_match('/CNPJ: (.*?)\s/m', $result, $cnpj);
        preg_match(
            '/CERTIDÃO EMITIDA .* às\s........(.*?)Data do processamento dos dados/m',
            $resultEncoding,
            $conteudo
        );

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);

        $data = [
            'nome' => $nome[1],
            'cnpj' => $cnpj[1],
            'conteudo' => trim($conteudo[1]),
            'pdf' => $this->certificateUrl
        ];

        return $data;
    }

    private function cleanUpTemporaryFiles()
    {
        if (file_exists($this->certificateLocalPath)) {
            unlink($this->certificateLocalPath);
        }
    }
}
