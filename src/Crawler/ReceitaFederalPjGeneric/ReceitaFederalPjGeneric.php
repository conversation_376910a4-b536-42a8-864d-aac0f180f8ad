<?php

namespace App\Crawler\ReceitaFederalPjGeneric;

use App\Crawler\ReceitaFederalPjGeneric\Models\ReceitaFederalPjGenericModel;
use App\Crawler\SpiderGeneric;
use App\Helper\Document;
use Exception;

class ReceitaFederalPjGeneric extends SpiderGeneric
{
    public function start(): ReceitaFederalPjGenericModel
    {
        $data = $this->runGenericSources($this->param, $this->auth ?? []);

        if ($data->source !=  'DadosCadastraisPj') {
            $this->updateSpine('ReceitaFederalPj', $data);
        }

        return $data;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->param['documento'] = preg_replace('/[^0-9]/isu', '', $this->param['documento']);

        if (!Document::validarCnpj($this->param['documento'])) {
            throw new Exception("Critério de busca inválido.", 6);
        }
    }

    protected function getSourcesConfig(): array
    {
        return [
            [
                'source' => 'ReceitaFederalPjBuffer',
                'params' => ['documento' => 'documento'],
                'auths' => []
            ],
            [
                'source' => 'ReceitaFederalPjSite',
                'params' => ['documento' => 'documento'],
                'auths' => []
            ],
            [
                'source' => 'ReceitaFederalPjInfoSimples',
                'params' => ['documento' => 'documento'],
                'auths' => []
            ],
            [
                'source' => 'DadosCadastraisPj',
                'params' => ['cnpj' => 'documento'],
                'auths' => []
            ]
        ];
    }
}
