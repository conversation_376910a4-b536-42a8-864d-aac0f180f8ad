<?php

namespace App\Crawler\ReceitaFederalPjGeneric\Models;

use Exception;

class ReceitaFederalPjGenericModel
{
    public $cnpj;
    public $tipo;
    public $data_abertura;
    public $nome_empresarial;
    public $nome_fantasia;
    public $atividade_economica_principal;
    public $aAtividadeSecundaria = [];
    public $natureza_juridica;
    public $logradouro;
    public $numero;
    public $complemento;
    public $cep;
    public $bairro;
    public $municipio;
    public $uf;
    public $ente_federativo_responsavel;
    public $endereco_eletronico;
    public $telefone;
    public $situacao_cadastral;
    public $data_situacao;
    public $motivo_situacao;
    public $situacao_especial;
    public $data_especial;
    public $data_consulta;
    public $hora_consulta;
    public $cod_atividade;
    public $nome_atividade;
    public $cod_natureza;
    public $nome_natureza;
    public $html;
    public $initial_capital;
    public $cnpj_uf;
    public $source;
    public $aQsa = [];

    public function __set($name, $value)
    {
        if (property_exists($this, $name)) {
            return $this->$name = $value;
        }

        throw new Exception("Propriedade set '{$name}' não existe", 3);
    }

    public function __get($name)
    {
        if (property_exists($this, $name)) {
            return $this->$name;
        }

        throw new Exception("Propriedade get '{$name}'  não existe", 3);
    }
}
