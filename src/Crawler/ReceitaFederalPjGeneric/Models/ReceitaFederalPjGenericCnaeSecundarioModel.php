<?php

namespace App\Crawler\ReceitaFederalPjGeneric\Models;

use Exception;

class ReceitaFederalPjGenericCnaeSecundarioModel
{
    public $codigo;
    public $descricao;
    public $atividade_economica_secundaria;

    public function __set($name, $value)
    {
        if (property_exists($this, $name)) {
            return $this->$name = $value;
        }

        throw new Exception("Propriedade set '{$name}' não existe", 3);
    }

    public function __get($name)
    {
        if (property_exists($this, $name)) {
            return $this->$name;
        }

        throw new Exception("Propriedade get '{$name}'  não existe", 3);
    }
}
