<?php

namespace App\Crawler\ReceitaFederalPjGeneric\Models;

use Exception;

class ReceitaFederalPjGenericQsaModel
{
    public $name;
    public $qualification;
    public $representante_qualificacao;
    public $representante_legal;
    public $pais_origem;
    public $qsa;

    public function __set($name, $value)
    {
        if (property_exists($this, $name)) {
            return $this->$name = $value;
        }

        throw new Exception("Propriedade set '{$name}' não existe", 3);
    }

    public function __get($name)
    {
        if (property_exists($this, $name)) {
            return $this->$name;
        }

        throw new Exception("Propriedade get '{$name}'  não existe", 3);
    }
}
