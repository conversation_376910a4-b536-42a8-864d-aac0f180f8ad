<?php

namespace App\Crawler\ReceitaFederalPjGeneric;

use App\Manager\BufferBrownarrow\BufferBrownarrowClient;
use App\Manager\BufferBrownarrow\BufferBrownarrowManager;
use App\Manager\BufferBrownarrow\DTO\SearchRequestDTO;
use GuzzleHttp\Client;

trait ReceitaFederalPjTrait
{
    private function verifyBuffer()
    {
        $bufferBrownarrowClient =  new BufferBrownarrowClient(new Client());
        $bufferBrownarrowManager = new BufferBrownarrowManager($bufferBrownarrowClient);

        $search = new SearchRequestDTO(
            $this->originalDocument,
            "30"
        );

        $this->bufferData = $bufferBrownarrowManager->getBufferData($search);
    }
}
