<?php

namespace App\Crawler\Mpf;

use App\Helper\Str;
use App\Crawler\Spider;
use App\Helper\Util;
use Exception;

class Mpf extends Spider
{
    private const MAIN_URL = "http://apps.mpf.mp.br/aptusmpf/portal";
    private const MAIN_ASSIST_URL = "http://apps.mpf.mp.br/aptusmpf/sistemas";
    private const SEC_MAIN_ASSIST_URL = "http://apps.mpf.mp.br/aptusmpf/protected/marcador?modulo=0&sistema=portal";
    private const QUERY_URL = "https://apps.mpf.mp.br/aptusmpf/protected/requisicao?";

    private $name;
    private $limit = 30;
    private $pages = 3;
    private $relevancia = false;

    /**
     * Valida valores de entrada
     * @throws Exception
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (!empty($this->param['limite'])) {
            $this->limit = $this->param['limite'];
            $this->pages = ceil($this->limit / 10);
        }

        if (empty($this->param['nome'])) {
            throw new Exception("Parâmetro ou critério invalido", 1);
        }

        $this->name = urlencode('"' . $this->param["nome"] . '"');

        if (!empty($this->param['relevancia'])) {
            $this->relevancia = $this->param['relevancia'];
        }
    }

    /**
     * Wrapper - invoca métodos da classe
     * @return array
     */
    protected function start()
    {
        $this->setAlternativeProxy();

        $processId = $this->getProcessId();

        if (!$processId) {
            throw new Exception("Nenhum resultado encontrado", 2);
        }

        $details = $this->getDetails($processId);
        return $details;
    }

    /**
     * Retorna número de processos necessários para acessar suas páginas de detalhes
     * @return array
     */
    private function getProcessId()
    {
        $mainParams = array(
            "modulo" => "portal"
        );

        $arrQueryString = array(
            "facetsMarcados" => "",
            "filtroDinamicoSelecionados" => "",
            "idConsultaSele1cionada" => "",
            "marcadoresSelecionados" => "",
            "idConsultaSelecionada" => "",
            "modulo" => 0,
            "ordenacao" => "id_dt_documento",
            "qOriginal" =>  $this->name,
            "qtdeFacetsDinamicos" => 0,
            "quantidadeDocumentos" => 0,
            "querySelecionada" => "",
            "query_fields" => "idx_numeracao idx_numeracao2 idx_partes id_autuacao" .
                " id_documento nm_membro_participacao ",
            "selecionaTodosConsulta" => "false",
            "sistema" => "portal",
            "subQuerySelecionada" => "",
            "tamanhoPagina" => 10
        );

        // $this->setProxy();
        $this->getResponse(self::MAIN_URL);
        $this->getResponse(self::MAIN_ASSIST_URL, "POST", $mainParams);
        $this->getResponse(self::SEC_MAIN_ASSIST_URL, "GET");

        $ids = [];
        for ($i = 1; $i <= $this->pages; $i++) {
            $arrQueryString["paginaAtual"] = $i;
            $param = http_build_query($arrQueryString);
            $result = json_decode($this->getResponse(self::QUERY_URL . $param, "POST", json_encode([])))->documentos;

            foreach ($result as $key => $value) {
                if (count($ids) == $this->limit) {
                    break;
                }
                $ids[] = $value->id;
            }
        }

        return $ids;
    }

    /**
     * Acessa páginas dos processos e retorna JSON contendo os valores da busca
     * @param $ids
     * @return array|string
     */
    private function getDetails($ids)
    {
        $response = [];
        foreach ($ids as $key => $id) {
            $result = json_decode($this->getResponse(
                "http://apps.mpf.mp.br/aptusmpf/protected/expediente/{$id}?modulo=0&sistema=portal",
                'GET'
            ));

            if (!empty($result->mensagensErro)) {
                // throw new Exception("Erro no retorno do site");
                continue;
            }
            if (!empty($result->mensagemErro)) {
                // throw new Exception("Erro no retorno do site: {$result->mensagemErro}");
                continue;
            }

            $response['result'][$key] = $result;
        }

        return $this->parseResults($response);
    }

    /**
     * @param $result
     * @return array|string
     */
    private function parseResults($result)
    {
        $arrResult = [];
        foreach ($result['result'] as $key => $arrParsed) {
            $distribuicaoTitualar = [];
            $arrResult['processos'][$key]['detalhes'][0]['num_processo'] = $arrParsed->etiqueta;
            $arrResult['processos'][$key]['detalhes'][0]['partes'] = $arrParsed->partes;
            $arrResult['processos'][$key]['detalhes'][0]['orgao_do_poder_judiciario'] = $arrParsed->localizacao;
            $arrResult['processos'][$key]['detalhes'][0]['vara'] = $arrParsed->localizacao;
            $arrResult['processos'][$key]['detalhes'][0]['localizacao_atual'] = $arrParsed->localizacao;
            $arrResult['processos'][$key]['detalhes'][0]['classe'] = $arrParsed->especie;
            $arrResult['processos'][$key]['detalhes'][0]['camara'] = $arrParsed->camaras;
            $arrResult['processos'][$key]['detalhes'][0]['data_de_autuacao'] = $arrParsed->data;
            $arrResult['processos'][$key]['detalhes'][0]['assunto'] = $arrParsed->temas;

            if ($arrParsed->andamentos) {
                foreach ($arrParsed->andamentos as $k => $v) {
                    $arrResult['processos'][$key]['detalhes'][0]['andamentos'][$k]['localizacao'] = $v->descricao;
                    $arrResult['processos'][$key]['detalhes'][0]['andamentos'][$k]['data'] = $v->data;
                    $arrResult['processos'][$key]['detalhes'][0]['andamentos'][$k]['manifestacao'] = '';
                    if (preg_match('@distribui.{1,8}o\s*?titular\s*?-\s*?Autom.{1,8}a@i', $v->descricao)) {
                        $distribuicaoTitualar[$k] = $v->descricao;
                    }
                }
            }

            $arrResult['processos'][$key]['detalhes'][0]['distribuicao'] = implode("; ", $distribuicaoTitualar);
            $arrResult['processos'][$key]['nome'] = strtoupper($this->param['nome']);
        }
        $arrResult['qnt_processos'] = count($arrResult['processos']);

        if ($this->relevancia) {
            return Str::encoding($arrResult['processos']);
        }
        return Str::encoding($arrResult);
    }
}
