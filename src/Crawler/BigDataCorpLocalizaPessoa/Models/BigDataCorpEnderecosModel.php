<?php

namespace App\Crawler\BigDataCorpLocalizaPessoa\Models;

class BigDataCorpEnderecosModel
{
    public $LOGRADOURO = '';
    public $NUMERO = '';
    public $COMPLEMENTO = '';
    public $BAIRRO = '';
    public $CEP = '';
    public $CIDADE = '';
    public $UF = '';

    public function __construct($dados)
    {
        $this->LOGRADOURO = $dados->Typology;
        $this->LOGRADOURO .= " {$dados->AddressMain}";
        $this->NUMERO = $dados->Number;
        $this->COMPLEMENTO = $dados->Complement;
        $this->BAIRRO = $dados->Neighborhood;
        $this->CEP = $dados->ZipCode;
        $this->CIDADE = $dados->City;
        $this->UF = $dados->State;
    }
}
