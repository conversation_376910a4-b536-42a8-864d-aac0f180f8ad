<?php

namespace App\Crawler\BigDataCorpLocalizaPessoa\Models;

class BigDataCorpParticipacaoEmpresasModel
{
    public $NOME = '';
    public $DOCUMENTO = '';
    public $PCT_PARTICIPACAO = '';
    public $DATA_ENTRADA = '';

    public function __construct($dados)
    {
        $this->NOME = $dados->RelatedEntityName;
        $this->DOCUMENTO = $dados->RelatedEntityTaxIdNumber;
        $this->DATA_ENTRADA = $this->formatDate($dados->RelationshipStartDate);
    }

    private function formatDate($date)
    {
        $date = explode("T", $date)[0];
        $date = explode("-", $date);
        return "{$date[2]}/{$date[1]}/{$date[0]}";
    }
}
