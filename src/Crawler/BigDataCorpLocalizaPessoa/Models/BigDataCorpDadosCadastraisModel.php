<?php

namespace App\Crawler\BigDataCorpLocalizaPessoa\Models;

class BigDataCorpDadosCadastraisModel
{
    public $CPF = '';
    public $NOME = '';
    public $NOME_ULTIMO = '';
    public $SEXO = '';
    public $NOME_MAE = '';
    public $DATANASC = '';
    public $IDADE = '';
    public $SIGNO = '';
    public $SITUACAO_RECEITA = '';
    public $NOME_DATANASC = '';
    public $CPF_DATANASC = '';
    public $CNPJ = '';
    public $RAZAO = '';
    public $NOME_FANTASIA = '';
    public $DT_ABERTURA = '';
    public $COD_CNAE = '';
    public $DESCRICAO_CNAE = '';
    public $QTD_FUNCIONARIOS = '';
    public $DESCR_SITUACAO_CAD = '';
    public $NJUR = '';
    public $NATUREZA = '';
    public $PORTE = '';
    public $QTD_PROP = '';
    public $SEXO_STR = '';

    public function __construct($dados)
    {
        $this->setFields($dados);
    }

    private function setFields($dados)
    {
        $this->CPF = !empty($dados->TaxIdNumber) ? $dados->TaxIdNumber : '';
        $this->NOME = !empty($dados->Name) ? $dados->Name : '';
        $this->NOME_ULTIMO = !empty($this->NOME) ? end(explode(" ", $this->NOME)) : '';
        $this->SEXO = !empty($dados->Gender) ? $dados->Gender : '';
        $this->NOME_MAE = !empty($dados->MotherName) ? $dados->MotherName : '';
        $this->DATANASC = !empty($dados->BirthDate) ? $this->formatDate($dados->BirthDate) : '';
        $this->IDADE = !empty($dados->Age) ? $dados->Age : '';
        $this->SIGNO = !empty($dados->ZodiacSign) ? $dados->ZodiacSign : '';
        $this->SITUACAO_RECEITA = !empty($dados->TaxIdStatus) ? $dados->TaxIdStatus : '';
        $this->NOME_DATANASC = $this->NOME . '|' . $this->DATANASC;
        if (!empty($this->SEXO)) {
            $this->SEXO_STR = $this->SEXO == 'M' ? 'Masculino' : 'Feminino';
        }
    }

    private function formatDate($date)
    {
        $date = explode("T", $date)[0];
        $date = explode("-", $date);
        return "{$date[2]}/{$date[1]}/{$date[0]}";
    }
}
