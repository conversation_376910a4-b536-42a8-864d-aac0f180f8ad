<?php

namespace App\Crawler\BigDataCorpLocalizaPessoa;

use Exception;
use App\Helper\Date;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\DynamoManager;
use App\Manager\BigDataCorpManager;
use App\Crawler\BigDataCorpLocalizaPessoa\Models\BigDataCorpEmailsModel;
use App\Crawler\BigDataCorpLocalizaPessoa\Models\BigDataCorpTelefonesModel;
use App\Crawler\BigDataCorpLocalizaPessoa\Models\BigDataCorpDadosCadastraisModel;
use App\Crawler\BigDataCorpEntidadesRelacionadas\Models\BigDataCorpEnderecosModel;
use App\Crawler\BigDataCorpEntidadesRelacionadas\Models\BigDataCorpParticipacaoEmpresasModel;
use App\Crawler\GenericInterface;
use App\Crawler\LocalizaPessoaGeneric\Models\LocalizaPessoaGenericModel;

class BigDataCorpLocalizaPessoa extends Spider implements GenericInterface
{
    public $urlDadosCadastrais;
    public $urlTelefones;
    public $urlEmails;
    public $urlEnderecos;
    public $urlParticipacao;
    public $urlRelacionamentoPessoal;
    public $urlVizinhos;
    public $allDataSets;
    public $allData;

    protected function validateAndSetCrawlerAttributes()
    {
        if (isset($this->param['documento']) && Document::validarCpf($this->param['documento'])) {
            return true;
        }

        throw new Exception('Parâmetro inválido');
    }

    protected function start()
    {
        $this->config();

        $this->allData = $this->getDados($this->allDatasets);

        $dadosCadastrais = $this->getDadosCadastrais();
        $telefones = $this->getTelefones();
        $emails = $this->getEmails();
        $enderecos = $this->getEnderecos();
        $participacaoEmpresas = $this->getParticipacaoEmpresas();

        return [
            'dados_cadastrais' => $dadosCadastrais[0],
            'aTelefones' => $telefones,
            'aEmails' => $emails,
            'aEnderecos' => $enderecos,
            'aParticipacao_empresa' => $participacaoEmpresas,
            'cadastro_unit' => [
                'DOCUMENTO' => $dadosCadastrais[0]->CPF,
                'NOME' => $dadosCadastrais[0]->NOME
            ]
        ];
    }

    private function config()
    {
        $documento = $this->param['documento'];
        $this->allDatasets = 'basic_data,phones_extended,emails_extended,addresses_extended,business_relationships';
    }

    public function getDadosCadastrais()
    {
        $results = [];
        foreach ($this->allData->Result as $value) {
            $results[] = new BigDataCorpDadosCadastraisModel($value->BasicData);
        }

        return $results;
    }

    public function getTelefones()
    {
        $results = [];
        foreach ($this->allData->Result as $value) {
            foreach ($value->ExtendedPhones->Phones as $phone) {
                $results[] = new BigDataCorpTelefonesModel($phone);
            }
        }

        return $results;
    }

    public function getEmails()
    {
        $results = [];
        foreach ($this->allData->Result as $value) {
            foreach ($value->ExtendedEmails->Emails as $email) {
                $results[] = new BigDataCorpEmailsModel($email);
            }
        }

        return $results;
    }

    public function getEnderecos()
    {
        $results = [];
        foreach ($this->allData->Result as $value) {
            foreach ($value->ExtendedAddresses->Addresses as $address) {
                $results[] = new BigDataCorpEnderecosModel($address);
            }
        }

        return $results;
    }

    public function getParticipacaoEmpresas()
    {
        $results = [];
        foreach ($this->allData->Result as $value) {
            foreach ($value->BusinessRelationships->BusinessRelationships as $BusinessRelationship) {
                $results[] = new BigDataCorpParticipacaoEmpresasModel($BusinessRelationship);
            }
        }

        return $results;
    }

    private function getDados($datasets)
    {
        $class = 'BigDataCorpLocalizaPessoa';
        $bigDataManager = new BigDataCorpManager($this->idUser);
        $result = $bigDataManager->getDataPersonApi($datasets, $this->param['documento'], $class);
        $data = json_decode($result);
        return $data;
    }

    private function getFromTheDatabase($data)
    {
        $cpf = str_replace(['doc{', '}'], '', $data->Result[0]->MatchKeys);

        $this->dynamoManager = new DynamoManager($this->debug);
        $spine = $this->dynamoManager->getItem('spine_pf', [
            'cpf' => $cpf
        ]);

        // Formatando data de nascimento.
        $spine_data_nasc = date_create_from_format('Ymd', $spine['data_nascimento']);
        $data_nascimento = date_format($spine_data_nasc, 'Y-m-d');

        // Pegar signo pela data de nascimento.
        $signo = Date::getZodiacSign($spine['data_nascimento'], 'Ymd');

        $data->Result[0]->BasicData->BirthDate = $data_nascimento;
        $data->Result[0]->BasicData->ZodiacSign = $signo;
        $data->Result[0]->BasicData->TaxIdStatus = $spine['situacao'];

        return $data;
    }

    public function parseToGeneric(array $data, string $sourceName)
    {
        $results = new LocalizaPessoaGenericModel();
        foreach ($data as $key => $item) {
            $results->$key = $item;
        }

        return $results;
    }
}
