<?php

namespace App\Crawler\CadastroInstituicoesEnsino;

use App\Crawler\Spider;
use App\Helper\Util;
use App\Helper\Document;
use App\Helper\Str;
use Exception;

class CadastroInstituicoesEnsino extends Spider
{
    private const URL_CONSULTA = 'http://emec.mec.gov.br/emec/nova-index/listar-consulta-simples/list/300';
    private const URL_ID = 'http://emec.mec.gov.br/emec/modal/listar-mantenedora-ies';
    private const URL_OCORRENCIAS = 'http://emec.mec.gov.br/emec/consulta-ies/lista-ocorrencias';
    private const URL_OCORRENCIAS_VIEW = 'http://emec.mec.gov.br/emec/consulta-ies/visualizar-ocorrencia/';
    private const URL_DADOS = 'http://emec.mec.gov.br/emec/consulta-ies/';

    private const REGEX_OCORRENCIA_DETALHES = [
        'codigo_ocorrencia' => [
            '#<td[^>]*?>\s*c.{1,16}digo\s*da\s*ocorr.{1,8}ncia\s*<\/td>.*?<tr>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is', null
        ],
        'codigo_sidoc' => [
            '#<td[^>]*?>\s*c.{1,16}digo\s*sidoc\s*<\/td>.*?<tr>\s*<td[^>]*?>.*?<\/td>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is',
            null
        ],
        'instituicao' => [
            '#<td[^>]*?>\s*institui.{1,16}o\s*<\/td>.*?<tr>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is', null
        ],
        'data_publicacao' => [
            '#<td[^>]*?>\s*Data\s*de\s*Publica.{1,16}o\s*no\s*D\.O\.U\.\s*<\/td>.*?<tr>\s*<td[^>]*?>\s*(.*?)\s*' .
                '<\/td>#is',
            null
        ],
        'prazo_saneamento' => [
            '#<td[^>]*?>\s*Prazo\s*final\s*para\s*o\s*Saneamento\s*<\/td>.*?<tr>\s*<td[^>]*?>.*?<\/td>\s*<td[^>]*?>' .
                '\s*(.*?)\s*<\/td>#is',
            null
        ],
        'tipo_ocorrencia' => [
            '#<td[^>]*?>\s*tipo\s*da\s*ocorr.{1,8}ncia\s*<\/td>.*?<tr>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is',
            null
        ],
        'despacho_termo_saneamento' => [
            '#<td[^>]*?>\s*Despacho\s*\/\s*Termo\s*de\s*Saneamento\s*<\/td>.*?<tr>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is',
            null
        ],
        'processo_regulacao' => [
            '#<td[^>]*?>\s*Processo\s*de\s*Regula.{1,16}o\s*<\/td>.*?<tr>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is',
            null
        ],
        'adesao_fies' => [
            '#<td[^>]*?>\s*Ades.{1,8}o\s*ao\s*Fies\s*<\/td>.*?<tr>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is',
            null
        ],
        'adesao_pro_uni' => [
            '#<td[^>]*?>\s*Ades.{1,8}o\s*ao\s*ProUni\s*<\/td>.*?<tr>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is',
            null
        ],
        'comentario' => [
            '#<td[^>]*?>\s*Coment.{1,8}rio\s*<\/td>.*?<tr>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is',
            null
        ],
        'nota' => [
            '#<div\s*id="rodapeContent">\s*(.*?)\s*<div\s*id="rodapeRight">#s',
            null
        ],
    ];

    private const URL_APPENDS = array(
        'detalhesIes' => 'index/',
        'atoRegulatorio' => 'listar-ato-regulatorio/',
        'graduacao' => 'listar-curso-agrupado/',
        'especializacao' => 'listar-especializacao/',
        'processos' => 'listar-processo/',
        'listarEnderecos' => 'listar-endereco/',
        'listarOcorrencias' => 'ocorrencias/',
    );

    private const URL_COMPLEMENTS = array(
        'detalhesIes' => '/list/1000',
        'atoRegulatorio' => '/MQ==/list/1000',
        'graduacao' => '/list/1000',
        'especializacao' => '/list/1000',
        'processos' => '/list/1000',
        'listarEnderecos' => '/list/1000',
        'listarOcorrencias' => '/list/1000',
    );

    private $razaoCnpj;

    private function getHashDetails($instituicaoCodigo)
    {
        if (isset($this->param['cnpj'])) {
            return  $this->getInstituicaoIdByInstituicaoCodigo($instituicaoCodigo);
        }

        return  md5('co_ies') . '/' . base64_encode(trim($instituicaoCodigo));
    }

    /**
     * @return array
     */
    protected function start()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $this->setProxy();
        $htmlResponse = $this->getHtmlResponseWithRazaoCnpj($this->razaoCnpj);
        $instituicaoCodigos = $this->parseHtmlResponseToInstituicaoCodigo($htmlResponse);

        $response = [];

        $i = 1;
        foreach ($instituicaoCodigos as $key => $instituicaoCodigo) {
            $instituicaoId = $this->getHashDetails($instituicaoCodigo);
            $response[] = $this->getHtmlResponseArrayWithInstituicaoId($instituicaoId);

            if (isset($this->param['limit']) && $i++ >= $this->param['limit']) {
                break;
            }
        }

        //file_put_contents('./reponse.txt', print_r($response, true));
        return $response;
    }

    /**
     * @param string $instituicaoId
     * @return array
     */
    private function getHtmlResponseArrayWithInstituicaoId($instituicaoId)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $appends = self::URL_APPENDS;
        $baseUrl = self::URL_DADOS;
        $complements = self::URL_COMPLEMENTS;
        $response = [];


        foreach ($appends as $name => $urlAppend) {
            $url = $baseUrl . $urlAppend . $instituicaoId . $complements[$name];

            if ($this->debug) {
                print(PHP_EOL . $url . PHP_EOL);
            }

            $response[$name] = $this->getResponseArrayByHtmlArray($name, $this->getResponse($url, 'GET'));
        }

        return Str::encoding($response);
    }

    /**
     * @param $htmlArray
     */
    private function getResponseArrayByHtmlArray($name, $htmlArray)
    {
        switch ($name) {
            case 'detalhesIes':
                return $this->getDetalhesIes($htmlArray);
                break;
            case 'atoRegulatorio':
                return $this->getAtoRegulatorio($htmlArray);
                break;
            case 'graduacao':
                return $this->getGraduacao($htmlArray);
                break;
            case 'especializacao':
                return $this->getEspecializacao($htmlArray);
                break;
            case 'processos':
                return $this->getProcessos($htmlArray);
                break;
            case 'listarEnderecos':
                return $this->getlistarEnderecos($htmlArray);
                break;
            case 'listarOcorrencias':
                return $this->getListarOcorrencias($htmlArray);
                break;
        }
    }

    private function getListarOcorrencias($html)
    {
        if (
            !preg_match(
                '#<input\s*type="hidden"\s*name="data\[hid_co_ies\]"\s*value="(.*?)"\s*id="data-hid_co_ies"\s*\/>#is',
                $html,
                $match
            )
        ) {
            return [];
        }

        return $this->getOcorrencias($match[1]);
    }

    private function getlistarEnderecos($html)
    {
        $trs = Util::queryXPath($html, '//*[@id="listar-ies-cadastro"]/tbody');


        if (count($trs) == 0) {
            return [];
        }

        $response = [];
        foreach ($trs as $key => $tr) {
            list($codigo, $denominacao, $endereco, $polo, $municipio, $uf) = Util::queryXPath($tr, '//td');

            $response[] = array(
                'codigo' => trim(strip_tags($codigo)),
                'denominacao' => trim(strip_tags($denominacao)),
                'endereco' => trim(strip_tags($endereco)),
                'polo' => trim(strip_tags($polo)),
                'municipio' => trim(strip_tags($municipio)),
                'uf' => trim(strip_tags($uf)),
            );
        }

        return $response;
    }

    private function getProcessos($html)
    {
        $trs = Util::queryXPath($html, '//*[@id="listar-ies-cadastro"]/tbody/tr');

        if (count($trs) == 0) {
            return [];
        }

        $response = [];
        foreach ($trs as $key => $tr) {
            list(
                $numero_processo,
                $ato_regulatorio,
                $nome_curso,
                $estado_atual
            ) = Util::queryXPath($tr, '//td');

            $response[] = array(
                'numero_processo' => trim(strip_tags($numero_processo)),
                'ato_regulatorio' => trim(strip_tags($ato_regulatorio)),
                'nome_curso' => trim(strip_tags($nome_curso)),
                'estado_atual' => trim(strip_tags($estado_atual)),
            );
        }

        return $response;
    }

    private function getEspecializacao($html)
    {
        $trs = Util::queryXPath($html, '//*[@id="listar-ies-cadastro"]/tbody/tr');

        if (count($trs) == 0) {
            return [];
        }

        $response = [];
        foreach ($trs as $key => $tr) {
            list($especializacoes) = Util::queryXPath($tr, '//td');

            $response[] = array(
                'especializacao' => trim(strip_tags($especializacoes)),
            );
        }

        return $response;
    }

    private function getGraduacao($html)
    {
        $trs = Util::queryXPath($html, '//*[@id="listar-ies-cadastro"]/tbody/tr');

        if (count($trs) == 0) {
            return [];
        }

        $response = [];
        foreach ($trs as $key => $tr) {
            list($curso, $qtd) = Util::queryXPath($tr, '//td');

            $response[] = array(
                'curso' => trim(strip_tags($curso)),
                'qtd' => trim(strip_tags($qtd)),
            );
        }

        return $response;
    }

    private function getAtoRegulatorio($html)
    {

        // consertando html que bem quebrado
        $html = preg_replace("</tbody>", "</tbody><tbody>", $html);

        $tbodies = Util::queryXPath($html, '//table/tbody');

        if (count($tbodies) == 0) {
            return [];
        }

        $response = [];

        foreach ($tbodies as $key_tbody => $tbody) {
            $tds = Util::queryXPath($tbody, '//td');

            unset($tds[12], $tds[13], $tds[14]);



            $row = [];
            foreach (array_filter($tds) as $key => $td) {
                if ($key % 2 === 0) {
                    $row[trim(strip_tags($tds[$key]))] = isset($tds[($key + 1)]) ? trim($tds[($key + 1)]) : null;
                }
            }
            $response[] = $row;
        }

        return $response;
    }

    private function getDetalhesIes($html)
    {
        $patterns['mantenedora'] = array(
            'Mantenedora' => array(
                '#Mantenedora\s*:<\/td>\s*<td[^>]*?>\s*<a[^>]*?>\s*<img[^>]*?>\s*<\/img>\s*(.*?)\s*<\/a>\s*<\/td>#is',
                null
            ),
            'cnpj' => array('#cnpj\s*:<\/td>\s*<td[^>]*?>\s*(.*?)<\/td>#is', null),
            'natureza_juridica' => array('#Natureza\s*Jur.{1,8}dica\s*:<\/td>\s*<td[^>]*?>\s*(.*?)<\/td>#is', null),
            'representante_legal' => array('#Representante\s*Legal:\s*<\/td>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is', null),
        );

        $patterns['ies'] = array(
            'nome_ies_sigla' => array(
                '#Nome\s*da\s*IES\s*-\s*Sigla\s*:\s*<\/td>\s*<td[^>]*?>\s*<div[^>]*?>(.*?)\s*<\/div>\s*<\/td>#is',
                null
            ),
            'endereco' => array('#Endere.{1,8}o:\s*<\/td>\s*<td[^>]*?>\s*(.*?)\s*<\/td#is', null),
            'numero' => array('#\sn.{1,8}:\s*<\/td>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is', null),
            'complemento' => array('#Complemento:\s*<\/td>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is', null),
            'cep' => array('#cep:\s*<\/td>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is', null),
            'bairro' => array('#bairro:\s*<\/td>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is', null),
            'municipio' => array('#Munic.{1,8}pio\s*:*\s*<\/td>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is', null),
            'uf' => array('#uf:*\s*<\/td>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is', null),
            'telefone' => array('#telefone:*\s*<\/td>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is', null),
            'fax' => array('#fax:*\s*<\/td>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is', null),
            'organizacao_academica' => array(
                '#Organiza.{1,16}o\s*Acad.{1,8}mica:*\s*<\/td>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is',
                null
            ),
            'Sitio' => array('#s.{1,8}tio:*\s*<\/td>\s*<td[^>]*?>\s*<a[^>]*?>(.*?)\s*<\/a>\s*<\/td>#is', null),
            'categoria_administrativa' => array(
                '#Categoria\s*Administrativa:*\s*<\/td>\s*<td[^>]*?>\s*<div[^>]*?>(.*?)<\/div>\s*<\/td>#is',
                null
            ),
            'email' => array('#e-mail:*\s*<\/td>\s*<td[^>]*?>\s*(.*?)\s*<\/td>#is', null),
        );


        $response = [];
        foreach ($patterns as $key => $pattern) {
            $response[$key] = Util::parseDados($pattern, $html);
        }

        $response['indices'] = $this->getListarIesCadastro($html);

        return $response;
    }

    private function getListarIesCadastro($html)
    {
        $trs = Util::queryXPath($html, '//*[@id="listar-ies-cadastro"]/tbody/tr');

        if (count($trs) == 0) {
            return [];
        }

        $response = [];

        foreach ($trs as $key => $tr) {
            list($indice, $valor, $ano) = Util::queryXPath($tr, '//td');

            $response[] = array(
                'indice' => $indice,
                'valor' => $valor,
                'ano' => $ano,
            );
        }

        return $response;
    }

    private function getOcorrencias($instituicaoCodigo)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $parameters = array(
            'data[hid_co_ies]' => $instituicaoCodigo,
            'data[hid_co_ies_curso]' => 0,
            'data[hid_order]' => 'ocorrencia.dt_cadastro ASC',
            'data[sel_tipo_busca]' => 'nu_sidoc',
        );

        $html = $this->getResponse(self::URL_OCORRENCIAS, 'POST', $parameters);

        $trs = Util::queryXPath($html, '/html/body/table/tbody/tr');

        if (count($trs) == 0) {
            return [];
        }

        $response = [];
        foreach ($trs as $key => $tr) {
            list(
                $data,
                $ocorrencia,
                $sidoc,
                $curso,
                $id_ocorrencia
            ) = Util::queryXPath($tr, '//td');

            preg_match('#id="(.*?)"#is', $id_ocorrencia, $id_ocorrencia);

            $response[] = array(
                'data' => trim(strip_tags($data)),
                'ocorrencia' => trim(strip_tags($ocorrencia)),
                'sidoc' => trim(strip_tags($sidoc)),
                'curso' => trim(strip_tags($curso)),
                'id' => $id_ocorrencia[1],
            );
        }

        foreach ($response as $key => $value) {
            $cod = md5('co_ocorrencia') . '/' . base64_encode(trim($value['id']));
            $response[$key]['detalhes'] = $this->getOcorrenciasDetalhes(
                $this->getResponse(
                    self::URL_OCORRENCIAS_VIEW . $cod,
                    'POST',
                    $parameters
                )
            );
        }

        return $response;
    }

    private function getOcorrenciasDetalhes($html)
    {
        $pattern = self::REGEX_OCORRENCIA_DETALHES;

        $dados = Util::parseDados($pattern, $html);

        if (!preg_match_all('#(?<=href=")\/emec\/comum\/download(.*?)(?="\s*title)#is', $html, $links)) {
            $dados['anexos'] = [];
        } else {
            foreach ($links[0] as $key => $link) {
                $dados['anexos'][] = 'http://emec.mec.gov.br' . $link;
            }
        }

        return $dados;
    }


    /**
     * @param array $instituicaoArray
     * @return string
     */
    private function getInstituicaoIdByInstituicaoCodigo($instituicaoCodigo)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $parameters = array(
            'data[MANTENEDORA_IES][hid_order]' => 'ies.no_ies ASC',
            'data[MANTENEDORA_IES][hid_co_mantenedora]' => $instituicaoCodigo,
            'data[MANTENEDORA_IES][sel_tp_filtro]' => 'co_ies',
        );

        $response = $this->getResponse(self::URL_ID, 'POST', $parameters);

        preg_match_all("#<td style=\"text-align:center;\" style=\"padding: 2px\" >(.*?)<\\/td>#is", $response, $output);

        $codigo = $output[1][0];

        $encodedId = md5('co_ies') . '/' . base64_encode($codigo);
        return $encodedId;
    }

    /**
     * @param string $htmlResponse
     * @return array
     */
    private function parseHtmlResponseToInstituicaoCodigo($htmlResponse)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        preg_match_all(
            "#objTelaConsultaPublicaSimples.(abrirPopUpDetalhesIes|abrirModalMantenedoraIes)\\((.*?)\\);#is",
            $htmlResponse,
            $output
        );

        $instituicaoCodigo = $output[2];
        return $instituicaoCodigo;
    }

    /**
     * @param string $razaoCnpj
     * @return string
     * @throws Exception
     */
    private function getHtmlResponseWithRazaoCnpj($razaoCnpj)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $parameters = [];
        $parameters['data[CONSULTA_SIMPLES][hid_template]'] = Document::validarCnpj($razaoCnpj)
            ? 'listar-consulta-simples-mantenedora'
            : 'listar-consulta-simples-ies';
        $parameters['data[CONSULTA_SIMPLES][hid_order]'] = Document::validarCnpj($razaoCnpj)
            ? 'mantenedora.no_razao_social ASC'
            : 'ies.no_ies ASC';
        $parameters['data[CONSULTA_SIMPLES][sel_tp_filtro]'] = Document::validarCnpj($razaoCnpj)
            ? 'nu_cnpj'
            : 'no_ies_sigla';
        $parameters['data[CONSULTA_SIMPLES][txt_ds_filtro]'] = $razaoCnpj;

        $response = $this->getResponse(self::URL_CONSULTA, 'POST', $parameters);

        if (preg_match_all("/Nenhum Registro Encontrado!/isu", $response)) {
            throw new Exception('Nenhum Registro Encontrado', 2);
        }

        return $response;
    }

    /**
     * @return string
     * @throws Exception
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $razaoCnpj = $this->param['nome'] ??  $this->param['cnpj'];

        if (strlen($razaoCnpj)) {
            $razaoCnpj = trim($razaoCnpj);

            if (Document::validarCnpj($razaoCnpj)) {
                $razaoCnpj = \App\Helper\Document::formatCnpj($razaoCnpj);
            }
        } else {
            throw new Exception('Parâmetro de criterio inválido', 1);
        }

        $this->razaoCnpj = $razaoCnpj;
    }
}
