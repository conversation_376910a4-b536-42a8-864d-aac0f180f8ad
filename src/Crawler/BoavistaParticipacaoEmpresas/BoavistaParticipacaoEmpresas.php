<?php

namespace App\Crawler\BoavistaParticipacaoEmpresas;

use Exception;
use SimpleXMLElement;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\BilhetadorFornecedoresDynamo;

class BoavistaParticipacaoEmpresas extends Spider
{
    private const USUARIO = '00610494';
    private const SENHA = '78VE5T';

    private const URL = 'https://pf.bvsnet.com.br/ModularPFXmlWeb/servicos/GERAL/2.0';

    protected function validateAndSetCrawlerAttributes()
    {
        if (isset($this->param['cpf']) && !empty($this->param['cpf'])) {
            $this->cpf = trim($this->param['cpf']);
            if (Document::validarCpf($this->cpf)) {
                $this->cpf = preg_replace("/[^0-9]/", "", $this->cpf);
            } else {
                throw new Exception('CPF Inválido', 1);
            }
        } else {
            throw new Exception('Parâmetro de criterio inválido', 1);
        }
        return array($this->cpf);
    }

    protected function start()
    {
        $xml = $this->createXmlWithCpf();
        $xmlResponse = $this->getXmlResponseWithXml($xml);
        $resultsArray = $this->parseXmlResponseToArrayWithCpf($xmlResponse);
        $this->saveLogByResultsArrayAndCpf($resultsArray);
        $response = $resultsArray;

        $this->updateSpine('boavistaParticipacaoEmpresas', $response);

        return $response;
    }

    private function saveLogByResultsArrayAndCpf($resultsArray)
    {
        if (empty($this->auth['usuario']) || $this->auth['usuario'] === self::USUARIO) {
            $bilhetador = new BilhetadorFornecedoresDynamo([
                'captura' => 'captura',
                'fornecedor' => 'boavista',
                'consulta' => 'boavista_ModularPf_ParticipacaoEmpresas',
                'criterio' =>  $this->cpf,
                'resultado' => json_encode($resultsArray),
            ]);
            $bilhetador->run();
        }
    }

    private function parseXmlResponseToArrayWithCpf($xmlResponse)
    {
        $xmlResponse = preg_replace(
            '/<ns2:modularpf_2\.0[^>]*>/isu',
            '<ns2:modularpf_2.0 produto="GERAL" versao="2.0">',
            $xmlResponse
        );

        $array = simplexml_load_string($xmlResponse, 'SimpleXMLElement', LIBXML_NOWARNING | LIBXML_NOERROR);

        $array = json_decode(json_encode($array), true);

        if ($array['ns2:participacoes_empresa']['ns24:resumo']['ns24:quantidade'] == 1) {
            $empresas[] = $array['ns2:participacoes_empresa']['ns24:empresas']['ns24:empresa'];
        } else {
            $empresas = $array['ns2:participacoes_empresa']['ns24:empresas']['ns24:empresa'];
        }

        $mensagem = isset($array['ns2:participacoes_empresa']['ns24:mensagem_informativa'])
            ? $array['ns2:participacoes_empresa']['ns24:mensagem_informativa']
            : '';

        $responseArray = array(
            'cpf' => \App\Helper\Document::formatCpf($this->cpf),
            'mensagem' => '',
            'numeroEmpresas' => 0,
            'empresas' => [],
        );

        if (!isset($empresas) || empty($empresas)) {
            $empresas = [];
        }

        if (!isset($mensagem) || empty($mensagem)) {
            $mensagem = '';
        }
        $responseArray['mensagem'] = $mensagem;

        foreach ($empresas as $empresa) {
            $transitionArray = [];
            foreach ($empresa as $key => $value) {
                $arrayKey = $this->translate($key);

                switch ($arrayKey) {
                    case 'cnpj':
                        $value = \App\Helper\Document::formatCnpj($value);
                        break;
                    case 'percentualParticipacao':
                        $value = str_pad($value, 4, '0', STR_PAD_RIGHT);
                        $value = substr_replace($value, '.', -2, 0);
                        $value .=  '%';
                        break;
                    case 'dataEntrada':
                        if (preg_match('/0{6,}/isu', $value)) {
                            $value = '';
                            break;
                        }
                        $value = preg_replace("/(\\d\\d)(\\d\\d)(\\d\\d\\d\\d)/isu", "$1/$2/$3", $value);
                        break;
                }
                $transitionArray[$arrayKey] = $value;
            }

            if (empty($transitionArray)) {
                throw new Exception('Falha ao parsear os dados da empresa', 3);
            }

            $responseArray['empresas'][] = $transitionArray;
            $responseArray['numeroEmpresas']++;
        }

        return $responseArray;
    }


    private function getXmlResponseWithXml($xml)
    {
        $response = $this->getResponse(self::URL, 'POST', $xml);

        return $response;
    }

    private function createXmlWithCpf()
    {
        if (isset($this->auth) && !empty($this->auth)) {
            $usuario = $this->auth['usuario'];
            $senha = $this->auth['senha'];
        } else {
            $usuario = self::USUARIO;
            $senha = self::SENHA;
        }

        $xml = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8" standalone="yes"?> 
<modularPfContratoEntrada/>');
        $xml->addChild('usuario', $usuario);
        $xml->addChild('senha', $senha);
        $xml->addChild('cpf', $this->cpf);
        $blocos = $xml->addChild('blocos');
        $blocos->addChild('bloco', 'participacoesEmpresas');
        $xml->addChild('tipoCredito', 'CC');


        $stringXml = $xml->asXML();
        return $stringXml;
    }

    private function translate($key)
    {
        $table = array(
            'ns2:participacoes_empresa' => 'participacoesEmpresa',
            'ns2:modularpf_2.0' => 'modularPf',
            'ns24:resumo' => 'resumo',
            'ns24:quantidade' => 'quantidade',
            'ns24:empresas' => 'empresas',
            'ns24:empresa' => 'empresa',
            'ns24:cnpj' => 'cnpj',
            'ns24:razao_social' => 'razaoSocial',
            'ns24:tipo' => 'tipo',
            'ns24:percentual_participacao' => 'percentualParticipacao',
            'ns24:data_entrada' => 'dataEntrada',
            'ns24:cargo' => 'cargo',
        );
        if (isset($table[$key])) {
            return $table[$key];
        }
        return $key;
    }
}
