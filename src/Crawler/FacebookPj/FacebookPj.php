<?php

namespace App\Crawler\FacebookPj;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\DynamoManager;
use App\Manager\ElasticsearchManager;
use Carbon\Carbon;
use App\Manager\SnsManager;
use Exception;

/**
 * Classe de busca dos Dados Cadastrais PJ
 *
 * <AUTHOR>
 */
class FacebookPj extends Spider
{
    protected function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCnpj($this->param['documento'])) {
            throw new Exception('O documento informado não é um CNPJ válido.', 1);
        }
    }

    protected function start()
    {
        $document = preg_replace("/\D+/i", '', $this->param['documento']);

        $data = $this->searchLocalByDocument($document);

        if (empty($data)) {
            $data = $this->searchBigDataCorpByDocument($document);
        }

        $this->requestUpdateSpine($document);

        return $data;
    }

    private function searchLocalByDocument($document)
    {
        $query = [
            'index' => 'spine_pj',
            'type' => 'pj',
            'body' => [
                'query' => [
                    'match' => [
                        'cnpj' => "{$document}"
                    ]
                ],
                'size' => 1
            ]
        ];

        $result = (new ElasticsearchManager())->search($query);

        if ($result['hits']['total'] == 0) {
            return [];
        }

        $result = $this->parseElastic($result['hits']['hits'][0]['_source']);

        //ESSA PARTE É ATÉ COLOCAR NO ELASTIC A OPÇÃO SIMEI, SE ALGUEM VER ME LEMBRE DELA 09/04/2020 - Max
        $result['simples_nacional'] = $this->searchSimplesDynamo($document);

        return $result;
    }

    private function parseElastic($data)
    {
        return [
            'informacoes_gerais' => [
                'cnpj' => $data['cnpj'],
                'razao_social' => $data['razao_social'],
                'nome_fantasia' => $data['nome_fantasia'],
                'data_abertura' => $data['data_abertura'],
                'capital_social' => $data['capital_social'],
                'matriz' => !empty($data['matriz']) ? (bool) strtolower($data['matriz']) : null,
                'tipo' => $data['tipo_cnae'] ?? null,
                'data_consulta_fonte' => date('d/m/y H:i:s')
            ],
            'situacao_cadastral' => [
                'descricao' => $data['situacao_cadastral'],
                'data' => $data['data_situacao'],
            ],
            'natureza_juridica' => [
                'codigo' => $data['natureza_juridica']
            ],
            'cnae' => [
                'codigo' => $data['cnae'],
                'descricao' => $data['cnae_descricao'] ?? null
            ],
            'simples_nacional' => [
                'status' => null,
                'status_simei' => null,
                'data_simples_nacional' => null,
                'data_simei' => null
            ],
            'endereco' => [
                'bairro' => $data['bairro'],
                'cidade' => $data['municipio'],
                'uf' => $data['uf'],
                'cep' => $data['cep'],
                'ibge' => null,
                'numero' => $data['numero'],
                'complemento' => $data['logr_complemento'],
                'logradouro' => $data['logradouro'],
                'latitude' => $data['location']['lat'] ?? null,
                'longitude' => $data['location']['lon'] ?? null
            ]
        ];
    }

    private function searchSimplesDynamo($document)
    {
        $dynamoManager = new DynamoManager(false);

        $conditions = 'cnpj = :cnpj';

        $expression = [
            ':cnpj' => $document
        ];

        $result = $dynamoManager->getQuery('spine_pj_simplesnacional', $conditions, $expression);

        return $this->parseSimplesDynamo($result);
    }

    public function parseSimplesDynamo($data)
    {
        return [
            'status' => empty($data[0]['status_sn']) || $data[0]['status_sn'] == 'I' ? null : $data[0]['status_sn'],
            'status_simei' => empty($data[0]['status_simei']) || $data[0]['status_simei'] == 'I'
                ? null : $data[0]['status_simei'],
            'data_simples_nacional' => $data[0]['data_opcao_sn'] ?? null,
            'data_simei' => $data[0]['data_opcao_simei'] ?? null
        ];
    }

    private function searchBigDataCorpByDocument($document)
    {
        $url = 'https://bigboost.bigdatacorp.com.br/companies?AccessToken=cbbe8621-e7ba-47cc-913b-ece93924e713&q=doc{' .
            $document . '}&Datasets=basic_data,addresses';

        $result = json_decode($this->getResponse($url, 'GET'), true);

        $this->verifyBigDataCorpError($result);

        if (empty($result['Result'][0]['BasicData'])) {
            throw new Exception("Nenhum resultado encontrado", 2);
        }

        return $this->parseBigDataCorp($result);
    }

    private function verifyBigDataCorpError($data)
    {
        if (isset($data['Status']['api'][0]['Code']) && $data['Status']['api'][0]['Code'] < 0) {
            throw new Exception('Erro ao realizar a consulta.', 3);
        }
    }

    private function parseBigDataCorp($data)
    {
        return [
            'informacoes_gerais' => [
                'cnpj' => $data['Result'][0]['BasicData']['TaxIdNumber'],
                'razao_social' => $data['Result'][0]['BasicData']['OfficialName'],
                'nome_fantasia' => $data['Result'][0]['BasicData']['TradeName'],
                'data_abertura' => Carbon::parse($data['Result'][0]['BasicData']['FoundedDate'])->format('Ymd'),
                'capital_social' => $data['Result'][0]['BasicData']['AdditionalOutputData']['CapitalRS'] ?? null,
                'matriz' => $data['Result'][0]['BasicData']['IsHeadquarter'],
                'tipo' => null,
                'data_consulta_fonte' => date('d/m/y H:i:s')
            ],
            'situacao_cadastral' => [
                'descricao' => $data['Result'][0]['BasicData']['TaxIdStatus'],
                'data' => Carbon::parse($data['Result'][0]['BasicData']['TaxIdStatusDate'])->format('Ymd'),
            ],
            'natureza_juridica' => [
                'codigo' => $data['Result'][0]['BasicData']['LegalNature']['Code']
            ],
            'cnae' => [
                'codigo' => $data['Result'][0]['BasicData']['Activities'][0]['Code'],
                'descricao' => $data['Result'][0]['BasicData']['Activities'][0]['Activity']
            ],
            'simples_nacional' => [
                'status' => $data['Result'][0]['BasicData']['TaxRegimes']['Simples'] == true ? 'P' : 'N',
                'status_simei' => $data['Result'][0]['BasicData']['TaxRegime'] == 'MEI' ? 'P' : 'N',
                'data_simples_nacional' => null,
                'data_simei' => null
            ],
            'endereco' => [
                'bairro' => $data['Result'][0]['Addresses'][0]['Neighborhood'] ?? null,
                'cidade' => $data['Result'][0]['Addresses'][0]['City'] ?? null,
                'uf' => $data['Result'][0]['Addresses'][0]['State'] ?? null,
                'cep' => $data['Result'][0]['Addresses'][0]['ZipCode'] ?? null,
                'ibge' => null,
                'numero' => $data['Result'][0]['Addresses'][0]['Number'] ?? null,
                'complemento' => $data['Result'][0]['Addresses'][0]['Complement'] ?? null,
                'logradouro' => $data['Result'][0]['Addresses'][0]['AddressMain'] ?? null,
                'latitude' => $data['Result'][0]['Addresses'][0]['Latitude'] ?? null,
                'longitude' => $data['Result'][0]['Addresses'][0]['Longitude'] ?? null
            ]
        ];
    }

    /**
     * Parseia a resposta final para o formato do lambda
     *
     * @version 1.0.0
     * <AUTHOR>
     * Revisão - Maximiliano Minucelli 20/08/2018 - Param billing
     * Revisão - Maximiliano Minucelli 09/04/2020 - Não lembro por que fiz esse billing mas deixei
     *
     * @param array $body
     * @return array
     */
    private function generateResponse($data)
    {
        if (
            isset($data['situacao_cadastral']['descricao'])
            && !empty($data['situacao_cadastral']['descricao'])
            && !empty($data['endereco'])
        ) {
            $billing = true;
        } else {
            $billing = false;
        }

        return [
            'code' => '200',
            'message' => 'OK',
            'status' => 'success',
            'billing' => $billing,
            'data' => $data
        ];
    }

    protected function addExtraDataToResponse($response)
    {
        if ($response['code'] == 2) {
            $response['status'] = 'success';
            $response['billing'] = false;
        } elseif ($response['status'] == 'error') {
            $response['billing'] = false;
        } elseif (
            isset($response['data']['situacao_cadastral']['descricao'])
            && !empty($response['data']['situacao_cadastral']['descricao'])
            && !empty($response['data']['endereco'])
        ) {
            $response['billing'] = true;
        } else {
            $response['billing'] = false;
        }

        return $response;
    }

    private function requestUpdateSpine($document)
    {
        $snsManager = new SnsManager([
            'access_key' => AWS_ACCESS_KEY,
            'secret_key' => AWS_SECRET_KEY
        ]);

        $target = 'arn:aws:sns:us-east-1:' . ACCOUNT_ID . ':ReceitaFederalLambda';
        $subject = 'Atualização SPINE PJ';
        $message = json_encode([
            "retry" => "2",
            "source" => "ReceitaFederalPj",
            "param" => [
                "documento" => $document
            ]
        ]);

        $snsManager->sendMessage($target, $subject, $message);
    }
}
