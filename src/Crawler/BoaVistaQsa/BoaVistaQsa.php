<?php

namespace App\Crawler\BoaVistaQsa;

use App\Crawler\BoavistaParticipacaoEmpresas\BoavistaParticipacaoEmpresas;
use App\Crawler\BoaVistaQsaScore\BoaVistaQsaScore;
use App\Crawler\Spider;
use App\Helper\Document;
use Exception;

class BoaVistaQsa extends Spider
{
    private $isCNPJ = false;
    private $data = [];

    public function start()
    {
        $this->data['score'] = [];

        if ($this->isCNPJ) {
            $response = (new BoaVistaQsaScore(['cnpj' => $this->param['cpf_cnpj']], []))->run();

            foreach ($response['data']['socios'] as $socio) {
                $this->data['socios'][] = [
                    'cpf_cnpj' => Document::formatCpfOrCnpj($socio['cpf_cnpj']),
                    'nome' => $socio['nome_empresa'],
                    'entrada' => $socio['data_entrada'],
                    'qualificacao' => $socio['qualificacao'],
                    'participacao' => $socio['participacao'],
                ];
            }

            $this->data['score'] = $response['data']['score'];

            return $this->data;
        }

        $response = (new BoavistaParticipacaoEmpresas(['cpf' => $this->param['cpf_cnpj']], []))->run();

        foreach ($response['data']['empresas'] as $empresas) {
            $this->data['socios'][] = [
                'cpf_cnpj' => $empresas['cnpj'],
                'nome' => $empresas['razaoSocial'],
                'entrada' => $empresas['dataEntrada'],
                'qualificacao' => $empresas['cargo'],
                'participacao' => $empresas['percentualParticipacao'],
            ];
        }

        return $this->data;
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['cpf_cnpj'])) {
            throw new Exception("Parâmetro CNPJ ou CPF é obrigatório", 1);
        }

        if (Document::validarCnpj($this->param['cpf_cnpj'])) {
            $this->isCNPJ = true;
        } elseif (Document::validarCpf($this->param['cpf_cnpj'])) {
            $this->isCNPJ = false;
        } else {
            throw new Exception('Parâmetro Inválido', 1);
        }
    }
}
