<?php

namespace App\Crawler\Inea;

use App\Crawler\Spider;
use App\Factory\MongoDB;
use App\Helper\Str;
use Exception;

class Inea extends Spider
{
    private const INDEX_MONGODB = 'corporate_name';
    private const LIMIT = 10;

    protected function start()
    {
        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('common')
            ->setCollection('inea');
        $fields = ['razao_social'];
        $results = $manager->atlasSearch(
            $this->param['nome'],
            self::INDEX_MONGODB,
            $this->param['limite'],
            $fields,
            'phrase'
        )->toArray();

        if (empty($results)) {
            throw new Exception('Nada Encontrado', 2);
        }

        return $results;
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if (!isset($this->param['nome']) or empty($this->param['nome'])) {
            throw new Exception('Parâmetro inválido');
        }

        $this->param['nome'] = Str::removeSiglas($this->param['nome'], true, false);
        $this->param['nome'] = preg_replace('@/@', '\/', $this->param['nome']);

        $this->param['limite'] = $this->param['limite'] ??  self::LIMIT;
    }
}
