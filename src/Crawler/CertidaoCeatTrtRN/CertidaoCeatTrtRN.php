<?php

namespace App\Crawler\CertidaoCeatTrtRN;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

class CertidaoCeatTrtRN extends Spider
{
    private const BASE_URL = 'https://apps.trt21.jus.br/certidao-web/#/certidao';
    private const PESQUISA_URL = 'https://apps.trt21.jus.br/certidao-web/service/certidao/portipo/';
    private const PATH_STATIC_S3 = S3_STATIC_URL;
    private const PATH_CERTIDAO_S3 = 'captura/certidao_ceat_trt21_rn/';

    private string $cpfCnpj;
    private string $tipoDocumento;
    private string $tipoCertidao;

    /**
     * @throws \Exception
     */
    public function start(): array
    {
        try {
            $file = $this->makeRequest();
            $pdf = $this->savePdfAndGetText($file);
            $result = $this->parseText($pdf['textPdf']);

            return [
                'pdf' => $pdf['urlPdf'],
                'info' => $result
            ];
        } catch (Exception $e) {
            throw new Exception('Não foi possível capturar as informações da página.', 6);
        }
    }

    /** O site só emite 9 certidões por dia para o mesmo IP
     *
     * @param  int  $retry
     */
    private function setProxyRetry(int $retry): void
    {
        switch (true) {
            case $retry < 5:
                $this->setProxy();
                break;
            case $retry > 5:
                $this->setAlternativeProxy();
                break;
            default:
                $this->setAnonymousProxy();
                break;
        }
    }

    /** <AUTHOR> Santos - 08 maio 2023
     */
    private function makeRequest(): string
    {
        $retry = 6;
        do {
            $this->setProxyRetry($retry);
            $captcha = $this->resolveRecaptcha();

            $urlString = self::PESQUISA_URL . "{$this->tipoDocumento}?documento={$this->cpfCnpj}&arquivados=T" .
                "&g-recaptcha-response={$captcha}&dataInicial=undefined&dataFinal=undefined&tipoNome=R";

            $headers = [
                "User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0",
            ];

            $result = $this->getResponse($urlString, 'GET', [], $headers);

            if (!is_null($result)) {
                return $result;
            }
            $retry--;
        } while ($retry > 0);
        throw new Exception('Não foi possível capturar as informações da página.', 6);
    }

    /**
     * @throws \Exception
     */
    private function savePdfAndGetText(string $pdfString): array
    {
        $uniqid = md5(uniqid(mt_rand(), true));
        $certificateName = "$uniqid.pdf";
        $certificateLocalPath = "/tmp/{$certificateName}";
        $certificateS3Path = self::PATH_CERTIDAO_S3 . $certificateName;
        $certificateUrl = self::PATH_STATIC_S3 . $certificateS3Path;

        file_put_contents($certificateLocalPath, $pdfString);

        $text = (new Pdf())->getTextFromPdf($certificateLocalPath, ['layout', 'nopgbrk']);

        (new S3(new StaticUplexisBucket()))->save($certificateS3Path, $certificateLocalPath);

        return ["urlPdf" => $certificateUrl, "textPdf" => $text];
    }

    /**
     * @throws \Exception
     */
    private function parseText(string $text): array
    {
        $patterns = [
            'status' => ['@Certid.o\sn..(.*?)\n@u'],
            'numCertidao' => ['@Certid.o\sn..(.*?)\n@u'],
            'validade' => ['@V.lida\sat..(.*?)\\n@u'],
            'dataEmissao' => ['@Data\sde\sEmiss.o.(.*?)\\n@u'],
            'descricao' => ['@(CERTIFICA-SE[\s\S]*?(\s+conforme\slistagem abaixo.|passivo\sprocessual.))@u'],
            'observacoes' => ['@Observações:([\s\S]*?)\s+Natal\/RN@u'],
            'acoes' => ['@conforme\slistagem\sabaixo:([\s\S]*?\s+Total\sde\sA..es:.*)@u']
        ];

        $data = Util::parseDados($patterns, $text);
        $data = array_map("utf8_decode", $data);

        $observacoes = preg_replace('/\\n\n/', '', $data['observacoes']);
        $descricao = preg_replace('/\\n\n/', '', $data['descricao']);
        $data['descricao'] = $this->removeHeaders($descricao);
        $data['observacoes'] = $this->removeHeaders($observacoes);

        if (isset($data['acoes'])) {
            $data['acoes'] = $this->removeHeaders($data['acoes']);
        }

        return $data;
    }

    private function removeHeaders(string $text): string
    {
        $patterns = array(
            '/\s+PODER\sJUDICI.RIO\n/u',
            '/\s+JUSTI.A\sDO\sTRABALHO\n/u',
            '/\s+TRIBUNAL\sREGIONAL\sDO\sTRABALHO\sDA\s21..\sREGI.O\n/u',
            '/\s+Miss.o.\sPromover\sjusti.a.\sno\s.mbito\sdas\srela..es\sde\strabalho.\scom\n/u',
            '/\s+celeridade.\sefici.ncia\se\sefetividade.\scontribuindo\spara\sa\spaz\ssocial\se\so\n/u',
            '/\s+fortalecimento\sda\scidadania./u',
            '/(\s+Este\sdocumento\s.*)/u',
            '/(pode\sser\svalidado\sem\s.*)/u',
        );
        return preg_replace($patterns, '', $text);
    }

    /**
     * @throws \Exception
     */
    private function resolveRecaptcha(): string
    {
        $keyCapctha = '6Lc9itYlAAAAAKoWAkuJ06B4_3wKqR50Ozb2nYM4';
        $retry = 3;
        do {
            if (!empty($keyCapctha)) {
                return $this->solveReCaptchaV3($keyCapctha, $this->tipoCertidao, self::BASE_URL);
            }
            $retry--;
        } while ($retry > 0);
        throw new Exception("Erro ao localizar dados do captcha na página.", 3);
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->cpfCnpj = trim($this->param['cpf_cnpj']);
        $this->cpfCnpj = Document::removeMask($this->cpfCnpj);
        $this->tipoDocumento = "CPF";
        $this->tipoCertidao = "certidao_pf";

        if (empty($this->cpfCnpj)) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        if (Document::validarCnpj($this->cpfCnpj)) {
            $this->tipoDocumento = "CNPJ";
            $this->tipoCertidao = "certidao_pj";
        }
    }
}
