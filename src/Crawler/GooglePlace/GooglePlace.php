<?php

namespace App\Crawler\GooglePlace;

use App\Crawler\Spider;
use Exception;

class GooglePlace extends Spider
{
    private const URL_GOOGLE_PLACE = 'https://maps.googleapis.com/maps/api/place/findplacefromtext/json?input=';
    private const URL_GOOGLE_PLACE_DETAILS = 'https://maps.googleapis.com/maps/api/place/details/json?placeid=';
    private const CHAVE_API_GOOGLE_1 = 'AIzaSyD3cZ2QPOg1WzKF-eenIO37d77JmTnXJ0A';
    private const CHAVE_API_GOOGLE_2 = 'AIzaSyB6o0Al0Dt-awGN395y_BgNvi4oiJc5-yo';

    protected function validateAndSetCrawlerAttributes()
    {
        if (!empty($this->param['company'])) {
            return true;
        }

        throw new Exception('Parâmetro inválido');
    }

    protected function start()
    {
        $returnPlaceSearch = self::getPlaceSearch($this->param['company']);

        if ($returnPlaceSearch['status'] == 'OK') {
            $response = self::getPlaceDetails($returnPlaceSearch['candidates'][0]['place_id']);

            if (empty($response['result']['website'])) {
                throw new Exception('Nada encontrado', 2);
            }

            return $response['result']['website'];
        }

        throw new Exception('Nada encontrado', 2);
    }

    /**
     * Pesquisa no Google Places
     *
     * @version 1.0.0
     * <AUTHOR> Costa - 11/09/2018
     *
     * @param string $company
     *
     * @return []
     *
     */
    private function getPlaceSearch($company)
    {
        $parameters = '&inputtype=textquery&fields=all&key=';

        $response = $this->getResponse(self::URL_GOOGLE_PLACE . $company . $parameters . $this->randKeysApi(), 'GET');
        return json_decode($response, true);
    }
    /**
     * Pesquisa através do Place ID
     *
     * @version 1.0.0
     * <AUTHOR> Costa - 11/09/2018
     *
     * @param string $placeId
     *
     * @return []
     *
     */
    private function getPlaceDetails($placeId)
    {
        $parameters = '&fields=all&key=';

        $response = $this->getResponse(
            self::URL_GOOGLE_PLACE_DETAILS . $placeId . $parameters . $this->randKeysApi(),
            'GET'
        );
        return json_decode($response, true);
    }

    private function randKeysApi()
    {
        $api_keys = array(self::CHAVE_API_GOOGLE_1, self::CHAVE_API_GOOGLE_2);
        return $api_keys[array_rand($api_keys, 1)];
    }
}
