<?php

namespace App\Crawler\CialDnB;

use DateTime;

class CialDnBModel
{
    public $score;
    public $lastName;
    public $firstName;
    public $category;
    public $subCategory;
    public $title;
    public $dateOfBirth;
    public $entityType;
    public $listedOn;
    public $updatedOn;
    public $links = [];
    public $positions = [];
    public $associates = [];
    public $rawAddresses = [];
    public $alias = [];
    public $locations = [];
    public $dataSource = [];
    public $riskStatus = '';
    public $name = '';
    public $additionalInformation = '';

    public function __construct($screenResult)
    {
        $this->score = $screenResult->score;
        $this->riskStatus = $screenResult->riskStatus;

        foreach ($screenResult->auditLogElements as $auditLogElements) {
            $this->switchElements($auditLogElements);
        }

        foreach ($screenResult->auditLogLargeData as $auditLogLargeData) {
            $this->additionalInformation = $auditLogLargeData->infoLobString;
        }

        $this->adjustLocation();
        $this->adjustLinks();
        $this->adjustDate();
        $this->adjustEntityType();
        $this->adjustDataSource();
        $this->adjustAdditionalInformation();
        $this->adjustName();
        $this->adjustAssociate();
        $this->adjustArraysAtt();
    }

    /**
     * Seta os dados providos pela API no atributo correto
     *
     * @param stdObj $auditLogElements
     * @return void
     */
    private function switchElements($auditLogElements)
    {
        switch ($auditLogElements->labelDescription) {
            case 'Last Name':
                $this->lastName = $auditLogElements->logValue;
                break;
            case 'First Name':
                $this->firstName = $auditLogElements->logValue;
                break;
            case 'Category':
                $this->category = $auditLogElements->logValue;
                break;
            case 'Sub-Category':
                $this->subCategory = $auditLogElements->logValue;
                break;
            case 'Title':
                $this->title = $auditLogElements->logValue;
                break;
            case 'DOB':
                $this->dateOfBirth = $auditLogElements->logValue;
                break;
            case 'Entity Type':
                $this->entityType = $auditLogElements->logValue;
                break;
            case 'Listed On':
                $this->listedOn = $auditLogElements->logValue;
                break;
            case 'Updated On':
                $this->updatedOn = $auditLogElements->logValue;
                break;
            case 'Alias':
                $this->alias[] = $auditLogElements->logValue;
                break;
            case 'External Link':
                $this->links[] = $auditLogElements->logValue;
                break;
            case 'Position':
                $this->positions[] = $auditLogElements->logValue;
                break;
            case 'Associate':
                $this->associates[] = $auditLogElements->logValue;
                break;
            case 'RAW ADDRESS':
                $this->rawAddresses[] = $auditLogElements->logValue;
                break;
            case 'Locations person is active in':
                $this->locations[] = $auditLogElements->logValue;
                break;
            case 'Data Source':
                $this->dataSource[] = $auditLogElements->logValue;
                break;
        }
    }

    /**
     * Formata location, removendo itens desnecessários
     *
     * @return void
     */
    private function adjustLocation()
    {
        array_walk($this->locations, function (&$location) {
            $location = trim(str_replace('~,~ ', '', $location));
            $location = trim(
                preg_replace(
                    [
                        '/~(?=[^~\s])/isU', '/(?=[^\s])~/isU',
                        '/^,/isU',
                        '/,$/isU'
                    ],
                    '',
                    $location
                )
            );
        });
    }

    /**
     * Arruma os links para mostrar a url na formatação correto
     *
     * @return void
     */
    private function adjustLinks()
    {
        array_walk($this->links, function (&$link) {
            $link = urldecode($link);
        });
    }

    /**
     * Formata as datas para o padrão dd/mm/yyyy
     *
     * @return void
     */
    private function adjustDate()
    {
        $dateOfBirth = DateTime::createFromFormat('Y/m/d', $this->dateOfBirth);
        if ($dateOfBirth) {
            $this->dateOfBirth = $dateOfBirth->format('d/m/Y');
        }

        $listedOn = DateTime::createFromFormat('Y/m/d', $this->listedOn);
        if ($listedOn) {
            $this->listedOn = $listedOn->format('d/m/Y');
        }

        $updatedOn = DateTime::createFromFormat('Y/m/d', $this->updatedOn);
        if ($updatedOn) {
            $this->updatedOn = $updatedOn->format('d/m/Y');
        }
    }

    /**
     * Transforma de código para nome o entityType
     *
     * @return void
     */
    private function adjustEntityType()
    {
        switch (strtolower($this->entityType)) {
            case 'i':
                $this->entityType = 'Individual';
                break;
            case 'c':
                $this->entityType = 'Company';
                break;
        }
    }

    /**
     * Separa o data source num array
     *
     * @return void
     */
    private function adjustDataSource()
    {
        $dataSource = $this->dataSource;
        $this->dataSource = [];
        foreach ($dataSource as $source) {
            $sourceArr = explode(' ', $source);
            foreach ($sourceArr as $newSource) {
                $this->dataSource[] = $newSource;
            }
        }
    }

    /**
     * Separa o additional information, onde o que tiver depois da "flag"
     * "Snippet:" seja o título e o que vier depois de "Snippet Text:" seja
     * o conteúdo deste snippet
     *
     * @return void
     */
    private function adjustAdditionalInformation()
    {
        // Pula duas linhas antes "título" do snippet e abre a tag strong
        $this->additionalInformation = str_replace("Snippet: ", "\n\nSnippet:", $this->additionalInformation);
        // Fecha a tag strong (do título) e quebra uma linha para demonstrar o conteúdo
        $this->additionalInformation = str_replace("Snippet Text: ", "\nSnippet:", $this->additionalInformation);
        // Remove as tags snippet
        $this->additionalInformation = str_replace("Snippet:", "", $this->additionalInformation);
        $this->additionalInformation = str_replace(["\n\n\n", "\n\n  \n"], "\n\n", $this->additionalInformation);

        // Trim para remover as quebra de linha do começo do additionalInformation
        $this->additionalInformation = trim($this->additionalInformation);
        // Array de snippets
        $this->additionalInformation = explode("\n\n", $this->additionalInformation);
    }

    /**
     * Seta o atributo name (firstname + lastname)
     *
     * @return void
     */
    private function adjustName()
    {
        if (!empty($this->firstName)) {
            $this->name .= $this->firstName;
        }

        if (!empty($this->lastName)) {
            $this->name .= ' ' . $this->lastName;
        }

        $this->name = trim($this->name);
    }

    private function adjustAssociate()
    {
        usort($this->associates, function ($a, $b) {
            if (
                strpos($a, 'associate') !== false
                && strpos($b, 'associate') !== false
            ) {
                return 0;
            }

            if (
                strpos($a, 'associate') !== false
                && strpos($b, 'associate') === false
            ) {
                return 1;
            }

            return -1;
        });
    }

    private function adjustArraysAtt()
    {
        $this->links = array_unique($this->links);
        $this->positions = array_unique($this->positions);
        $this->associates = array_unique($this->associates);
        $this->rawAddresses = array_unique($this->rawAddresses);
        $this->alias = array_unique($this->alias);
        $this->locations = array_unique($this->locations);
        $this->dataSource = array_unique($this->dataSource);
    }
}
