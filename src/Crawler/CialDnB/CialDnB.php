<?php

namespace App\Crawler\CialDnB;

use Exception;
use GuzzleHttp\Client;
use App\Crawler\Spider;
use App\Manager\CialDnBmanager;

class CialDnB extends Spider
{
    /**
     * URL to do screen
     */

    private $Manager;

    /**
     * Fill the field firstName when API was called
     *
     * @var string
     */
    private $firstName = '';

    /**
     * Fill the field lastName when API was called
     *
     * @var string
     */
    private $lastName = '';

    /**
     * Start searching after the validation
     *
     * @return array
     */
    protected function start()
    {
        $name = $this->param['name'];

        $this->client = new Client();

        $this->Manager = new CialDnBmanager();

        $data = $this->Manager->doScreen($this->firstName, $this->lastName, $name);
        $formatData = $this->formatResponse($data);
        if (empty($formatData)) {
            throw new Exception('Não foi encontrado resultados', 2);
        }

        return $formatData;
    }

    /**
     * Validate all params wich must be received
     *
     * @return void
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['name'])) {
            throw new Exception('Parametro nome inválido');
        }

        $splitName = explode(" ", $this->param['name'], 2);
        $this->firstName = $splitName[0] ?? '';
        $this->lastName = $splitName[1] ?? '';
    }

    /**
     * Format result (transform into CialDnBModel format)
     *
     * @param stdObj $screenResponse
     * @return array
     */
    private function formatResponse($screenResponse)
    {
        $cialDnBArr = [];
        foreach ($screenResponse->auditLogDetailSet as $detail) {
            $cialDnBModel = new CialDnBModel($detail);
            if ($cialDnBModel->name) {
                $cialDnBArr[] = $cialDnBModel;
            }
        }

        usort($cialDnBArr, function ($a, $b) {
            if ($a->score == $b->score) {
                return 0;
            }
            return ($a->score > $b->score) ? -1 : 1;
        });

        return $cialDnBArr;
    }
}
