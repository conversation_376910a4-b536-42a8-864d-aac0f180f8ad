<?php

namespace App\Crawler\CommonQsa\Models;

use App\Crawler\CommonQsa\Models\QsaPartnersModel;
use Exception;

class QsaModel
{
    public $cnpj;
    public $razao_social;
    public $data_consulta;
    public $aSocio;
    public $pep;
    public $resumeParams;

    public function __set($name, $value)
    {
        if (property_exists($this, $name)) {
            return $this->$name = $value;
        }

        throw new Exception("Propriedade set '{$name}' não existe", 3);
    }

    public function __get($name)
    {
        if (property_exists($this, $name)) {
            return $this->$name;
        }
        throw new Exception("Propriedade get '{$name}'  não existe", 3);
    }

    public function setPartners(QsaPartnersModel $partners)
    {
        $this->aSocio[$partners->cpf_cnpj] = $partners;
    }
}
