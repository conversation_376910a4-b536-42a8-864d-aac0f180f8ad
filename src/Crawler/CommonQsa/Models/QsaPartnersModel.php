<?php

namespace App\Crawler\CommonQsa\Models;

use Exception;

class QsaPartnersModel
{
    public $cpf_cnpj;
    public $nome;
    public $entrada;
    public $qualificacao;
    public $participacao;
    public $situacao;
    public $matriz;
    public $pep;

    public function __set($name, $value)
    {
        if (property_exists($this, $name)) {
            return $this->$name = $value;
        }

        throw new Exception("Propriedade set '{$name}' não existe", 3);
    }

    public function __get($name)
    {
        if (property_exists($this, $name)) {
            return $this->$name;
        }
        throw new Exception("Propriedade get '{$name}'  não existe", 3);
    }
}
