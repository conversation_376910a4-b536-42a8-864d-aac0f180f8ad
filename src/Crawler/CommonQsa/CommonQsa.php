<?php

namespace App\Crawler\CommonQsa;

use App\Factory\MongoDB;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\DynamoManager;
use App\Manager\ElasticsearchManager;
use Carbon\Carbon;
use App\Crawler\CommonQsa\Models\QsaPartnersModel;
use App\Crawler\CommonQsa\Models\QsaModel;
use Exception;
use Tightenco\Collect\Support\Arr;

class CommonQsa extends Spider
{
    private const TYPE_PF = 'PF';
    private const TYPE_PJ = 'PJ';
    private const INDEX_MONGODB = 'document_name';

    private $typeSearch;
    private $document;
    private $dynamoManager;
    private $year;
    private $currentYear;
    private $onlyCount;
    private $paginationSize;
    private $resumeQueryParams;
    private $checkSociosSpine;

    /**
     *  Inicia pesquisa da fonte Common Participacao
     *
     *  <AUTHOR> Mesquita 18/09/2020
     *
     *  @version 1.0.0
     */
    protected function start()
    {
        $this->dynamoManager = new DynamoManager();


        if ($this->onlyCount) {
            return $this->typeSearch == self::TYPE_PF ? $this->countQsaPf() : $this->countQsaPj();
        }

        return $this->typeSearch == self::TYPE_PF ? $this->parseQsaPf() : $this->parseQsaPj();
    }

    /**
     *  Valida e seta os parametros
     *
     *  <AUTHOR> Mesquita 18/09/2020
     *
     *  @version 1.0.0
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCpfOuCnpj($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }

        $this->document = Document::removeMask($this->param['cpf_cnpj']);
        $this->typeSearch = Document::validarCpf($this->document) ? self::TYPE_PF : self::TYPE_PJ;
        $this->currentYear = Carbon::now()->format('Y');
        $this->year = !empty($this->param['ano']) ? $this->param['ano'] : Carbon::now()->format('Y');
        $this->onlyCount = !empty($this->param['only_count']) ? $this->param['only_count'] : false;
        $this->paginationSize = !empty($this->param['paginationSize']) ? $this->param['paginationSize'] : false;
        $this->resumeQueryParams = !empty($this->param['resumeQueryParams'])
        ? $this->param['resumeQueryParams'] : false;
        $this->checkSociosSpine = !empty($this->param['checkSociosSpine'])
        ? $this->param['checkSociosSpine'] : false;
    }

    /**
     *  Retorna  quantidade de participações do QSA pf
     *
     *  <AUTHOR> Mendes 11/01/2021
     *  <AUTHOR> Junior 14/01/2022 - Verificação de decremento
     *
     *  @version 1.0.0
     */
    public function countQsaPf()
    {
        $qsaPf = $this->getQsaPf($this->year);

        if (empty($qsaPf['items']) && $this->year == $this->currentYear) {
            $qsaPf = $this->getQsaPf($this->year - 1);
        }

        $qsa = array_filter($qsaPf['items'], function ($dado) {
            return is_numeric($dado['documento_socio']);
        });
        return [
            'total_participacoes' => count($qsa)
        ];
    }

    /**
     *  Retorna  quantidade de participações do QSA pj
     *
     *  <AUTHOR> Mendes 11/01/2021
     *  <AUTHOR> Junior 14/01/2022 - Verificação de decremento
     *
     *  @version 1.0.0
     */
    public function countQsaPj()
    {
        $qsaPj = $this->getQsaPj($this->year);

        if (empty($qsaPj['items']) && $this->year == $this->currentYear) {
            $qsaPj = $this->getQsaPj($this->year - 1);
        }

        $qsa = array_filter($qsaPj['items'], function ($dado) {
            return is_numeric($dado['documento_socio']);
        });

        return [
            'total_participacoes' => count($qsa)
        ];
    }

    /**
     *  Retorna os dados de QSA PJ parseados
     *
     *  <AUTHOR> Mesquita 18/09/2020
     *
     *  @version 1.0.0
     */
    private function parseQsaPj(): QsaModel
    {
        $qsaPj = $this->getQsaPj($this->year);
        /**
         * Pessoa do futuro, isso foi feito para que caso não venha nada no ano atual,
         *  buscar no ano anterior nos casos onde buscamos o ano atual. Normalmente no Dossie
        **/
        if (empty($qsaPj['items']) && $this->year == $this->currentYear) {
            $qsaPj = $this->getQsaPj($this->year - 1);
        }

        if (empty($qsaPj['items'])) {
            throw new Exception('Nenhum dado encontrado!', 2);
        }

        $spinePj = $this->getSpinePJ($this->document);

        $qsa = new QsaModel();
        $qsa->cnpj = $this->document;
        $qsa->razao_social = addslashes($spinePj['razao_social']);

        $searchPep = [];
        $searchCompanyName = [];
        $getLastYear = count($qsaPj['items']) <= 10;

        $DocList = [];

        if ($this->checkSociosSpine !== false) {
            foreach ($qsaPj['items'] as $document) {
                array_push($DocList, $document['documento_socio']);
            }
        }

        $docDetails = $this->getDocumentStatusAndType($DocList);

        foreach ($qsaPj['items'] as $item) {
            if ($getLastYear && ctype_digit($item['documento_socio'])) {
                $this->tryToCatchFromOldYears($item, self::TYPE_PJ);
            }

            if (empty($item['nome'])) {
                $resultName = $this->searchNames([$item['documento_socio']]);
                if ($resultName) {
                    $item['nome'] = $resultName[0]['_source']['nome'];
                } else {
                    $item['nome'] = '';
                }
            }

            $partner = new QsaPartnersModel();
            $partner->cpf_cnpj = $item['documento_socio'];
            $partner->nome = $item['nome'];
            $partner->entrada = !empty($item['data_entrada']) && strlen($item['data_entrada']) > 1
                ? $this->formatDate($item['data_entrada'])
                : null;
            $partner->qualificacao = $item['qualificacao'];
            $partner->participacao = !empty($item['participacao']) ? $this->formatStake($item['participacao']) : null;
            $partner->pep = 'Critério Não encontrado na listagem do portal da Transparência';

            $currentItemDetails = null;

            foreach ($docDetails as $docData) {
                if ($docData['_id'] == $item['documento_socio']) {
                    $currentItemDetails = $docData;
                }
            }

            if (isset($currentItemDetails)) {
                $partner->situacao = isset($currentItemDetails['_source']['situacao_cadastral']) ?
                $currentItemDetails['_source']['situacao_cadastral'] : $currentItemDetails['_source']['situacao'];

                $partner->matriz = isset($currentItemDetails['_source']['matriz']) ?
                    $currentItemDetails['_source']['matriz'] : '';
            }

            if (strtoupper($item['tipo_socio']) == "PF") {
                $searchPep[] = [
                    'cpf' => (Document::validarCpf($item['documento_socio'])) ? $item['documento_socio'] : '',
                    'nome' => $item['nome'],
                ];
            }

            if (Document::validarCnpj($item['documento_socio']) && empty($item['nome'])) {
                $searchCompanyName[] = $item['documento_socio'];
            }

            $qsa->setPartners($partner);

            $qsa->data_consulta = !empty($item['data_alt']) ? $this->formatDate($item['data_alt']) : null;

            $cpf_cnpj = (Document::validarCpfOuCnpj($item['documento_socio'])) ? $item['documento_socio'] : '';
            $qsa->aSocio[$item['documento_socio']]->cpf_cnpj = $cpf_cnpj;
        }

        if (!empty($searchPep)) {
            $resultPep = $this->searchPeps($searchPep);

            foreach ($qsa->aSocio as $k => $socio) {
                $cpfMask = preg_replace("/(\d{3})(\d{3})(\d{3})(\d{2})/", "***.$2.$3-**", $socio->cpf_cnpj);

                foreach ($resultPep as $pep) {
                    if (
                        $socio->cpf_cnpj != ""
                            &&
                        (($pep['cpf'] == $socio->cpf_cnpj || $pep['cpf'] == $cpfMask) ||
                        $pep['nome'] == $socio->nome)
                    ) {
                        $msg = 'Critério encontrado na listagem do portal da Transparência';
                        $qsa->aSocio[$k]->pep = $msg;
                    }
                }
            }
        }

        if (!empty($searchCompanyName)) {
            $resultCompanyNames = $this->searchCompanyNames($searchCompanyName);

            foreach ($resultCompanyNames as $name) {
                $qsa->aSocio[$name['_id']]->nome = $name['_source']['razao_social'];
            }
        }

        $qsa->aSocio = array_values($qsa->aSocio);
        $qsa->resumeParams = $qsaPj['resumeParams'];

        return $qsa;
    }

    private function parseQsaPf(): QsaModel
    {
        $qsaPf = $this->getQsaPf($this->year);
        /**
         * Criei essa lógica porque houve um caso onde o cliente criou dois lotes com um mesmo
         * critério em datas diferentes e no ultimo ano as participações estavam zeradas
         * ou vazias para os cnpjs que no ano anterior constavam participações.
         */


        // Estou comentando esse trecho pois está entregando qsa do ano anterior para o ano atual;
        // Caso ocorra o proble descrito acima, o ideal é corrigir esses casos diretamente no Dynamo;

        // $participationsDifference = $this->checkCurrentAndPreviousYearQsaParticipationsDifference(
        //     $this->getCurrentAndPreviousQsaYear()
        // );

        /**
         * Pessoa do futuro, isso foi feito para que caso não venha nada no ano atual,
         *  buscar no ano anterior nos casos onde buscamos o ano atual. Normalmente no Dossie.
         *
         **/

        if ((empty($qsaPf['items']) && $this->year == $this->currentYear)) {
            $qsaPf = $this->getQsaPf($this->year - 1);
        }

        if (empty($qsaPf['items'])) {
            throw new Exception('Nenhum dado encontrado!', 2);
        }

        $spinePf = $this->getSpinePF($this->document);

        $qsa = new QsaModel();
        $qsa->cnpj = $this->document;
        $qsa->razao_social = isset($spinePf['hits']['hits'][0]['_source'])
            ? $spinePf['hits']['hits'][0]['_source']['nome']
            : '';

        $dadosPep[] = [
            'cpf' => $this->document,
            'nome' => $qsa->razao_social
        ];

        $pep = $this->searchPeps($dadosPep);

        $qsa->pep = (!empty($pep[0]['cpf']) && !empty($pep[0]['nome']))
            ? 'Critério encontrado na listagem do portal da Transparência'
            : 'Critério Não encontrado na listagem do portal da Transparência';

        $searchCompanyName = [];
        $getLastYear = count($qsaPf['items']) <= 10;

        $DocList = [];

        if ($this->checkSociosSpine !== false) {
            foreach ($qsaPf['items'] as $document) {
                array_push($DocList, $document['cnpj']);
            }
        }

        $docDetails = $this->getDocumentStatusAndType($DocList);

        foreach ($qsaPf['items'] as $item) {
            if ($getLastYear && ctype_digit($item['documento_socio'])) {
                $this->tryToCatchFromOldYears($item, self::TYPE_PF);
            }

            $partner = new QsaPartnersModel();
            $partner->cpf_cnpj = $item['cnpj'];
            $partner->entrada = !empty($item['data_entrada']) && strlen($item['data_entrada']) > 1
                ? $this->formatDate($item['data_entrada'])
                : null;
            $partner->qualificacao = $item['qualificacao'];
            $partner->participacao = !empty($item['participacao']) ? $this->formatStake($item['participacao']) : null;

            $searchCompanyName[] = $item['cnpj'];

            $currentItemDetails = null;

            foreach ($docDetails as $docData) {
                if (isset($item['cnpj'])) {
                    if ($docData['_id'] == $item['cnpj']) {
                        $currentItemDetails = $docData;
                    }
                } else {
                    if ($docData['_id'] == $item['documento_socio']) {
                        $currentItemDetails = $docData;
                    }
                }
            }

            if (isset($currentItemDetails)) {
                $partner->situacao = $currentItemDetails['_source']['situacao_cadastral'] ?
                $currentItemDetails['_source']['situacao_cadastral'] : $currentItemDetails['_source']['situacao'];

                $partner->matriz = $currentItemDetails['_source']['matriz'];
            }

            $qsa->setPartners($partner);

            $qsa->data_consulta = !empty($item['data_alt']) ? $this->formatDate($item['data_alt']) : null;
        }

        if (!empty($searchCompanyName)) {
            $resultCompanyNames = $this->searchCompanyNames($searchCompanyName);

            foreach ($resultCompanyNames as $name) {
                $qsa->aSocio[$name['_id']]->nome = $name['_source']['razao_social'];
            }
        }

        $qsa->aSocio = array_values($qsa->aSocio);
        $qsa->resumeParams = $qsaPf['resumeParams'] ?? null;

        return $qsa;
    }

    private function searchNames($docs)
    {
        $response = [];
        foreach (array_chunk($docs, 1000) as $doc) {
            $params = [
                'index' => 'spine_pf',
                'type' => 'pf',
                'size' => count($doc),
                '_source' => 'nome',
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => [
                                'terms' => [
                                    '_id' => $doc
                                ]
                            ]
                        ]
                    ]
                ]
            ];

            $result = (new ElasticsearchManager())->search($params);

            $response = array_merge($response, $result['hits']['hits']);
        }

        return $response;
    }

    private function searchCompanyNames($docs)
    {
        $response = [];
        foreach (array_chunk($docs, 1000) as $doc) {
            $params = [
                'index' => 'spine_pj',
                'type' => 'pj',
                'size' => count($doc),
                '_source' => 'razao_social',
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => [
                                'terms' => [
                                    '_id' => $doc
                                ]
                            ]
                        ]
                    ]
                ]
            ];

            $result = (new ElasticsearchManager())->search($params);

            $response = array_merge($response, $result['hits']['hits']);
        }

        return $response;
    }

    private function searchPeps($dados)
    {
        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('transparencia_brasil')
            ->setCollection('pep');

        $query = [];
        $results = [];

        foreach ($dados as $dado) {
            $cpf = '';
            $cpfMask = '';

            if ($dado['cpf']) {
                $cpf = Document::removeMask($dado['cpf']);
                $cpfMask = preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '***.$2.$3-**', $cpf);
            }

            if (!empty($dado['cpf']) && !empty($dado['nome'])) {
                $query[] = [
                    '$search' => [
                        'index' => self::INDEX_MONGODB,
                        'compound' => [
                            'filter' => [
                                [
                                    'text' => [
                                        'query' => [$cpf,$cpfMask],
                                        'path' => 'cpf'
                                    ]
                                ]
                            ],
                            'must' => [
                                [
                                    'phrase' => [
                                        'query' => [$dado['nome']],
                                        'path' => 'nome'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ];
                $query[] = ['$limit' => 1];
                $query[] = ['$project' => ['cpf' => 1,'_id' => 0,'nome' => 1]];
            } elseif (empty($dado['nome']) && !empty($dado['cpf'])) {
                $query[] = [
                    '$search' => [
                        'index' => self::INDEX_MONGODB,
                        'compound' => [
                            'filter' => [
                                [
                                    'text' => [
                                        'query' => [$cpf,$cpfMask],
                                        'path' => 'cpf'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ];
                $query[] = ['$limit' => 1];
                $query[] = ['$project' => ['cpf' => 1,'_id' => 0,'nome' => 1]];
            } elseif (empty($dado['cpf']) && !empty($dado['nome'])) {
                $query[] = [
                    '$search' => [
                        'index' => self::INDEX_MONGODB,
                        'phrase' => [
                            'query' => [$dado['nome']],
                            'path' => 'nome'
                        ]
                    ]
                ];
                $query[] = ['$limit' => 1];
                $query[] = ['$project' => ['cpf' => 1,'_id' => 0,'nome' => 1]];
            }

            if ($query) {
                $result = json_decode(
                    json_encode(
                        $manager
                            ->query(
                                'aggregate',
                                $query,
                                null,
                                null,
                                true
                            )
                            ->toArray(),
                        true
                    ),
                    true
                );
            }
            $query = [];
            $results[] = [
              'nome' => $result[0]['nome'] ?? "",
              'cpf' => $result[0]['cpf'] ?? ""
            ];
        }

        return $results;
    }

    private function getDocumentStatusAndType($documents)
    {

        $results = [];

        $pfDocArray = [];
        foreach ($documents as $document) {
            if (strlen($document) < 12) {
                array_push($pfDocArray, $document);
            }
        };

        if (count($pfDocArray) > 0) {
            $params = [
                'index' => 'spine_pf',
                'type' => 'pf',
                'size' => count($pfDocArray),
                'body' => [
                    'query' => [
                        'terms' => [
                          'cpf' => $pfDocArray
                        ]
                    ]
                ]
            ];

            $response1 = (new ElasticsearchManager())->search($params);
            $results = array_merge($results, $response1['hits']['hits']);
        }


        $pjDocArray = [];
        foreach ($documents as $document) {
            if (strlen($document) > 12) {
                array_push($pjDocArray, $document);
            }
        };

        if (count($pjDocArray) > 0) {
            $responseTerms = [];
            //quebrando array em partes devido limitação de termos do elastic searsh
            $terms = array_chunk($pjDocArray, 1000);
            foreach ($terms as $term) {
                $params = [
                    'index' => 'spine_pj',
                    'type' => 'pj',
                    'size' => count($pjDocArray),
                    'body' => [
                        'query' => [
                            'terms' => [
                        'cnpj' => $term
                            ]
                        ]
                    ]
                ];
                $response2 = (new ElasticsearchManager())->search($params);
                $responseTerms = array_merge($responseTerms, $response2);
            }


            $results = array_merge($results, $responseTerms['hits']['hits']);
        }

        return $results;
    }

    /**
     *  Tenta pegar os dados data de entrada, qualificação e participação de anos anteriores
     *
     *  <AUTHOR> Mesquita 18/09/2020
     *
     *  @param array &$part
     *  @param int $yearEnd
     *
     *  @version 1.0.0
     */
    private function tryToCatchFromOldYears(&$part, $type, $yearEnd = 2013)
    {
        for ($i = $this->year - 1; $i >= $yearEnd; $i--) {
            if (!empty($part['data_entrada']) && !empty($part['qualificacao']) && !empty($part['participacao'])) {
                return;
            }

            try {
                $spineTmp = ($type === self::TYPE_PF)
                    ? $this->getQsaPf((string)$i)
                    : $this->getQsaPj((string)$i);

                foreach ($spineTmp['items'] as $tmp) {
                    if ($part['qualificacao'] !== $tmp['qualificacao']) {
                        continue;
                    }

                    if ($part['documento_socio'] == $tmp['documento_socio']) {
                        if (empty($part['data_entrada'])) {
                            $part['data_entrada'] = $part['data_entrada'] ?? $tmp['data_entrada'] ?? null;
                        }

                        if (empty($part['qualificacao'])) {
                            $part['qualificacao'] = $part['qualificacao'] ?? $tmp['qualificacao'] ?? null;
                        }

                        if (empty($participacao['participacao'])) {
                            $part['participacao'] = $part['participacao'] ?? $tmp['participacao'] ?? null;
                        }
                    }
                }
            } catch (\Exception $e) {
            }
        }
    }

    /**
     *  Formata data
     *
     *  <AUTHOR> Mesquita 18/09/2020
     *
     *  @param string $date
     *
     *  @version 1.0.0
     */
    private function formatDate(string $date): string
    {
        return Carbon::createFromFormat('Ymd', $date)->format('d/m/Y');
    }

    /**
     *  Formata Participação
     *
     *  <AUTHOR> Mesquita 18/09/2020
     *
     *  @param string $stake
     *
     *  @version 1.0.0
     */
    private function formatStake(string $stake): string|null
    {
        if (!empty($stake)) {
            $pattern = "/([0-9]+)(?:(\.0+[1-9]|\.[1-9])0+|\.0+(?![1-9]))/isu";

            return preg_replace($pattern, "$1$2", number_format(floatval($stake), 2, '.', ''));
        }

        return null;
    }

    /**
     *  Retorna SpinePjQsa por CNPJ
     *
     *  <AUTHOR> Mesquita 18/09/2020
     *
     *  @param string $ano
     *
     *  @version 1.0.0
     */
    private function getQsaPj(string $ano): array
    {
        $index = 'cnpj-ano-index';
        $conditions = 'cnpj = :cnpj and ano = :ano';
        $expression = [
            ':cnpj' => $this->document,
            ':ano' => $ano
        ];

        $result = $this->dynamoManager
            ->getQuery(
                'spine_pj_qsa',
                $conditions,
                $expression,
                $index,
                'ALL_ATTRIBUTES',
                true,
                $this->paginationSize,
                $this->resumeQueryParams
            );

        if (!$this->paginationSize) {
            $result = ['items' => $result];
        }

        return $result;
    }

    /**
     *  Retorna SpinePjQsa por documento sócio
     *
     *  <AUTHOR> Mesquita 18/09/2020
     *
     *  @param string $ano
     *
     *  @version 1.0.0
     */
    private function getQsaPf(string $ano): array
    {
        $index = 'documento_socio-ano-index';
        $conditions = 'documento_socio = :documento_socio and ano = :ano';
        $expression = [
            ':documento_socio' => $this->document,
            ':ano' => $ano
        ];

        $result = $this->dynamoManager
            ->getQuery(
                'spine_pj_qsa',
                $conditions,
                $expression,
                $index,
                'ALL_ATTRIBUTES',
                true,
                $this->paginationSize,
                $this->resumeQueryParams
            );

        if (!$this->paginationSize) {
            $result = ['items' => $result];
        }

        return $result;
    }

    /**
     *  Retorna SpinePJ por CNPJ
     *
     *  <AUTHOR> Mesquita 18/09/2020
     *
     *  @param string $cnpj
     *
     *  @version 1.0.0
     */
    private function getSpinePJ(string $cnpj): array
    {
        $res = $this->dynamoManager->getItem('spine_pj', [
            'cnpj' => (string)$cnpj
        ]);
        if (!$res) {
            return [];
        }
        return $res;
    }

    /**
     *  Retorna SpinePF por CPF
     *
     *  <AUTHOR> Mesquita 18/09/2020
     *
     *  @param string $cpf
     *
     *  @version 1.0.0
     */
    private function getSpinePF(string $cpf): array
    {
        return (new ElasticsearchManager())->search($this->getQueryParams($cpf));
    }

    /**
     * Retorna os parametros para a busca no Elastic Search
     *
     * <AUTHOR> Guilherme
     * @param string $cpf
     * @return array
     */
    private function getQueryParams(string $cpf): array
    {
        return [
            'index' => 'spine_pf',
            'type' => 'pf',
            'body' => [
                'query' => [
                    'match' => [
                        'cpf' => $cpf
                    ]
                ]
            ]
        ];
    }

    /***
     * Função criada para comparar as participações do ano atual com o ano anterior
     * e retornar a contagem.
     *
     * <AUTHOR> Medeiros 05/04/2022
     *
     * @param $currentPreviousParticipationsArray
     *
     * @return int
     */
    private function checkCurrentAndPreviousYearQsaParticipationsDifference($currentPreviousParticipationsArray)
    {
        $currentYearParticipations = $this->parseDocumentsAndParticipations(
            $currentPreviousParticipationsArray["currentYear"]
        );

        $previousYearParticipations = $this->parseDocumentsAndParticipations(
            $currentPreviousParticipationsArray["previousYear"]
        );

        $diferenceCount = 0;

        foreach ($currentYearParticipations as $key => $value) {
            if (key_exists($key, $previousYearParticipations)) {
                if ($previousYearParticipations[$key] != $value) {
                    $diferenceCount++;
                }
            }
        }
        return $diferenceCount;
    }

    /**
     *
     * Função criada para separar cnpjs e participações em um array ["cnpj" => "participação"]
     *
     * <AUTHOR> Medeiros 05/04/2022
     *
     * @param $qsaArray
     *
     * @return array
     */
    private function parseDocumentsAndParticipations($qsaArray)
    {
        $documentsAndParticipations = [];

        foreach ($qsaArray as $participation) {
            $documentsAndParticipations[$participation['cnpj']] = $participation['participacao'];
        }
        return $documentsAndParticipations;
    }

    /**
     * Função criada para recuperar QSA do ano atual e anterior.
     *
     * <AUTHOR> Medeiros 05/04/2022
     *
     * @return array
     */
    private function getCurrentAndPreviousQsaYear()
    {
        return [
            "currentYear" => $this->getQsaPf($this->year)['items'],
            "previousYear" => $this->getQsaPf($this->year - 1)['items']
        ];
    }
}
