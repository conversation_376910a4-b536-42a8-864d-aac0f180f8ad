<?php

namespace App\Crawler\BoaVistaDefineRisco;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\BoaVista\BoaVistaManager;
use Exception;

class BoaVistaDefineRisco extends Spider
{
    private $cnpj;

    public function start()
    {
        $sources = [
            'decisao',
            'limite_credito',
            'identificacao_basico',
            'localizacao_basico',
            'faturamento_presumido',
            'empresas_situada_no_mesmo_endereco',
            'consultas',
            'ultimas_consultas',
            'anvisa',
            'historico_cheque_informados',
            'historico_conta_correntes',
            'endereco_telefones_agencia_bancaria',
            'pendencias_restricoes',
            'cheques_sem_fundo',
            'cheques_sustados',
            'cheques_devolvidos_informados_usuario',
            'tempo_relacionamento_fornecedores',
            'protestos',
            'demais_informacoes',
            'credito_obtido',
            'compromissos',
            'pagamento_a_vista',
            'pagamento_pontual',
            'pagamento_atrasado',
            'comprometimento_futuro',
            'segmento_empresarial',
            'factor'
        ];

        return (new BoaVistaManager())->searchDefineRisco($this->cnpj, $sources, false, 'defineRisco');
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['cnpj'])) {
            throw new Exception("Parâmetro CNPJ é obrigatório", 1);
        }

        if (!Document::validarCnpj($this->param['cnpj'])) {
            throw new Exception('Parâmetro Inválido', 1);
        }

        $this->cnpj = Document::removeMask($this->param['cnpj']);
    }
}
