<?php

namespace App\Crawler\DowJonesApi;

use Exception;
use DOMDocument;
use ReflectionClass;
use App\Crawler\Spider;
use App\Helper\Document;

class DowJonesApi extends Spider
{
    /**
     * URL base
     *
     * @var string
     */
    private $base_url = 'https://djrc.api.dowjones.com';

    /**
     * API Version
     *
     * @var string
     */
    private $version = 'v1';


    private $xmlList = [];

    private $indice = 0;

    /**
     * Quantidade de tentativas
     *
     * @var integet
     */
    private $tries = 0;

    private $username = '';
    private $password = '';

    /**
     *  Just to debug
     */
    private $refClass = null;
    protected $removeNameSpaceFromDebug = true;

    public $debug = false;

    protected function start(): array
    {
        $this->username = $this->auth['user'];
        $this->password = $this->auth['password'];
        $this->debug();

        $this->setCurlOptLogin();

        $resultString = $this->search();

        return [$resultString];
    }

    /**
     *  Set the cURL authentication
     */
    private function setCurlOptLogin(): void
    {
        $this->debug();

        $loginArr = [
            CURLOPT_USERPWD => $this->username . ":" . $this->password,
        ];

        $this->setCurlOpt($loginArr);
    }

    private function search(): string
    {
        $this->debug();

        $result = $this->getFirstCharge();

        $this->xmlList[] = $result;

        $data = $this->getDataFromXml($result);

        if (
            empty($data) ||
            (empty($data['body']) && empty($data['body']['match']))
        ) {
            throw new Exception(
                'Nenhum registro encontrado',
                2
            );
            //echo "Nenhum registro encontrado\n";
            //return "{}";
        }

        if (empty($data['body']['match'][0])) {
            $match = $data['body']['match'];
            $data['body']['match'] = array($match);
        }

        foreach ($data['body']['match'] as $k => $item) {
            $url = $this->getUrl('data/records/' . $item['@attributes']['peid']);

            $result = $this->sendRequest($url);

            $this->xmlList[] = $result;

            $data['body']['match'][$k]['informations'] = $this->getDataFromXml($result);
        }

        return $this->applySchema($data);
    }

    /**
     * Aplica o schema do elasticsearch nos dados
     *
     * @param array $data Dados da api
     *
     * @return string
     */
    private function applySchema(array $data): string
    {
        $this->debug();

        $schema = [
            'took' => 0,
            'timeout' => false,
            '_shards' => [
                'total' => 5,
                'successful' => 5,
                'failed' => 0
            ],
            'hits' => [
                'total' => count($data['body']['match']),
                'max_score' => 10,
                'hits' => [],
            ],
        ];

        foreach ($data['body']['match'] as $k => $item) {
            $this->indice = $k;
            $hit = [
                '_score' => 10,
                '_source' => [],
            ];
            $hit['_index'] = 'dowjones_watchlist';
            $hit['_type'] = strtolower($item["@attributes"]['record-type']);
            $hit['_id'] = $hit['_type'] . '-' . $item["@attributes"]['peid'];

            if (!empty($item['informations']['person'])) {
                $hit['_source'] = $this->getPersonSource($item);
            } elseif ($item['informations']['entity']) {
                $hit['_source'] = $this->getEntitySource($item);
            } else {
                throw new Exception('');
            }
            $schema['hits']['hits'][] = $hit;
        }

        return json_encode($schema);
    }

    /**
     * Extrai os dados de pessoa fisica
     *
     * @param array $item Item do resultado
     *
     * @return array
     */
    private function getPersonSource(array $item): array
    {
        $this->debug();

        $type = 'person';
        $source = $this->getCommonDataSource($item, $type);

        $source['deceased'] = $item['informations']['person']['deceased'] != 'false' ?  'Yes' : 'No';
        $source['name_details'] = $this->getNameDetails($item, $type);

        $source['descriptions'] = $this->getDescriptions($item, $type);

        $source['role_detail'] = $this->getRoleDetails();
        $source['date_details'] = $this->getDateDetails();


        $source['birth_place'] = $this->getBirthPlace();

        $source['sanctions_reference'] = $this->getSanctionsReferences();

        $source['country_details'] = $this->getCountryDetails($item, $type);

        $source['sources'] = $this->getSources();

        $source['profile_notes'] = $this->getProfileNotes($item, $type);

        $source['images'] = $this->getImages($item, $type);

        $source['simple_name'][] = implode(
            ' ',
            array_reverse(
                explode(
                    ', ',
                    $item['payload']['primary-name']
                )
            )
        );

        $source['id_numbers'] = $this->getIdNumbers();

        $source['simple_id'] = $this->getSimpleId();

        $source['associates'] = $this->getAssociates();

        return $source;
    }

    private function getEntitySource(array $item): array
    {
        $this->debug();

        $type = 'entity';
        $source = $this->getCommonDataSource($item, $type);
        $source['name_details'] = $this->getNameDetails($item, $type);

        $source['company_details'] = $this->getCompanyDetails($item, $type);

        $source['descriptions'] = $this->getDescriptions($item, $type);
        $source['date_details'] = $this->getDateDetails();
        $source['sanctions_reference'] = $this->getSanctionsReferences();
        $source['country_details'] = $this->getCountryDetails($item, $type);
        $source['sources'] = $this->getSources();

        $source['adverse_media'] = $this->getAdverseMedia();

        $source['profile_notes'] = $this->getProfileNotes($item, $type);
        $source['id_numbers'] = $this->getIdNumbers();
        $source['associates'] = $this->getAssociates();
        $source['simple_id'] = $this->getSimpleId();

        return $source;
    }

    /**
     * Retorna os detalhes da companhia
     *
     * @return array
     */
    private function getCompanyDetails(array $item, string $type): array
    {
        $this->debug();

        if (!isset($item['informations'][$type]['address-details'])) {
            return [];
        }

        $companyDetails = [];
        foreach ($item['informations'][$type]['address-details'] as $address) {
            $addressDetail = array();

            $addressLine = $address['address-line'] ?? '';
            $city = $address['city'] ?? '';
            $region = $address['region'] ?? '';
            $zipCode = $address['zip-code'] ?? '';

            $addressDetail['address_line'] = $addressLine;
            $addressDetail['address_city'] = $city . ';' . $region . ';' . $zipCode;
            $addressDetail['address_country'] = $address['country']['djii-region-code'] ?? '';
            $addressDetail['url'] = $address['address-url'] ?? '';

            $companyDetails[] = $addressDetail;
        }
        return $companyDetails;
    }

    /**
     * Retorna os dados de adverse media
     *
     * @return array
     */
    private function getAdverseMedia(): array
    {
        $this->debug();

        $dom = new DOMDocument();
        $dom->loadXML($this->xmlList[$this->indice + 1]);
        $adverseMedia = [];
        foreach ($dom->getElementsByTagName('adverse-media-source') as $media) {
            if ($media->hasAttributes()) {
                $attributes = array();
                foreach ($media->attributes as $name => $value) {
                    $attributes[$name] = $value->value;
                }
                $attributes['text'] = $media->nodeValue;

                $adverseMedia[] = $this->changeKeys($attributes);
            }
        }
        return $adverseMedia;
    }

    /**
     * Retorna os perfis associados
     *
     * @return array
     */
    private function getAssociates(): array
    {
        $this->debug();

        $dom = new DOMDocument();
        $dom->loadXML($this->xmlList[$this->indice + 1]);
        $associates = [];
        foreach ($dom->getElementsByTagName('associate') as $associate) {
            $item = array();
            if ($associate->hasAttribute('peid')) {
                $item['id'] = $associate->getAttribute('peid');
            }
            if ($associate->hasAttribute('relationship')) {
                $item['relationship'] = $associate->getAttribute('relationship');
            }
            if ($associate->hasAttribute('description1')) {
                $item['description1'] = $associate->getAttribute('description1');
            }
            if ($associate->hasAttribute('description2')) {
                $item['description2'] = $associate->getAttribute('description2');
            }
            if ($associate->hasAttribute('ex')) {
                $ex = $associate->getAttribute('ex');
                $item['ex'] = $ex === 'false' ? 'No' : 'Yes';
            }
            $associates[] = $item;
        }

        return $associates;
    }

    /**
     * Retorna o simple id
     *
     * @return array
     */
    private function getSimpleId(): array
    {
        $this->debug();

        $dom = new DOMDocument();
        $dom->loadXML($this->xmlList[$this->indice + 1]);

        $identifications = [];
        foreach ($dom->getElementsByTagName('identification') as $identification) {
            $type = $identification->getAttribute('identification-type');
            $identifications[] = $type . ' ' . $identification->nodeValue;
        }
        return $identifications;
    }

    /**
     * Retorna as indentificações
     *
     * @return array
     */
    private function getIdNumbers(): array
    {
        $this->debug();

        $dom = new DOMDocument();
        $dom->loadXML($this->xmlList[$this->indice + 1]);

        $identifications = [];
        foreach ($dom->getElementsByTagName('identification') as $identification) {
            $identifications[] = array(
                'id_value' => $identification->nodeValue,
                'id_type' => $identification->getAttribute('identification-type')
            );
        }
        return $identifications;
    }

    /**
     * Retorna as imagens
     *
     * @param array $item Item do resultado
     * @param array $type Tipo de pessoa person|entity
     *
     * @return array
     */
    private function getImages($item, $type)
    {
        $this->debug();

        $keys = ['informations', $type, 'image-details', 'image-url'];

        if ($this->isValid($item, $keys)) {
            if (!empty($item['informations'][$type]['image-details']['image-url'][0])) {
                return $item['informations'][$type]['image-details']['image-url'];
            }

            return $item['informations'][$type]['image-details']['image-url'];
        }
    }

    /**
     * Retorna os profile notes
     *
     * @param array $item Item do resultado
     * @param array $type Tipo de pessoa person|entity
     *
     * @return array
     */
    private function getProfileNotes($item, $type)
    {
        $this->debug();

        if (
            $this->isValid(
                $item,
                array('informations', $type, 'watchlist-content', 'profile-notes')
            )
        ) {
            return $item['informations'][$type]['watchlist-content']['profile-notes'];
        }

        return null;
    }

    /**
     * Retorna as fontes
     *
     * @return array
     */
    private function getSources(): array
    {
        $this->debug();

        $dom = new DOMDocument();
        $dom->loadXML($this->xmlList[$this->indice + 1]);
        $sources = [];
        foreach ($dom->getElementsByTagName('source') as $source) {
            $sources[] = $source->nodeValue;
        }
        return $sources;
    }

    /**
     * Retorna os detalhes das localizações
     *
     * @param array $item Item do resultado
     * @param array $type Tipo de pessoa person|entity
     *
     * @return array
     */
    private function getCountryDetails(array $item, string $type): array
    {
        $this->debug();

        if ($this->isValid($item, ['payload','countries','country'])) {
            $countries = [];
            foreach ($item['payload']['countries']['country'] as $country) {
                $countries[] = array(
                    'country_type' => $country['country-type'] ?? '',
                    'country' => $country['country-code'] ?? ''
                );
            }
            return $countries;
        }
    }

    /**
     * Retorna as sanctions
     *
     * @return array
     */
    private function getSanctionsReferences(): array
    {
        $this->debug();

        $dom = new DOMDocument();
        $dom->loadXML($this->xmlList[$this->indice + 1]);

        $attributesArray = [
            'since-year', 'since-month', 'since-day',
            'to-year', 'to-month', 'to-day',
            'list-provider-name', 'list-provider-code', 'description2',
            'status'
        ];

        $sanctions = [];

        foreach ($dom->getElementsByTagName('sanctions-reference') as $sanction) {
            $item = array();
            $item['text'] = $sanction->nodeValue;

            foreach ($attributesArray as $attr) {
                if ($sanction->hasAttribute($attr)) {
                    // trocar hifens por underlines
                    $underscored = preg_replace('/\-/', '_', $attr);
                    $underscored = preg_replace('/^list_/', '', $underscored);
                    $item[$underscored] = $sanction->getAttribute($attr);
                }
            }

            $sanctions[]  = $item;
        }

        return $sanctions;
    }

    /**
     * Retorna o local de nascimento
     *
     * @return array
     */
    private function getBirthPlace(): array
    {
        $this->debug();

        $dom = new DOMDocument();
        $dom->loadXML($this->xmlList[$this->indice + 1]);
        foreach ($dom->getElementsByTagName('birth-place') as $birthPlace) {
            foreach ($birthPlace->childNodes as $tag) {
                ${$tag->nodeName} = $tag->nodeValue;
            }
        }
        return array($region . ',' . $country);
    }

    /**
     * Retorna as datas
     *
     * @param array $item Item do resultado
     * @param array $type Tipo de pessoa person|entity
     *
     * @return array
     */
    private function getDateDetails(): array
    {
        $this->debug();

        $dom = new DOMDocument();
        $dom->loadXML($this->xmlList[$this->indice + 1]);

        $attributesArray = ['date-type', 'year', 'month', 'day'];

        $dateDatails = array();
        foreach ($dom->getElementsByTagName('date') as $date) {
            $dateDetail = array();

            foreach ($attributesArray as $attr) {
                $undercored = preg_replace('/\-/', '_', $attr);
                if ($date->hasAttribute($attr)) {
                    $dateDetail[$undercored] = $date->getAttribute($attr);
                }
            }

            $dateDatails[] = $dateDetail;
        }
        return $dateDatails;
    }

    /**
     * Retorna as roles``
     *
     * @return array
     */
    private function getRoleDetails(): array
    {
        $this->debug();

        $dom = new DOMDocument();
        $dom->loadXML($this->xmlList[$this->indice + 1]);

        $roleDetail = array();
        foreach ($dom->getElementsByTagName('role') as $role) {
            $detail = array();
            if ($role->hasAttribute('role-type')) {
                $detail['role_type'] = $role->getAttribute('role-type');
            }
            $occupation = $role->firstChild;
            if (!empty($occupation)) {
                $detail['text'] = $occupation->nodeValue;
                if ($occupation->hasAttribute('occupation-category')) {
                    $detail['category'] = $occupation->getAttribute('occupation-category');
                }
                if ($occupation->hasAttribute('since-year')) {
                    $detail['since_year'] = $occupation->getAttribute('since-year');
                }
                if ($occupation->hasAttribute('since-month')) {
                    $detail['since_month'] = $occupation->getAttribute('since-month');
                }
                if ($occupation->hasAttribute('since-day')) {
                    $detail['since_day'] = $occupation->getAttribute('since-day');
                }
                if ($occupation->hasAttribute('to-year')) {
                    $detail['to_year'] = $occupation->getAttribute('to-year');
                }
                if ($occupation->hasAttribute('to-month')) {
                    $detail['to_month'] = $occupation->getAttribute('to-month');
                }
                if ($occupation->hasAttribute('to-day')) {
                    $detail['to_day'] = $occupation->getAttribute('to-day');
                }
            }
            $roleDetail[] = $detail;
        }
        return $roleDetail;
    }

    /**
     * Retorna as descrições
     *
     * @param array $item Item do resultado
     * @param array $type Tipo de pessoa person|entity
     *
     * @return array
     */
    private function getDescriptions(array $item, string $type): array
    {
        $this->debug();

        $descriptions = [];

        if (isset($item['informations'][$type]['watchlist-content'])) {
            foreach ($item['informations'][$type]['watchlist-content']['descriptions'] as $description) {
                // removendo os attributos do xml
                // setando os valores para o nó parent

                if (isset($description['@attributes'])) {
                    $description = $this->removeAttributesFromArray($description);
                    $description = array($description);
                } else {
                    foreach ($description as $key => $desc) {
                        $description[$key] = $this->removeAttributesFromArray($desc);
                    }
                }

                $descriptions = array_merge($descriptions, $description);
            }
        } elseif ($this->debug) {
            echo "\nNenhuma descrição foi encontrada para este resultado\n";
        }

        return $descriptions;
    }

    /**
     * Retorna os nomes
     *
     * @param array $item Item do resultado
     * @param array $type Tipo de pessoa person|entity
     *
     * @return array
     */
    private function getNameDetails(array $item, string $type): array
    {
        $this->debug();

        $nameDetails = array();
        foreach ($item['informations'][$type][$type . '-name-details'][$type . '-names'] as $name) {
            if (!empty($name[$type . '-names'])) {
                foreach ($name[$type . '-names'] as $groupName) {
                    $nameDetails[] = $this->changeKeys($groupName[$type . '-name-value']);
                }
                continue;
            }

            if (!isset($name[$type . '-name-value'])) {
                $name = array(
                    $type . '-name-value' => $name
                );
            }

            if (isset($name[$type . '-name-value'])) {
                $result = $this->changeKeys($name[$type . '-name-value']);
            } else {
                /*
                 * Em determinados casos como na consulta por Gafisa as chaves são retornadas em outro padrão
                 * Neste caso sem -name-value, com isso o valor é passado direto para o changeKeys
                 */
                $result = $this->changeKeys($name);
            }

            $nameDetails[] = $result;
        }
        return $nameDetails;
    }

    /**
     * Retorna os dados comuns a todos tipos
     *
     * @param array $item Item do resultado
     * @param array $type Tipo de pessoa person|entity
     *
     * @return array
     */
    private function getCommonDataSource(array $item, string $type): array
    {
        $this->debug();

        //$type =  'person';

        $source = array();
        $simpleData = [
            'id' => ['informations', $type, '@attributes','peid'],
            'date' => ['informations', $type, '@attributes', 'date'],
            'gender' => ['informations', $type, 'gender'],
            'active_status' => ['informations', $type, 'watchlist-content', 'active-status']
        ];

        foreach ($simpleData as $k => $v) {
            $r = $this->getSimpleData($item, $v);

            if (!empty($r)) {
                $source[$k] = $r;
            }
        }
        return $source;
    }

    /**
     * Retorna os dados em formato simples sem a necessidade de tratamento
     *
     * @param array $item      Item do resultado
     * @param array $structure Estrutura do array
     *
     * @return array|string
     */
    private function getSimpleData($item, $structure)
    {
        $this->debug();

        if ($this->isValid($item, $structure)) {
            $data = $item;

            foreach ($structure as $indice) {
                $data = $data[$indice];
            }

            return $data;
        }

        return null;
    }

    /**
     * Verifica se os indices de um array exitem
     *
     * @param array $item  Item do resultado
     * @param array $items Lista de indices que devem ser verificados
     *
     * @return array
     */
    private function isValid($item, array $items): bool
    {
        $this->debug();

        if (!empty($item) && empty($items)) {
            return true;
        }

        $indice = array_shift($items);
        return isset($item[$indice]) && $this->isValid($item[$indice], $items);
    }

    private function getUrl(string $pathname): string
    {
        $this->debug();

        return $this->base_url . '/' . $this->version . '/' . ltrim($pathname, '/');
    }

    private function getFirstCharge(): string
    {
        $this->debug();

        $nome = utf8_decode($this->criterion);

        $params = [
            'name'  => $nome,
            'search-type' => 'precise'
        ];

        $url = $this->getUrl('search/name');

        return $this->sendRequest($url, $params);
    }

    /**
     * Troca o hifen pelo underscore nas chaves do array
     *
     * @param array $data Array com as chaves que devem ser trocadas
     *
     * @return array
     */
    private function changeKeys(?array $data): ?array
    {
        $this->debug();

        if (!is_array($data)) {
            return null;
        }

        $data = $this->removeAttributesFromArray($data);

        $returnData = array();
        foreach ($data as $key => $value) {
            $newKey = preg_replace('/\-/', '_', $key);
            $returnData[$newKey] = $value;
        }

        return $returnData;
    }

    /**
     *  Remove a key @attributes que vem do xml
     */
    public function removeAttributesFromArray(array $data): array
    {
        $this->debug();

        $newArray = [];

        foreach ($data as $key => $value) {
            if ($key == '@attributes') {
                if (is_array($value)) {
                    foreach ($value as $k => $v) {
                        $newArray[$k] = $v;
                    }
                } else {
                    $newArray[] = $value;
                }
            } else {
                $newArray[$key] = $value;
            }
        }

        return $newArray;
    }

    private function getDataFromXml(string $result): array
    {
        if (preg_match('/Not authorized/', $result)) {
            throw new Exception('Não autorizado', 6);
        }
        try {
            $xml = simplexml_load_string($result);
            $json = json_encode($xml);
            return json_decode($json, true);
        } catch (Exception $e) {
            throw new Exception('Invalid Xml', 1);
        }
        /*
        try {
            if (!empty($result)) {
                file_put_contents(
                    $f = $this->getDefaultPath().'/'.time().'.xml', $result
                );
                $xmlData = new Zend_Config_Xml($f);
                unlink($f);
                return $xmlData->toArray();
            }
            throw new Exception();
        } catch (Exception $e) {
            throw new Exception('Invalid Xml', 1);
        }
        */
    }

    /**
     *  Send request to the Dow Jones API
     *  befora start to call this method, the code must need to pass in setCurlOptLogin method
     */
    private function sendRequest(string $url, array $params = []): string
    {
        $this->debug();

        try {
            $query = http_build_query($params);

            $response =  $this->getResponse($url . '?' . $query);
        } catch (Exception $e) {
            if (++$this->tries > 10) {
                throw new Exception('Fonte indisponível no momento', 3);
            }

            sleep(2);
            return $this->sendRequest($url);
        }

        $this->validateDataFromSendRequest($response);

        return $response;
    }

    private function validateDataFromSendRequest(string $data): void
    {
        $this->debug();

        if (preg_match('/131000 The login details entered are incorrect/m', $data)) {
            throw new Exception('Usuário ou senha incorretos', 6);
        }

        if (preg_match('/131029 We could not process your request. Check your entry for length, duplicates/m', $data)) {
            throw new Exception('Usuário ou senha inválidos!', 6);
        }

        if (preg_match('/access denied/m', $data)) {
            throw new Exception('Credenciais com acesso negado na Dow Jones. verifique seu usuário e senha', 6);
        }
    }

    protected function validateAndSetCrawlerAttributes(): void
    {
        $this->debug();

        // validation
        if (empty($this->param['nome'])) {
            throw new Exception('Parâmetro Nome obrigatório', 1);
        }

        $this->criterion = utf8_decode($this->param['nome']);
    }



    public function debug(): void
    {
        return;
        if (property_exists($this, 'debug') && $this->debug) {
            if (!$refClass) {
                $refClass = new ReflectionClass($this);
            }

            $trace = debug_backtrace()[1];
            $class = $trace['class'];

            $removePropName = 'removeNameSpaceFromDebug';

            if (property_exists($this, $removePropName) && $this->{$removePropName}) {
                $class = $refClass->getShortName();
            }

            $fn = $trace['function'];
            print PHP_EOL . $class . '::' . $fn . PHP_EOL;
        }
    }
}
