<?php

namespace App\Crawler\CertidaoCeatTrtAMRR;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Helper\Util;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

class CertidaoCeatTrtAMRR extends Spider
{
    private $cpfCnpj = '';
    private $face = '';
    private $tipoPessoa = '';
    private $tipoBusca = '';
    private $form = '';
    private $data = [];
    private $acao = [];
    private $acoes = [];
    private const BASE_URL = "https://certtrab.trt11.jus.br/";
    private const URL_REQUEST = "ceat/certidaoTrabalhista/emiteCertidao.xhtml";
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const CERTIDAO_S3_PATH = 'captura/certidao_ceat_trt11_am_rr/';

    public function start()
    {
        $this->setProxy();
        $this->setCurlOpt([
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_COOKIESESSION => true,
        ]);

        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::CERTIDAO_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;

        $retry = 6;
        while ($retry >= 0) {
            try {
                $html = $this->makeRequest();
                $clearedHtml = Str::cleanString($this->makeRequest());
                $this->getFormJid($clearedHtml);
                $pdf = $this->returnPdf($html);
                $text = $this->savePdfAndReturnText($pdf);
                $this->parseData($text);
                $this->data['pdf'] = $this->pdf;

                return $this->data;
            } catch (Exception $e) {
                if ($retry == 0) {
                    throw new Exception($e->getMessage(), 3);
                }

                $retry--;
            }
        }
    }

    private function makeRequest()
    {
        print __METHOD__ . "\n";
        return $this->getResponse(self::BASE_URL . self::URL_REQUEST);
    }

    /**
     * Resolve o captcha e seta a resposta
     * <AUTHOR> Pereira
     * @return string
     */
    private function resolveRecaptcha($html)
    {
        print __METHOD__ . "\n";
        preg_match('/sitekey:"([\s\S]*?)"/', $html, $siteKey);
        $this->token = $this->solveReCaptcha($siteKey[1], self::BASE_URL . self::URL_REQUEST);
        return $this->token;
    }

    private function returnPdf($html)
    {
        print __METHOD__ . "\n";
        $token = $this->resolveRecaptcha($html);
        preg_match('/ViewState:0"\svalue="([\s\S]*?)"/', $html, $viewState);

        if (strtoupper($this->tipoPessoa) == "CNPJ") {
            $this->makeRequestTransition($viewState);
        }

        $this->makeRequestName($viewState);
        $list = $this->makeRequestVerificar($token, $viewState);
        return $this->makeRequestCertificate($viewState, $list);
    }

    private function getFormJid($html)
    {
        print __METHOD__ . "\n";

        preg_match('/<form\s+id=\"(j_[\d\w]+)\"/is', $html, $output);

        if (count($output) > 0) {
            $this->form = $output[1];
        } else {
            throw new Exception("Não foi possível recuperar os dados do formulário", 3);
        }
    }

    private function makeRequestTransition($viewState)
    {
        print __METHOD__ . "\n";
        $params = [
            'javax.faces.partial.ajax' => 'true',
            'javax.faces.source' => $this->tipoBusca,
            'javax.faces.partial.execute' => 'tipoBusca',
            'javax.faces.partial.render' => $this->form,
            'javax.faces.behavior.event' => 'change',
            'javax.faces.partial.event' => 'change',
            $this->form => $this->form,
            'tipoBusca' => $this->tipoPessoa,
            'idCpf' => '',
            'g-recaptcha-response' => '',
            'javax.faces.ViewState' => $viewState[1],
        ];
        $this->getResponse(self::BASE_URL . self::URL_REQUEST, 'POST', $params);
    }

    private function makeRequestName($viewState)
    {
        print __METHOD__ . "\n";
        $params = [
            'javax.faces.partial.ajax' => 'true',
            'javax.faces.source' => $this->face,
            'javax.faces.partial.execute' => $this->face,
            'javax.faces.partial.render' => 'cpfCNPJNome idConsta idBotoes messages',
            'javax.faces.behavior.event' => 'blur',
            'javax.faces.partial.event' => 'blur',
            $this->form => $this->form,
            'tipoBusca' => $this->tipoPessoa,
            $this->face => $this->cpfCnpj,
            'g-recaptcha-response' => '',
            'javax.faces.ViewState' => $viewState[1],
        ];

        $this->getResponse(self::BASE_URL . self::URL_REQUEST, 'POST', $params);
    }

    private function makeRequestVerificar($captcha, $viewState)
    {
        print __METHOD__ . "\n";
        $params = [
            'javax.faces.partial.ajax' => 'true',
            'javax.faces.source' => 'confirmar',
            'javax.faces.partial.execute' => '@all',
            'javax.faces.partial.render' => 'tabelaEmissao idConsta idBotoes ajax idResultadoNegativo',
            'confirmar' => 'confirmar',
            $this->form => $this->form,
            'tipoBusca' => $this->tipoPessoa,
            $this->face => $this->cpfCnpj,
            'g-recaptcha-response' => $captcha,
            'javax.faces.ViewState' => $viewState[1],
        ];

        $list = $this->getResponse(self::BASE_URL . self::URL_REQUEST, 'POST', $params);

        return $list;
    }

    private function makeRequestGetAcoes($list, $viewState)
    {
        print __METHOD__ . "\n";
        preg_match('/(\d+)<\/option><\/select>/', $list, $countItens);

        $params = [
            'javax.faces.partial.ajax' => true,
            'javax.faces.source' => 'idProcessosDataTable',
            'javax.faces.partial.execute' => 'idProcessosDataTable',
            'javax.faces.partial.render' => 'idProcessosDataTable',
            'idProcessosDataTable' => 'idProcessosDataTable',
            'idProcessosDataTable_pagination' => true,
            'idProcessosDataTable_first' => 0,
            'idProcessosDataTable_rows' => $countItens[1],
            'idProcessosDataTable_skipChildren' => 'true',
            'idProcessosDataTable_encodeFeature' => 'true',
            $this->form => $this->form,
            'tipoBusca' => $this->tipoPessoa,
            $this->face => $this->cpfCnpj,
            'g-recaptcha-response' => '',
            'idProcessosDataTable_rppDD' => $countItens[1],
            'idProcessosDataTable:j_idt38_focus' => '',
            'idProcessosDataTable:j_idt38_input' => '',
            'idProcessosDataTable:j_idt45_focus' => '',
            'idProcessosDataTable:j_idt45_input' => '',
            'idProcessosDataTable_rppDD' => 10,
            'javax.faces.ViewState' => $viewState[1]
        ];

        $acoes = $this->getResponse(self::BASE_URL . self::URL_REQUEST, 'POST', $params);
        $this->parseAcoes($acoes);
    }

    private function makeRequestCertificate($viewState, $html)
    {
        print __METHOD__ . "\n";
        $captchaHTML = $this->makeRequest();
        $token = $this->resolveRecaptcha($captchaHTML);
        if (preg_match('/não\sconsta\sem\stramitação/', $html)) {
            $params = [
                $this->form => $this->form,
                'tipoBusca' => $this->tipoPessoa,
                $this->face => $this->cpfCnpj,
                'g-recaptcha-response' => $token,
                'idBaixar' => '',
                'javax.faces.ViewState' => $viewState[1],
            ];
        } else {
            $params = [
                $this->form => $this->form,
                'tipoBusca' => $this->tipoPessoa,
                $this->face => $this->cpfCnpj,
                'g-recaptcha-response' => $token,
                'j_idt30' => '',
                'idProcessosDataTable_rppDD' => 10,
                'idProcessosDataTable:j_idt38_focus' => '',
                'idProcessosDataTable:j_idt38_input' => '',
                'idProcessosDataTable:j_idt44_focus' => '',
                'idProcessosDataTable:j_idt44_input' => '',
                'idProcessosDataTable_rppDD' => 10,
                'javax.faces.ViewState' => $viewState[1],
            ];
        }
        $pdf = $this->getResponse(self::BASE_URL . self::URL_REQUEST, 'POST', $params);
        return $pdf;
    }

    private function parseAcoes($html)
    {
        print __METHOD__ . "\n";
        if (!preg_match('/<tr[\s\S]*?>([\s\S]*?)<\/tr>/', $html)) {
            return $this->acoes = [];
        }

        preg_match_all('/<tr[\s\S]*?>([\s\S]*?)<\/tr>/', $html, $itemsAcoes);

        foreach ($itemsAcoes[1] as $acao) {
            preg_match_all('/<td[\s\S]*?>([\s\S]*?)</', $acao, $itemsAcao);

            $this->acao['numeroProcesso'] = $itemsAcao[1][0];
            $this->acao['vara'] = $itemsAcao[1][1];
            $this->acao['sistema'] = $itemsAcao[1][2];
            $this->acao['fase'] = $itemsAcao[1][3];

            $this->acoes[] = $this->acao;
            $this->acao = [];
        }
    }

    private function parseData($text)
    {
        print __METHOD__ . "\n";
        $patterns = [
            'numCertidao' => ['@Certidão\sn\.:\s(.*)\n@'],
            'expedicao' => ['@Expedição:\s([\s\S]*?)\n@'],
            'codAutenticidade' => ['@autenticação:\s([\s\S]*?)\n@'],
            'validade' => ['@Válida\saté:\s(.*)\n@'],
            'situacao' => ['@(Certifica-se[\s\S]*?sob\so\snº\s[\d.\/-]+)@'],
            'observacao' => ['@OBSERVAÇÕES:\\n+([\s\S]*?Judiciária\.)@'],
        ];

        $this->data = Util::parseDados($patterns, $text);
        $this->data = array_map("utf8_decode", $this->data);

        if (!empty($this->acoes)) {
            $this->data['acoes'] = $this->acoes;
        }

        return $this->data;
    }

    /**
     * Retorna o texto do PDF
     * <AUTHOR> Pereira
     * @return string
     */
    private function savePdfAndReturnText($pdf)
    {
        print __METHOD__ . "\n";
        file_put_contents($this->certificateLocalPath, $pdf);

        $text = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'layout',
            'nopgbrk'
        ]);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        $this->pdf = $this->certificateUrl;

        return $text;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->cpfCnpj = trim($this->param['cpf_cnpj']);

        if (!Document::validarCpfOuCnpj($this->cpfCnpj)) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        if (Document::validarCnpj($this->cpfCnpj)) {
            $this->cpfCnpj = Document::formatCnpj($this->cpfCnpj);
            $this->face = 'idCNPJ';
            $this->tipoPessoa = 'cnpj';
            $this->idTransition = 'idCpf';
            $this->tipoBusca = 'tipoBusca:1';
        } elseif (Document::validarCpf($this->cpfCnpj)) {
            $this->cpfCnpj = Document::formatCpf($this->cpfCnpj);
            $this->face = 'idCpf';
            $this->tipoPessoa = 'cpf';
            $this->idTransition = 'idCNPJ';
            $this->tipoBusca = 'tipoBusca:0';
        }
    }
}
