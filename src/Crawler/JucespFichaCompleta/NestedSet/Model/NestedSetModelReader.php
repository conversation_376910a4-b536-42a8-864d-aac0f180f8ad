<?php

namespace App\Crawler\JucespFichaCompleta\NestedSet\Model;

use App\Crawler\JucespFichaCompleta\NestedSet\NestedsetModel;
use Exception;

class NestedSetModelReader
{
    /**
     * Get all elements from nested set
     *
     * @param $model|NestedSet_Model    Nested set model
     * @param $depth|array      Array of depth wanted. Default is all
     * @param $mode|string      Mode of depth selection: include/exclude
     * @param $order|string     Mode of sort
     *
     * @return array
     */
    public function getAll(NestedsetModel $nestedset, $depth = null, $mode = 'include', $order = 'ASC')
    {
        $db = $nestedset->getDb();

        $query = "
            SELECT
                node.{$nestedset->getStructureId()},
                node.{$nestedset->getStructureName()},
                node.{$nestedset->getStructureLeft()},
                node.{$nestedset->getStructureRight()},
                node.{$nestedset->getStructureIsCategory()},
                node.{$nestedset->getStructureVisibleName()},
                node.{$nestedset->getStructureIsBottom()},
                node.{$nestedset->getStructureTemplate()},
                node.{$nestedset->getStructureIsHidden()},
                node.{$nestedset->getStructureIsStopword()},
                COUNT(parent.{$nestedset->getStructureName()}) - 1 AS depth
            FROM
                {$nestedset->getTableName()} AS node,
                {$nestedset->getTableName()} AS parent
            WHERE 
                node.{$nestedset->getStructureLeft()} 
                BETWEEN 
                    parent.{$nestedset->getStructureLeft()} 
                        AND 
                    parent.{$nestedset->getStructureRight()}
                AND
                    node.{$nestedset->getStructureIsHidden()} IS NOT TRUE
            GROUP BY 
                node.{$nestedset->getStructureId()}, 
                node.{$nestedset->getStructureName()}, 
                node.{$nestedset->getStructureLeft()}, 
                node.{$nestedset->getStructureRight()}, 
                node.{$nestedset->getStructureIsCategory()} , 
                node.{$nestedset->getStructureVisibleName()}, 
                node.{$nestedset->getStructureIsBottom()}, 
                node.{$nestedset->getStructureTemplate()},
                node.{$nestedset->getStructureIsHidden()}, 
                node.{$nestedset->getStructureIsStopword()} 
        ";

        // Handle depth if required
        if (!is_null($depth)) {
            if (!is_array($depth)) {
                $depth = (int) $depth;

                if ($mode == 'exclude') {
                    $mode = '=';
                } else {
                    $mode = '!=';
                }

                $query .= "HAVING COUNT(parent.{$nestedset->getStructureName()}) - 1 $mode $depth";
            } else {
                foreach ($depth as &$one) {
                    $one = (int) $one;
                }
                $depth = implode(', ', $depth);

                if ($mode == 'exclude') {
                    $mode = 'NOT IN';
                } else {
                    $mode = 'IN';
                }

                $query .= "HAVING COUNT(parent.{$nestedset->getStructureName()}) - 1 $mode ($depth)";
            }
        }

        $query .= " ORDER BY node.{$nestedset->getStructureLeft()} $order;";

        return $db->getConnection()->executeQuery($query)->fetchAll();
    }

    /**
     * Get one element with its children.
     * @TODO depth
     *
     * @param $model|NestedsetModel    Nested set model
     * @param $elementId|int    Element Id
     * @param $depth|int        Optional, depth of the tree. Default null means
     *                          full tree
     *
     * @return array
     */
    public function getElement(NestedsetModel $nestedset, $elementId, $depth = null, $order = 'ASC')
    {
        // @TODO: test -> if multiple elements with depth 1 are found -> error
        $db = $nestedset->getDb();

        // Get main element left and right
        $element = $db
            ->select($nestedset->getStructureLeft(), $nestedset->getStructureRight())
            ->from($nestedset->getTableName())
            ->where("{$nestedset->getStructureId()}  = '{$elementId}'")
            ->execute()
            ->fetch();

        if (empty($element)) {
            return [];
        }

        // Get the tree
        $query = "
            SELECT
                node.{$nestedset->getStructureId()},
                node.{$nestedset->getStructureName()},
                node.{$nestedset->getStructureLeft()},
                node.{$nestedset->getStructureRight()},
                node.{$nestedset->getStructureIsCategory()},
                node.{$nestedset->getStructureVisibleName()},
                node.{$nestedset->getStructureIsBottom()},
                node.{$nestedset->getStructureTemplate()},
                node.{$nestedset->getStructureIsHidden()},
                node.{$nestedset->getStructureIsStopword()},
                COUNT(parent.{$nestedset->getStructureName()}) - 1 AS depth
              FROM
                {$nestedset->getTableName()} AS node,
                {$nestedset->getTableName()} AS parent
             WHERE node.{$nestedset->getStructureLeft()}
                BETWEEN parent.{$nestedset->getStructureLeft()} AND parent.{$nestedset->getStructureRight()}
               AND node.{$nestedset->getStructureLeft()}
                BETWEEN {$element[$nestedset->getStructureLeft()]} AND {$element[$nestedset->getStructureRight()]}
             GROUP BY node.{$nestedset->getStructureId()}, node.{$nestedset->getStructureName()},
             node.{$nestedset->getStructureLeft()}, node.{$nestedset->getStructureRight()}
             ORDER BY node.{$nestedset->getStructureLeft()} $order
        ";

        return $db->getConnection()->executeQuery($query)->fetchAll();
    }

    /**
     * Get one element by name with its children.
     * @TODO depth
     *
     * @param $model|NestedsetModel    Nested set model
     * @param $elementName|int    Element Name
     * @param $depth|int        Optional, depth of the tree. Default null means
     *                          full tree
     *
     * @return array
     */
    public function getElementByName(NestedsetModel $nestedset, $elementName, $depth = null, $order = 'ASC')
    {
        // @TODO: test -> if multiple elements with depth 1 are found -> error
        $db = $nestedset->getDb();

        // Get main element left and right
        $element = $db
            ->select($nestedset->getStructureLeft(), $nestedset->getStructureRight())
            ->from($nestedset->getTableName())
            ->where("{$nestedset->getStructureName()} = '{$elementName}'")
            ->execute()
            ->fetch();

        if (empty($element)) {
            return [];
        }

        try {
            // Get the tree
            $query = "
                SELECT
                    node.{$nestedset->getStructureId()},
                    node.{$nestedset->getStructureName()},
                    node.{$nestedset->getStructureLeft()},
                    node.{$nestedset->getStructureRight()},
                    node.{$nestedset->getStructureIsCategory()},
                    node.{$nestedset->getStructureVisibleName()},
                    node.{$nestedset->getStructureIsBottom()},
                    node.{$nestedset->getStructureTemplate()},
                    node.{$nestedset->getStructureIsHidden()},
                    node.{$nestedset->getStructureIsStopword()},
                    COUNT(parent.{$nestedset->getStructureName()}) - 1 AS depth
                FROM
                    {$nestedset->getTableName()} AS node,
                    {$nestedset->getTableName()} AS parent
                WHERE node.{$nestedset->getStructureLeft()}
                    BETWEEN parent.{$nestedset->getStructureLeft()} AND parent.{$nestedset->getStructureRight()}
                AND node.{$nestedset->getStructureLeft()}
                    BETWEEN {$element[$nestedset->getStructureLeft()]} AND {$element[$nestedset->getStructureRight()]}
                GROUP BY node.{$nestedset->getStructureId()}, node.{$nestedset->getStructureName()},
                node.{$nestedset->getStructureLeft()}, node.{$nestedset->getStructureRight()}
                ORDER BY node.{$nestedset->getStructureLeft()} $order
            ";

            return $db->getConnection()->executeQuery($query)->fetchAll();
        } catch (Exception $ex) {
            print_r($element);
            dd($ex->getMessage());
        }
    }

    /**
     * Get width of a node
     *
     * @param $model|NestedsetModel    Nested set model
     * @param $elementId|int    Id of the node
     *
     * @return int
     */
    public function getNodeWidth(NestedsetModel $nestedset, $elementId)
    {
        $db = $nestedset->getDb();

        $stmt = $db->getConnection()->executeQuery("
            SELECT {$nestedset->getStructureRight()} - {$nestedset->getStructureLeft()} + 1
              FROM {$nestedset->getTableName()}
             WHERE {$nestedset->getStructureId()} = $elementId
        ");
        $width = $stmt->fetchColumn();

        return $width;
    }

    /**
     * Get all nodes without children
     *
     * @param $model|NestedsetModel    Nested set model
     *
     * @return array
     */
    public function getLeafs(NestedsetModel $nestedset)
    {
        $db = $nestedset->getDb();

        $result = $db->select()
            ->from($nestedset->getTableName(), array($nestedset->getStructureId(), $nestedset->getStructureName()))
            ->where("{$nestedset->getStructureRight()} = {$nestedset->getStructureLeft()} + 1")
            ->execute()
            ->fetchAll();

        return $result;
    }

    /**
     * Get path of an element
     *
     * @param $model|NestedsetModel    Nested set model
     * @param $elementId|int    Id of the element we want the path of
     *
     * @return array
     */
    public function getPath(NestedsetModel $nestedset, $elementId, $order = 'ASC')
    {
        $db = $nestedset->getDb();

        $query = "
            SELECT
                node.{$nestedset->getStructureId()},
                node.{$nestedset->getStructureName()},
                COUNT(parent.{$nestedset->getStructureName()}) - 1 AS depth
            FROM
                {$nestedset->getTableName()} AS node,
                {$nestedset->getTableName()} AS parent
            WHERE node.{$nestedset->getStructureLeft()}
                BETWEEN parent.{$nestedset->getStructureLeft()} AND parent.{$nestedset->getStructureRight()}
              AND node.{$nestedset->getStructureId()} = $elementId
            GROUP BY node.{$nestedset->getStructureId()}, node.{$nestedset->getStructureName()},
                node.{$nestedset->getStructureLeft()}
            ORDER BY node.{$nestedset->getStructureLeft()} $order;
        ";

        return $db->getConnection()->executeQuery($query)->fetchAll();
    }

    /**
     * Get the parent of an element.
     *
     * @param $model|NestedsetModel    Nested set model
     * @param $elementId|int    Element ID
     * @param $depth|int        Depth of the parent, compared to the child.
     *                          Default is 1 (as immediate)
     *
     * @return array|false
     */
    public function getParent(NestedsetModel $nestedset, $elementId, $depth = 1)
    {
        $db = $nestedset->getDb();

        $child = $db
            ->select()
            ->from($nestedset->getTableName(), array($nestedset->getStructureLeft(), $nestedset->getStructureRight()))
            ->where($nestedset->getStructureId() . ' = ?', $elementId)
            ->execute()
            ->fetch();

        $result = $db
            ->select()
            ->from($nestedset->getTableName(), array($nestedset->getStructureId(), $nestedset->getStructureName()))
            ->where($nestedset->getStructureLeft() . ' < ?', $child[$nestedset->getStructureLeft()])
            ->where($nestedset->getStructureRight() . ' > ?', $child[$nestedset->getStructureRight()])
            ->order('(' . $child[$nestedset->getStructureLeft()] . ' - ' . $nestedset->getStructureLeft() . ')')
            ->limitPage($depth, 1)
            ->execute()
            ->fetch();

        return $result;
    }

    /**
     * Returns if the element is root.
     *
     * @param $model|NestedSet_Model    Nested set model
     * @param $elementId|int    Element ID
     *
     * @return bool
     */
    public function isRoot(NestedsetModel $nestedset, $elementId)
    {
        $db = $nestedset->getDb();

        $query = "
            SELECT 1
              FROM {$nestedset->getTableName()}
             WHERE {$nestedset->getStructureId()} = $elementId
               AND {$nestedset->getStructureLeft()} = (
                       SELECT MIN({$nestedset->getStructureLeft()})
                       FROM {$nestedset->getTableName()}
                   )
               AND {$nestedset->getStructureRight()} = (
                       SELECT MAX({$nestedset->getStructureRight()})
                         FROM {$nestedset->getTableName()}
                   )
        ";

        $result   = $db->getConnection()->executeQuery($query)->fetchColumn();

        return (bool)$result;
    }
}
