<?php

namespace App\Crawler\JucespFichaCompleta\NestedSet;

use App\Crawler\JucespFichaCompleta\NestedSet\Model\NestedSetModelBuilder;
use App\Crawler\JucespFichaCompleta\NestedSet\Model\NestedSetModelOutput;
use App\Crawler\JucespFichaCompleta\NestedSet\Model\NestedSetModelReader;

/**
 * This object is a pattern to store hieriarchical data into a SQL database.
 *
 * The objective is to make it easier to get a full or partial tree from the database
 * with a single request. In addition, it adds multiple methods in order to
 * manipulate the nested tree:
 *  - add()
 *  - delete()
 *  - move()
 *
 * methods to get results:
 * - getAll()
 * - getLeafs()
 * - getChildren()
 *
 * methods to get state of elements:
 * - hasChildren()
 * - isRoot()
 * - getLevel()
 * - numberOfDescendant()
 *
 * methods to get those result to a specific output:
 * - toArray()
 * - toXml()
 * - toJson()
 * - toCsv()
 *
 * Hierarchical data are handled as an array with depth information, but is
 * never outputed that way.
 *
 * @version 0.5
 * <AUTHOR> (fpietka)
 *
 * Powered by Nextcode, 2009
 */

class NestedSetModel
{
    /**
     * In MySQL and PostgreSQL, 'left' and 'right' are reserved words
     *
     * This represent the default table structure
     */
    protected $structure = [
        'id'    => 'id',
        'name'  => 'name',
        'left'  => 'lft',
        'right' => 'rgt',
        'is_category' => 'is_category',
        'visible_name' => 'visible_name',
        'is_bottom' => 'is_bottom',
        'template' => 'template',
        'is_hidden' => 'is_hidden',
        'is_stopword' => 'is_stopword',
    ];

    /**
     * Database informations required to locate/save the set
     */
    protected $db;
    protected $tableName;

    /**
     * @param $tableName|string
     *
     * @return $this
     */
    public function setTableName($tableName)
    {
        if (!is_null($tableName)) {
            $this->tableName = $tableName;
        }

        return $this;
    }

    public function getTableName()
    {
        return $this->tableName;
    }

    /**
     * @param $db
     *
     * @return $this
     */
    public function setDb($db)
    {
        $this->db = $db;

        return $this;
    }

    public function getDb()
    {
        return $this->db;
    }

    /**
     * @param $fieldName
     *
     * @return $this
     */
    public function setStructureId($fieldName)
    {
        $this->structure['id'] = $fieldName;
        return $this;
    }

    public function getStructureId()
    {
        return $this->structure['id'];
    }

    /**
     * @param $fieldName
     *
     * @return $this
     */
    public function setStructureName($fieldName)
    {
        $this->structure['name'] = $fieldName;
        return $this;
    }

    public function getStructureName()
    {
        return $this->structure['name'];
    }

    /**
     * @param $fieldName
     *
     * @return $this
     */
    public function setStructureLeft($fieldName)
    {
        $this->structure['left'] = $fieldName;
        return $this;
    }

    public function getStructureLeft()
    {
        return $this->structure['left'];
    }

    /**
     * @param $fieldName
     *
     * @return $this
     */
    public function setStructureRight($fieldName)
    {
        $this->structure['right'] = $fieldName;
        return $this;
    }

    public function getStructureRight()
    {
        return $this->structure['right'];
    }

    /**
     * @param $fieldName
     *
     * @return $this
     */
    public function setStructureIsCategory($fieldName)
    {
        $this->structure['is_category'] = $fieldName;
        return $this;
    }

    public function getStructureIsCategory()
    {
        return $this->structure['is_category'];
    }

    /**
     * @param $fieldName
     *
     * @return $this
     */
    public function setStructureVisibleName($fieldName)
    {
        $this->structure['visible_name'] = $fieldName;
        return $this;
    }

    public function getStructureVisibleName()
    {
        return $this->structure['visible_name'];
    }

    /**
     * @param $fieldName
     *
     * @return $this
     */
    public function setStructureIsBottom($fieldName)
    {
        $this->structure['is_bottom'] = $fieldName;
        return $this;
    }

    public function getStructureIsBottom()
    {
        return $this->structure['is_bottom'];
    }

    /**
     * @param $fieldName
     *
     * @return $this
     */
    public function setStructureTemplate($fieldName)
    {
        $this->structure['template'] = $fieldName;
        return $this;
    }

    public function getStructureTemplate()
    {
        return $this->structure['template'];
    }

    /**
     * @param $fieldName
     *
     * @return $this
     */
    public function setStructureIsHidden($fieldName)
    {
        $this->structure['is_hidden'] = $fieldName;
        return $this;
    }

    public function getStructureIsHidden()
    {
        return $this->structure['is_hidden'];
    }

    /**
     * @param $fieldName
     *
     * @return $this
     */
    public function setStructureIsStopword($fieldName)
    {
        $this->structure['is_stopword'] = $fieldName;
        return $this;
    }

    public function getStructureIsStopword()
    {
        return $this->structure['is_stopword'];
    }

    /**
     * @param $name|string      Name of the element
     * @param $reference|int    Id of the reference element
     * @param $position|string  Position from the reference element. Values are
     *                          'into', 'before', 'after'.
     *
     * @return $this
     */
    public function add($name, $reference = null, $position = 'into')
    {
        $builder = new NestedSetModelBuilder();
        if (is_null($reference)) {
            $builder->append($this, $name);
        } else {
            $reference = (int) $reference;

            $builder->addInto($this, $name, $reference);
        }

        return $this;
    }

    /**
     * If recursive, delete children, else children move up in the tree
     *
     * @param $id|int               Id of the element to delete
     * @param $recursive|boolean    Delete element's childrens, default is true
     *
     * @return $this
     */
    public function delete($id, $recursive = true)
    {
        $db = $this->getDb();

        $select = $db
            ->select()
            ->from($this->tableName, array($this->structure['id'], $this->structure['left'], $this->structure['right']))
            ->where($this->structure['id'] . ' = ?', $id);

        $stmt   = $db->query($select);
        $result = $stmt->fetch();

        if (!$result) {
            return false;
        }

        $builder = new NestedsetModelBuilder();
        if ($recursive) {
            $builder->deleteRecursive($this, $result);
        } else {
            $builder->deleteNonRecursive($this, $result);
        }

        return $this;
    }

    /**
     * @param $elementId|int    Id of the element to move
     * @param $referenceId|int  Id of the reference element
     * @param $position|string  Position from the reference element. Values are
     *                          'into', 'before', 'after'.
     *
     * @return $this
     */
    public function move($elementId, $referenceId, $position = 'into')
    {
        $db = $this->getDb();

        $reference = $this->getElement($referenceId);
        $element   = $this->getElement($elementId); // @TODO get one level, we don't need all this tree

        // error handling
        if (empty($element) || empty($reference)) {
            return false;
        }

        switch ($position) {
            case 'into':
            default:
                $builder = new NestedsetModelBuilder();
                $builder->moveInto($this, $element, $reference);
        }

        return true;
    }

    /**
     * Get width of a node
     */
    public function getNodeWidth($elementId)
    {
        $reader = new NestedsetModelReader();
        return $reader->getNodeWidth($this, $elementId);
    }

    /**
     * Get all nodes without children
     *
     * @return array
     */
    public function getLeafs()
    {
        $reader = new NestedsetModelReader();
        return $reader->getLeafs($this);
    }

    /**
     * Get all elements from nested set
     *
     * @param $depth|array      Array of depth wanted. Default is all
     * @param $mode|string      Mode of depth selection: include/exclude
     * @param $order|string     Mode of sort
     *
     * @return array
     */
    public function getAll($depth = null, $mode = 'include', $order = 'ASC')
    {
        $reader = new NestedsetModelReader();
        return $reader->getAll($this, $depth, $mode, $order);
    }

    /**
     * Convert a tree array (with depth) into a hierarchical array.
     *
     * @param $nodes|array   Array with depth value.
     *
     * @return array
     */
    public function toArray(array $nodes = array())
    {
        if (empty($nodes)) {
            $nodes = $this->getAll();
        }

        $output = new NestedsetModelOutput();
        return $output->toArray($nodes);
    }

    /**
     * Convert a tree array (with depth) into a hierarchical array.
     *
     * @param $nodes|array   Array with depth value.
     *
     * @return array
     */
    public function toStructure(array $nodes = array())
    {
        if (empty($nodes)) {
            $nodes = $this->getAll();
        }

        $output = new NestedsetModelOutput();
        return $output->toStructure($nodes);
    }

    /**
     * Convert a tree array (with depth) into a hierarchical XML string.
     *
     * @param $nodes|array   Array with depth value.
     *
     * @return string
     */
    public function toXml(array $nodes = array())
    {
        if (empty($nodes)) {
            $nodes = $this->getAll();
        }

        $output = new NestedsetModelOutput();
        return $output->toXml($nodes);
    }

    /**
     * Return nested set as JSON
     *
     * @params $nodes|array          Original 'flat' nested tree
     *
     * @return string
     */
    public function toJson(array $nodes = array())
    {
        if (empty($nodes)) {
            $nodes = $this->getAll();
        }

        $output = new NestedsetModelOutput();
        return $output->toJson($nodes);
    }

    /**
     * Returns all elements as <ul>/<li> structure
     *
     * Possible options:
     *  - list (simple <ul><li>)
     *
     * @return string
     */
    public function toHtml(array $nodes = array(), $method = 'list')
    {
        if (empty($nodes)) {
            $nodes = $this->getAll();
        }

        $output = new NestedsetModelOutput();
        return $output->toHtml($nodes, $method);
    }

    /**
     * Public method to get an element
     *
     */
    public function getElement($elementId, $depth = null)
    {
        $reader = new NestedSetModelReader();
        return $reader->getElement($this, $elementId, $depth);
    }

    /**
     * Public method to get an element by name
     *
     */
    public function getElementByName($elementName, $depth = null)
    {
        $reader = new NestedSetModelReader();
        return $reader->getElementByName($this, $elementName, $depth);
    }

    /**
     * Get path of an element
     *
     * @param $elementId|int    Id of the element we want the path of
     *
     * @return array
     */
    public function getPath($elementId, $order = 'ASC')
    {
        $reader = new NestedSetModelReader();
        return $reader->getPath($this, $elementId, $order);
    }

    /**
     * Get the parent of an element.
     *
     * @param $elementId|int    Element ID
     * @param $depth|int        Depth of the parent, compared to the child.
     *                          Default is 1 (as immediate)
     *
     * @return array|false
     */
    public function getParent($elementId, $depth = 1)
    {
        $reader = new NestedSetModelReader();
        return $reader->getParent($this, $elementId, $depth);
    }

    /**
     * Returns the number of descendant of an element.
     *
     * @params $elementId|int   ID of the element
     *
     * @return int
     */
    public function numberOfDescendant($elementId)
    {
        $reader = new NestedSetModelReader();
        $width = $reader->getNodeWidth($this, $elementId);
        $result = ($width - 2) / 2;

        return $result;
    }

    /**
     * Returns if the element is root.
     *
     * @param $elementId|int    Element ID
     *
     * @return boolean
     */
    public function isRoot($elementId)
    {
        $reader = new NestedSetModelReader();
        return $reader->isRoot($this, $elementId);
    }
}
