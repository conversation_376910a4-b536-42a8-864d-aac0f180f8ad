<?php

namespace App\Crawler\JucespFichaCompleta;

use App\Crawler\Spider;
use App\Helper\Pdf;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use App\Factory\PostgresDB;
use App\Crawler\JucespFichaCompleta\NestedSet\NestedSetModel;
use App\Crawler\JucespFichaCompleta\JucespFichaCompletaParse;
use Exception;

/**
 * Classe da fonte Jucesp Ficha Completa
 *
 * Fonte utilizada apenas no histórico, por este motivo ele faz o parse apenas de uma empresa da lista
 * usando a similaridade, a fonte antiga para o histórico já retornava apenas 1 resultado
 *
 * <AUTHOR> <PERSON> - 02/12/2020
 */
class JucespFichaCompleta extends Spider
{
    private const URL = 'https://www.jucesponline.sp.gov.br/';
    private const STATIC_URL = S3_STATIC_URL;
    private const STATIC_FOLDER = 'captura/jucesp_ficha_completa';
    private const RETRY_FORCE = 8;

    private $criterio;
    private $eventVars;
    private $login;
    private $senha;
    private $linkFile;
    private $nestedset;
    private $db;
    private $nire;

    private $pdfPath = "/tmp/pdf_{uniqid}.pdf";
    private $retryCaptcha = 0;

    /**
     * Método que inicia a execução da fonte
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return array
     */
    public function start(): array
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $this->setProxy();

        $result = $this->searchCompany();

        if (!is_array($result)) {
            throw new Exception("Não foi possível recuperar os dados", 3);
        }

        if (empty($result)) {
            throw new Exception("Sua pesquisa não encontrou nenhuma empresa correspondente.", 2);
        }

        return $result;
    }

    /**
     * Método para validação dos parametros da fonte
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return array
     */
    public function validateAndSetCrawlerAttributes()
    {
        if (empty($this->auth['usuario']) || empty($this->auth['senha'])) {
            throw new Exception('Usuario ou senha inválidos', 6);
        }

        if (empty(trim($this->param['criterio']))) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }

        $this->criterio = $this->param['criterio'];
        $this->login = $this->auth['usuario'];
        $this->senha = $this->auth['senha'];
    }

    /**
     * Retorna os resultads do critério parseados
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return array
     */
    protected function searchCompany()
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $this->setEventsFirstPageAndLogin();

        $params = [
            'ctl00$ajaxMaster' => 'ctl00$cphContent$ajaxForm|ctl00$cphContent$frmBuscaSimples$btPesquisar',
            '__LASTFOCUS' => $this->eventVars['__LASTFOCUS'],
            '__EVENTTARGET' => $this->eventVars['__EVENTTARGET'],
            '__EVENTARGUMENT' => $this->eventVars['__EVENTARGUMENT'],
            '__VIEWSTATE' => $this->eventVars['__VIEWSTATE'],
            '__EVENTVALIDATION' => $this->eventVars['__EVENTVALIDATION'],
            'ctl00$cphContent$frmBuscaSimples$txtPalavraChave' => $this->criterio,
            'ctl00$cphContent$frmBuscaSimples$twePalavraChave_ClientState' => '',
            'ctl00$cphContent$frmBuscaSimples$btPesquisar' => 'Buscar'
        ];

        $responseList = $this->forceResponse(self::URL . 'Default.aspx', 'POST', $params, [
            'Referer' => self::URL . 'Default.aspx'
        ]);

        return $this->parseResultList($responseList);
    }

    /**
     * Retorna para a página correta após a quebra do captcha
     *
     * @param string $response
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return string
     */
    private function captchaResponse(string $response): string
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $captchaParams = $this->getCaptchaResponseParams($response);

        $response = $this->getResponse($captchaParams["url"], 'POST', $captchaParams["params"], [
            'Referer' => $captchaParams["url"]
        ]);

        $this->setEventVars($response);

        return $response;
    }

    /**
     * Retorna os parametros necessários para voltar a página correta após a quebra do captcha
     *
     * @param string $response
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return array
     */
    private function getCaptchaResponseParams(string $response): array
    {
        if (preg_match("#ResultadoBusca#", $response)) {
            return [
                'params' => [
                    'ctl00$ajaxMaster' => 'ctl00$cphContent$ajaxGrid|ctl00$cphContent$gdvResultadoBusca$btEntrar',
                    '__LASTFOCUS' => '',
                    '__EVENTTARGET' => '',
                    '__EVENTARGUMENT' => '',
                    '__VIEWSTATE' => $this->eventVars['__VIEWSTATE'],
                    '__EVENTVALIDATION' => $this->eventVars['__EVENTVALIDATION'],
                    'ctl00$cphContent$frmBuscaSimples$txtPalavraChave' => $this->criterio,
                    'ctl00$cphContent$frmBuscaSimples$twePalavraChave_ClientState' => '',
                    'ctl00$cphContent$gdvResultadoBusca$CaptchaControl1' => $this->captcha,
                    'ctl00$cphContent$gdvResultadoBusca$btEntrar' => 'Continuar',
                ],
                'url' => self::URL . 'ResultadoBusca.aspx'
            ];
        }

        if (preg_match("#Pre_Visualiza#", $response)) {
            return [
                'params' => [
                    'ctl00$ajaxMaster' => 'ctl00$cphContent$ajaxForm|ctl00$cphContent$frmPreVisualiza$btEntrar',
                    '__EVENTTARGET' => '',
                    '__EVENTARGUMENT' => '',
                    '__VIEWSTATE' => $this->eventVars['__VIEWSTATE'],
                    '__EVENTVALIDATION' => $this->eventVars['__EVENTVALIDATION'],
                    'ctl00$cphContent$frmPreVisualiza$CaptchaControl1' => $this->captcha,
                    'ctl00$cphContent$frmPreVisualiza$btEntrar' => 'Continuar'
                ],
                'url' => self::URL . 'Pre_Visualiza.aspx?nire=' . $this->nire . '&idproduto=2'
            ];
        }

        throw new Exception('Está solicitando captcha em local inesperado', 3);
    }

    /**
     * Verifica se tem captcha na página e quebra o captcha
     *
     * @param string $response
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return bool
     */
    private function hasCaptcha(string $response): bool
    {
        if (preg_match('#src="CaptchaImage\.aspx\?guid=(.*?)"#is', $response)) {
            $this->stepCaptcha($response);
            return true;
        }

        return false;
    }

    /**
     * Resgata e quebra o captcha
     *
     * @param string $response
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return void
     */
    private function stepCaptcha(string $response)
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        if (!preg_match('#src="CaptchaImage\.aspx\?guid=(.*?)"#is', $response, $matches)) {
            throw new Exception("Erro ao capturar captcha", 3);
        }

        $captchaUrl = self::URL . '/CaptchaImage.aspx?guid=' . $matches[1];

        $this->getImageAndBreakCaptcha($captchaUrl);

        if (empty($this->captcha)) {
            throw new Exception("Erro ao quebrar o captcha", 3);
        }
    }

    /**
     * Seta as váriaveis de eventos usadas nos posts
     *
     * @param string $content
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return void
     */
    private function setEventVars(string $content)
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $events = [
            '__LASTFOCUS',
            '__EVENTTARGET',
            '__EVENTARGUMENT',
            '__VIEWSTATE',
            '__EVENTVALIDATION',
            '__PREVIOUSPAGE',
            '__VIEWSTATEGENERATOR'
        ];

        foreach ($events as $event) {
            if (preg_match('#id="' . $event . '"\s*value="(.*?)"#is', $content, $matches)) {
                $this->eventVars[$event] = $matches[1];
                continue;
            }

            $this->eventVars[$event] = '';
        }
    }

    /**
     * Seta as primeiras váriaveis de eventos e efetua o login no site
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return void
     */
    private function setEventsFirstPageAndLogin()
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $response = $this->getResponse(self::URL . '/Default.aspx', 'GET');

        $this->setEventVars($response);
        $this->stepLogin();
    }

    /**
     * Efetua o login no site
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return void
     */
    private function stepLogin()
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $params = [
            'ctl00$ajaxMaster' => 'ctl00$ajaxLogin|ctl00$frmLogin$btLogin',
            '__LASTFOCUS' => $this->eventVars['__LASTFOCUS'],
            '__EVENTTARGET' => $this->eventVars['__EVENTTARGET'],
            '__EVENTARGUMENT' => $this->eventVars['__EVENTARGUMENT'],
            '__VIEWSTATE' => $this->eventVars['__VIEWSTATE'],
            '__EVENTVALIDATION' => $this->eventVars['__EVENTVALIDATION'],
            'ctl00$frmLogin$txtLogin' => $this->login,
            'ctl00$frmLogin$tweLogin_ClientState' => '',
            'ctl00$frmLogin$txtSenha' => $this->senha,
            'ctl00$frmLogin$tweSenha_ClientState' => '',
            'ctl00$cphContent$frmBuscaSimples$txtPalavraChave' => '',
            'ctl00$cphContent$frmBuscaSimples$twePalavraChave_ClientState' => '',
            'ctl00$frmLogin$btLogin' => 'OK'
        ];

        $response = $this->getResponse(self::URL . '/Default.aspx', 'POST', $params, [
            'Referer' => self::URL . '/Default.aspx'
        ]);

        $this->setEventVars($response);

        if (
            preg_match_all(
                "/Usu.*?rio n.*?o cadastrado na Nota Fiscal Paulista/ui",
                $response,
                $result
            )
        ) {
            throw new Exception(
                "Erro de autenticação, o CPF informado não está cadastrado na nota fiscal paulista!",
                6
            );
        }

        if (
            preg_match_all(
                "/N.*?o foi poss.*?vel autenticar o usu.*?rio: CPF ou Senha inv.*?lidos/ui",
                $response,
                $result
            )
        ) {
            throw new Exception(
                "Erro de autenticação, verifique se seu login ou senha para esta fonte são válidos!",
                6
            );
        }
    }

    /**
     * Procura na lista da primeira página o critério com maior % de similaridade
     *
     * @param array $nires
     * @param array $names
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return array
     */
    private function similarCriterion(array $nires, array $names): array
    {
        $similarCriterion = [
            'percente' => 0,
            'nire' => '',
            'name' => ''
        ];

        $percente = 0;

        foreach ($names as $index => $name) {
            similar_text(
                strtolower($name),
                strtolower($this->criterio),
                $percente
            );

            if ($percente > $similarCriterion['percente']) {
                $similarCriterion = [
                    'percente' => $percente,
                    'nire' => $nires[$index],
                    'name' => $name
                ];
            }
        }

        return $similarCriterion;
    }

    /**
     * Parse da lista de resultados
     *
     * @param string $response
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return array
     */
    private function parseResultList(string $response): array
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        if (preg_match('#n.*?encontrou.nenhuma.empresa.correspondente#is', $response)) {
            throw new Exception("Nenhum dado encontrado", 2);
        }

        $matches = [];

        if (
            !preg_match_all(
                '#<a[^>]*href="javascript:__doPostBack\((.*?),(.*?)\)">\s*(\d+)\s*</a>#',
                $response,
                $matches
            )
        ) {
            throw new Exception("Não foi possível capturar a lista", 3);
        }

        if (!isset($matches[3]) || empty($matches[3])) {
            throw new Exception("Erro ao pegar o link da lista de resultados", 3);
        }

        preg_match_all(
            "/\<span\s*id=\"ctl00_cphContent_gdvResultadoBusca_gdvContent_ctl[0-9]{1,}_lblRazaoSocial\">(.*)<\/span>/",
            $response,
            $names
        );

        $nires = $matches[3];

        if (count($nires) !== count($names[1])) {
            throw new Exception("Array de nires não tem a mesma quantidade que o array de nomes", 3);
        }

        $criterion = $this->similarCriterion($nires, $names[1]);

        $this->nire = $criterion['nire'];

        $response = $this->forceResponse(
            self::URL . "Pre_Visualiza.aspx?nire={$this->nire}&idproduto=2",
            'POST'
        );

        return $this->parseItem($response, $criterion['name']);
    }

    /**
     * Parse de um item da lista
     *
     * @param string $response
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return array
     */
    private function parseItem(string $response, string $razaoSocial)
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $this->setEventVars($response);
        $response = $this->selectFicha();

        if (empty($response)) {
            return [];
        }

        $response = $this->getPDF($response);

        return (new JucespFichaCompletaParse(
            $this->linkFile,
            $razaoSocial
        ))->parse($response);
    }

    /**
     * Retorna o texto do PDF
     *
     * @param string $response
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return string
     */
    private function getPDF(string $response): string
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        if (!$this->isPDF($response)) {
            if (!preg_match('#src="(DocumentoTicket\.aspx\?ticket=.*?)">#is', $response, $matches)) {
                throw new Exception("Não foi possível capturar a ficha ", 3);
            }

            $response = $this->getResponse(self::URL . $matches[1], 'GET');
        }

        return $this->parsePdfToText($response);
    }

    /**
     * Verifica se é um pdf
     *
     * @param string $response
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return bool
     */
    private function isPDF(string $response): bool
    {
        if (preg_match('#PDF-1.4#is', $response, $matches)) {
            return true;
        }

        return false;
    }

    /**
     * Submit para a emissão do PDF
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return string
     */
    private function selectFicha(): string
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $params = [
            '__EVENTTARGET' => '',
            '__EVENTARGUMENT' => '',
            '__LASTFOCUS' => '',
            '__VIEWSTATE' => $this->eventVars['__VIEWSTATE'],
            '__PREVIOUSPAGE' => $this->eventVars['__PREVIOUSPAGE'],
            '__EVENTVALIDATION' => $this->eventVars['__EVENTVALIDATION'],
            'ctl00$cphContent$frmPreVisualiza$rblTipoDocumento' => '2',
            'ctl00$cphContent$frmPreVisualiza$btnEmitir' => 'OK'
        ];

        try {
            $this->setCurlOpt([
                CURLOPT_TIMEOUT => 1200000
            ]);

            $response = $this->getResponse(self::URL . 'SelecionaProduto.aspx', 'POST', $params, [
                'Referer' => self::URL . 'Pre_Visualiza.aspx?nire=' . $this->nire . '&idproduto='
            ]);

            return $response;
        } catch (Exception $ex) {
            throw new Exception('Erro ao baixar o PDF, tempo de espera esgotado', 3);
        }
    }

    /**
     * Converte o PDF para Texto
     *
     * @param string $content
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return string
     */
    private function parsePdfToText(string $content): string
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $this->setPdfPath();
        file_put_contents($this->pdfPath, $content);

        $this->saveS3File();

        return (new Pdf())->getTextFromPdf($this->pdfPath, [
            'layout'
        ]);
    }

    /**
     * Seta o caminho do PDF
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return void
     */
    private function setPdfPath()
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $this->uniqid = md5(uniqid(rand(), true));
        $this->pdfPath = str_replace('{uniqid}', $this->uniqid, $this->pdfPath);
    }

    /**
     * Salva o PDF no S3
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return void
     */
    private function saveS3File()
    {
        (new S3(new StaticUplexisBucket()))->save($this->getS3Path(), $this->pdfPath);
    }

    /**
     * Retorna o caminho do PDF no S3
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return string
     */
    private function getS3Path(): string
    {
        $file = self::STATIC_FOLDER . $this->pdfPath;
        $this->linkFile = self::STATIC_URL . $file;
        return $file;
    }

    /**
     * Força o POST para apareça o captcha quebrar antes de continuar
     *
     * @param string $url
     * @param string $method
     * @param array $params
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return string
     */
    private function forceResponse(string $url, string $method = 'GET', array $params = []): string
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $response  = $this->getResponse(
            $url,
            $method,
            $params
        );

        $this->setEventVars($response);

        if ($this->hasCaptcha($response)) {
            return $this->retryCaptcha($response);
        }

        return $response;
    }

    /**
     * Retry para falha na quebra do captcha durante a navegação
     *
     * @param string $response
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return string
     */
    private function retryCaptcha(string $response): string
    {
        $this->retryCaptcha++;

        if ($this->retryCaptcha === self::RETRY_FORCE) {
            return $response;
        }

        $response = $this->captchaResponse($response);

        if ($this->hasCaptcha($response)) {
            return $this->retryCaptcha($response);
        }

        $this->retryCaptcha = 0;
        return $response;
    }

    /**
     * Verifica se está logado
     *
     * @param string $response
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return bool
     */
    private function isLogged(string $response): bool
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        if (preg_match('#<span\s*id="ctl00_frmLogin_lblUsuario"\s*>\s*([^<]+)\s*</span>#', $response)) {
            return true;
        }
        return false;
    }
}
