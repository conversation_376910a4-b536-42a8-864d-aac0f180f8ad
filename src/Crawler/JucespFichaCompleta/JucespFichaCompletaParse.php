<?php

namespace App\Crawler\JucespFichaCompleta;

use App\Helper\Str;
use App\Helper\Util;
use App\Crawler\JucespFichaCompleta\NestedSet\NestedSetModel;
use App\Crawler\JucespFichaCompleta\JucespFichaDefaultCategories;
use Doctrine\DBAL\Query\QueryBuilder;

/**
 * Classe parse da fonte Jucesp Ficha Completa
 *
 * <AUTHOR> 02/12/2020
 */
class JucespFichaCompletaParse
{
    private $linkFile;
    private $structure;
    private $capital;
    private $razaoSocial;

    /**
     * Construtor da classe de parse
     *
     * @param string $linkFile
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return void
     */
    public function __construct(string $linkFile, string $razaoSocial)
    {
        $this->linkFile = $linkFile;
        $this->razaoSocial = $razaoSocial;
    }

    /**
     * Inicia o parse da fonte
     *
     * @param string $response
     *
     * @version 1.0.0
     *
     * <AUTHOR> 02/12/2020
     *
     * @return array
     */
    public function parse(string $response)
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $response = preg_replace("#\s*Documento\s*Gratuito.*?#is", '', $response);

        $companyData = $this->parseCompany($response);

        if (empty($companyData)) {
            return [];
        }

        $this->capital = $companyData['capitalSocial'];
        $companyData['razaoSocial'] = $this->razaoSocial;
        $companyData['socios'] = $this->getSocios($response);
        $companyData['arquivamentos'] = $this->getArquivamentos($response);
        $companyData['link'] = $this->linkFile;
        $companyData['situacao'] = $this->parseSituation($response);
        $companyData['texto'] = Str::cleanString(trim($companyData['texto']));
        $companyData['endereco'] = Str::cleanString(trim($companyData['endereco']));

        return $companyData;
    }

    /**
     * Retorna o parse da situação
     *
     * @param string $response
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return string
     */
    private function parseSituation(string $response): string
    {
        $situation = "ATIVA";

        if (
            preg_match_all(
                '#\n\n\s*EMPRESA\n\n\s*(DISSOLVIDA|INCORPORADA|TRANSFORMADA|CINDIDA)\n\n#is',
                $response,
                $matches
            )
        ) {
            $situation = preg_replace("#\s+#", ' ', $matches[1])[0];
        }

        return $situation;
    }

    /**
     * Retorna o parse da empresa
     *
     * @param string $response
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return array
     */
    private function parseCompany(string $response): array
    {
        return Str::encoding(Util::parseDados(
            $this->getCompanyPatterns(),
            $response
        ));
    }

    /**
     * Retorna array de regex para parse da empresa
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return array
     */
    private function getCompanyPatterns()
    {
        return [
            'razaoSocial' => ['#EMPRESA\\n\s*\\n\s*([^s].*)TIPO#is', null],
            'tipo' => ['#TIPO:\s*(.*?)\\n#is',null],
            'nireMatriz' => ['#NIRE\s*MATRIZ.*?\\n\s*\\n\s*(\d+)\s*.*?\\n#is',null],
            'dataConstituicao' => ['#DATA\sDA\sCONS.*?(\d{2}\/\d{2}\/\d{4})#is',null],
            'dataEmissao' => ['#NIRE\s*MATRIZ.*?\\n\s*\\n\s*\d+\s*\d{2}\/\d{2}\/\d{4}\s*(.*?)\s*\\n\s*\\n#is', null],
            'dataInicio' => ['#IN.*?CIO\s*DE\s*ATIVIDADE.*?\\n\s*\\n\s*(\d{2}\/\d{2}\/\d{4})#is', null],
            'cnpj' => ['#CNPJ.*?(\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2})#is', null],
            'ie' => [
                '#IN.*?CIO\s*DE\s*ATIVIDADE.*?\\n\s*\\n\s*.*?\s*\d{2}.\d{3}.\d{3}\/\d{4}-\d{2}\s*(.*?)\s*CAPITAL#is',
                null
            ],
            'capitalSocial' => ['#CAPITAL\\n.*?R\$.*?(.*?)\(#is', null],
            'capitalSocialExtenso' => ['#CAPITAL\\n.*?R\$.*?(\(.*?)\\n#is', null],
            'logradouro' => ['#LOGRADOURO:\s?(.*?)\s{2,}#is', null],
            'numero' => ['#N.*?MERO:\s(.*?)\s{2,}#is', null],
            'bairro' => ['#BAIRRO:\s?(.*?)\s{2,}#is', null],
            'complemento' => ['#COMPLEMENTO:\s(.*?)\\n#is', null],
            'municipio' => ['#MUNIC.*?PIO:\s(.*?)\s{2,}#is', null],
            'cep' => ['#CEP:\s(.*?)\s{2,}#is', null],
            'uf' => ['#UF:\s(.*?)\\n#is', null],
            'objetoSocial' => ['#OBJETO\sSOCIAL\\n(.*?)TITULAR#is', null]
        ];
    }

    /**
     * Retorna todos os dados de todos os sócios parseados
     *
     * @param string $response
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return array
     */
    private function getSocios(string $response): array
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        if (
            preg_match(
                "#TITULAR\s\/\sS.*?CIOS\s\/\sDIRETORIA\\n(.*?)ARQUIVAMENTOS|
                FIM\s*DAS\s*INFORMA.*?ES\s*PARA\s*NIRE#is",
                $response,
                $matches
            )
        ) {
            $aTemp = explode("LTIMOS ARQUIVAMENTOS", $matches[1]);
            $aTemp = explode("DIRETORIA", $aTemp[0]);
            $aTemp = explode("\n\n", $aTemp[0]);

            $sociosArray = [];

            foreach ($aTemp as $temp) {
                if (!empty(trim($temp))) {
                    $sociosArray[] = $this->parseSocios($temp);
                }
            }

            return $sociosArray;
        }

        return [];
    }

    /**
     * Retorna sócio parseado
     *
     * @param string $socio
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return array
     */
    private function parseSocios(string $socio): array
    {
        $patterns = [
            'nome' => ['#(.*?),#is', null],
            'cpf' => ['#CPF:(.*?),#is', null],
            'rg_rne' => ['#RG\/RNE:(.*?),#is', null],
            'valor' => ['#\$(.*?,\d{2}?)#is', null],
            'nire' => ['#NIRE:?\s*?([0-9\.\-]{1,}),#is', null],
            'nacionalidade' => ['#NACIONALIDADE\s?(\w+),#is', null],
            'documento' => ['#DOCUMENTO:?\s*?(\w{1,}),#is', null],
            'endereco' => ['#RESIDENTE\s?.\s?(.*?CEP.*?),#is', null]
        ];

        $cargo = $this->parseCargo($socio);

        $dados = Util::parseDados($patterns, $socio);
        $dados['endereco'] = Str::cleanString(trim($dados['endereco']));
        $dados['representado'] = strpos($cargo, 'REPRESENTADO') !== false ? 1 : null;
        $dados['socio'] = strpos($cargo, 'SÓCIO') !== false ? 1 : null;
        $dados['administrador'] = strpos($cargo, 'ADMINISTRADOR') !== false ? 1 : null;
        $dados['capital'] = $dados['valor'];
        $dados['texto'] = Str::cleanString(trim($socio));
        $dados['tipo'] = 1;
        $dados['template'] = 'templates/others.html';
        $dados['cargo'] = $cargo;
        $dados['categorias'] = '';

        return $dados;
    }

    /**
     * Retorna cargo parseado
     *
     * @param string $texto
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return string
     */
    private function parseCargo(string $texto): string
    {
        $patterns = [
            '/NA\s*SITUAÇÃO\s*DE\s*(S.*CIO\s*E\s*ADMINIS.*?|S.*CIO|DIRETOR\s*[\w\s]{1,}?E\s*ADMIN.*?|PROC.*?)[,\.\ ]/',
            '/NA\s*SITUAÇÃO\s*DE\s*(.*?)[\.,]/',
            '/DO\s*CARGO\s*DE\s*(S.*CIO\s*E\s*ADMINIS.*?|S.*CIO|DIRETOR\s*[\w\s]{1,}?E\s*ADMINIS.*?|PROC.*?)[,\.\ ]/',
            '/COMO\s*(S.*CIO\s*E\s*ADMINIS.*?|S.*CIO|DIRETOR\s*[\w\s]{1,}?E\s*ADMINIS.*?|ADMINIS.*?|PROC.*?)[,\.\ ]/',
            '/(REPRESENTANDO\s*.*)./'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $texto, $match)) {
                return $match[1];
            }
        }

        return '';
    }

    /**
     * Retorna arquivamentos parseados
     *
     * @param string $response
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return array
     */
    private function getArquivamentos(string $response): array
    {
        if (preg_match("#ARQUIVAMENTOS\s*?\\n?(.*?)FIM\s*DAS\s*INFORMA#is", $response, $matches)) {
            return $this->parseArquivamentos($matches[1]);
        }

        return [];
    }

    /**
     * Retorna arquivamento parseado
     *
     * @param string $texto
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return array
     */
    private function parseArquivamentos(string $texto): array
    {
        $arquivamentos = [];

        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $aDoc = explode('NUM.DOC:', $texto);

        foreach ($aDoc as $doc) {
            $pattern = "#^\s*(.*?)\s*SESS.*?O:\s*(.*?)\s*\n(.*?)$#is";

            if (!preg_match($pattern, $doc, $matches)) {
                continue;
            }

            $jucespArquivamento = [];
            $jucespArquivamento['numeroDocumento'] = $matches[1];
            $jucespArquivamento['sessao'] = $matches[2];
            $ocorrencias = $this->parseOcorrencia(trim($matches[3]));
            $jucespArquivamento['atracoes'] = $ocorrencias;
            $arquivamentos[] = $jucespArquivamento;
        }

        return $arquivamentos;
    }

    /**
     * Retorna ocorrencia parseada
     *
     * @param string $ocorrencia
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return array
     */
    private function parseOcorrencia(string $ocorrencia): array
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $ocorrencias = explode("\n\n", $ocorrencia);
        $dadosOcorrencia = [];

        foreach ($ocorrencias as $ocorrencia) {
            $patterns = [
                'endereco' => ['#RESIDENTE\s?.\s?(.*?CEP.*?),#is', null],
                'cpf' => ['#CPF:(.*?),#is', null],
                'rg_rne' => ['#RG\/RNE:(.*?),#is', null],
                'valor' => ['#\$(.*?,\d{2}?)#is', null],
                'nire' => ['#NIRE:?\s*?([0-9\.\-]{1,}),#is', null],
                'nacionalidade' => ['#NACIONALIDADE\s?(\w+),#is', null],
                'documento' => ['#DOCUMENTO:?\s*?(\w{1,}),#is', null]
            ];
            $cargo = $this->parseCargo($ocorrencia);
            $nome = $this->parseNome($ocorrencia);
            $dados = Util::parseDados($patterns, $ocorrencia);

            $dados['texto'] = Str::cleanString(trim($ocorrencia));
            $dados['endereco'] = Str::cleanString(trim($dados['endereco']));
            $dados['representado'] = strpos($cargo, 'REPRESENTADO') !== false ? 1 : null;
            $dados['socio'] = strpos($cargo, 'SÓCIO') !== false ? 1 : null;
            $dados['administrador'] = strpos($cargo, 'ADMINISTRADOR') !== false ? 1 : null;
            $dados['capital'] = $dados['valor'];
            $dados['tipo'] = $this->parseTipo($ocorrencia);
            $dados['cargo'] = $cargo;
            $dados['nome'] = $nome;
            $dados['template'] = 'templates/others.html';
            $dados['capital'] = $this->capital;
            $dados['categorias'] = $this->parseCategories($dados);
            $dadosOcorrencia[] = $dados;
        }

        return $dadosOcorrencia;
    }

    /**
     * Retorna nome parseado
     *
     * @param string $texto
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return string
     */
    private function parseNome(string $texto): string
    {
        $patterns = [
            '/^REMANESCENTE\s*([\w\s\.]{1,}),?/',
            '/^NOMEADO\s*([\w\s\.]{1,}),?/',
            '/^CITADO\s*([\w\s\.]{1,}),?/',
            '/^ADMITIDO\s*([\w\s\.]{1,}),?/',
            '/^REDISTRIBUI.*DO\s*CAPITAL\s*DE\s*([\w\s\.]{1,}),?/',
            '/^DESTITUI.*\/REN.*CIA\sDE\s*([\w\s\.]{1,}),?/',
            '/^ALTERA.*\s*DO\s*NOME\s*EMPRESARIAL\s*PARA\s*([\w\s\.]{1,}),?/',
            '/^ALTERA.*\s*DOS\s*DADOS\s*CADASTRAIS\s*DE\s*([\w\s\.]{1,}),?/',
            '/^RETIRA\-SE\s*DA\s*[SOCIEDA]{1,}\s*([\w\s\.]{1,}),?/',
            '/^CARTA\s*RENUNCIA\s*DATADA\s*DE:\s*?[0-9]{2}\/[0-9]{2}\/[0-9]{4}\s*DE\s*(.*?),/'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $texto, $match)) {
                return $match[1];
            }
        }

        return '';
    }

    /**
     * Retorna tipo parseado
     *
     * @param string $occurrence
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return string
     */
    private function parseTipo(string $occurrence): string
    {
        $patterns = [
            1 => [
                '/^REMANESCENTE\s*([\w\s\.]{1,}),?/',
                '/^NOMEADO\s*([\w\s\.]{1,}),?/',
                '/^ADMITIDO\s*([\w\s\.]{1,}),?/',
                '/^REDISTRIBUI.*DO\s*CAPITAL\s*DE\s*([\w\s\.]{1,}),?/',
            ],
            2 => [
                '/^DESTITUI.*\/REN.*CIA\sDE\s*([\w\s\.]{1,}),?/',
                '/^RETIRA\-SE\s*DA\s*[SOCIEDA]{1,}\s*([\w\s\.]{1,}),?/',
                '/^CARTA\s*RENUNCIA\s*DATADA\s*DE:\s*?[0-9]{2}\/[0-9]{2}\/[0-9]{4}\s*DE\s*(.*?),/'
            ]
        ];

        foreach ($patterns as $key => $indexes) {
            foreach ($indexes as $pattern) {
                if (preg_match($pattern, $occurrence)) {
                    return $key;
                }
            }
        }

        return '';
    }

    /**
     * Retorna categorias padrões do texto
     * Método criado para não precisar procurar no banco as categorias mais faceis
     * procurando todas no banco muitos critérios dão timeout do lambda
     *
     * @param string $text
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return array
     */
    public function getDefaultCategories(string $text): array
    {

        $dafultCategories = (new JucespFichaDefaultCategories())->categories;

        foreach ($dafultCategories as $key => $category) {
            $categoryRegex = str_replace(' ', '.', $key);

            if (preg_match("#{$categoryRegex}#is", $text)) {
                return $category;
            }
        }

        return [];
    }

    /**
     * Retorna categoria parseada
     *
     * @param array &$dados
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return array
     */
    private function parseCategories(array &$dados): array
    {
        $text = trim($dados['texto']);

        //caso seja uma categoria default faz o parse por regex para não precisar ir no banco
        $defaultCategories = $this->getDefaultCategories($text);

        if (!empty($defaultCategories)) {
            $this->templatesVerify($dados, $defaultCategories);
            return $defaultCategories;
        }

        return array('OUTROS');
    }

    /**
     * Seta novo template caso tenha no array defaultCategories
     *
     * @param array &$dados
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return array
     */
    private function templatesVerify(array &$dados, &$defaultCategories): void
    {
        if (array_key_exists('templates', $defaultCategories)) {
            $dados['template'] = $defaultCategories['templates'];
            unset($defaultCategories['templates']);
        }
    }

    /**
     * Limpa o texto
     *
     * @param string $text
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return string
     */
    private function cleanText(string $text): string
    {
        $text = preg_replace(
            '/[0-9]{2}\.[0-9]{3}\.[0-9]{3}\/[0-9]{4}\-[0-9]{2}/',
            '',
            $text
        );

        $text = preg_replace(
            "/\ (DA|DAS|DE|DO|DOS|PARA)$/",
            '',
            $text
        );

        $text = preg_replace(
            '/\ \ /',
            ' ',
            $text
        );

        return $text;
    }

    /**
     * Transforma o texto em array
     *
     * @param string $string
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return array
     */
    private function tokenize(string $string): array
    {
        return explode(" ", $string);
    }

    /**
     * Monta a estrutura retornada da tabela via NestedSet
     *
     * @param NestedSetModel $nestedset
     * @param string $name
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 02/12/2020
     *
     * @return void
     */
    private function setStructure(NestedSetModel $nestedset, string $name)
    {
        $nodes = $nestedset->getElementByName($name);
        $this->structure = $nestedset->toStructure(
            $nestedset->toArray($nodes)
        );
    }
}
