<?php

namespace App\Crawler\ChatGPT;

use App\Crawler\Spider;
use App\Manager\ChatGPT\ChatGPTManager;
use Exception;

class ChatGPT extends Spider
{
    /**
     * @var string
     */
    private string $name;

    /**
     * @var string
     */
    private string $context;

    public function start()
    {
        $chatGPTManager = new ChatGPTManager();
        return $chatGPTManager->getCompany($this->name, $this->context);
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty(trim($this->param['name']))) {
            throw new Exception('Parâmetros inválidos', 3);
        }

        if (empty(trim($this->param['context']))) {
            throw new Exception('Parâmetros inválidos', 3);
        }

        $this->name = $this->param['name'];
        $this->context = $this->param['context'];
    }
}
