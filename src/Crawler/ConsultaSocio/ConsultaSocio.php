<?php

namespace App\Crawler\ConsultaSocio;

use App\Crawler\Spider;
use Exception;
use App\Helper\Client;
use App\Helper\Str;
use App\Helper\Util;
use Symfony\Component\DomCrawler\Crawler;

class ConsultaSocio extends Spider
{
    private const ENTERPRISE_LIMIT = 30;

    private $url = 'https://www.quadrosocietario.com/';

    private $personalCounter = 0;

    private $limite = 25;

    /**
     * @var Goutte\Client;
     */
    private $client;

    protected function start()
    {
        $this->config();

        $parsedResults = [];
        $lastPage = false;
        $crawler = $this->client->request('GET', $this->url);

        $searchNome = trim(Str::removeSiglas($this->name));

        $searchForm = $crawler->selectButton('Pesquisar')->form();
        $searchResult = $this->client->submit($searchForm, ['keyword' => $searchNome]);


        $results = $searchResult->filter('div.results.cell')->first();

        while ($lastPage == false) {
            $parsedResults[] = $this->getResults($results);

            if ($results->selectLink('Próximo ->')->count()) {
                $link = $results->selectLink('Próximo ->')->link();
                $results = $this->client->click($link);
                continue;
            }

            $lastPage = true;
        }

        $flatted = Util::arrayFlatten($parsedResults, 1);
        $results = $this->checkSimilarities($flatted, $this->name, $this->similarity);

        if (empty($results)) {
            throw new Exception('Nenhum registro encontrado com esse nome!', 2);
        }

        return $results;
    }

    /**
     * Verifica se a busca tem resultados e parseia
     *
     * @param Crawler $results
     * @return array
     */
    private function getResults(Crawler $results)
    {
        $parsedResult = [];
        $noResults = $results->filter('.no_result')->count();

        if ($noResults > 0) {
            throw new Exception('Nenhum registro encontrado com esse nome!', 2);
        }

        $parsedResult = $results->filter('div.result')->each(function (Crawler $node) {
            if ($this->personalCounter === $this->limite) {
                return false;
            }

            $description = $node->filter('p.description')->first()->text();
            if (preg_match('@n.o\s.\ss.cio.*@iu', $description)) {
                return [
                    'nome' => $node->filter('h3 > span')->first()->text(),
                    'qtdEmpresas' => 0,
                    'capitalSocial' => 0,
                    'primeiraSociedade' => null,
                    'descricao' => trim($description),
                    'socios' => [],
                    'empresas' => [],
                ];
            }

            $link = $node->filter('a')->first()->attr('href');
            $person = $this->getPersonalDetails($link);

            $this->personalCounter++;
            return $person;
        });

        return array_values(array_filter($parsedResult));
    }

    /**
     * Pega as informações da Pessoa e suas Empresas
     *
     * @param string $link
     * @return array
     * <AUTHOR> Hugo <<EMAIL>>
     * @version 1.0.0 - Criação do metodo
     *          1.1.0 - Otimização da velocidade na paginação
     */
    private function getPersonalDetails(string $link)
    {

        $page = $this->client->request('GET', "{$this->url}$link");

        $result = $page->filter('div > h1')->first()->text();
        if (preg_match('/page not found/iu', $result)) {
            return false;
        }

        $enterprises = [];

        // Bloco principal dos detalhes
        $informationBlock = $page->filter('div[style="margin-left:320px"]')->first();

        // Div dos detalhes
        $detailedBlock = $informationBlock->filter('div')->eq(0)->filter('p');

        $detailed = $this->sanitizeDetailedBlock($detailedBlock);

        $lastPage = false;
        while ($lastPage == false) {
            $this->getEnterprisesFromPage($page, $enterprises);

            if (count($enterprises) === self::ENTERPRISE_LIMIT) {
                $lastPage = true;
                break;
            }

            if ($page->selectLink('Próximo ->')->count()) {
                $link = $page->selectLink('Próximo ->')->link();
                $page = $this->client->click($link);
                continue;
            }

            $lastPage = true;
        }

        return [
            'nome' => $page->filter('div > h2')->first()->text(),
            'qtdEmpresas' => count($enterprises),
            'capitalSocial' => $detailed['capitalSocial'],
            'primeiraSociedade' => $detailed['primeiraSociedade'],
            'descricao' => null,
            'socios' => $detailed['socios'],
            'empresas' => $enterprises,
        ];
    }

    /**
     * Pega as informações das empresas da página da pessoa
     *
     * @param Crawler $page
     * @param array $enterprises
     * @return void
     * <AUTHOR> Hugo R. <<EMAIL>>
     * @version 1.0.0
     */
    private function getEnterprisesFromPage(Crawler $page, &$enterprises)
    {
        // Bloco principal dos detalhes
        $informationBlock = $page->filter('div[style="margin-left:320px"]')->first();

        // Div das empresas
        $enterprisesBlock = $informationBlock->filter('div.bd .info');

        foreach ($enterprisesBlock as $enterprise) {
            $enterprise = new Crawler($enterprise);

            $data = [
                'razaoSocial' => '',
                'cnpj' => '',
                'nomeFantasia' => '',
                'endereco' => '',
                'capitalSocial' => '',
                'atividadeEconomica' => '',
                'naturezaJuridica' => '',
                'dataAbertura' => '',
                'telefone' => '',
                'email' => '',
            ];

            $email = $enterprise->filter('a.__cf_email__');
            $count = $enterprise->filter('.cnpj p');

            foreach (range(0, $count->count() - 1) as $position) {
                $exists = $enterprise->filter('.cnpj p')->eq($position);

                if ($exists->count() == 0) {
                    continue;
                }

                $text = $enterprise->filter('.cnpj p')->eq($position)->html();

                if (preg_match('@n.o\sh.\sinforma..es@iu', $text)) {
                    continue;
                }

                $key = Str::camelCase(strtok($text, ':'));

                if ($key === 'eMail') {
                    continue;
                }

                if ($key === 'telefoneContato') {
                    $data['telefone'] = $this->sanitizeDetailField($text);
                    continue;
                }

                if (isset($data[$key])) {
                    $data[$key] = $this->sanitizeDetailField($text);
                }
            }

            if ($email->count() > 0) {
                $data['email'] = $this->cfDecodeEmail($email->attr('data-cfemail'));
            }

            $enterprises[] = $data;
            if (count($enterprises) === self::ENTERPRISE_LIMIT) {
                break;
            }
        }
    }

    /**
     * Pega as informações dos blocos de detalhes
     *
     * @param Crawler $detailedBlock
     * @return array
     */
    private function sanitizeDetailedBlock(Crawler $detailedBlock)
    {
        $data = [
            'capitalSocial' => '',
            'primeiraSociedade' => '',
            'socios' => [],
        ];

        $detailedBlock->each(function ($node) use (&$data) {
            if (preg_match('@capital\ssocial.\s(.*).@iu', $node->text(), $capitalSocial)) {
                $data['capitalSocial'] = $capitalSocial[1];
            }

            if (preg_match('@primeira\s*sociedade@iu', $node->text())) {
                $data['primeiraSociedade'] = $node->filter('strong')->first()->text();
            }

            if (preg_match('@s.cios\s*de(?:[\s\S]*):@iu', $node->text())) {
                $text = trim(substr_replace($node->text(), "", 0, strrpos($node->text(), ":") + 1));
                $socios = array_map('trim', explode(',', $text));

                $data['socios'] = $socios;
            }
        });

        return $data;
    }

    /**
     * Trata as informações dos Detalhes da Empresa
     *
     * @param string $text
     * @return string
     */
    private function sanitizeDetailField($text)
    {
        $text = str_replace(array('<br>', '<\br>', '<\ br>'), " ", $text);

        $text = substr($text, strpos($text, ": ") + 1);

        return trim(strip_tags($text), ".- ");
    }

    /**
     * Quebra a codificação de proteção de e-mails da CloudFlare
     *
     * @param string $encodedString
     * @return string
     * <AUTHOR> Hugo R. <<EMAIL>>
     * @version 1.0.0
     */
    private function cfDecodeEmail($encodedString)
    {
        $k = hexdec(substr($encodedString, 0, 2));

        for ($i = 2, $email = ''; $i < strlen($encodedString) - 1; $i += 2) {
            $email .= chr(hexdec(substr($encodedString, $i, 2)) ^ $k);
        }

        return filter_var(strtolower($email), FILTER_SANITIZE_EMAIL);
    }

    /**
     * Undocumented function
     *
     * @return array
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['nome'])) {
            throw new Exception("Parâmetro nome é obrigatório!", 1);
        }

        if (!empty($this->param['limite'])) {
            $this->limite = $this->param['limite'];
        }

        if (empty($this->param['similaridade'])) {
            $this->param['similaridade'] = 100;
        }

        $this->name = Str::removerAcentos($this->param['nome']);
        $this->similarity = (int) $this->param['similaridade'];
    }

    /**
     * Define as configurações do Client
     *
     * @return void
     * <AUTHOR> Hugo R. <<EMAIL>>
     */
    private function config()
    {
        $this->client = (new Client())->createGoutteClient((new Client())->createHttpClient([], 'native'));
    }

    /**
     * Percorre o resultado e verifica a similaridade do criterio pesquisado
     *
     * <AUTHOR> Hugo R. <<EMAIL>> - 2018-08-01
     * @version 1.1.0 - Maximiliano Minucelli 05/12/18 - Add similaridade no resultado
     *
     * @param array $results
     * @param string $criterio
     * @param integer $similarity
     *
     * @return array
     */
    private function checkSimilarities($results, $criterio, $similarity = 100)
    {
        $hasSimilarity = [];

        foreach ($results as $person) {
            $check = Str::hasSimilarityValid($person, $criterio, $similarity, 'nome', null, false);

            if ($check['check'] === true) {
                $person['similarity'] = $check['similarity'];
                $hasSimilarity[] = $person;
            }
        }

        return $hasSimilarity;
    }
}
