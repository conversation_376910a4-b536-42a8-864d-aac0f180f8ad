<?php

namespace App\Crawler\Icmbio;

use App\Crawler\Spider;
use App\Helper\Str;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Carbon\Carbon;
use Exception;
use Symfony\Component\DomCrawler\Crawler;

class Icmbio extends Spider
{
    private const BASE_URL = "https://www.gov.br";
    private const API_URL = "https://portalunico.estaleiro.serpro.gov.br/api/search/";

    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const FILE_S3_PATH = 'captura/icmbio/';

    private $criterio;
    private $limite;
    private $count_results = 0;
    private $data = [];


    public function start()
    {
        $this->search();

        return $this->data;
    }


    /** Busca dados na página
     *
     * <AUTHOR> - 12 jul. 22
     *
     * @param  int  $page
     *
     * @throws Exception
     */
    private function search(int $page = 1)
    {
        $params = [
            'q' => $this->criterio,
            'aba' => 'noticias',
            'ordenacao' => '-relevancia',
            'site' => '//www.gov.br/icmbio',
            'categoriasFiltro' => '',
            'orgaosFiltro' => '',
            'orgaoId' => 'instituto-chico-mendes-de-conservacao-da-biodiversidade',
            'tipo' => 'ServicoEstadual|collective.nitf.content|App|Campanha|Servico|Site|Document' .
                '|News Item|sc.embedder|Link|File|Tema|Audio|Image|ExternalContent',
            'pagina' => $page,
            'tam_pagina' => 30
        ];

        $url = self::API_URL . "?" . http_build_query($params);
        $response = json_decode($this->getResponse($url));

        if ($response->total == 0) {
            throw new Exception('Nenhum resultado encontrado.', 2);
        }

        foreach ($response->items as $idx => $value) {
            if ($value->type == "File") {
                $result = $this->saveFile(self::BASE_URL . $value->contentUrl);
                $this->data[$idx]['title'] = $result['name'];
                $this->data[$idx]['file'] = $result['fileUrl'];
            } else {
                $content = $this->parseContent(self::BASE_URL . $value->contentUrl);
                $this->data[$idx]['title'] = $value->title;
                $this->data[$idx]['description'] = $content;
            }

            $this->data[$idx]['link'] = self::BASE_URL . $value->contentUrl;
            $this->data[$idx]['created_at'] = Carbon::parse($value->createdAt)->format('d/m/Y H\hi');
            $this->data[$idx]['modified_at'] = Carbon::parse($value->modifiedAt)->format('d/m/Y H\hi');

            $this->count_results++;

            if ($this->limite == $this->count_results) {
                break;
            }
        }
        if ($response->total > $this->count_results && $this->limite > $this->count_results) {
            //Chamada recursiva para mais resultados.
            $this->search($page + 1);
        }
    }

    /** Salva arquivo no S3 (se houver)
     *
     * @return array
     * <AUTHOR> Santos - 19 jul. 22
     * @param  string  $url
     * @throws \Exception
     */
    private function saveFile(string $url)
    {
        $response = $this->getResponse($url);

        $crawler = new Crawler($response);
        $file = $crawler->filter('#content-core > p > a')->attr('href');
        $name = $crawler->filter('#content-core > p')->text();

        $uniqid = md5(uniqid(rand(), true));
        $fileName = "{$uniqid}.pdf";
        $fileLocalPath = "/tmp/{$fileName}";
        $fileS3Path = self::FILE_S3_PATH . $fileName;
        $fileUrl = self::S3_STATIC_PATH . $fileS3Path;

        file_put_contents($fileLocalPath, file_get_contents($file));
        (new S3(new StaticUplexisBucket()))->save($fileS3Path, $fileLocalPath);

        return ['fileUrl' => $fileUrl, 'name' => $name];
    }

    /** Busca notícia na íntegra
     *
     * @return string
     * <AUTHOR> Santos - 12 jul. 22
     *
     * @param  string  $url
     *
     * @throws Exception
     */
    private function parseContent(string $url): string
    {
        $response = $this->getResponse($url);

        $crawler = new Crawler($response);
        $text = $crawler->filter('#content-core > #parent-fieldname-text')->html();
        $text = preg_replace('/<(?:"[^"]*"[\'"]*|\'[^\']*\'[\'"]*|[^\'">])+>/m', ' ', $text);
        return Str::mbTrim($text);
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->criterio = trim($this->param['criterio']);
        $this->limite = $this->param['limite'] ?? 10;

        if (empty($this->criterio)) {
            throw new Exception('Parâmetro de critério inválido', 1);
        }
    }
}
