<?php

namespace App\Crawler\OfacInstant;

use App\Crawler\Spider;
use App\Helper\Str;
use App\Helper\Util;
use Exception;

class OfacInstant extends Spider
{
    private const URL_FORM = "https://sanctionssearch.ofac.treas.gov/";
    private const PARAM    = "nome";

    private $formViewstate = '';
    private $formViewstateGenerator = '';
    private $formHiddenField = '';
    private $arrayDetailIds = [];


    /**
     * Valida parâmetros da fonte
     *
     * <AUTHOR> <PERSON> 26/03/2019
     *
     * @version 1.0.0
     *
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (!isset($this->param[self::PARAM]) || empty($this->param[self::PARAM])) {
            throw new Exception("Critério não informado ou inválido.", 1);
        }
    }

    /**
     *  Início da execução da fonte
     *
     *  <AUTHOR> 26/03/2019
     *
     *  @version 1.0.0
     *
     *  @return array
     *
     */
    protected function start()
    {
        $this->setAlternativeProxy();

        $html = $this->getResponse(
            self::URL_FORM,
            'GET',
            [],
            array('Upgrade-Insecure-Requests: 1')
        );

        $this->getFormValues($html);
        $htmlPost = $this->getPostHtml($html);
        $this->verifyError($htmlPost);
        $arrRows = $this->getTrsResult($htmlPost);
        $result = $this->getArrayResponse($arrRows);

        return $result;
    }

    /**
     * Retorna o html do resultado da pesquisa
     *
     * <AUTHOR> Mesquita 26/03/2019
     *
     * @version 1.0.0
     *
     * @return String html
     */
    private function getPostHtml()
    {
        $postParams = [];
        $postParams['ctl00_ctl03_HiddenField']          = $this->formHiddenField;
        $postParams['__VIEWSTATE']                      = $this->formViewstate;
        $postParams['__VIEWSTATEGENERATOR']             = $this->formViewstateGenerator;
        $postParams['ctl00$MainContent$ddlType']        = ''; //Quando vazio seleciona ALL
        $postParams['ctl00$MainContent$txtLastName']    = $this->param[self::PARAM];
        $postParams['ctl00$MainContent$Slider1']        = 100; //Minimum Name Score
        $postParams['ctl00$MainContent$Slider1_Boundcontrol'] = 100;
        $postParams['ctl00$MainContent$btnSearch']      =   'Search';

        $postHtml = $this->getResponse(self::URL_FORM, 'POST', $postParams, array('Upgrade-Insecure-Requests: 1'));

        if ($this->debug) {
            file_put_contents('/var/www/testofac.html', $postHtml);
        }

        return $postHtml;
    }

    /**
     * Recupera dados e preenche variáveis necessárias para envio do form
     *
     * <AUTHOR> Mesquita 26/03/2019
     *
     * @version 1.0.0
     *
     * @param  String html de retorno dos resultados da busca
     *
     */
    private function getFormValues($html)
    {
        preg_match('/<input.*id=.__VIEWSTATE.\s*.value=.(.*?).\s\/>/i', $html, $output);

        if (count($output) == 0 || !isset($output[1]) || empty($output[1])) {
            throw new Exception('Falha ao obter o parâmetro __VIEWSTATE do formuláro', 1);
        }

        $this->formViewstate = $output[1];
        preg_match('/<input.*id=.__VIEWSTATEGENERATOR.\s*.value=.(.*?).\s\/>/i', $html, $output);

        if (count($output) == 0 || !isset($output[1]) || empty($output[1])) {
            throw new Exception('Falha ao obter o parâmetro __VIEWSTATEGENERATOR do formuláro', 1);
        }

        $this->formViewstateGenerator = $output[1];
        preg_match('/=ctl00_ctl03_HiddenField&amp;_TSM_CombinedScripts_=(.*)"\s/i', $html, $output);

        if (count($output) == 0 || !isset($output[1]) || empty($output[1])) {
            throw new Exception('Falha ao obter o parâmetro ctl00_ctl03_HiddenField do formuláro', 1);
        }

        $this->formHiddenField = urldecode($output[1]);
    }

    /**
     * Retorna todas as Trs da tabela de resultados
     *
     * <AUTHOR> Mesquita 26/03/2019
     *
     * @version 1.0.0
     *
     * @param  String html de retorno da busca
     *
     * @return Array
     */
    private function getTrsResult($html)
    {
        $xPath = '//*[@id="gvSearchResults"]/tr';
        $trs = Util::queryXPath($html, $xPath);
        return $trs;
    }

    /**
     * Retorna os detalhes para o parâmetro informado
     *
     * <AUTHOR> Mesquita 26/03/2019
     *
     * @version 1.0.0
     *
     * @param  Array de Trs de resultados
     *
     * @return Array com os nomes e detalhes encontrados
     */
    private function getArrayResponse($arrRows)
    {
        $arrayResult = [];

        foreach ($arrRows as $htmlTr) {
            $result = $this->getDetail($htmlTr);

            if (is_array($result) && count($result) > 0) {
                $arrayResult[] = $result;
            }
        }
        return $arrayResult;
    }

    /**
     * Retorna o html do resultado da pesquisa
     *
     * <AUTHOR> Mesquita 26/03/2019
     *
     * @param  String htmlTr
     *
     * @version 1.0.0
     *
     * @return String html
     */
    private function getDetail($htmlTr)
    {
        $arrTds = $this->getTdsResult($htmlTr);

        $score = "";
        $type  = "";

        if (count($arrTds) > 0 && isset($arrTds[5])) {
            $score = trim($arrTds[5]);
        }

        if (count($arrTds) > 0 && isset($arrTds[2])) {
            $type = trim($arrTds[2]);
        }

        if ($type != "Entity" && $type != "Individual") {
            throw new Exception("Não foi encontrado resultado para esta consulta", 2);
        }

        $detailId = $this->getDetailId($htmlTr);

        if (in_array($detailId, $this->arrayDetailIds)) {
            return null;
        }

        $this->arrayDetailIds[] = $detailId;

        $htmlDetail = $this->getDetailHtml($detailId);
        $result_alias = $this->getAliases($htmlDetail);

        $result = [];
        $result['score'] =  $score;
        $result['sdn_number'] = '';
        $result['name'] = $this->getName($htmlDetail, $type);
        $result['title'] = $this->getTitle($htmlDetail);
        $result['aAlias'] = $result_alias;
        $result['address'] = $this->getAddress($htmlDetail);
        $result['aRemark'] = $this->getRemarks($htmlDetail);

        $response = Str::encoding($result);

        return $response;
    }

    /**
     * Retorna o html da página de detalhes
     *
     * <AUTHOR> Mesquita 26/03/2019
     *
     * @param  Int id da página de detalhes
     *
     * @version 1.0.0
     *
     * @return String html da página de detalhe
     */
    private function getDetailHtml($detailId)
    {
        $htmlDetail = $this->getResponse(
            self::URL_FORM . "Details.aspx?id=" . $detailId,
            'GET',
            [],
            array('Upgrade-Insecure-Requests: 1')
        );

        return $htmlDetail;
    }

    /**
     * Retorna Array com as tds dos resultados da busca
     *
     * <AUTHOR> Mesquita 26/03/2019
     *
     * @param  String htmlTr
     *
     * @version 1.0.0
     *
     * @return Array Tds dos resultados da busca
     */
    private function getTdsResult($htmlTr)
    {
        $xPath = '//td';
        $tds = Util::queryXPath($htmlTr, $xPath);
        return $tds;
    }

    /**
     * Pega o ID da página de detalhes
     *
     * <AUTHOR> Mesquita 26/03/2019
     *
     * @param  String html
     *
     * @version 1.0.0
     *
     * @return Int com ID da página de detalhes
     */
    private function getDetailId($html)
    {
        preg_match('/Details.aspx\?id=([0-9]*)/i', $html, $match);

        if (empty($match) || empty($match[1])) {
            throw new Exception('ID na resposta do formulário não encontrado', 3);
        }

        return trim($match[1]);
    }

    /**
     * Verificar se houve erros ao efetuar a pesquisa e tratar
     *
     * <AUTHOR> Mesquita 26/03/2019
     *
     * @param  String html
     *
     * @version 1.0.0
     *
     */
    private function verifyError($html)
    {
        $message = $this->getErrorMessage($html);

        if ($message == "Your search has not returned any results.") {
            throw new Exception("Não foi encontrado resultado para esta consulta", 2);
        }

        if ($message == "Your search returned too many results. Please narrow your search by adding search criteria.") {
            throw new Exception(
                "Sua pesquisa retornou muitos resultados. Forneça mais informações para este critério",
                6
            );
        }
    }

    /**
     * Recupera uma mensagem de erro caso exista
     *
     * <AUTHOR> Mesquita 26/03/2019
     *
     * @param  String html
     *
     * @version 1.0.0
     *
     * @return String Mensagem de erro
     *
     */
    private function getErrorMessage($html)
    {
        preg_match('/<span\sid=\"ctl00_MainContent_lblMessage\"\s?class=.errormessage.>(.*)<\/span>/i', $html, $output);

        $errorMessage = "";

        if (count($output) > 0 && isset($output[1]) && !empty($output[1])) {
            $errorMessage = trim($output[1]);
        }

        return  trim($errorMessage);
    }

    /**
     * Verifica o tipo para saber qual informação será necessária para o nome
     *
     * <AUTHOR> Mesquita 26/03/2019
     *
     * @param  String html
     *
     * @version 1.0.0
     *
     * @return String com o Nome
     *
     */
    private function getName($html, $type)
    {
        if ($type == 'Entity') {
            return $this->getEntityName($html);
        }

        if ($type == 'Individual') {
            $nome = $this->getLastName($html);

            if (strlen($nome) > 0) {
                $nome .= ", ";
            }

            $nome .= $this->getFirstName($html);
            return $nome;
        }
    }

    /**
     * Pegar o último nome
     *
     * <AUTHOR> Mesquita 26/03/2019
     *
     * @param  String html
     *
     * @version 1.0.0
     *
     * @return  String com o útimo nome
     *
     */
    private function getLastName($html)
    {
        preg_match('/<span\sid=\"ctl00_MainContent_lblLastName\">(\w*)/i', $html, $output);
        $lastName = "";

        if (count($output) > 0 && isset($output[1]) && !empty($output[1])) {
            $lastName = trim($output[1]);
        }

        return  $lastName;
    }

    /**
     * Pegar o primeiro nome
     *
     * <AUTHOR> Mesquita 26/03/2019
     *
     * @param  String html
     *
     * @version 1.0.0
     *
     * @return  String com o primeiro nome
     *
     */
    private function getFirstName($html)
    {
        preg_match('/<span\sid=\"ctl00_MainContent_lblFirstName\">(\w*)/i', $html, $output);

        $firstName = "";

        if (count($output) > 0 && isset($output[1]) && !empty($output[1])) {
            $firstName = trim($output[1]);
        }

        return  $firstName;
    }

    /**
     * Pegar o nome da empresa
     *
     * <AUTHOR> Mesquita 26/03/2019
     *
     * @param  String html
     *
     * @version 1.0.0
     *
     * @return  String com o nome da empresa
     *
     */
    private function getEntityName($html)
    {
        preg_match('/<span\sid=\"ctl00_MainContent_lblNameOther\">(.*)<\/span>/i', $html, $output);
        $firstName = "";

        if (count($output) > 0 && isset($output[1]) && !empty($output[1])) {
            $firstName = trim($output[1]);
        }

        return  $firstName;
    }

    /**
     * Pegar o título
     *
     * <AUTHOR> Mesquita 26/03/2019
     *
     * @param  String html
     *
     * @version 1.0.0
     *
     * @return  String com o título
     *
     */
    private function getTitle($html)
    {
        preg_match('/<span\sid=\"ctl00_MainContent_lblTitle\">(\w*)/i', $html, $output);
        $firstTitle = "";

        if (count($output) > 0 && isset($output[1]) && !empty($output[1])) {
            $firstTitle  = trim($output[1]);
        }

        return  $firstTitle;
    }

    /**
     * Pegar o todos os Alias
     *
     * <AUTHOR> Mesquita 26/03/2019
     *
     * @param  String html
     *
     * @version 1.0.0
     *
     * @return  Array de Alias
     *
     */
    private function getAliases($html)
    {
        $xPath = '//*[@id="ctl00_MainContent_gvAliases"]/tr';
        $aliasesTrs = Util::queryXPath($html, $xPath);
        $resultArray = [];

        unset($aliasesTrs[0]); //Retirar cabeçalho

        foreach ($aliasesTrs as $index => $aliasesTr) {
            $xPathTds = '//td';
            $aliasesTds = Util::queryXPath($aliasesTr, $xPathTds);
            $alias = $this->renderAliases($aliasesTds);
            $resultArray[$index] = $alias;
        }

        return $resultArray;
    }

    /**
     * Monta as informações corretas de Alias
     *
     * <AUTHOR> Mesquita 26/03/2019
     *
     * @param  Array Tds com os Alias
     *
     * @version 1.0.0
     *
     * @return  String Alias
     *
     */
    private function renderAliases($aliasesTds)
    {
        if (count($aliasesTds) < 3) {
            throw new Exception('Falha ao carregar os Alias', 1);
        }

        return trim($aliasesTds[2]);
    }

    /**
     * Pegar Observações
     *
     * <AUTHOR> Mesquita 26/03/2019
     *
     * @param  String html
     *
     * @version 1.0.0
     *
     * @return  Array de observações
     *
     */
    private function getRemarks($html)
    {
        preg_match(
            '/<span\b\sid=.(ctl00_MainContent_lblRemarksOther|ctl00_MainContent_lblRemarks).>\(?(.*?)\)?<\/span>/i',
            $html,
            $output
        );

        if (count($output) == 0 || !isset($output[2])) {
            return null;
        }

        $retirar = array(")", "(", "Linked To:");
        $remarks = str_replace($retirar, "", $output[2]);
        $remarks = explode(';', strip_tags($remarks));
        $resultArray = [];
        foreach ($remarks as $remark) {
            if (!empty(trim($remark))) {
                $resultArray[] = trim($remark);
            }
        }
        return $resultArray;
    }

    /**
     * Pegar endereços
     *
     * <AUTHOR> Mesquita 26/03/2019
     *
     * @param  String html
     *
     * @version 1.0.0
     *
     * @return  Array de endereços
     *
     */
    private function getAddress($html)
    {
        $xPath = '//*[@id="ctl00_MainContent_pnlAddress"]/table/tr';
        $AddressTrs = Util::queryXPath($html, $xPath);
        $resultArray = [];

        foreach ($AddressTrs as $AddressTr) {
            preg_match('/<td\b\s*[^>]*?>(.*?)<\/td>/i', $AddressTr, $output);

            if (!empty(trim($output[1]))) {
                $resultArray[] = trim($output[1]);
            }
        }
        return $resultArray;
    }
}
