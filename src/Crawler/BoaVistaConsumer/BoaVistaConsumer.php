<?php

namespace App\Crawler\BoaVistaConsumer;

use App\Crawler\BoaVistaConsumer\BoaVistaScpcNetService;
use App\Crawler\BoaVistaConsumer\BoaVistaService;
use App\Crawler\Spider;
use App\Helper\Document;
use Exception;

class BoaVistaConsumer extends Spider
{
    private const URL = "https://www.bvsnet.com.br/cgi-bin/db2www/netpo028.mbr/string?consulta=";
    private const USER = 610494;
    private const PASS = "78VE5T";
    private const CONSULTAS = [
        'localiza_informacoes_cadastrais',
        'localiza_capital_social',
        'localiza_ramos_atividades_secundario',
        'localiza_participantes',
        'localiza_por_razao_social',
        'localiza_falencias_e_recuperacao_judicial',
        'localiza_titulos_protestados',
        'localiza_acoes_civeis',
        'localiza_registros_de_debitos',
        'localiza_cheques_sem_fundos',
        'localiza_devolucoes_informadas',
        'localiza_cheques_sustados',
        'localiza_relacao_de_passagens',
        'localiza_relacao_de_empresas_mesmo_endereco',
        'localiza_scpc_net',
    ];
    private $consumerMethod = '';
    private $documento;
    private $consulta;

    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['documento'])) {
            throw new Exception('É necessário um Documento para realizar a consulta');
        }

        if (!Document::validarCpfOuCnpj($this->param['documento'])) {
            throw new Exception('Documento inválido');
        }

        if (empty($this->param['consulta'])) {
            throw new Exception('É necessário informar a consulta');
        }

        if (!in_array($this->param['consulta'], self::CONSULTAS)) {
            throw new Exception('Consulta informada inválida');
        }
    }

    /**
     * First and default method
     *
     * @return void
     */
    protected function start()
    {
        try {
            $service = new BoaVistaService(self::USER, self::PASS, self::URL, $this);
            $scpcNet = new BoaVistaScpcNetService(self::USER, self::PASS, self::URL);

            switch ($this->param['consulta']) {
                case 'localiza_informacoes_cadastrais':
                    return $service->localizaInformacoesCadastrais($this->param);
                    break;

                case 'localiza_capital_social':
                    return $service->localizaCapitalSocial($this->param);
                    break;

                case 'localiza_ramos_atividades_secundario':
                    return $service->localizaRamosAtividadesSecundario($this->param);
                    break;

                case 'localiza_participantes':
                    return $service->localizaParticipantes($this->param);
                    break;

                case 'localiza_falencias_e_recuperacao_judicial':
                    return $service->localizaFalenciasERecuperacaoJudicial($this->param);
                    break;

                case 'localiza_titulos_protestados':
                    return $service->localizaTitulosProtestados($this->param);
                    break;

                case 'localiza_acoes_civeis':
                    return $service->localizaAcoesCiveis($this->param);
                    break;

                case 'localiza_registros_de_debitos':
                    return $service->localizaRegistrosDeDebitos($this->param);
                    break;

                case 'localiza_cheques_sem_fundos':
                    return $service->localizaChequesSemFundos($this->param);
                    break;

                case 'localiza_devolucoes_informadas':
                    return $service->localizaDevolucoesInformadas($this->param);
                    break;

                case 'localiza_cheques_sustados':
                    return $service->localizaChequesSustados($this->param);
                    break;

                case 'localiza_relacao_de_passagens':
                    return $service->localizaRelacaoDePassagens($this->param);
                    break;

                case 'localiza_relacao_de_empresas_mesmo_endereco':
                    return $service->localizaRelacaoDeEmpresasMesmoEndereco($this->param);
                    break;

                case 'localiza_scpc_net':
                    return $scpcNet->searchByDocument($this->param);
                    break;
            }
        } catch (Exception $e) {
            throw $e;
        }

        throw new Exception('Não possível localizar a consulta');
    }

    public function callSpiderUpdateSpine($source, $data)
    {
        $this->updateSpine($source, $data);
    }
}
