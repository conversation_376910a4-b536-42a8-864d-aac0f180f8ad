<?php

namespace App\Crawler\BoaVistaConsumer;

use Exception;
use GuzzleHttp\Client as GuzzleClient;
use App\Helper\BilhetadorFornecedoresDynamo;

/**
 * Classe para centralizar as chamadas à API da UnitFour
 *
 * <AUTHOR> <<EMAIL>>
 */
class BoaVistaService
{
    /** @var string */
    protected $usuario;

    /** @var string */
    protected $senha;

    /** @var string */
    protected $endpoint;

    /**
     *  Passo a referencia para chamar o updateSpine
     */
    protected $parentClass;

    /**
     * Construtor da classe
     * @param string $usuario
     * @param string $senha
     * @param string $endpoint
     */
    public function __construct($usuario, $senha, $endpoint, $parentClass)
    {
        $this->usuario  = $usuario;
        $this->senha    = $senha;
        $this->endpoint = $endpoint;
        $this->parentClass = $parentClass;
    }

    /**
     * Metodo para localizar informacoes cadastrais
     *
     * @param array $parametros
     *
     * @return array
     */
    public function localizaInformacoesCadastrais($parametros)
    {
        $texto = $this->search($parametros['documento'], null, 'CERTOCR');

        $dados = array(
            'CNPJ'                   => trim(substr($texto, 7, 14)),
            'NIRE'                   => trim(substr($texto, 21, 11)),
            'NOME'                   => trim(substr($texto, 32, 115)),
            'CONDICAO'               => $this->getConditionType(trim(substr($texto, 147, 1))),
            'DATA_FUNDACAO'          => $this->dateFormat((substr($texto, 148, 8))),
            'INSCRICAO'              => trim(substr($texto, 156, 14)),
            'SITUACAO'               => $this->getStateInscriptionType(trim(substr($texto, 170, 1))),
            'ENDERECO'               => trim(substr($texto, 171, 70)),
            'BAIRRO'                 => trim(substr($texto, 241, 20)),
            'CEP'                    => trim(substr($texto, 261, 8)),
            'CIDADE'                 => trim(substr($texto, 269, 20)),
            'UF'                     => trim(substr($texto, 289, 2)),
            'TELEFONE'               => trim(substr($texto, 291, 20)),
            'TELEFONE2'              => trim(substr($texto, 311, 15)),
            'FAX'                    => trim(substr($texto, 326, 15)),
            'COD_IBGE'               => trim(substr($texto, 341, 8)),
            'NATUREZA_JURIDICA'      => trim(substr($texto, 349, 8)),
            'DESC_NATUREZA_JURIDICA' => trim(substr($texto, 357, 55)),
            'SEGMENTO'               => trim(substr($texto, 412, 79)),
            'RAMO_ATIVIDADE'         => trim(substr($texto, 491, 8)),
            'DESC_RAMO_ATIVIDADE'    => trim(substr($texto, 499, 55))
        );

        return $dados;
    }

    /**
     * Metodo para localizar capital social
     *
     * @param array $parametros
     *
     * @return array
     */
    public function localizaCapitalSocial($parametros)
    {
        $texto = $this->search($parametros['documento'], null, 'CERTOCP');

        $dados = array(
            'CNPJ'                 => trim(substr($texto, 7, 14)),
            'MOEDA'                => trim(substr($texto, 21, 4)),
            'VALOR_CAPITAL_SOCIAL' => trim(substr($texto, 25, 15)),
            'ORGAO_SOCIAL'         => trim(substr($texto, 40, 8)),
            'DATA_DE_REGISTRO'     => $this->dateFormat(trim(substr($texto, 48, 8))),
            'MOEDA_CAPITAL_ATUAL'  => trim(substr($texto, 56, 4)),
            'VALOR_CAPITAL_ATUAL'  => trim(substr($texto, 60, 15)),
            'ORGAO_ATUAL'          => trim(substr($texto, 75, 8)),
            'DATA_DE_ALTERACAO'    => $this->dateFormat(trim(substr($texto, 83, 8)))
        );

        return $dados;
    }

    /**
     * Metodo para localizar ramos de atividade secundario
     *
     * @param array $parametros
     *
     * @return array
     */
    public function localizaRamosAtividadesSecundario($parametros)
    {
        $texto = $this->search($parametros['documento'], null, 'CERTOAT');

        $divider = "067285";
        $registros = explode($divider, substr($texto, strlen($divider)));

        foreach ($registros as $key => $valor) {
            $dados[] = array(
                'RAMO_ATIVIDADE' => trim(substr($valor, 1, 8)),
                'DESC_ATIVIDADE' => trim(substr($valor, 9, 50))
            );
        }

        return $dados;
    }

    /**
     * Metodo para localizar participantes
     *
     * @param array $parametros
     *
     * @return array
     */
    public function localizaParticipantes($parametros)
    {
        $texto = $this->search($parametros['documento'], null, 'CERTOPT');

        $divider = "124283";
        $registros = explode($divider, substr($texto, strlen($divider)));

        $dados = [];

        foreach ($registros as $registro) {
            $dados[] = array(
                'razao_social'   => utf8_encode(trim(substr($registro, 6, 50))),
                'tipo_documento' => utf8_encode($this->getDocumentType(trim(substr($registro, 56, 1)))),
                'documento'      => utf8_encode(trim(substr($registro, 57, 14))),
                'data_entrada'   => utf8_encode($this->dateFormat(trim(substr($registro, 71, 8)))),
                'tipo_moeda'     => utf8_encode($this->getCashType(trim(substr($registro, 79, 1)))),
                'valor'          => utf8_encode(trim(substr($registro, 80, 15))),
                'funcao'         => utf8_encode(trim(substr($registro, 95, 20))),
                'assina'         => utf8_encode(trim(substr($registro, 115, 1))),
                'percentual'     => utf8_encode(trim(substr($registro, 116, 5)))
            );
        }

        $parsedData = [];
        foreach ($dados as $socio) {
            if (empty($socio['razao_social']) && empty($socio['documento'])) {
                throw new Exception("A consulta no QSA Boa Vista não retornou dados.");
            }

            if (!isset($socio['documento'])) {
                throw new Exception('A consulta no QSA Boa Vista retornou dados inesperados.');
            }

            if (preg_match('@INVALIDA@', $socio['razao_social'])) {
                throw new Exception("Documento invalidado pela Boa Vista.");
            }

            $cpf = '';
            $cnpj = '';
            $nome = '';
            $razao = '';
            $documento = '';

            // documento sempre tem 14 dígitos, aqui padronizamos como cpf ou cnpj
            if (strtolower($socio['tipo_documento']) == 'cpf') {
                $cpf = $documento = substr($socio['documento'], 3, 11);
                $nome = $socio['razao_social'];
            } elseif (strtolower($socio['tipo_documento']) == 'cnpj') {
                $cnpj = $documento = $socio['documento'];
                $razao = $socio['razao_social'];
            } else {
                // provavelmente empresa no exterior: tipo de documento em branco, mas possui razão social
                $razao = $socio['razao_social'];
            }

            $parsedData[] = [
                'razao_social' => $razao,
                'tipo_documento' => $socio['tipo_documento'],
                'nome' => $nome,
                'documento' => $documento,
                'cpf' => $cpf,
                'cnpj' => $cnpj,
                'data_entrada' => $socio['data_entrada'],
                'tipo_moeda' => $socio['tipo_moeda'],
                'valor' => $socio['valor'],
                'funcao' => $socio['funcao'],
                'assina' => $socio['assina'],
                'percentual' => $socio['percentual'],
                'cpf_nome' => $cpf . '|' . $nome,
                'cnpj_nome' => $cnpj . '|' . $razao
            ];
        }

        $updateSpineData = [
            'cnpj' => $parametros['documento'],
            'qsa' => $parsedData
        ];

        $this->parentClass->callSpiderUpdateSpine('QsaBoaVista', $updateSpineData);

        return $parsedData;
    }

    /**
     * Metodo para localizar falencias e recuperacao judicial
     *
     * @param array $parametros
     *
     * @return array
     */
    public function localizaFalenciasERecuperacaoJudicial($parametros)
    {
        $texto = $this->search($parametros['documento'], null, 'CERTOFR');

        $divider = "060295";
        $registros = explode($divider, substr($texto, strlen($divider)));

        foreach ($registros as $key => $valor) {
            $dados[] = array(
                'TIPO_OCORRENCIA' => trim(substr($valor, 1, 2)),
                'VARA_CIVEL'      => trim(substr($valor, 3, 4)),
                'DATA_OCORRENCIA' => $this->dateFormat(trim(substr($valor, 7, 8))),
                'PRACA'           => trim(substr($valor, 15, 40)),
                'UF'              => trim(substr($valor, 55, 2))
            );
        }

        return $dados;
    }

    /**
     * Metodo para localizar titulos protestados
     *
     * @param array $parametros
     *
     * @return array
     */
    public function localizaTitulosProtestados($parametros)
    {
        $texto = $this->search($parametros['documento'], null, 'CERTOTP');

        $divider = "077299";
        $registros = explode($divider, substr($texto, strlen($divider)));

        foreach ($registros as $key => $valor) {
            $dados[] = array(
                'CARTORIO'        => trim(substr($valor, 1, 4)),
                'DATA_OCORRENCIA' => $this->dateFormat(trim(substr($valor, 5, 8))),
                'MOEDA'           => trim(substr($valor, 13, 4)),
                'VALOR_TITULO'    => trim(substr($valor, 17, 15)),
                'PRACA'           => trim(substr($valor, 32, 40)),
                'UF'              => trim(substr($valor, 72, 2))
            );
        }

        return $dados;
    }

    /**
     * Metodo para localizar acoes civeis
     *
     * @param array $parametros
     *
     * @return array
     */
    public function localizaAcoesCiveis($parametros)
    {
        $texto = $this->search($parametros['documento'], null, 'CERTOAC');

        $divider = "065021";
        $registros = explode($divider, substr($texto, strlen($divider)));

        foreach ($registros as $key => $valor) {
            $dados[] = array(
                'VARA'              => trim(substr($valor, 1, 4)),
                'ACAO'              => trim(substr($valor, 5, 17)),
                'DATA_DISTRIBUICAO' => $this->dateFormat(trim(substr($valor, 22, 8))),
                'PRACA'             => trim(substr($valor, 30, 30)),
                'UF'                => trim(substr($valor, 60, 2))
            );
        }

        return $dados;
    }

    /**
     * Metodo para localizar registros de debitos
     *
     * @param array $parametros
     *
     * @return array
     */
    public function localizaRegistrosDeDebitos($parametros)
    {
        $texto = $this->search($parametros['documento'], null, 'CERTORD');

        $divider = "138301";
        $registros = explode($divider, substr($texto, strlen($divider)));

        foreach ($registros as $key => $valor) {
            $dados[] = array(
                "TIPO"                  => trim(substr($valor, 1, 2)),
                "INFORMANTE"            => trim(substr($valor, 3, 50)),
                "CONTRATO"              => trim(substr($valor, 53, 25)),
                "DATA_VENCIMENTO"       => $this->dateFormat(trim(substr($valor, 78, 8))),
                "DATA_DISPONIBILIZACAO" => $this->dateFormat(trim(substr($valor, 86, 8))),
                "CIDADE"                => trim(substr($valor, 94, 20)),
                "UF"                    => trim(substr($valor, 114, 2)),
                "MOEDA"                 => trim(substr($valor, 116, 4)),
                "VALOR"                 => trim(substr($valor, 120, 15))
            );
        }

        return $dados;
    }

    /**
     * Metodo para localizar cheques sem fundos
     *
     * @param array $parametros
     *
     * @return array
     */
    public function localizaChequesSemFundos($parametros)
    {
        $texto = $this->search($parametros['documento'], null, 'CERTOCF');

        $divider = "120242";
        $registros = explode($divider, substr($texto, strlen($divider)));

        foreach ($registros as $key => $valor) {
            $dados[] = array(
                "TIPO_DOCUMENTO"            => trim(substr($valor, 1, 1)),
                "DOCUMENTO"                 => trim(substr($valor, 2, 14)),
                "NOME"                      => trim(substr($valor, 16, 50)),
                "BANCO"                     => trim(substr($valor, 66, 3)),
                "AGENCIA"                   => trim(substr($valor, 69, 4)),
                "MOTIVO_12"                 => trim(substr($valor, 73, 3)),
                "DATA_ULTIMA_OCORRENCIA_12" => $this->dateFormat(trim(substr($valor, 76, 8))),
                "MOTIVO_13"                 => trim(substr($valor, 84, 3)),
                "DATA_ULTIMA_OCORRENCIA_13" => $this->dateFormat(trim(substr($valor, 87, 8))),
                "MOTIVO_14"                 => trim(substr($valor, 95, 3)),
                "DATA_ULTIMA_OCORRENCIA_14" => $this->dateFormat(trim(substr($valor, 98, 8))),
                "MOTIVO_99"                 => trim(substr($valor, 106, 3)),
                "DATA_ULTIMA_OCORRENCIA_99" => $this->dateFormat(trim(substr($valor, 109, 8)))
            );
        }

        return $dados;
    }

    /**
     * Metodo para localizar devolucoes informadas
     *
     * @param array $parametros
     *
     * @return array
     */
    public function localizaDevolucoesInformadas($parametros)
    {
        $texto = $this->search($parametros['documento'], null, 'CERTOCD');

        $divider = "157244";
        $registros = explode($divider, substr($texto, strlen($divider)));

        foreach ($registros as $key => $valor) {
            $dados[] = array(
                "TIPO_DOCUMENTO"    => trim(substr($valor, 1, 1)),
                "DOCUMENTO"         => trim(substr($valor, 2, 14)),
                "BANCO"             => trim(substr($valor, 16, 3)),
                "AGENCIA"           => trim(substr($valor, 19, 4)),
                "CONTA_CORRENTE"    => trim(substr($valor, 23, 15)),
                "CHEQUE_INICIAL"    => trim(substr($valor, 38, 8)),
                "CHEQUE_FINAL"      => trim(substr($valor, 46, 8)),
                "MOTIVO"            => trim(substr($valor, 54, 1)),
                "ALINEA"            => trim(substr($valor, 55, 2)),
                "DATA_OCORRENCIA"   => $this->dateFormat(trim(substr($valor, 57, 8))),
                "DATA_REGISTRO"     => $this->dateFormat(trim(substr($valor, 65, 8))),
                "MOEDA"             => trim(substr($valor, 73, 4)),
                "VALOR"             => trim(substr($valor, 77, 11)),
                "CODIGO_INFORMANTE" => trim(substr($valor, 88, 8)),
                "INFORMANTE"        => trim(substr($valor, 96, 36)),
                "CIDADE"            => trim(substr($valor, 132, 20)),
                "UF"                => trim(substr($valor, 152, 2))
            );
        }

        return $dados;
    }

    /**
     * Metodo para localizar cheques sustados
     *
     * @param array $parametros
     *
     * @return array
     */
    public function localizaChequesSustados($parametros)
    {
        $texto = $this->search($parametros['documento'], null, 'CERTO21');

        $divider = "126245";
        $registros = explode($divider, substr($texto, strlen($divider)));

        foreach ($registros as $key => $valor) {
            $dados[] = array(
                "TIPO_DOCUMENTO"    => trim(substr($valor, 1, 1)),
                "DOCUMENTO"         => trim(substr($valor, 2, 14)),
                "BANCO"             => trim(substr($valor, 16, 3)),
                "AGENCIA"           => trim(substr($valor, 19, 4)),
                "CONTA_CORRENTE"    => trim(substr($valor, 23, 15)),
                "CHEQUE_INICIAL"    => trim(substr($valor, 38, 8)),
                "CHEQUE_FINAL"      => trim(substr($valor, 46, 8)),
                "ALINEA"            => trim(substr($valor, 54, 2)),
                "DATA_OCORRENCIA"   => $this->dateFormat(trim(substr($valor, 56, 8))),
                "DATA_REGISTRO"     => $this->dateFormat(trim(substr($valor, 64, 8))),
                "MOEDA"             => trim(substr($valor, 72, 4)),
                "VALOR"             => trim(substr($valor, 76, 11)),
                "INFORMANTE"        => trim(substr($valor, 87, 36))
            );
        }

        return $dados;
    }

    /**
     * Metodo para localizar relacao de passagens
     *
     * @param array $parametros
     *
     * @return array
     */
    public function localizaRelacaoDePassagens($parametros)
    {
        $texto = $this->search($parametros['documento'], null, 'CERTOPA');

        $divider = "091304";
        $registros = explode($divider, substr($texto, strlen($divider)));

        foreach ($registros as $key => $valor) {
            $dados[] = array(
                "DATA"          => $this->dateFormat(trim(substr($valor, 1, 8))),
                "TIPO_CREDITO"  => trim(substr($valor, 9, 10)),
                "MOEDA"         => trim(substr($valor, 19, 4)),
                "VALOR_CREDITO" => trim(substr($valor, 23, 15)),
                "INFORMANTE"    => trim(substr($valor, 38))
            );
        }

        return $dados;
    }

    /**
     * Metodo para localizar relacao de empresas no mesmo endereco
     *
     * @param array $parametros
     *
     * @return array
     */
    public function localizaRelacaoDeEmpresasMesmoEndereco($parametros)
    {
        $texto = $this->search($parametros['documento'], null, 'CERTOEN');

        $divider = "244368";
        $registros = explode($divider, substr($texto, strlen($divider)));

        foreach ($registros as $key => $valor) {
            $dados[] = array(
                "CNPJ"     => trim(substr($valor, 1, 14)),
                "NOME"     => trim(substr($valor, 15, 115)),
                "ENDERECO" => trim(substr($valor, 130, 100))
            );
        }

        return $dados;
    }

    /**
     * @param string $cnpj        CNPJ
     * @param string $razaoSocial Razao Social
     * @param string $tipoBusca   Codigo de busca da Boa Vista
     *
     * @return string
     *
     * @codeCoverageIgnore
     */
    protected function search($cnpj = null, $razaoSocial = null, $tipoBusca = null)
    {
        if ($cnpj) {
            $cnpj = preg_replace('@\D*@', '', $cnpj);
        }

        if ((!$this->usuario) || (!$this->senha)) {
            throw new Exception("Usuário ou senha não cadastrado para essa fonte.", 1);
        }

        $getData = $this->get(array(
            'transacao' => 'CSR60',
            'versao' => '01',
            'reservado_solicitante' => '',
            'reservado_bvs' => '',
            'codigo' => $this->usuario,
            'senha' => $this->senha,
            'consulta' => $tipoBusca,
            'versao_consulta' => '01',
            'tipo_resposta' => '2',
            'tipo_transmissao_resposta' => 'C',
            'tipo_documento' => '2',
            'cnpj' => $cnpj,
            'razao_social' => $razaoSocial,
            'tipo_credito' => 'FI',
            'valor_credito' => '1000000',
            'codigo_facilitador' => '',
            'indicador_fim_texto' => 'X'
        ));


        $resultado = $this->parse($getData);

        return $resultado;
    }

    /**
     * @param array $params array de dados
     *
     * @return string
     *
     * @codeCoverageIgnore
     */
    private function get($params)
    {
        try {
            $params = array(
                'transacao'                 => str_pad(
                    (isset($params['transacao'])                 ? $params['transacao']                 : ''),
                    8,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'versao'                    => str_pad(
                    (isset($params['versao'])                    ? $params['versao']                    : ''),
                    2,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'reservado_solicitante'     => str_pad(
                    (isset($params['reservado_solicitante'])     ? $params['reservado_solicitante']     : ''),
                    10,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'reservado_bvs'             => str_pad(
                    (isset($params['reservado_bvs'])             ? $params['reservado_bvs']             : ''),
                    20,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'codigo'                    => str_pad(
                    (isset($params['codigo'])                    ? $params['codigo']                    : ''),
                    8,
                    '0',
                    STR_PAD_LEFT
                ),
                'senha'                     => str_pad(
                    (isset($params['senha'])                     ? $params['senha']                     : ''),
                    8,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'consulta'                  => str_pad(
                    (isset($params['consulta'])                  ? $params['consulta']                  : ''),
                    8,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'versao_consulta'           => str_pad(
                    (isset($params['versao_consulta'])           ? $params['versao_consulta']           : ''),
                    2,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'tipo_resposta'             => str_pad(
                    (isset($params['tipo_resposta'])             ? $params['tipo_resposta']             : ''),
                    1,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'tipo_transmissao_resposta' => str_pad(
                    (isset($params['tipo_transmissao_resposta']) ? $params['tipo_transmissao_resposta'] : ''),
                    1,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'tipo_documento'            => str_pad(
                    (isset($params['tipo_documento'])            ? $params['tipo_documento']            : ''),
                    1,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'cnpj'                      => str_pad(
                    (isset($params['cnpj'])                      ? $params['cnpj']                      : ''),
                    14,
                    '0',
                    STR_PAD_LEFT
                ),
                'nome'                      => str_pad(
                    (isset($params['razao_social'])              ? $params['razao_social']              : ''),
                    50,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'tipo_credito'              => str_pad(
                    (isset($params['tipo_credito'])              ? $params['tipo_credito']              : ''),
                    2,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'valor_credito'             => str_pad(
                    (isset($params['valor_credito'])             ? $params['valor_credito']             : ''),
                    11,
                    '0',
                    STR_PAD_LEFT
                ),
                'codigo_facilitador'        => str_pad(
                    (isset($params['codigo_facilitador'])        ? $params['codigo_facilitador']        : ''),
                    8,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'indicador_fim_texto'       => str_pad(
                    (isset($params['indicador_fim_texto'])       ? $params['indicador_fim_texto']       : ''),
                    1,
                    ' ',
                    STR_PAD_RIGHT
                )
            );
            $client = new GuzzleClient();
            $response = $client->request('GET', $this->endpoint . urlencode(implode('', $params)));
            $contents = $response->getBody()->getContents();

            return $contents;
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * @param string $body string de retorno da boa vista
     *
     * @codeCoverageIgnore
     */
    private function checkErros($body)
    {
        $patterns = "@SENHA\\s*INVALIDA@";
        if (preg_match($patterns, $body)) {
            throw new \Exception("SENHA INVALIDA", 6);
        }

        $patterns = "@DOCUMENTO\\s*INVALIDO@";
        if (preg_match($patterns, $body)) {
            throw new \Exception("DOCUMENTO INVALIDO", 6);
        }

        $patterns = "@ESTA\\s*EMPRESA\\s*NAO\\s*FOI\\s*ENCONTRADA\\s*EM\\s*NOSSO\\s*BANCO\\s*DE\\s*DADOS@";
        if (preg_match($patterns, $body)) {
            throw new \Exception("EMPRESA NAO ENCONTRADA NA BASE", 6);
        }

        $patterns = "@IDENTIFICADOR\\s*COM\\s*RESTRICAO\\s*DE\\s*ACESSO@";
        if (preg_match($patterns, $body)) {
            throw new \Exception("ERRO NA AUTENTICACAO", 6);
        }

        $patterns = "/cnpj\\snao\\sdisponivel/isU";
        if (preg_match($patterns, $body)) {
            throw new \Exception("CNPJ INDISPONÍVEL NA BASE DO BOA VISTA", 6);
        }

        $body = preg_replace('@<PRE>\s*@', '', trim($body));
        $body = preg_replace('@</PRE>\s*@', '', trim($body));
        $body = trim($body);
        if (strlen($body) <= 78) {
            throw new \Exception("A PESQUISA NAO RETORNOU DADOS", 2);
        }
    }

    /**
     * @param string $body string de retorno da boa vista
     *
     * @return string
     *
     * @codeCoverageIgnore
     */
    private function parse($body)
    {
        $this->checkErros($body);

        $body = preg_replace('@<PRE>\s*@', '', trim($body));
        $body = preg_replace('@</PRE>\s*@', '', trim($body));
        $results = array(
            'transacao'             => substr($body, 0, 8),
            'versao'                => substr($body, 8, 2),
            'reservado_solicitante' => substr($body, 10, 10),
            'reservado_acsp'        => substr($body, 20, 20),
            'codigo'                => substr($body, 40, 8),
            'consulta'              => substr($body, 48, 8),
            'versao_consulta'       => substr($body, 56, 2),
            'tipo_resposta'         => substr($body, 58, 1),
            'codigo_retorno'        => substr($body, 59, 1),
            'numero_consulta'       => substr($body, 60, 7),
            'tamanho_texto'         => substr($body, 67, 4),
            'texto'                 => substr($body, 71)
        );

        $texto = $results['texto'];

        return $texto;
    }

    /**
     * @param string $date formataçao de data
     *
     * @return date
     *
     * @codeCoverageIgnore
     */
    private function dateFormat($date)
    {
        return substr($date, 0, 2) . "/" . substr($date, 2, 2) . "/" . substr($date, 4, 4);
    }

    /**
     * @param int $cod codigo da situaçao
     *
     * @return string
     *
     * @codeCoverageIgnore
     */
    private function getConditionType($cod)
    {
        switch ($cod) {
            case 0:
                return "ATIVO";

            case 1:
                return "INAPTO";

            case 2:
                return "SUSPENSO";

            case 6:
                return "BAIXADO";

            case 7:
                return "NULO";

            case 8:
                return "CANCELADO";

            default:
                return "";
        }
    }

    /**
     * @param int $cod codigo da inscricao estadual
     *
     * @return string
     *
     * @codeCoverageIgnore
     */
    private function getStateInscriptionType($cod)
    {
        switch ($cod) {
            case 1:
                return "HABILITADO";

            case 2:
                return "NAO HABILITADO";

            default:
                return "";
        }
    }

    /**
     * @param int $cod tipo de documento
     *
     * @return string
     *
     * @codeCoverageIgnore
     */
    private function getDocumentType($cod)
    {
        switch ($cod) {
            case 1:
                return "CPF";

            case 2:
                return "CNPJ";

            case 3:
                return "RG";

            case 4:
                return "NIRE";

            default:
                return "";
        }
    }

    /**
     * @param int $cod tipo de moeda
     *
     * @return string
     *
     * @codeCoverageIgnore
     */
    private function getCashType($cod)
    {
        switch ($cod) {
            case 0:
                return "CR$";

            case 1:
                return "CZ$";

            case 2:
                return "NCZ$";

            case 3:
                return "CR$";

            case 4:
                return "RCR$";

            case 5:
                return "R$";

            case 9:
                return "%";

            default:
                return "";
        }
    }

    /**
     * Add to dynamodb the result to, on future, could be count
     * on the consumption report
     *
     * @param string $url
     * @param string $jsonResult
     * @return void
     */
    private function insertCounter($params, $jsonResult)
    {
        $params = [
            'captura' => 'boavista',
            'fornecedor' => 'boavista',
            'consulta' => $params['consulta'],
            'criterio' => $params['cnpj'],
            'resultado' => $jsonResult,
        ];

        $bilhetador = new BilhetadorFornecedoresDynamo($params);
        $bilhetador->run();
    }
}
