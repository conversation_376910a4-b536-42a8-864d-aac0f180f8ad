<?php

namespace App\Crawler\BoaVistaConsumer;

use Exception;
use Guz<PERSON>Http\Client as GuzzleClient;
use App\Helper\BilhetadorFornecedoresDynamo;

/**
 * Classe para centralizar as chamadas à API da UnitFour
 *
 * <AUTHOR> <<EMAIL>>
 */
class BoaVistaScpcNetService
{
    /** @var string */
    protected $usuario;

    /** @var string */
    protected $senha;

    /** @var string */
    protected $endpoint;

    /** @var boolean */
    protected $debug;

    /**
     * Construtor da classe
     * @param string $usuario
     * @param string $senha
     * @param string $endpoint
     */
    public function __construct($usuario, $senha, $endpoint, $debug = false)
    {
        $this->usuario = $usuario;
        $this->senha = $senha;
        $this->endpoint = $endpoint;
        $this->debug = $debug;
    }

    /**
     * Metodo para localizar informacoes cadastrais
     *
     * @param array $parametros
     *
     * @return array
     */

    public function searchByDocument($parametros)
    {
        if ($this->debug == true) {
            print "Metodo: " . __METHOD__ . "\n <br>";
        }
        $texto = $this->search($parametros['documento'], null, 'BVSNET'); //CERTOCR


        if (!is_array($texto)) {
            if (preg_match('/ERRO/', $texto, $match)) {
                throw new Exception($match, 6);
            }
            throw new Exception("Erro desconhecido", 6);
        }

        return $texto;
    }

    /**
     * @param string $cnpj CNPJ
     * @param string $razaoSocial Razao Social
     * @param string $tipoBusca Codigo de busca da Boa Vista
     *
     * @return string
     *
     * @codeCoverageIgnore
     */
    protected function search($documento = null, $razaoSocial = null, $tipoBusca = null)
    {
        if ($this->debug == true) {
            print "Metodo: " . __METHOD__ . "\n <br>";
        }
        if ($documento) {
            $documento = preg_replace('@\D*@', '', $documento);
        }

        (strlen($documento) == 11) ? $cpf = $documento : $cpf = '';
        (strlen($documento) > 11) ? $cnpj = $documento : $cnpj = '';
        if ($cpf == '') {
            $tipo_documento = '2';
            $tipoPessoa = 'J';
            $credit_type = 'FI';
            $scoreModel = '09';
        } else {
            $tipo_documento = '1';
            $tipoPessoa = 'F';
            $credit_type = 'XX';
            $scoreModel = '07';
        }

        if ((!$this->usuario) || (!$this->senha)) {
            throw new Exception("Usuário ou senha não cadastrado para essa fonte.", 1);
        }

        // https://www.bvsnet.com.br/cgi-bin/db2www/netpo028.mbr/string?consulta=CSR60+++01++++++++++++++++++++++++++++++0061049478VE5T++BVSNET1F032C100007125580475++XX+++++++++++++++++++++++++++++++++++++++++++++++++++++++S07N+++++++++++++++++++++++++++++++++++++++++\n

        $getData = $this->get(array(
            'transacao' => 'CSR60', // 08 posições vazias completar com +
            'versao' => '01', // 2 posições
            'reservado_solicitante' => '', //10 posiçoes ++++++++++
            'reservado_bvs' => '', //20 posições ++++++++++++++++++++
            'codigo' => $this->usuario, // 08 posições
            'senha' => $this->senha, // 08 posições
            'consulta' => $tipoBusca, //06 posições
            'tipo_documento' => $tipo_documento, // 1 posição
            'tipo_pessoa' => $tipoPessoa, //1 posição
            'versao_consulta' => '03', // 2 posiçoes
            'tipo_resposta' => '2', // 1 posição
            'tipo_transmissao_resposta' => 'C', // 1 Posição
            'scoreCredito_scpc_cheque' => '1',
            'razao_social' => $razaoSocial,
            'tipo_credito' => $tipo_credito = '',
            'valor_credito' => '1000000',
            'codigo_facilitador' => '',
            'indicador_fim_texto' => '',
            'documento' => $documento, //14 posições
            'uf' => '', //14 posições opcional
            'credit_type' => $credit_type,
            'fonte_informacao_check' => '', // 1 posiçao opcional
            'check_info' => '', // 33 posições opcional
            'date' => '', // 8 posições
            'count' => '', // 2 posições
            'value' => '', // 11 posições
            'scpc_cheque' => '',
            'score_credito' => 'S',
            'score_model' => $scoreModel,
            'cut_point' => 'N',
            'cutpoint' => '', // unkown 4 posições
            'zip_code' => '', //8 posições
            'ddd' => '', //4 posições
            'phone' => '', //9 posições
            'origin_zip_code' => '', //8 posições
            'facilitador' => '', //8 posições
            // 'quebra_linha' => '\n', //1 posição
        ));


        $resultado = $this->parse($getData, $tipo_documento);

        return $resultado; //retornar o get data e o spider faz o resto
    }

    /**
     * @param array $params array de dados
     *
     * @return string
     *
     * @codeCoverageIgnore
     */
    private function get($params)
    {
        if ($this->debug == true) {
            print "Metodo: " . __METHOD__ . "\n <br>";
        }
        try {
            $params = array(
                'transacao' => str_pad(
                    (isset($params['transacao']) ? $params['transacao'] : ''),
                    8,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'versao' => str_pad(
                    (isset($params['versao']) ? $params['versao'] : ''),
                    2,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'reservado_solicitante' => str_pad(
                    (isset($params['reservado_solicitante']) ? $params['reservado_solicitante'] : ''),
                    10,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'reservado_bvs' => str_pad(
                    (isset($params['reservado_bvs']) ? $params['reservado_bvs'] : ''),
                    20,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'codigo' => str_pad(
                    (isset($params['codigo']) ? $params['codigo'] : ''),
                    8,
                    '0',
                    STR_PAD_LEFT
                ),
                'senha' => str_pad(
                    (isset($params['senha']) ? $params['senha'] : ''),
                    8,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'consulta' => str_pad(
                    (isset($params['consulta']) ? $params['consulta'] : ''),
                    6,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'scoreCredito_scpc_cheque' => str_pad(
                    (isset($params['scoreCredito_scpc_cheque']) ? $params['scoreCredito_scpc_cheque'] : ''),
                    1,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'tipo_pessoa' => str_pad(
                    (isset($params['tipo_pessoa']) ? $params['tipo_pessoa'] : ''),
                    1,
                    ' ',
                    STR_PAD_RIGHT
                ),

                'versao_consulta' => str_pad(
                    (isset($params['versao_consulta']) ? $params['versao_consulta'] : ''),
                    2,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'tipo_resposta' => str_pad(
                    (isset($params['tipo_resposta']) ? $params['tipo_resposta'] : ''),
                    1,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'tipo_transmissao_resposta' => str_pad(
                    (isset($params['tipo_transmissao_resposta']) ? $params['tipo_transmissao_resposta'] : ''),
                    1,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'tipo_documento' => str_pad(
                    (isset($params['tipo_documento']) ? $params['tipo_documento'] : ''),
                    1,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'documento' => str_pad(
                    (isset($params['documento']) ? $params['documento'] : ''),
                    14,
                    '0',
                    STR_PAD_LEFT
                ),
                'uf' => str_pad(
                    (isset($params['uf']) ? $params['uf'] : ''),
                    2,
                    ' ',
                    STR_PAD_LEFT
                ),
                'credit_type' => str_pad(
                    (isset($params['credit_type']) ? $params['credit_type'] : ''),
                    2,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'fonte_informacao_check' => str_pad(
                    (isset($params['fonte_informacao_check']) ? $params['fonte_informacao_check'] : ''),
                    1,
                    ' ',
                    STR_PAD_LEFT
                ),
                'check_info' => str_pad(
                    (isset($params['check_info']) ? $params['check_info'] : ''),
                    33,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'date' => str_pad(
                    (isset($params['date']) ? $params['date'] : ''),
                    8,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'count' => str_pad(
                    (isset($params['count']) ? $params['count'] : ''),
                    2,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'value' => str_pad(
                    (isset($params['value']) ? $params['value'] : ''),
                    11,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'score_credito' => str_pad(
                    (isset($params['score_credito']) ? $params['score_credito'] : ''),
                    1,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'score_model' => str_pad(
                    (isset($params['score_model']) ? $params['score_model'] : ''),
                    2,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'cut_point' => str_pad(
                    (isset($params['cut_point']) ? $params['cut_point'] : ''),
                    1,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'cutpoint' => str_pad(
                    (isset($params['cutpoint']) ? $params['cutpoint'] : ''),
                    4,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'zip_code' => str_pad(
                    (isset($params['zip_code']) ? $params['zip_code'] : ''),
                    8,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'ddd' => str_pad(
                    (isset($params['ddd']) ? $params['ddd'] : ''),
                    4,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'phone' => str_pad(
                    (isset($params['phone']) ? $params['phone'] : ''),
                    9,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'origin_zip_code' => str_pad(
                    (isset($params['origin_zip_code']) ? $params['origin_zip_code'] : ''),
                    8,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'facilitador' => str_pad(
                    (isset($params['facilitador']) ? $params['facilitador'] : ''),
                    8,
                    ' ',
                    STR_PAD_RIGHT
                ),
                'quebra_linha' => str_pad(
                    (isset($params['quebra_linha']) ? $params['quebra_linha'] : ''),
                    2,
                    ' ',
                    STR_PAD_RIGHT
                ),



            );
            $client = new GuzzleClient();
            $response = $client->request('GET', $this->endpoint . urlencode(implode('', $params)));
            $contents = $response->getBody()->getContents();

            return $contents;
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * @param string $body string de retorno da boa vista
     *
     * @codeCoverageIgnore
     */
    private function checkErros($body)
    {
        // print_r($body);
        if ($this->debug == true) {
            print "Metodo: " . __METHOD__ . "\n <br>";
        }

        // echo "<pre>";
        // print_r($body);

        $patters = "@SENHA\\s*INVALIDA@";
        if (preg_match($patters, $body)) {
            throw new \Exception("SENHA INVALIDA", 6);
        }

        if (preg_match($patters, $body)) {
            throw new \Exception("CODIGO DE SERVICO INVALIDO...", 6);
        }

        $patters = "@DOCUMENTO\\s*INVALIDO@";
        if (preg_match($patters, $body)) {
            throw new \Exception("DOCUMENTO INVALIDO", 6);
        }

        $patters = "@ESTA\\s*EMPRESA\\s*NAO\\s*FOI\\s*ENCONTRADA\\s*EM\\s*NOSSO\\s*BANCO\\s*DE" .
            "\\s*DADOS@";
        if (preg_match($patters, $body)) {
            throw new \Exception("EMPRESA NAO ENCONTRADA NA BASE", 6);
        }

        $patters = "@IDENTIFICADOR\\s*COM\\s*RESTRICAO\\s*DE\\s*ACESSO@";
        if (preg_match($patters, $body)) {
            throw new \Exception("ERRO NA AUTENTICACAO", 6);
        }

        if (strlen($body) <= 78) {
            throw new \Exception("A PESQUISA NAO RETORNOU DADOS", 6);
        }
    }

    /**
     * @param string $body string de retorno da boa vista
     *
     * @return string
     *
     * @codeCoverageIgnore
     */
    private function parse($body, $tipo_documento)
    {
        if ($this->debug == true) {
            print "Metodo: " . __METHOD__ . "\n <br>";
        }

        $this->checkErros($body);
        $response = ($tipo_documento == 1) ? $this->parseDataFromCpf($body) : $this->parseDataFromCnpj($body);

        return $response;
    }

    /**
     * @param string $date formataçao de data
     *
     * @return date
     *
     * @codeCoverageIgnore
     */
    private function dateFormat($date)
    {
        if ($this->debug == true) {
            print "Metodo: " . __METHOD__ . "\n <br>";
        }
        return substr($date, 0, 2) . "/" . substr($date, 2, 2) . "/" . substr($date, 4, 4);
    }

    /**
     * @param int $cod codigo da situaçao
     *
     * @return string
     *
     * @codeCoverageIgnore
     */
    private function getConditionType($cod)
    {
        if ($this->debug == true) {
            print "Metodo: " . __METHOD__ . "\n <br>";
        }
        switch ($cod) {
            case 0:
                return "ATIVO";

            case 1:
                return "INAPTO";

            case 2:
                return "SUSPENSO";

            case 6:
                return "BAIXADO";

            case 7:
                return "NULO";

            case 8:
                return "CANCELADO";

            default:
                return "";
        }
    }

    /**
     * @param int $cod codigo da inscricao estadual
     *
     * @return string
     *
     * @codeCoverageIgnore
     */
    private function getStateInscriptionType($cod)
    {
        if ($this->debug == true) {
            print "Metodo: " . __METHOD__ . "\n <br>";
        }
        switch ($cod) {
            case 1:
                return "HABILITADO";

            case 2:
                return "NAO HABILITADO";

            default:
                return "";
        }
    }



    public function parseDataFromCpf($cpf_result)
    {
        $dadosArray = [];

        preg_match('/<PRE>(.*)/', $cpf_result, $match);

        $cpfBlocks = array(
            'block999', 'block249', 'block111', 'block123', 'block126', 'block141', 'block124',
            'block142', 'block146', 'block213', 'block254', 'block127', 'block128', 'block242',
            'block244', 'block245', 'block256', 'block268', 'block301', 'block601'
        );

        foreach ($cpfBlocks as $callBlock) {
            // o block999 é pra verificar se houve algum erro
            if ($callBlock != 'block999') {
                foreach ($this->{$callBlock}($match[1]) as $key => $value) {
                    $dadosArray[$key] = $value;
                }
            }
        }

        return $dadosArray;
    }

    public function parseDataFromCnpj($cnpj_result)
    {
        $dadosArray = [];

        preg_match('/<PRE>(.*)/', $cnpj_result, $match);

        $cnpjBlocks = array(
            'block999', 'block222', 'block111', 'block124', 'block126', 'block127',
            'block128', 'block300', 'block301', 'block303', 'block304', 'block242',
            'block244', 'block245', 'block254', 'block256', 'block268', 'block142',
            'block146', 'block213', 'block254', 'block601'
        );

        foreach ($cnpjBlocks as $callBlock) {
            // o block999 é pra verificar se houve algum erro
            if ($callBlock != 'block999') {
                foreach ($this->{$callBlock}($match[1]) as $key => $value) {
                    $dadosArray[$key] = $value;
                }
            }
        }
        return $dadosArray;
    }

    private function tipoOcorencia($tipo)
    {
        switch ($tipo) {
            case 'RG':
                $tipo = "Registrado";
                break;

            case 'IA':
                $tipo = "Imob. e Administração de Bens";
                break;

            case 'EC':
                $tipo = "Consórcio";
                break;

            case 'AL':
                $tipo = "Locadora";
                break;

            case 'SF':
                $tipo = "Emp. Crédito Imobiliário";
                break;

            case 'OJ':
                $tipo = "Outras Ativ. Econômicas";
                break;

            case 'TP':
                $tipo = "Título Protestado";
                break;

            default:
                $tipo = $tipo;
                break;
        }

        return $tipo;
    }

    private function block111($blocks)
    {
        // Resumo das Consultas Anteriores (Últimos 90 dias)
        $block111 = explode('111S', $blocks);
        $block['resumo_consultas_anteriores'] = [];

        if (count($block111) > 1) {
            unset($block111[0]);

            foreach ($block111 as $key => $value) {
                $block['resumo_consultas_anteriores'][$key] = array(
                    'nome' => trim(substr($value, 0, 40)),
                    'nascimento' => trim(substr($value, 40, 8)),
                    'quantidade_de_nomes' => trim(substr($value, 48, 5)),
                );
            }
        }

        return $block;
    }

    private function block123($blocks)
    {
        // Informações de Alerta (Pessoa Física)
        $block123 = explode('123S', $blocks);
        $block['informacoes_alerta_pf'] = [];

        if (count($block123) > 1) {
            unset($block123[0]);

            foreach ($block123 as $key => $value) {
                $block['informacoes_alerta_pf'][$key] = array(
                    'texto' => trim(substr($value, 0, 79)),
                    'identificacao' => trim(substr($value, 79, 2)),
                    'origem' => trim(substr($value, 81, 2)),
                    'tipo' => trim(substr($value, 83, 2)),
                );
            }
        }

        return $block;
    }

    private function block124($blocks)
    {
        // Débitos do SCPC
        $block124 = explode('124S', $blocks);
        $block['debitos_scpc'] = [];

        if (count($block124) > 1) {
            unset($block124[0]);

            foreach ($block124 as $key => $value) {
                $valor = substr($value, 44, 11);
                $valor = number_format(
                    substr($valor, 0, strlen($valor) - 2) . "." . substr($valor, -2, 2),
                    2,
                    ',',
                    '.'
                );
                (substr($value, 55, 1) == 'C') ? $situacao = "Comprador" : $situacao = "Avalista";
                (substr($value, 125, 1) == 'A') ? $condicao = "Ativo" : $condicao = "Inibido";


                $block['debitos_scpc'][$key] = array(
                    'tipo_ocorencia' => $this->tipoOcorencia(substr($value, 0, 2)),
                    'contrato' => trim(substr($value, 2, 22)),
                    'data_ocorrencia' => substr($value, 24, 2) .
                        "/" . substr($value, 26, 2) . "/" . substr($value, 28, 4),
                    'data_ disponibilizacao' => substr($value, 32, 2) .
                        "/" . substr($value, 34, 2) . "/" . substr($value, 36, 4),
                    'moeda' => trim(substr($value, 40, 4)),
                    'valor' => $valor,
                    'situacao' => $situacao,
                    'informante' => trim(substr($value, 56, 36)),
                    'Informado_pelo_consulente' => trim(substr($value, 92, 1)),
                    'cidade' => trim(substr($value, 93, 30)),
                    'uf' => trim(substr($value, 123, 2)),
                    'Condicao_do_debito' => $condicao,

                );
            }
        }

        return $block;
    }

    private function block126($blocks)
    {
        // Consultas Anteriores
        $block126 = explode('126S', $blocks);
        $block['consultas_anteriores'] = [];

        if (count($block126) > 1) {
            unset($block126[0]);

            foreach ($block126 as $key => $value) {
                $tipo = substr($value, 0, 2);

                switch ($tipo) {
                    case 'CC':
                        $tipo = "Cartão de Crédito";
                        break;

                    case 'CD':
                        $tipo = "Crédito Direto";
                        break;

                    case 'CG':
                        $tipo = "Crédito Consignado";
                        break;

                    case 'CH':
                        $tipo = "Cheque";
                        break;

                    case 'CP':
                        $tipo = "Crédito Pessoal";
                        break;

                    case 'CV':
                        $tipo = "Crédito Veículos";
                        break;

                    case 'OU':
                        $tipo = "Outros";
                        break;

                    default:
                        $tipo = substr($value, 0, 2);
                        break;
                }

                $block['consultas_anteriores'][$key] = array(
                    'tipo' => trim(substr($value, 0, 2)),
                    'data' => substr($value, 2, 2) . "/" . substr($value, 4, 2) . "/" . substr($value, 6, 4),
                    'informante' => trim(substr($value, 10, 36)),
                );
            }
        }

        return $block;
    }

    private function block127($blocks)
    {
        // Consultas Anteriores de Telefone
        $block127 = explode('127S', $blocks);
        $block['consultas_anteriores_telefone'] = [];

        if (count($block127) > 1) {
            unset($block127[0]);

            foreach ($block127 as $key => $value) {
                $block['consultas_anteriores_telefone'][$key] = array(
                    'ddd' => trim(substr($value, 0, 4)),
                    'phone' => trim(substr($value, 4, 9)),
                );
            }
        }

        return $block;
    }

    private function block128($blocks)
    {
        // Consultas Anteriores de Telefone - Últimos 36 meses (Constam mais telefones)
        $block128 = explode('128S', $blocks);
        $block['consultas_anteriores_telefone_analitico'] = [];

        if (count($block128) > 1) {
            unset($block128[0]);

            foreach ($block128 as $key => $value) {
                $block['consultas_anteriores_telefone_analitico'][$key] = array(
                    'quantidade' => trim(substr($value, 0, 5)),
                    'mensagem' => trim(substr($value, 5, 90)),
                );
            }
        }

        return $block;
    }

    private function block141($blocks)
    {
        // Resumo das Ocorrências de Débitos (Pessoa Física)
        $block141 = explode('141S', $blocks);
        $block['resumo_ocorrencias_debitos_pf'] = [];

        if (count($block141) > 1) {
            unset($block141[0]);

            foreach ($block141 as $key => $value) {
                $totalDebitos = substr($value, 28, 13);
                $totalDebitos = number_format(
                    substr($totalDebitos, 0, strlen($totalDebitos) - 2) . "." . substr($totalDebitos, -2, 2),
                    2,
                    ',',
                    '.'
                );

                $block['resumo_ocorrencias_debitos_pf'][$key] = array(
                    'quantidade_de_debitos' => trim(substr($value, 0, 8)),
                    'data_primeiro_debito' => substr(
                        $value,
                        8,
                        2
                    ) . "/" . substr($value, 10, 2) . "/" . substr($value, 12, 4),
                    'data_ultimo_debito' => substr(
                        $value,
                        16,
                        2
                    ) . "/" . substr($value, 18, 2) . "/" . substr($value, 20, 4),
                    'moeda' => trim(substr($value, 24, 4)),
                    'valor_total_debitos' => $totalDebitos,
                );
            }
        }

        return $block;
    }

    private function block142($blocks)
    {
        // Título Protestados Regionalizados
        $block142 = explode('142S', $blocks);
        $block['titulos_protestados_regionalizados'] = [];

        if (count($block142) > 1) {
            unset($block142[0]);

            foreach ($block142 as $key => $value) {
                $valor = substr($value, 22, 11);
                $valor = number_format(
                    substr($valor, 0, strlen($valor) - 2) . "." . substr($valor, -2, 2),
                    2,
                    ',',
                    '.'
                );
                $block['titulos_protestados_regionalizados'][$key] = array(
                    'tipo' => $this->tipoOcorencia(trim(substr($value, 0, 2))),
                    'cartorio' => trim(substr($value, 2, 8)),
                    'data_ocorrencia' => substr($value, 10, 2) .
                        "/" . substr($value, 12, 2) . "/" . substr($value, 14, 4),
                    'moeda' => trim(substr($value, 18, 4)),
                    'valor' => $valor,
                    'cidade' => trim(substr($value, 33, 30)),
                    'uf' => trim(substr($value, 63, 2)),
                );
            }
        }

        return $block;
    }

    private function block146($blocks)
    {
        // Total de Titulos Protestados Regionalizados
        $block146 = explode('146S', $blocks);
        $block['total_titulos_protestados_regionalizados'] = [];

        if (count($block146) > 1) {
            unset($block146[0]);

            foreach ($block146 as $key => $value) {
                $valorAcumulado = substr($value, 30, 13);
                $valorAcumulado = number_format(
                    substr($valorAcumulado, 0, strlen($valorAcumulado) - 2) . "." . substr($valorAcumulado, -2, 2),
                    2,
                    ',',
                    '.'
                );
                $block['total_titulos_protestados_regionalizados'][$key] = array(
                    'total' => trim(substr($value, 0, 8)),
                    'uf' => trim(substr($value, 8, 2)),
                    'periodo_inicial' => substr($value, 10, 2) .
                        "/" . substr($value, 12, 2) . "/" . substr($value, 14, 4),
                    'periodo_final' => substr($value, 18, 2) .
                        "/" . substr($value, 20, 2) . "/" . substr($value, 22, 4),
                    'moeda' => trim(substr($value, 26, 4)),
                    'valor_acumulado' => $valorAcumulado,
                );
            }
        }

        return $block;
    }

    private function block213($blocks)
    {
        // Indica Cheque Sustado para Documento Consultado
        $block213 = explode('213S', $blocks);
        $block['cheque_sustado'] = [];

        if (count($block213) > 1) {
            unset($block213[0]);

            foreach ($block213 as $key => $value) {
                $docType = substr($value, 0, 1);
                ($docType > 1) ? $doc = 'CNPJ' : $doc = 'CPF';

                $block['cheque_sustado'][$key] = array(
                    'tipo_documento' => $doc,
                    'documento' => trim(substr($value, 1, 14)),
                    'ocorrencia_sustado' => trim(substr($value, 15, 1)),
                );
            }
        }

        return $block;
    }

    private function block222($blocks)
    {
        //Informações Cadastrais de Pessoa Jurídica
        $block222 = explode('222S', $blocks);
        $block['informacoes_cadastrais_pj'] = [];

        if (count($block222) > 1) {
            $block['informacoes_cadastrais_pj'] = array(
                'cnpj' => trim(substr($block222[1], 0, 14)),
                'razao_social' => trim(substr($block222[1], 14, 55)),
                'nome_fantasia' => trim(substr($block222[1], 69, 55)),
                'condicao' => $this->getConditionType(substr($block222[1], 124, 1)),
                'data_de_fundacao' => trim(substr($block222[1], 125, 10)),
            );
        }

        return $block;
    }

    private function block242($blocks)
    {
        // Devoluções Informadas pelo CCF
        $block242 = explode('242S', $blocks);
        $block['devolucoes_ccf'] = [];

        if (count($block242) > 1) {
            unset($block242[0]);

            foreach ($block242 as $key => $value) {
                $docType = substr($value, 0, 1);
                ($docType > 1) ? $doc = 'CNPJ' : $doc = 'CPF';

                $block['devolucoes_ccf'][$key] = array(
                    'tipo_documento' => $doc,
                    'documento' => trim(substr($value, 1, 14)),
                    'nome' => trim(substr($value, 15, 50)),
                    'banco' => trim(substr($value, 65, 3)),
                    'agencia' => trim(substr($value, 68, 4)),
                    'total_dev_motivo_12' => trim(substr($value, 72, 3)),
                    'data_ultima_ocorrencia_m12' => substr($value, 75, 2) .
                        "/" . substr($value, 77, 2) . "/" . substr($value, 79, 4),
                    'total_dev_motivo_13' => trim(substr($value, 83, 3)),
                    'data_ultima_ocorrencia_m13' => substr($value, 86, 2) .
                        "/" . substr($value, 88, 2) . "/" . substr($value, 90, 4),
                    'total_dev_motivo_14' => trim(substr($value, 94, 3)),
                    'data_ultima_ocorrencia_m14' => substr($value, 97, 2) .
                        "/" . substr($value, 99, 2) . "/" . substr($value, 101, 4),
                    'total_dev_motivo_99' => trim(substr($value, 105, 3)),
                    'data_ultima_ocorrencia_m99' => substr($value, 108, 2) .
                        "/" . substr($value, 110, 2) . "/" . substr($value, 112, 4),
                );
            }
        }

        return $block;
    }

    private function block244($blocks)
    {
        $block244 = explode('244S', $blocks);
        $block['devolucoes_informadas_por_usuarios'] = [];

        if (count($block244) > 1) {
            unset($block244[0]);

            foreach ($block244 as $key => $value) {
                $docType = substr($value, 0, 1);
                ($docType > 1) ? $doc = 'CNPJ' : $doc = 'CPF';
                $valor = substr($value, 75, 11);
                $valor = number_format(
                    substr($valor, 0, strlen($valor) - 2) . "." . substr($valor, -2, 2),
                    2,
                    ',',
                    '.'
                );

                $block['devolucoes_informadas_por_usuarios'][$key] = array(
                    'tipo_documento' => $doc,
                    'documento' => trim(substr($value, 1, 14)),
                    'banco' => trim(substr($value, 15, 3)),
                    'agencia' => trim(substr($value, 18, 4)),
                    'conta_corrente' => trim(substr($value, 22, 15)),
                    'primeiro_cheque' => trim(substr($value, 37, 8)),
                    'ultimo_cheque' => trim(substr($value, 45, 8)),
                    'codigo_alinea' => trim(substr($value, 53, 2)),
                    'data_ocorrencia' => substr($value, 55, 2) .
                        "/" . substr($value, 57, 2) . "/" . substr($value, 59, 4),
                    'data_disponibilizacao' => substr($value, 63, 2) .
                        "/" . substr($value, 65, 2) . "/" . substr($value, 67, 4),
                    'moeda' => trim(substr($value, 71, 4)),
                    'valor' => $valor,
                    'informante' => trim(substr($value, 86, 36)),
                    'cidade' => trim(substr($value, 132, 20)),
                    'uf' => trim(substr($value, 152, 2)),
                );
            }
        }

        return $block;
    }

    private function block245($blocks)
    {
        // Cheques Sustados pelo Motivo 21 (contra-ordem ou oposição ao pagamento)
        $block245 = explode('245S', $blocks);
        $block['cheques_sustados_motivo_21'] = [];

        if (count($block245) > 1) {
            unset($block245[0]);

            foreach ($block245 as $key => $value) {
                $docType = substr($value, 0, 1);
                ($docType > 1) ? $doc = 'CNPJ' : $doc = 'CPF';
                $valor = substr($value, 75, 11);
                $valor = number_format(
                    substr($valor, 0, strlen($valor) - 2) . "." . substr($valor, -2, 2),
                    2,
                    ',',
                    '.'
                );

                $block['cheques_sustados_motivo_21'][$key] = array(
                    'tipo_documento' => $doc,
                    'documento' => trim(substr($value, 1, 14)),
                    'banco' => trim(substr($value, 15, 3)),
                    'agencia' => trim(substr($value, 18, 4)),
                    'conta_corrente' => trim(substr($value, 22, 15)),
                    'primeiro_cheque' => trim(substr($value, 37, 8)),
                    'ultimo_cheque' => trim(substr($value, 45, 8)),
                    'codigo_alinea' => trim(substr($value, 53, 2)),
                    'data_ocorrencia' => substr($value, 55, 2) .
                        "/" . substr($value, 57, 2) . "/" . substr($value, 59, 4),
                    'data_disponibilizacao' => substr($value, 63, 2) .
                        "/" . substr($value, 65, 2) . "/" . substr($value, 67, 4),
                    'moeda' => trim(substr($value, 71, 4)),
                    'valor' => $valor,
                    'informante' => trim(substr($value, 86, 36)),
                );
            }
        }

        return $block;
    }

    private function block249($blocks)
    {
        // Informações Cadastrais de Pessoa Física
        $block249 = explode('249S', $blocks);
        $block['informacoes_cadastrais_pf'] = [];

        if (count($block249) > 1) {
            $block['informacoes_cadastrais_pf'] = array(
                'nome' => trim(substr($block249[1], 0, 60)),
                'cpf' => trim(substr($block249[1], 60, 11)),
                'data_nascimento' => $nascimento = substr($block249[1], 71, 2) .
                    "/" . substr($block249[1], 73, 2) . "/" . substr($block249[1], 75, 4),
                'nome_mae' => trim(substr($block249[1], 79, 50)),
            );
        }

        return $block;
    }

    private function block254($blocks)
    {
        //Devoluções Informadas pelo CCF
        $block254 = explode('254S', $blocks);
        $block['resumo_devolucoes_ccf'] = [];

        if (count($block254) > 1) {
            unset($block254[0]);

            foreach ($block254 as $key => $value) {
                $docType = substr($value, 0, 1);
                ($docType > 1) ? $doc = 'CNPJ' : $doc = 'CPF';

                $block['resumo_devolucoes_ccf'][$key] = array(
                    'tipo_documento' => $doc,
                    'documento' => trim(substr($value, 1, 14)),
                    'total' => trim(substr($value, 15, 5)),
                    'data_primeira_ocorrencia' => substr($value, 20, 2) .
                        "/" . substr($value, 22, 2) . "/" . substr($value, 24, 4),
                    'data_ultima_ocorrencia' => substr($value, 28, 2) .
                        "/" . substr($value, 30, 2) . "/" . substr($value, 32, 4),
                );
            }
        }

        return $block;
    }

    private function block256($blocks)
    {
        // Resumo Cheques Sustados pelo Motivo 21 - Contra-Ordem ou Oposição ao Pagamento
        $block256 = explode('256S', $blocks);
        $block['resumo_cheques_sustados_motivo_21'] = [];

        if (count($block256) > 1) {
            unset($block256[0]);

            foreach ($block256 as $key => $value) {
                $docType = substr($value, 0, 1);
                ($docType > 1) ? $doc = 'CNPJ' : $doc = 'CPF';

                $block['resumo_cheques_sustados_motivo_21'][$key] = array(
                    'tipo_documento' => $doc,
                    'documento' => trim(substr($value, 1, 14)),
                    'total' => trim(substr($value, 15, 5)),
                    'data_primeira_ocorrencia' => substr($value, 20, 2) .
                        "/" . substr($value, 22, 2) . "/" . substr($value, 24, 4),
                    'data_ultima_ocorrencia' => substr($value, 28, 2) .
                        "/" . substr($value, 30, 2) . "/" . substr($value, 32, 4),
                );
            }
        }

        return $block;
    }

    private function block268($blocks)
    {
        // Resumo das Devoluções Informadas pelos Usuários
        $block268 = explode('268S', $blocks);
        $block['resumo_devolucoes_informadas_por_usuarios'] = [];

        if (count($block268) > 1) {
            unset($block268[0]);

            foreach ($block268 as $key => $value) {
                $docType = substr($value, 0, 1);
                ($docType > 1) ? $doc = 'CNPJ' : $doc = 'CPF';

                $block['resumo_devolucoes_informadas_por_usuarios'][$key] = array(
                    'tipo_documento' => $doc,
                    'documento' => trim(substr($value, 1, 14)),
                    'total' => trim(substr($value, 15, 5)),
                    'data_primeira_devolucao' => substr($value, 20, 2) .
                        "/" . substr($value, 22, 2) . "/" . substr($value, 24, 4),
                    'data_ultima_devolucao' => substr($value, 28, 2) .
                        "/" . substr($value, 30, 2) . "/" . substr($value, 32, 4),
                );
            }
        }

        return $block;
    }

    private function block300($blocks)
    {
        // Resumo das Ocorrências de Débitos (Pessoa Jurídica)
        $block300 = explode('300S', $blocks);
        $block['resumo_ocorrencias_debitos_pj'] = [];

        if (count($block300) > 1) {
            unset($block300[0]);

            foreach ($block300 as $key => $value) {
                $valor = substr($value, 9, 17);
                if (is_numeric($valor)) {
                    $valor = number_format(
                        substr($valor, 0, strlen($valor) - 2) . "." . substr($valor, -2, 2),
                        2,
                        ',',
                        '.'
                    );
                    $block['resumo_ocorrencias_debitos_pj'][$key] = array(
                        'total' => trim(substr($value, 0, 5)),
                        'moeda' => trim(substr($value, 5, 4)),
                        'valor' => $valor,
                        'data_primeiro_debito' => substr($value, 26, 2) .
                            "/" . substr($value, 28, 2) . "/" . substr($value, 30, 4),
                        'data_ultimo_debito' => substr($value, 34, 2) .
                            "/" . substr($value, 36, 2) . "/" . substr($value, 38, 4),
                    );
                }
            }
        }

        return $block;
    }

    private function block301($blocks)
    {
        // Relação dos Registros de Débitos
        $block301 = explode('301S', $blocks);
        $block['registros_debitos'] = [];

        if (count($block301) > 1) {
            unset($block301[0]);

            foreach ($block301 as $key => $value) {
                $tipo = substr($value, 0, 2);

                switch ($tipo) {
                    case 'RG':
                        $tipo = "Registrado";
                        break;

                    case 'IA':
                        $tipo = "Imob. e Administração de Bens";
                        break;

                    case 'EC':
                        $tipo = "Consórcio";
                        break;

                    case 'AL':
                        $tipo = "Locadora";
                        break;

                    case 'SF':
                        $tipo = "Emp. Crédito Imobiliário";
                        break;

                    case 'OJ':
                        $tipo = "Outras Ativ. Econômicas";
                        break;

                    case 'TP':
                        $tipo = "Título Protestado";
                        break;
                    default:
                        $tipo = substr($value, 0, 2);
                        break;
                }


                $situacao = trim(substr($value, 134, 1));

                switch ($situacao) {
                    case 'C':
                        $situacao = "Comprador";
                        break;

                    case 'A':
                        $situacao = "Avalista";
                        break;

                    default:
                        $situacao = substr($value, 134, 1);
                        break;
                }


                $document_type = substr($blocks, 135, 1);
                $document_type = ($document_type == 1) ? $document_type == 'CPF' : $document_type == 'CNPJ';
                $valor = substr($value, 119, 15);
                $valor = number_format(
                    substr($valor, 0, strlen($valor) - 2) . "." . substr($valor, -2, 2),
                    2,
                    ',',
                    '.'
                );

                $block['registros_debitos'][$key] = array(
                    'tipo' => $tipo,
                    'informante' => trim(substr($value, 2, 50)),
                    'doc_origem' => trim(substr($value, 52, 25)),
                    'data_debito' => substr($value, 77, 2) .
                        "/" . substr($value, 79, 2) . "/" . substr($value, 81, 4),
                    'data_disponibilizacao' => substr($value, 85, 2) .
                        "/" . substr($value, 87, 2) . "/" . substr($value, 89, 4),
                    'cidade' => trim(substr($value, 93, 20)),
                    'uf' => trim(substr($value, 113, 2)),
                    'moeda' => trim(substr($value, 115, 4)),
                    'valor' => $valor,
                    'situacao' => $situacao,
                    'tipo_documento' => $document_type,
                    'documento' => trim(substr($value, 136, 14)),
                );
            }
        }

        return $block;
    }

    private function block303($blocks)
    {
        // Quantidade de Consultas Anteriores (Pessoa Jurídica)
        $block303 = explode('303S', $blocks);
        $block['resumo_consultas_anteriores_pj'] = [];

        if (count($block303) > 1) {
            unset($block303[0]);

            foreach ($block303 as $key => $value) {
                $block['resumo_consultas_anteriores_pj'][$key] = array(
                    'total' => trim(substr($value, 0, 5)),
                    'data_primeira_consulta' => substr($value, 5, 2) .
                        "/" . substr($value, 7, 2) . "/" . substr($value, 9, 4),
                    'data_ultima_consulta' => substr($value, 13, 2) .
                        "/" . substr($value, 15, 2) . "/" . substr($value, 17, 4),

                );
            }
        }

        return $block;
    }

    private function block304($blocks)
    {
        // Consultas anteriores (Pessoa Jurídica)
        $block304 = explode('304S', $blocks);
        $block['consultas_anteriores_pj'] = [];

        if (count($block304) > 1) {
            unset($block304[0]);

            foreach ($block304 as $key => $value) {
                $tipo = substr($value, 0, 2);

                switch ($tipo) {
                    case 'CC':
                        $tipo = "Cartão de Crédito";
                        break;

                    case 'CD':
                        $tipo = "Crédito Direto";
                        break;

                    case 'CG':
                        $tipo = "Crédito Consignado";
                        break;

                    case 'CH':
                        $tipo = "Cheque";
                        break;

                    case 'CP':
                        $tipo = "Crédito Pessoal";
                        break;

                    case 'CV':
                        $tipo = "Crédito Veículos";
                        break;

                    case 'OU':
                        $tipo = "Outros";
                        break;

                    case 'FI':
                        $tipo = "Financeira";
                        break;

                    case 'ME':
                        $tipo = "Mercantil";
                        break;

                    case 'RC':
                        $tipo = "Renovação de Cadastro";
                        break;

                    case 'XX':
                        $tipo = "XX";
                        break;

                    default:
                        $tipo = substr($value, 0, 2);
                        break;
                }

                $block['consultas_anteriores_pj'][$key] = array(
                    'tipo' => $tipo,
                    'data' => substr($value, 2, 2) . "/" . substr($value, 4, 2) . "/" . substr($value, 6, 4),
                    'informante' => trim(substr($value, 10, 40)),

                );
            }
        }

        return $block;
    }

    private function block601($blocks)
    {
        // Score Crédito
        $block601 = explode('601S', $blocks);
        $block['score_credito'] = [];

        if (count($block601) > 1) {
            unset($block601[0]);

            foreach ($block601 as $key => $value) {
                $score_type = substr($value, 0, 1);
                ($score_type < 2) ? $tipo = "PF" : $tipo = "PJ";

                $block['score_credito'][$key] = array(
                    'tipo_score' => $tipo,
                    'score' => trim(substr($value, 1, 4)),
                    'plano_de_execucao' => trim(substr($value, 5, 1)), //Plano de Execução 'S' - Sim ou 'N' - Não
                    'nome_plano' => trim(substr($value, 6, 40)),
                    'nome_modelo_score' => trim(substr($value, 46, 40)),
                    'classificacao_numerica' => trim(substr($value, 86, 2)),
                    'classificacao_alfabetica' => trim(substr($value, 88, 1)),
                    'probabilidade_inadimplencia' => trim(substr($value, 89, 5)),
                    'texto_explicativo' => trim(substr($value, 94, 200)),

                );
            }
        }

        return $block;
    }

    private function block999($blocks)
    {
        // Erro
        $block999 = explode('999S', $blocks);
        if (count($block999) > 1) {
            unset($block999[0]);

            $message = $blocks;

            throw new Exception("Erro: " . $message, 1);
        }
    }

    /**
     * Add to dynamodb the result to, on future, could be count
     * on the consumption report
     *
     * @param string $url
     * @param string $jsonResult
     * @return void
     */
    private function insertCounter($params, $jsonResult)
    {
        $params = [
            'captura' => 'boavista',
            'fornecedor' => 'boavista',
            'consulta' => $params['consulta'],
            'criterio' => $params['cnpj'],
            'resultado' => $jsonResult,
        ];

        $bilhetador = new BilhetadorFornecedoresDynamo($params);
        $bilhetador->run();
    }
}
