<?php

namespace App\Crawler\BaseOffEscavador;

use App\Manager\Escavador\EscavadorManager;
use App\Crawler\Spider;
use App\Helper\Document;
use Carbon\Carbon;
use Exception;
use App\Helper\Str;

class BaseOffEscavador extends Spider
{
    //Número de termos para pegar por páginas
    private const NUMERO_TERMOS = 5;

    private $nome;
    private $courts = [];
    private $uf = [];
    private $manager;
    private $limit = 100;
    private $processes = [];
    private $countDetail = 0;

    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['nome'])) {
            throw new Exception("Parâmetro nome é obrigatório", 1);
        }

        if (Document::validarCpfOuCnpj($this->param['nome'])) {
            throw new Exception("Parâmetro inválido", 1);
        }

        if (!empty($this->param['limit'])) {
            $this->limit = $this->param['limit'];
        }

        if (!empty($this->param['tribunais'])) {
            $this->courts = $this->param['tribunais'];
        }

        if (!empty($this->param['uf'])) {
            $this->courts = [];
            $this->uf = $this->param['uf'];
        }

        $this->nome = $this->param['nome'];
    }

    protected function start()
    {
        $bilhetadorSource = !empty($this->param['bilhetadorSource']) ? $this->param['bilhetadorSource'] : '';
        $this->manager = new EscavadorManager($this->nome, $bilhetadorSource);
        $this->search();

        if (empty($this->processes)) {
            throw new Exception('Nenhum registro encontrado', 2);
        }

        return $this->processes;
    }

    private function adjustDate(string $date): string
    {
        return Carbon::parse($date)->format('d/m/Y');
    }

    private function search(): void
    {
        $response = $this->manager->searchCriteriaWithProcessByTerm();

        $count = 0;

        foreach ($response['items'] as $item) {
            $count++;
            if ($count > self::NUMERO_TERMOS) {
                break;
            }

            $this->getProcesses($item['id'], $item['link_api']);

            if (count($this->processes) >= $this->limit) {
                break;
            }
        }
    }

    private function getProcesses(int $peopleId, string $linkApi, $page = 1): void
    {
        $tipo = "pessoas";

        if (preg_match('#instituicoes#isu', $linkApi)) {
            $tipo = "instituicoes";
        }

        $response = $this->manager->searchCriteriaProcesses($peopleId, $tipo, $this->courts, $this->uf, $page);

        if (empty($response)) {
            return;
        }

        $detailProcess = $this->getProcessDetail($response['items']);

        $this->processes = array_merge($this->processes, $detailProcess);
        $nextPage = $response['links']['next'];

        if ($this->nextPage($nextPage)) {
            $this->getProcesses($peopleId, $page + 1);
        }
    }

    private function nextPage($nextPage): bool
    {
        if (empty($nextPage)) {
            return false;
        }

        if (count($this->processes) >= $this->limit) {
            return false;
        }

        return true;
    }

    private function getProcessDetail(array $processes): array
    {
        if (empty($processes)) {
            return [];
        }

        $result = [];

        foreach ($processes as $process) {
            if ($this->countDetail >= $this->limit) {
                break;
            }

            $detail = $this->parseMovement(
                $this->manager->searchProcessDetails($process['id'])
            );
            $result[] = array_merge($process, $detail);
            $this->countDetail++;
        }

        return $result;
    }

    private function parseMovement(array $process): array
    {
        if (empty($process['ultimas_movimentacoes_resumo'])) {
            return $process;
        }

        for ($i = 0; $i < count($process['ultimas_movimentacoes_resumo']); $i++) {
            $process['ultimas_movimentacoes_resumo'][$i]['data'] = $this->adjustDate(
                $process['ultimas_movimentacoes_resumo'][$i]['data']
            );

            $process['ultimas_movimentacoes_resumo'][$i]['conteudo_resumo'] = html_entity_decode(
                $process['ultimas_movimentacoes_resumo'][$i]['conteudo_resumo']
            );
        }

        return $process;
    }
}
