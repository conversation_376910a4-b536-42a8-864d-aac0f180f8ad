<?php

namespace App\Crawler\Cetesb;

use App\Crawler\Cetesb\Models\CetesbDocumentoModel;
use App\Crawler\Cetesb\Models\CetesbModel;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Helper\Util;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

class Cetesb extends Spider
{
    private const BASE_URL = 'https://licenciamento.cetesb.sp.gov.br/cetesb/';
    private const PDF_URL = 'https://autenticidade.cetesb.sp.gov.br/ajax/DocSipol.php?';
    private const PDF_VIEWER = 'https://autenticidade.cetesb.sp.gov.br/pdf/';

    private $document;
    private $type = 'razao';

    public function start()
    {
        $search = $this->search($this->type);
        $responseSearchLink = $this->getSearchLink($search);
        $parseResults = $this->parseResults($responseSearchLink);
        return $parseResults;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->document = trim($this->param['company_cnpj']);

        if (empty($this->document)) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        if (is_numeric(Document::removeMask($this->document))) {
            if (!Document::validarCnpj($this->document)) {
                throw new Exception('Cnpj Inválido', 6);
            }
            $this->document = Document::removeMask($this->document);
            $this->type = 'cnpj';
        }
    }

    /**
     * Primeira requisição ao site para listar os resultados
     *
     * <AUTHOR> Guilherme Sório - 16/03/2021
     * @return string
     */
    private function search($type)
    {
        $params = [
            'sd' => '',
            'cgc' => '',
            'razao' => $this->document,
            'endereco' => '',
            'cep' => '',
            'Submit' => 'Consulte ...'
        ];

        if ($type == 'cnpj') {
            $params['razao'] = '';
            $params['cgc'] = $this->document;
        }

        $result = $this->getResponse(self::BASE_URL . 'processo_resultado.asp', 'POST', $params);
        return $result;
    }

    /**
     * Efetuar a pesquisa no resultado da busca para pegar o link de pesquisa
     * corrrespondentes a empresa em questão
     *
     * <AUTHOR> Guilherme Sório - 16/03/2021
     * @param string $result
     * @return string
     */
    private function getSearchLink($result)
    {
        preg_match('/<a\s?href="(.*?)"/i', $result, $match);

        if (empty($match)) {
            throw new Exception('Nenhum resultado encontrado', 2);
        }
        //Efetuar o parse do link, pois colocando diretamente estava ocorrendo erro 400
        $patterns = [
            'razao' => ['@razao=(.*?)&@is'],
            'muni' => ['@muni=(.*?)&@is'],
            'logrd' => ['@logrd=(.*?)&@is'],
            'nmuncp' => ['@nmuncp=(.*?)&@is'],
            'nseqnc' => ['@nseqnc=(.*?)&@is'],
            'cgc' => ['@cgc=(.*)@is'],
        ];

        $link = http_build_query(Util::parseDados($patterns, $match[1]));
        $getResponseLink = $this->getResponse(self::BASE_URL . 'processo_resultado2.asp?' . $link);
        return $getResponseLink;
    }

    /**
     * Método responsável por efetuar o parse da resposta do link de pesquisa
     * do cnpj em questão
     *
     * <AUTHOR> Guilherme Sório - 16/03/2021
     * @param $responseLinkSearch
     * @return object
     */
    private function parseResults($responseLinkSearch)
    {
        $registredData = $this->parseRegistredData($responseLinkSearch);
        $documentResults = $this->parseDocumentsResult($responseLinkSearch);
        $finalParse = $this->finalParse($registredData, $documentResults);
        return $finalParse;
    }

    /**
     * Faz a criação do objeto completo
     *
     * <AUTHOR> Guilherme Sório - 18/03/2021
     * @param array $registredData
     * @param array $documentsResults
     * @return object
     */
    private function finalParse($registredData, $documentResults)
    {
        $cetesbModel = new CetesbModel();
        foreach ($registredData as $key => $value) {
            $cetesbModel->$key = $value;
        }
        $cetesbModel->documentos = $this->finalParseCetesbDocuments($documentResults);
        return $cetesbModel;
    }

    /**
     * Retorna o parse final dos documentos relacionados ao cetesb
     *
     * <AUTHOR> Guilherme Sório - 18/03/2021
     * @param array $documentResults
     * @return array
     */
    private function finalParseCetesbDocuments($documentResults)
    {
        $documents = [];
        foreach ($documentResults as $document) {
            $cetesbDocumentModel = new CetesbDocumentoModel();
            foreach ($document as $key => $value) {
                $cetesbDocumentModel->$key = $value;
            }
            $documents[] = $cetesbDocumentModel;
        }

        return $documents;
    }

    /**
     *
     * Parse dos dados cadsastrais do cnpj pesquisado
     *
     * <AUTHOR> Guilherme Sório - 17/03/2021
     * @param string $response
     * @return array
     */
    private function parseRegistredData($response)
    {
        $patterns = [
            'razaoSocial' => ['@<b>Raz&atilde;o.*?<\/font>\s?-\s?(.*?)<\/td>@is', null],
            'logradouro' => ['@<b>Logradouro<\/b><\/font>\s?-\s?(.*?)<\/td>@is', null],
            'numero' => ['@<b>N&ordm;<\/b><\/font>(.*?)<\/td>@is', null],
            'complemento' => ['@<b>Complemento<\/b><\/font>\s?-\s?(.*?)<\/td>@', null],
            'bairro' => ['@<b>Bairro<\/b><\/font>\s?-\s?(.*?)<\/td>@is', null],
            'cep' => ['@<b>CEP<\/b><\/font>\s?-\s?(.*?)<\/td>@is', null],
            'municipio' => [
                '@<b>Munic&iacute;pio<\/b><\/font>.*?\s?-\s?(.*?)<\/td>@is',
                null
            ],
            'cnpj' => ['@<b>CNPJ<\/b><\/font>.*?(\d{2}.\d{3}.\d{3}\/\d{4}-\d{2})@is', null],
            'numeroCadastroCetesb' => [
                '@N.*?do\sCadastro\sna\sCETESB<\/b><\/font>\s?-\s?(.*?)<\/td>@is',
                null
            ],
            'atividade' => ['@<b>Descri.*?o\sda\sAtividade.*?<\/b><\/font>(.*?)<\/td>@is', null]
        ];

        $data = Util::parseDados($patterns, $response);
        return $data;
    }

    /**
     * Separar os dados da tabela de documentos
     *
     * <AUTHOR> Guilherme Sório - 17/03/2021
     * @param string $response
     * @return array
     */
    private function parseDocumentsResult($response)
    {
        $tableResult = $this->parseTableResult($response);
        $rowsResult = $this->parseRowsResult($tableResult);
        return $rowsResult;
    }

    /**
     * Separa a tabela dos documentos
     *
     * <AUTHOR> Guilherme Sório - 17/03/2021
     * @param string $response
     * @return string
     */
    private function parseTableResult($response)
    {
        $pattern = '#<table\swidth="700".*?>(.*?)<\/table>#s';
        preg_match_all($pattern, $response, $matches);
        return $matches[1][1];
    }

    /**
     * Separa o cabecalho dos documentos
     * <AUTHOR> Guilherme Sório - 17/03/2021
     * @param string $tableReponse
     * @return array
     */
    private function parseRowsResult($tableReponse)
    {
        preg_match_all('#<tr.*?>(.*?)<\/tr>#s', $tableReponse, $matches);
        $results = $matches[1];
        //Retirar os primeiros matches, pois são linha de separacao e cabecalho
        // e a ultima linha
        array_splice($results, 0, 2);
        array_splice($results, -1, 1);

        $rows = [];
        foreach ($results as $result) {
            $rows[] = $this->parseRow($result);
        }

        return $rows;
    }

    /**
     * Separa cada linha com seu respectivo resultado
     *
     * <AUTHOR> Guilherme Sório - 17/03/2021
     * @param string $row
     * @return array
     */
    private function parseRow($row)
    {
        preg_match_all('#<td.*?>(.*?)<\/td>#s', $row, $matches);

        //Pegar o id do documento
        preg_match('/\?idocmn=(\d+)/', $matches[1][4], $match);
        $documentId = $match[1];

        foreach ($matches[1] as $key => $match) {
            //Retirando espacos, tags, caracteres \r\t e outros
            $matches[1][$key] = utf8_encode(trim(strip_tags($match)));
        }

        $finalRow = $matches[1];

        //Pegar o link correspondente ao pdf do servidor do cetesb
        $pdfUrl = $this->getPdfLink($documentId, $finalRow[4]);

        //Montar o array na estrutura do objeto do documento cetesb
        $cetesbDocument = [
            'sdNumero' => $finalRow[0],
            'dataSd' => $finalRow[1],
            'numeroProcesso' => $finalRow[2],
            'objetoSolicitacao' => $finalRow[3],
            'numeroDocumento' => $finalRow[4],
            'situacao' => $finalRow[5],
            'desde' => $finalRow[6],
            'pdf' => $pdfUrl
        ];

        return $cetesbDocument;
    }

    /**
     * Retorna o Link do servidor do Cetesb, caso exista, senão retorna null
     *
     * <AUTHOR> Guilherme Sório - 17/03/2021
     * @param integer $documentId
     * @param integer $documentNumber
     * @return string || null
     */
    private function getPdfLink($documentId, $documentNumber)
    {
        $params = [
            'idocmn' => $documentId,
            'ndocmn' => $documentNumber,
            'method' => 'post'
        ];

        $response = $this->getResponse(self::PDF_URL . http_build_query($params));

        if (preg_match("#<strong>Falha\sde\sComunica.*?o<\/strong>#s", $response)) {
            return null;
        }
        preg_match("/VisualizacaoSipol\('(\d+)'\)/", $response, $match);
        return self::PDF_VIEWER . $match[1] . '.pdf';
    }
}
