<?php

namespace App\Crawler\ColunaVertebralConsultaPf;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Crawler\ReceitaFederalPfGeneric\Models\ReceitaFederalPfGenericModel;
use App\Crawler\ColunaVertebralConsultaPf\Models\ColunaVertebralPfModel;
use App\Manager\ColunaVertebralManager;
use DateTime;
use Exception;

/**
 * Classe da fonte Coluna Vertebral PF
 *
 * @version 1.0.0
 *
 * <AUTHOR> Mesquita 01/10/2020
 */
class ColunaVertebralConsultaPf extends Spider
{
    private $colunaVertebralManager;

    /**
     * Busca os dados de pessoa fisica no Spine PF
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 01/10/2020
     *
     * @return array
     */
    public function start()
    {
        $this->colunaVertebralManager = new ColunaVertebralManager();

        return (array) $this->searchByCpf();
    }

    /**
     * Valida os parametros da fonte
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 01/10/2020
     *
     * @return void
     */
    public function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCpf($this->param['cpf'])) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }

        $this->param['cpf'] = Document::removeMask($this->param['cpf']);
    }

    /**
     * Retorna os dados PF do critério informado
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 01/10/2020
     *
     * @return array
     */
    private function searchByCpf(): ColunaVertebralPfModel
    {
        $spinePf = $this->colunaVertebralManager->getSpinePf($this->param['cpf']);

        $result = new ColunaVertebralPfModel();

        $birthDate = new DateTime($spinePf['data_nascimento']);

        $result->cpf = $spinePf['cpf'];
        $result->nome = $spinePf['nome'];
        $result->situacao = $spinePf['situacao'] ?? null;
        $result->data_hora = isset($spinePf['data_situacao'])
            ? $this->colunaVertebralManager->formatDate(
                "{$spinePf['data_situacao']}"
            )
            : null;
        $result->codigo = $spinePf['hash_rf'] ?? null;
        $result->count = 0;
        $result->dv = $spinePf['digito_verificador'] ?? 0;
        $result->inscricao = !empty($spinePf['inscricao'])
            ? (new DateTime($spinePf['inscricao']))->format('d/m/Y')
            : null;
        $result->cpf_nome = "{$spinePf['cpf']}|{$spinePf['nome']}";
        $result->nascimento = $birthDate->format('d/m/Y');
        $result->data_obito = $spinePf['data_obito'] ?? null;
        $result->idade = $birthDate->diff(new DateTime('now'))->y;

        return $result;
    }

    public function parseToGeneric(array $data, string $sourceName): ReceitaFederalPfGenericModel
    {
        $model = new ReceitaFederalPfGenericModel();

        $dataHora = explode(' ', $data['data_hora']);

        $model->source = $sourceName;
        $model->cpf = Document::formatCpf($data['cpf']);
        $model->nome = $data['nome'];
        $model->nascimento = $data['nascimento'];
        $model->situacao = $data['situacao'];
        $model->inscricao = $data['inscricao'];
        $model->digito_verificador = $data['dv'];
        $model->obito = $data['data_obito'];
        $model->hora = $dataHora[1];
        $model->data = $dataHora[0];
        $model->chave = $data['codigo'];
        $model->cpf_nome = "{$model->cpf}|{$data['nome']}";
        $model->nome_dataNascimento = "{$data['nome']}|{$data['nascimento']}";

        return $model;
    }
}
