<?php

namespace App\Crawler\SituacaoCadastralSp;

use App\Helper\Document;
use App\Crawler\Spider;
use Exception;

class SituacaoCadastralSp extends Spider
{
    private const BASE_URL = 'https://cpom.prefeitura.sp.gov.br';
    private const MAIN_URL = '/prestador/SituacaoCadastral';
    private const VALIDA_CAPTCHA_URL = '/prestador/SituacaoCadastral/ValidaCaptcha';
    private const LOGIN_CAPTCHA_URL = '/prestador/SituacaoCadastral/Login';
    private const DATA_URL = '/prestador/SituacaoCadastral/ConsultaSituacaoCadastral';
    private const PERIODO_URL = '/prestador/SituacaoCadastral/CarregarHistorico';
    private $cnpj;
    private $tmp_captcha_path;

    protected function start()
    {
        $uniqid = md5(uniqid(rand(), true));
        $this->tmp_captcha_path = '/tmp/captcha_' . $uniqid . '.bmp';

        $response = $this->getDataResponse($this->cnpj);
        $parsedData = $this->parseDataToResponse($response);

        return $parsedData;
    }

    private function parseDataToResponse($response)
    {
        $data = array(
            'cnpj'  =>  '',
            'razao_social'  =>  '',
            'situacao'  =>  '',
            'periodos'  =>  [],
        );
        $patterns = array(
            'cnpj'  =>  "/<input[^<]*?id=.cnpj.[^<]*?value=.([^<]*?)\"/is",
            'razao_social'  =>  "/<input[^<]*?id=.razaoSocial.[^<]*?value=.([^<]*?)\"/is",
            'situacao'  =>  "/msgTexto\\s*=\\s*\"[^\"]*?&lt;b&gt;(.*?)&lt;/is",
        );
        foreach ($patterns as $key => $pattern) {
            if (preg_match($pattern, $response, $output)) {
                $data[$key] = html_entity_decode($output[1]);
            }
        }

        if (preg_match_all('/PESSOA\sJUR.DICA\sN.O\sCADASTRADA\sJUNTO/isu', $response)) {
            throw new Exception("Nenhum resultado encontrado", 2);
        }

        /*
        * Captura Atividade Cadastrada
        */
        preg_match_all(
            '/<tr>\s*\s<td>((?s).*?)<\/td>\s*\s<td>((?s).*?)<\/td>\s*\s<td\sstyle="text-align:center">(.*?)<\/td/isu',
            $response,
            $outputAtividadesCadastradas
        );

        if (count($outputAtividadesCadastradas[0]) > 0) {
            $outAtCadastradas = $outputAtividadesCadastradas[0][0];

            preg_match('/<td\sstyle="text-align:center">(.*?)<\//', $outAtCadastradas, $periodoAtual);
            $periodo = array(
                'ativo'         =>  true,
                'data_inicial'  => $periodoAtual[1],
                'data_final'    => '',
                'atividades'    => []
            );

            if (preg_match_all('/<td>(.*?)<\//', $outAtCadastradas, $atividadesOut)) {
                $atividadesOut = $atividadesOut[1];
                for ($i = 0; $i < count($atividadesOut); $i++) {
                    $atividadeParseada = explode(' - ', $atividadesOut[$i]);
                    $atividade = array(
                        'codigo'    =>  html_entity_decode($atividadeParseada[0]),
                        'descricao' =>  html_entity_decode($atividadeParseada[1])
                    );
                    $periodo['atividades'][] = $atividade;
                }
                $data['periodos'][] = $periodo;
            }
        }

        /**
         * Pegar periodo atual
         */
        preg_match_all(
            "/<tr>\\s*<td>([^<]*?)<\\/td>\\s*<td>([^<]*?)<\\/td>\\s*<\\/tr>/isu",
            $response,
            $outputAtividadesAtuais
        );

        if (count($outputAtividadesAtuais[0]) > 0) {
            if (
                preg_match(
                    "/a\\s*partir\\s*de\\s*(\\d{2}\\/\\d{2}\\/\\d{4})/isu",
                    $data['situacao'],
                    $outputPeriodoAtual
                )
            ) {
                $periodoAtual = $outputPeriodoAtual[1];
            } else {
                $periodoAtual = '';
            }
            $periodo = array(
                'ativo'         =>  true,
                'data_inicial'  => $periodoAtual,
                'data_final'    => '',
                'atividades'    => []
            );
            for ($i = 0; $i < count($outputAtividadesAtuais[0]); $i++) {
                $atividadeParseada = explode(' - ', $outputAtividadesAtuais[2][$i]);
                $atividade = array(
                    'codigo'    =>  html_entity_decode($atividadeParseada[0]),
                    'descricao' =>  html_entity_decode($atividadeParseada[1])
                );
                $periodo['atividades'][] = $atividade;
            }
            $data['periodos'][] = $periodo;
        }

        /**
         * Pegar Periodos Antigos
         */
        preg_match_all(
            "/<a\\s*onclick=.abrirHistorico.([^<]*?),\\s*([^<]*?),\\s*([^<]*?).;.\\s*style=.cursor" .
                ":pointer;.>[^<]*?(\\d{2}\\/\\d{2}\\/\\d{4})[^<]*?(\\d{2}\\/\\d{2}\\/\\d{4})[^<]" .
                "*?<\\/a>\\s*<br\\s*\\/>/isu",
            $response,
            $outputPeriodos
        );
        for ($i = 0; $i < count($outputPeriodos[0]); $i++) {
            $postData = array(
                'cnpjPrestador'     =>  $outputPeriodos[1][$i],
                'identPrestador'    =>  $outputPeriodos[2][$i],
                'bVigente'          =>  $outputPeriodos[3][$i],
            );
            $periodo = array(
                'ativo'         =>  false,
                'data_inicial'  => $outputPeriodos[4][$i],
                'data_final'    => $outputPeriodos[5][$i],
                'atividades'    => []

            );
            $periodoResponse = $this->getResponse(
                self::BASE_URL . self::PERIODO_URL,
                'POST',
                $postData
            );
            preg_match_all(
                "/<tr>\\s*<td>([^<]*?)<\\/td>\\s*<td>([^<]*?)<\\/td>\\s*<td>([^<]*?)<\\/td>\\s*<\\/tr>/is",
                $periodoResponse,
                $outputAtividades
            );
            for ($j = 0; $j < count($outputAtividades[0]); $j++) {
                $atividadeParseada = explode(' - ', $outputAtividades[2][$j]);
                $atividade = array(
                    'codigo'    =>  html_entity_decode($atividadeParseada[0]),
                    'descricao' =>  html_entity_decode($atividadeParseada[1])
                );
                $periodo['atividades'][] = $atividade;
            }
            $data['periodos'][] = $periodo;
        }
        return $data;
    }

    private function getDataResponse($cnpj)
    {
        $response = '';
        $tries = 2;
        for ($i = 0; $i < $tries; $i++) {
            $response = $this->getResponse(self::BASE_URL . self::MAIN_URL);
            if (preg_match("/<img [^<]*?id=.imgCaptcha.[^<]*?src=.([^<]*?).\\s*alt/isu", $response, $output)) {
                $captchaUrl = self::BASE_URL . $output[1];
            } else {
                throw new Exception('Falha ao captura o captcha', 100);
            }

            // Guardar bmp temporário
            $captchaResponse = $this->getResponse($captchaUrl);
            file_put_contents($this->tmp_captcha_path, $captchaResponse);

            // Converter para PNG pois o fornecedor que quebra o captcha não suporta bmp.
            // imagecreatefrombmp
            $im = imagecreatefrompng($this->tmp_captcha_path);
            imagepng($im, $this->captcha_path, 0);

            // Quebra o captcha
            $this->breakCaptcha();

            $validacaoResponse = $this->getResponse(self::BASE_URL . self::VALIDA_CAPTCHA_URL, 'POST', array(
                'TextoCaptcha' => $this->captcha,
            ));
            if (preg_match("/Texto\\s*da\\s*imagem\\s*inv.*?lido./is", $validacaoResponse)) {
                if ($i >= ($tries - 1)) {
                    throw new Exception('Captcha incorreto', 100);
                }
                continue;
            }
            $this->getResponse(self::BASE_URL . self::LOGIN_CAPTCHA_URL, 'POST', array(
                'Usuario' => $cnpj,
                'textoCaptcha' => $this->captcha,
            ));

            $response = $this->getResponse(self::BASE_URL . self::DATA_URL);

            if (preg_match("/<img [^<]*?id=.imgCaptcha.[^<]*?src=.([^<]*?).\\s*alt/is", $response)) {
                if ($i >= ($tries - 1)) {
                    throw new Exception('Captcha incorreto', 100);
                }
                continue;
            }
            break;
        }

        return $response;
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if (
            isset($this->param['cnpj'])
            && !empty($this->param['cnpj'])
            && Document::validarCnpj($this->param['cnpj'])
        ) {
            $cnpj = preg_replace('/\D+/', '', $this->param['cnpj']);
        } else {
            throw new Exception('Parâmetro de criterio inválido', 1);
        }

        $this->cnpj = $cnpj;
    }
}
