<?php

namespace App\Crawler\ReporterBrasil;

use App\Crawler\Spider;
use App\Helper\Str;
use Symfony\Component\DomCrawler\Crawler;
use Exception;

class ReporterBrasil extends Spider
{
    private const BASE_URL = "https://reporterbrasil.org.br";

    private $criterio;
    private $limit;
    private $number_of_results = 0;
    private $data = [];

    public function start()
    {
        $this->setProxy();
        $this->getData();

        if (empty($this->number_of_results)) {
            throw new Exception('Nenhum resultado encontrado.', 2);
        }

        return $this->data;
    }


    private function getData()
    {
        $page = 1;
        /* Esse laço verifica o numero de resultados e percorre as páginas do site */
        do {
            $this->search($page);
            $page++;
        } while ($this->number_of_results < $this->limit);
    }

    /** Realiza busca no site
     * <AUTHOR> - 01 jul 22
     * @param  int  $page
     * @throws Exception
     */
    private function search(int $page)
    {
        $params = [
            'wpas' => 1,
            'search_query' => $this->criterio
        ];

        $url = self::BASE_URL . "/busca/page/{$page}/?" . http_build_query($params);

        $html = $this->getResponse($url);

        $crawler = new Crawler($html);
        $results = $crawler->filter('article')->count();

        if ($results > 0) {
            for ($x = 0; $x < $results; $x++) {
                $title = $crawler->filter('article')->eq($x)->children('h2 > a')->text();
                $link = $crawler->filter('article')->eq($x)->children('h2 > a')->attr('href');
                $subtitle = $crawler->filter('article')->eq($x)->children('.metadata')->text();

                $description = $this->getDetail($link);

                $this->data[] = [
                    'title' => $title,
                    'subtitle' => $subtitle,
                    'link' => $link,
                    'description' => $description
                ];

                $this->number_of_results++;
                if ($this->limit == $this->number_of_results) {
                    break;
                }
            }
        } else {
            $this->limit = $this->number_of_results;
        }
    }

    /** Busca a reportagem na íntegra
     * @return string
     * <AUTHOR> Santos - 01 jul 22
     * @param  string  $link
     * @throws Exception
     */
    private function getDetail(string $link): string
    {
        $response = $this->getResponse($link);
        $crawler = new Crawler($response);

        $text = $crawler->filter('.entry-content')->html();
        $text = Str::stripTagsRecursive($text);
        return Str::mbTrim($text);
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->criterio = trim($this->param['criterio']);
        $this->limit = $this->param['limit'] ?? 10;

        if (empty($this->criterio)) {
            throw new Exception('Parâmetro de critério inválido', 1);
        }
    }
}
