<?php

namespace App\Crawler\IbamaAreaEmbargadaOffline;

use Exception;
use App\Helper\Str;
use App\Crawler\Spider;
use App\Helper\Document;
use Tightenco\Collect\Support\Arr;
use App\Factory\MongoDB;

class IbamaAreaEmbargadaOffline extends Spider
{
    private $isDocument = false;

    private const INDEX_MONGODB = 'document_name';
    private const LIMIT = 30;

    /** @var string */
    private $criterion;

    /** @var int */
    private $limit = 0;

    protected function validateAndSetCrawlerAttributes()
    {
        $this->limit = $this->param['limit'] ?? self::LIMIT;
        $this->limit = intval($this->limit);

        $criterion = trim($this->param['nome_cpf_cnpj']);

        if (empty($criterion)) {
            throw new Exception("Nenhum Parâmetro informado!", 3);
        }

        if (Document::validarCpfOuCnpj($criterion)) {
            $this->isDocument = true;
            $this->criterion = Document::removeMask($criterion);

            return;
        }

        $this->isDocument = false;
        $this->criterion = strtoupper(Str::removerAcentos($criterion));
    }

    protected function start()
    {
        if ($this->isDocument) {
            $response = [
                'dados' => $this->searchByDocument($this->criterion, $this->limit)
            ];
            return $response;
        }

        $response = [
            'dados' => $this->searchByName($this->criterion, $this->limit)
        ];
        return $response;
    }

    private function searchByDocument(string $criterion, int $limit): array
    {
        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('common')
            ->setCollection('ibama_area_embargada');
        $fields = ['documento'];
        $results = json_decode(
            json_encode(
                $manager
                    ->atlasSearch(
                        $criterion,
                        self::INDEX_MONGODB,
                        self::LIMIT,
                        $fields
                    )
                    ->toArray(),
                true
            ),
            true
        );

        if (count($results) === 0) {
            throw new Exception('Nenhum resultado encontrado', 2);
        }

        return collect($results)
            ->map(function ($areaEmbargada) {
                $areaEmbargada = Arr::except($areaEmbargada, ['created_at', 'updated_at']);

                return array_merge(
                    $areaEmbargada,
                    [
                        'nomeRazao' => $areaEmbargada['nome_razao'],
                        'dataInsercao' => $areaEmbargada['data_insercao']['date']
                    ]
                );
            })
            ->toArray();
    }

    private function searchByName(string $criterion, int $limit): array
    {
        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('common')
            ->setCollection('ibama_area_embargada');
        $fields = ['nome_razao'];
        $results = json_decode(
            json_encode(
                $manager
                    ->atlasSearch(
                        $criterion,
                        self::INDEX_MONGODB,
                        self::LIMIT,
                        $fields
                    )
                    ->toArray(),
                true
            ),
            true
        );

        if (count($results) === 0) {
            throw new Exception('Nenhum resultado encontrado', 2);
        }

        return collect($results)
            ->map(function ($areaEmbargada) {
                $areaEmbargada = Arr::except($areaEmbargada, ['created_at', 'updated_at']);

                return array_merge(
                    $areaEmbargada,
                    [
                        'nomeRazao' => $areaEmbargada['nome_razao'],
                        'dataInsercao' => $areaEmbargada['data_insercao']['date']
                    ]
                );
            })
            ->toArray();
    }
}
