<?php

namespace App\Crawler\Cuit;

use App\Crawler\Spider;
use App\Helper\Str;
use Exception;

class Cuit extends Spider
{
    private const BASE_URL = 'https://www.cuitonline.com/';
    private const GET_URL = 'https://www.cuitonline.com/search.php?q=';

    private $name = '';

    protected function validateAndSetCrawlerAttributes()
    {
        $nome = $this->param['criterio'];

        if (is_array($nome)) {
            $nome = $nome[0];
        }

        $this->name = str_replace(' ', '+', $nome);
    }

    protected function start()
    {
        $this->setProxy();

        $searchHtml = $this->getResponse(self::GET_URL . $this->name);
        $searchHtml = $this->getPaginationHtml($searchHtml);

        $searchHtml = Str::encoding($searchHtml);
        $initialParse = $this->getDados($searchHtml);
        $semiLastParse = $this->getDadosFinal($initialParse);

        return $semiLastParse;
    }

    private function getdados($html)
    {
        $allPattern = '/"hit" onmouseover="javascript:thi.*?backgroundColor=\'#FFF\';">(.*?)<\/div> ' .
            '<div style="clear:both;">/m';
        preg_match_all($allPattern, $html, $all);

        $linkPattern = '/<a\shref="detalle(.*?)"\s/m';
        $nomesPattern = '/class="denominacion"><span class="denominacion">(.*?)<\/span>/m';
        $personaPattern = '/>Persona(.*?)<br\/>/m';
        $gananciasPattern = '/Ganancias:(.*?)<br\/>/m';
        $ivapattern = '/IVA:(.*?) <br\/>/m';
        $empleadorPattern = '/Empleador/m';

        $nome = [];
        $link = [];
        $persona = [];
        $ganancia = [];
        $iva = [];
        $empleador = [];

        foreach ($all[1] as $key => $value) {
            preg_match($linkPattern, $value, $links);
            preg_match($nomesPattern, $value, $nomes);
            preg_match($personaPattern, $value, $personas);
            preg_match($gananciasPattern, $value, $ganacias);
            preg_match($ivapattern, $value, $ivas);

            if (preg_match($empleadorPattern, $value)) {
                $empleador[] = 'Empleador';
            } else {
                $empleador[] = '-';
            }

            // Removendo Warnings
            $nomes[1] = (isset($nomes[1])) ? $nomes[1] : null;
            $links[1] = (isset($links[1])) ? $links[1] : null;
            $personas[1] = (isset($personas[1])) ? $personas[1] : null;
            $personas[1] = str_replace(array('<i>', '<b>', '</b>', '</i>'), '', $personas[1]);
            $ganacias[1] = (isset($ganacias[1])) ? $ganacias[1] : null;
            $ivas[1] = (isset($ivas[1])) ? $ivas[1] : null;

            $nome[] = $nomes[1];
            $link[] = $links[1];
            $persona[] = $personas[1];
            $ganancia[] = $ganacias[1];
            $iva[] = $ivas[1];
        }

        $initialParse = [];

        foreach ($link as $chave => $valor) {
            $initialParse[] = [
                'nome' => $nome[$chave],
                'persona' => $persona[$chave],
                'ganancia' => $ganancia[$chave],
                'chave' => $iva[$chave],
                'link' => $valor
            ];
        }

        if (empty($initialParse)) {
            throw new Exception('A Consulta não retornou resultados com o critério informado.', 2);
        }

        return $initialParse;
    }

    private function getDadosFinal($parse)
    {
        $semiLastParse = [];
        foreach ($parse as $key => $value) {
            try {
                $html = $this->getResponse(self::BASE_URL . 'detalle' . $value['link']);
            } catch (Exception $e) {
                if ($this->debug) {
                    print $e;
                }
            }

            // file_put_contents(__DIR__.'htmlfinal.html', $html);

            $localidadPattern = '/Localidad.*?black">(.*?)<\/span>/m';
            $personapattern = '/&nbsp; Persona <span class.*?">(.*?)<\/li>/m';
            $personaType = '/(.*?) /m';
            $personaGender = '/\(<span .*?"gender".*?>(.*?)<\/span>/m';
            $personaNationality = '/<span .*?"nationality".*?>(.*?)<\/span>/m';
            $provinciaPattern = '/Provincia: <span itemprop="addressRegion" class="p_cuit c_black">(.*?)<\/span>/m';
            $contratoSocialPattern = '/ Fecha de contrato social:.*?">(.*?)<\/span>/m';
            $empladorPattern = '/Empleador: <span class="p_cuit c_black">(.*?)<\/span>/m';
            $impuestoAllattern = '/Impuestos activos:(.*?)Regímenes activo/m';
            $impuestoPattern = '/style="dis.*?auto;">(.*?)<span class="tax-period">(.*?)<\/span>/m';
            $regimenesallPattern = '/Regímenes activo(.*?)Actividad/m';
            $regimenesPattern = '/style="dis.*?type">(.*?):<\/span>(.*?)<span class="tax-period">(.*?)<\/span>/m';
            $actividadAllPattern = '/Actividad[es]?:(.*?)script/m';
            $actividadpattern = '/class="activity-order">.*?">(.*?)<\/span> (.*?)<br\/><span style/m';
            $importamtePattern = '/4px; list-style: none;"> &nbsp.*?<br>(.*?)<a.*?\/\/(.*?)\',/m';

            preg_match($localidadPattern, $html, $localidad);
            preg_match($provinciaPattern, $html, $provincia);
            preg_match($contratoSocialPattern, $html, $contratoSocial);
            preg_match($empladorPattern, $html, $empleador);
            preg_match($personapattern, $html, $persona);
            preg_match($importamtePattern, Str::encoding($html), $importante);

            preg_match($impuestoAllattern, Str::encoding($html), $htmlImpuesto);
            $htmlImpuesto[1] = (isset($htmlImpuesto[1])) ? $htmlImpuesto[1] : '';
            preg_match_all($impuestoPattern, $htmlImpuesto[1], $impuesto);
            preg_match($regimenesallPattern, Str::encoding($html), $regimenesHtml);
            $regimenesHtml[1] = (isset($regimenesHtml[1])) ? $regimenesHtml[1] : '';
            preg_match_all($regimenesPattern, $regimenesHtml[1], $regimenes);
            preg_match($actividadAllPattern, Str::encoding($html), $actividadHtml);
            $actividadHtml[1] = (isset($actividadHtml[1])) ? $actividadHtml[1] : '';
            preg_match_all($actividadpattern, $actividadHtml[1], $actividaded);

            $impuestoParse = $this->parseImpuesto($impuesto);
            $regimenesParse = $this->regimenesParse($regimenes);
            $actividadParse = $this->actividadParse($actividaded);

            $actividadParse = str_replace(array('<i>', '</i>'), '', $actividadParse);

            $persona[1] = (isset($persona[1])) ? $persona[1] : '';
            preg_match($personaType, $persona[1], $typePersona);
            if (isset($typePersona[1])) {
                $typePersona[1] = preg_replace(
                    array('/F(.*?)sica/','/Jur(.*?)dica/'),
                    array('Física', 'Jurídica'),
                    $typePersona[1]
                );
            } else {
                $typePersona[1] = null;
            }
            preg_match($personaGender, $persona[1], $genderPersona);
            $genderPersona[1] = (isset($genderPersona[1])) ? ' (' . $genderPersona[1] . ')' : '';
            preg_match($personaNationality, $persona[1], $nationalityPersona);
            $nationalityPersona[1] = (isset($nationalityPersona[1])) ? ', ' . $nationalityPersona[1] : '';
            $tipoPersona = $typePersona[1] . $genderPersona[1] . $nationalityPersona[1];

            // Removendo Warnings
            $localidad[1] = (isset($localidad[1])) ? $localidad[1] : null;
            $provincia[1] = (isset($provincia[1])) ? $provincia[1] : null;
            $contratoSocial[1] = (isset($contratoSocial[1])) ? $contratoSocial[1] : null;
            $empleador[1] = (isset($empleador[1])) ? $empleador[1] : null;
            $importante[1] = (isset($importante[1])) ? $importante[1] : '';
            $importante[1] = str_replace(array('<i>', '<b>', '</b>', '</i>', '<br/>'), '', $importante[1]);
            $importante[2] = (isset($importante[2])) ? $importante[2] : '';

            if (empty($regimenesParse)) {
                $regimenesParse = ['NO REGISTRA IMPUESTOS ACTIVOS'];
            }
            if (empty($impuestoParse)) {
                $impuestoParse = ['NO REGISTRA REGÍMENES ACTIVOS'];
            }
            if (empty($actividadParse) || $actividadParse === " ") {
                $actividadParse = ['NO REGISTRA ACTIVIDADES ACTIVOS'];
            }

            $semiLastParse[] = [
                'data' => $parse[$key],
                'localidad' => $localidad[1],
                'provincia' => $provincia[1],
                'tipoPersona' => $tipoPersona,
                'contratoSocial' => $contratoSocial[1],
                'empleador' => $empleador[1],
                'impuesto' => $impuestoParse,
                'regimenes' => $regimenesParse,
                'actividad' => $actividadParse,
                'msg' => $importante[1],
                'link' => 'https://' . $importante[2]
            ];
        }
        return $semiLastParse;
    }

    private function actividadParse($dados)
    {
        $actividad = (isset($dados[2][0]) || isset($dados[1][0])) ?
            $dados[2][0] . " " . $dados[1][0] : '';
        return ($actividad);
    }

    private function regimenesParse($dados)
    {
        $regimenes = [];
        foreach ($dados[1] as $key => $value) {
            $cont = 0;
            if (empty($dados[1][$key])) {
                while (empty($dados[1])) {
                    if (empty($dados[1][$key - $cont])) {
                        $regimenes[$key - $cont] = $regimenes[$key - $cont] . $dados[3][$key];
                    }
                    $cont++;
                }
            } else {
                $regimenes[] = $dados[1][$key] . $dados[2][$key] . $dados[3][$key];
            }
        }

        return $regimenes;
    }

    private function parseImpuesto($dados)
    {
        $impuesto = [];
        foreach ($dados[1] as $key => $value) {
            $impuesto[] = $value;
        }

        foreach ($dados[2] as $chave => $valor) {
            $impuesto[$chave] = trim($impuesto[$chave]) . $valor;
        }

        return $impuesto;
    }

    private function getPaginationHtml($html)
    {
        $linksPattern = '/td valign="top"><a href="(.*?)">/m';
        preg_match_all($linksPattern, $html, $links);
        $i = 0;
        while ($i < count($links[1])) {
            $params = $links[1][$i];
            $params = str_replace('/', '', $params);
            $params = str_replace('&amp;', '&', $params);
            $htmlPage = $this->getResponse(self::BASE_URL . $params);
            $html = $html . $htmlPage;
            $i++;
        }
        return $html;
    }
}
