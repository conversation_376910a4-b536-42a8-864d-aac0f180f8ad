<?php

namespace App\Crawler\Bndes;

use App\Crawler\Spider;
use App\Helper\Util;
use Exception;
use App\Helper\Document;

class Bndes extends Spider
{
    private const MAIN_URL = 'https://www.bndes.gov.br/wps/portal/site/home/<USER>/consulta-operacoes-bndes';
    private const PARAMS_URL = 'https://www.bndes.gov.br/pelev2/consultaOperacoes.js';

    private $document = '';
    private $facetParams = '';
    private $defTypeParams = '';
    private $sortParams = '';
    private $qfParams = '';
    private $searchUrl = 'https://apigw.bndes.gov.br/operacoes/v1/select?';
    private $limit = 100;

    /**
     * Valida os parâmetros
     *
     * @return void
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['criterio'])) {
            throw new Exception('Documento inválido', 1);
        }

        if (!empty($this->param['limite'])) {
            $this->limit = $this->param['limite'];
        }

        //Quando o critério é nome e tem espaço precisa ser substituido pelos caracteres
        $this->document = str_replace(' ', '%20', $this->param['criterio']);
    }

    /**
     * Inicia a busca na api
     *
     * @return void
     */
    protected function start()
    {
        $html = $this->getResponse(self::PARAMS_URL);
        $this->setQfParams($html);
        $this->setSortParams($html);
        $this->setDefTypeParams($html);
        $this->setFacetParams($html);
        $this->setUrlSearch();
        return $this->getResult();
    }

    /**
     * Prepara o QF parâmetro que a api espera
     *
     * @param string $html
     * @return void
     */
    private function setQfParams($html)
    {
        preg_match_all(
            '/pesquisandoPorCPF\(\)\?\"\&qf\=(\w+)\"\:\"&qf=(.*)\"\}\}\,\{kind\:\"get\",key\:\"termo/i',
            $html,
            $qfParams
        );

        if (empty($qfParams)) {
            throw new Exception('Falha na busca dos qfParams', 3);
        }

        $this->qfParams = $qfParams[1][0];

        if (!Document::validarCpf($this->document)) {
            $this->qfParams = $qfParams[2][0];
        }
    }

    /**
     * Prepara o parâmetro sort que a api espera
     *
     * @param string $html
     * @return void
     */
    private function setSortParams($html)
    {
        preg_match_all('/\<option value=\"(product\(.*)\"\>\<\/option/i', $html, $sortParams);

        if (empty($sortParams)) {
            throw new Exception('Falha na busca dos sortParams', 3);
        }

        $this->sortParams = str_replace(' ', '%20', $sortParams[1][0]);
    }

    /**
     * Prepara o parâmetro defType que a api espera
     *
     * @param string $html
     * @return void
     */
    private function setDefTypeParams($html)
    {
        preg_match_all('/"&defType=(\w+)&/', $html, $defType);

        if (empty($defType)) {
            throw new Exception('Falha na busca dos defType', 3);
        }

        $this->defTypeParams = $defType[1][0];
    }

    /**
     * Prepara o paramêtro json facet que a api espera
     *
     * @param string $html
     * @return void
     */
    private function setFacetParams($html)
    {
        preg_match_all('/c=(\[.*\}\]),/i', $html, $facetParams);

        if (empty($facetParams)) {
            throw new Exception('Falha na busca dos facetParams', 3);
        }

        //Ajustes para formatar os dados para json
        $facetParams = str_replace(
            'formato:(r=2,function(e){return"".concat(e," - ").concat(+e+r-1)}),',
            '',
            $facetParams[1][0]
        );
        $facetParams = str_replace(':!0', ':true', $facetParams);
        $facetParams = str_replace('formato:s,', '', $facetParams);
        $facetParams = preg_replace('/(\w+)(\:)/', '"$1"$2', $facetParams);
        $facetParams = json_decode($facetParams);

        $facetResponse = [];
        if (!empty($facetParams)) {
            foreach ($facetParams as $value) {
                $facetResponse['facet'][$value->nome] = $value->configSolr;
            }

            //Ajustes para deixar do jeito que é esperado na url
            $facetResponse = json_encode($facetResponse);
            $facetResponse = preg_replace('/"(\w+)"(\:{)/', '$1$2', $facetResponse);
            $facetResponse = str_replace('"', '%22', $facetResponse);

            $this->facetParams = str_replace(' ', '%20', $facetResponse);
        }
    }

    /**
     * Prepara a url para a requisição na api
     *
     * @return void
     */
    private function setUrlSearch()
    {
        $this->searchUrl .= "q={$this->document}&";
        $this->searchUrl .= "defType={$this->defTypeParams}&";
        if ($this->facetParams) {
            $this->searchUrl .= "json={$this->facetParams}&";
        }
        $this->searchUrl .= "rows=5&";
        $this->searchUrl .= "start=0&";
        $this->searchUrl .= "sort={$this->sortParams}&";
        $this->searchUrl .= "qf={$this->qfParams}&";
        $this->searchUrl .= "fq=";
    }

    /**
     * Pega o resultado final
     *
     * @return void
     */
    private function getResult()
    {
        $response = $this->getResponse($this->searchUrl);

        if (empty($response)) {
            throw new Exception('Falha ao buscar resultado', 3);
        }

        $response = json_decode($response, true);
        if ($response['response']['numFound'] == 0) {
            throw new Exception('Nenhum resultado encontrado', 2);
        }

        if ($response['response']['numFound'] > $this->limit) {
            $response['response']['docs'] = array_slice($response['response']['docs'], 0, $this->limit);
        }

        $result = [];
        //Alguns resultados vinham multimensional sem precisar, fiz isso para corrigir
        foreach ($response['response']['docs'] as $keyDocs => $docs) {
            foreach ($docs as $key => $doc) {
                $result[$keyDocs][$key] = is_array($doc) ? $doc[0] : $doc;
            }
        }

        return $result;
    }
}
