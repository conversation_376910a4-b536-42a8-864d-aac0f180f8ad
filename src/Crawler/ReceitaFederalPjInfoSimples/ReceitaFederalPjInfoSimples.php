<?php

namespace App\Crawler\ReceitaFederalPjInfoSimples;

use App\Crawler\ReceitaFederalPjGeneric\Models\ReceitaFederalPjGenericModel;
use App\Crawler\ReceitaFederalPjGeneric\Models\ReceitaFederalPjGenericCnaeSecundarioModel;
use App\Crawler\ReceitaFederalPjGeneric\Models\ReceitaFederalPjGenericQsaModel;
use App\Crawler\GenericInterface;
use App\Crawler\Spider;
use App\Manager\InfoSimplesManager;
use App\Helper\Document;
use App\Helper\Date;
use Carbon\Carbon;
use Exception;

class ReceitaFederalPjInfoSimples extends Spider implements GenericInterface
{
    protected function validateAndSetCrawlerAttributes()
    {
        $this->param['documento'] = preg_replace('/[^0-9]/isu', '', $this->param['documento']);

        if (!Document::validarCnpj($this->param['documento'])) {
            throw new Exception("Critério de busca inválido.", 6);
        }
    }

    protected function start()
    {
        $manager = new InfoSimplesManager();

        $data = $manager->searchReceitaPJ($this->param['documento']);

        return $data['data'];
    }

    public function parseToGeneric(array $data, string $sourceName): ReceitaFederalPjGenericModel
    {
        $model = new ReceitaFederalPjGenericModel();

        $dataHora = explode(' ', $data['consulta_datahora']);
        $atividade = explode(' - ', $data['atividade_economica']);
        $natureza = explode(' - ', $data['natureza_juridica']);

        $model->source = $sourceName;
        $model->cnpj = $data['cnpj'];
        $model->tipo = $data['matriz_filial'];
        $model->data_abertura = $data['abertura_data'];
        $model->nome_empresarial = $data['razao_social'];
        $model->nome_fantasia = $data['nome_fantasia'];
        $model->atividade_economica_principal = $data['atividade_economica'];
        $model->natureza_juridica = $data['natureza_juridica'];
        $model->logradouro = $data['endereco_logradouro'];
        $model->numero = $data['endereco_numero'];
        $model->complemento = $data['endereco_complemento'];
        $model->cep = $data['endereco_cep'];
        $model->bairro = $data['endereco_bairro'];
        $model->municipio = $data['endereco_municipio'];
        $model->uf = $data['endereco_uf'];
        $model->ente_federativo_responsavel = $data['efr'];
        $model->endereco_eletronico = $data['email'];
        $model->telefone = $data['telefone'];
        $model->situacao_cadastral = $data['situacao_cadastral'];
        $model->data_situacao = $data['situacao_cadastral_data'];
        $model->motivo_situacao = $data['situacao_cadastral_observacoes'];
        $model->situacao_especial = $data['situacao_especial'];
        $model->data_especial = $data['situacao_especial_data'];
        $model->data_consulta = $dataHora[0];
        $model->hora_consulta = $dataHora[1];
        $model->cod_atividade = $atividade[0];
        $model->nome_atividade = $atividade[1];
        $model->cod_natureza = $natureza[0];
        $model->nome_natureza = $natureza[1];
        $model->cnpj_uf = trim($data['cnpj']) . '|' . trim($data['endereco_uf']);
        $model->html = '';
        $model->initial_capital = $data['normalizado_capital_social'];

        foreach ($data['atividade_economica_secundaria_lista'] as $cnae) {
            $modelCnae = new ReceitaFederalPjGenericCnaeSecundarioModel();

            $atividadeSec = explode(' - ', $cnae);

            $modelCnae->codigo = $atividadeSec[0];
            $modelCnae->descricao = $atividadeSec[1];
            $modelCnae->atividade_economica_secundaria = $cnae;

            $model->aAtividadeSecundaria[] = $modelCnae;
        }

        foreach ($data['qsa'] as $qsa) {
            $modelQsa = new ReceitaFederalPjGenericQsaModel();

            $modelQsa->name = $qsa['nome'];
            $modelQsa->qualification = $qsa['qualificacao'];
            $modelQsa->representante_qualificacao = $qsa['qualificacao_representante_legal'];
            $modelQsa->representante_legal = $qsa['nome_representante_legal'];
            $modelQsa->pais_origem = $qsa['pais_origem'];
            $modelQsa->qsa = $qsa['nome'] . ' - ' . $qsa['qualificacao'];

            $model->aQsa[] = $modelQsa;
        }

        return $model;
    }
}
