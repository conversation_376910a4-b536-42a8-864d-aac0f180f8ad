<?php

namespace App\Crawler\Cvm;

use Exception;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Helper\Date;
use App\Helper\Util;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use Symfony\Component\DomCrawler\Crawler;
use App\Manager\S3\Bucket\StaticUplexisBucket;

/**
 *  Clase de consulta da fonte CVM - Consulta Consolidada de Fundo
 *
 *  <AUTHOR> - ??
 *  <PERSON><PERSON> - 08/05/2018 -  Revisão
 *  @version 1.0.0
 *
 */
class Cvm extends Spider
{
    // Constantes para verificar com qual tipo de fundo estamos trabalhando
    private const FUNDO_IMOBILIARIO = 1;
    private const FUNDO_INVESTIMENTO = 2;
    private const ADM_FUNDO = 3;
    private const BASE_URL = 'http://cvmweb.cvm.gov.br/';

    // URL's para busca de fundos de investimento de todas as naturezas
    public $urlBase = 'https://cvmweb.cvm.gov.br/SWB/Sistemas/SCW/CPublica';
    public $urlForm = 'https://cvmweb.cvm.gov.br/SWB/Sistemas/SCW/CPublica/CConsolFdo/FormBuscaParticFdo.aspx';
    public $urlResult = 'https://cvmweb.cvm.gov.br/SWB/Sistemas/SCW/CPublica/CConsolFdo/ResultBuscaParticFdo.aspx?';
    public $urlPdf_2 = 'https://cvmweb.cvm.gov.br/SWB/Arquivos/BuscaArquivo.aspx';
    public $urlCaptcha = 'https://cvmweb.cvm.gov.br/SWB/Sistemas/SCW/CPublica/RandomTxt.aspx';

    // URL's para busca de documentos de fundos imobiliários do Fundos.net
    public $baseUrlBovespa = 'http://fnet.bmfbovespa.com.br/fnet/publico';
    public $urlBovespaManager = "http://fnet.bmfbovespa.com.br/fnet/publico/abrirGerenciadorDocumentosCVM?cnpjFundo=";
    public $urlBovespaDownload = "http://fnet.bmfbovespa.com.br/fnet/publico/downloadDocumento?id=";

    // URL's para busca de fundos de investimento que foram cancelados
    public $urlCancelFundsResult = self::BASE_URL .
        "SWB/Sistemas/SCW/CPublica/CConsolFdo/ResultBuscaParticFdoC.aspx?";

    public $urlCancelFundsFinancialStatement = self::BASE_URL .
        "SWB/Sistemas/SCW/CPublica/DemContabeis/CPublicaDemContabeis.aspx";

    private $name = '';
    private $cnpj = '';

    /**
     *  Validação dos parâmetros
     *
     *  @version 1.1.0
     *
     *  <AUTHOR> - ??
     *  Maximiliano Minucelli - 08/05/2018 - Remoção do typeParam e ajustes validação Revisão
     *  para apenas uma função de pesquisa, já que não tem diferença para nome e CNPJ
     *  Alan Santana de Andrade - 17/09/2018 - Alterado para aceitar nome como critério
     *                                         e retornar um array como parâmetro contendo
     *                                         os critérios de busca.
     *  @return array
     */
    protected function validateAndSetCrawlerAttributes()
    {
        $this->param['limite'] = $this->param['limite'] ??  1;

        if (empty($this->param['cnpj_nome'])) {
            throw new Exception("Nenhum critério de busca informado!", 3);
        }

        $this->type = Document::validarCnpj($this->param['cnpj_nome']) ? 1 : 2;

        if ($this->type === 1) {
            $this->cnpj = Str::removeSpecialCharacters($this->param['cnpj_nome']);
        } else {
            $this->name = $this->param['cnpj_nome'];
        }
    }

    /**
     *  Início da execução da pesquisa
     *
     *  @version 1.2.1
     *
     *  <AUTHOR> - ??
     *  Maximiliano Minucelli - 08/05/2018 -  Revisão
     *  Alan Santana de Andrade - 17/09/2018 - Adicionada validação para verificar qual tipo de fundo
     *                                         se esta trabalhando, no momento, somente os de investimento
     *                                         e imobiliários são capturados.
     *  Alan Santana de Andrade - 18/09/2018 - Adicionada busca de fundos de investimentos cancelados.
     *  Alan Santana de Andrade - 25/09/2018 - Movido o loop de captura para uma função própria.
     *
     *  @return array
     */
    protected function start()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $this->setProxy();

        // Encontra qual o critério de busca informado
        $critery = $this->type === 1 ? $this->cnpj : $this->name;

        $html = $this->requestForm($critery);

        list($links, $html, $cancelled) = $this->getResultLinks($html, $critery);

        $response = [];
        if (!empty($links)) {
            $response = $this->getFundsResult($html, $links, $cancelled, $this->limit);
        }

        if (empty($response)) {
            throw new Exception("Nada encontrado", 2);
        }

        return Str::encoding($response);
    }

    /**
     * Itera sobre o resultado das buscas do formulário de fundos.
     *
     * @version 1.0.0
     *
     * <AUTHOR> Santana de Andrade - 25/09/2018
     *
     * @param array $links - Deve conter o cnpj do fundo (entrada doc do array),
     *                       tipo (entrada type) e link para a página de dados do fundo
     * @param integer $limit
     *
     * @return void
     */
    public function getFundsResult($html, $links, $cancelled = false, $limit = 1)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if ($cancelled) {
            $url = $this->urlCancelFundsResult;
        } else {
            $url = $this->urlResult;
        }

        $i = 1;
        $response = [];
        foreach ($links as $key => $dadosLink) {
            if ($key != 0) {
                $html = $this->requestForm(null);
            }

            //Caso espeficico para empresas administradoras de fundos, exibir apenas CNPJ e Razão social no front-end
            if ($dadosLink['type'] == self::ADM_FUNDO) {
                unset($dadosLink['type']);
                if (isset($response["adm_fundo"])) {
                    $response["adm_fundo"][] = $dadosLink;
                } else {
                    $result["adm_fundo"][] = $dadosLink;
                    $response[] = $result;
                }
                continue;
            }

            $viewState = $this->getViewState($html);

            $queryString = array(
                'CNPJNome' => $dadosLink['doc'],
                'TpPartic' => 0,
                'Adm' => 'false',
                'numRandom' => $this->captcha,
                'SemFrame' => ''
            );

            $dadosLink = array_merge($dadosLink, $viewState);

            $html = $this->makeRequest($url, 'POST', $queryString, $dadosLink);

            $this->isProxyError($html);

            try {
                if ($dadosLink['type'] === self::FUNDO_INVESTIMENTO) {
                    $result = $this->parseData($html);

                    // Se for um fundo cancelado, obter os demonstrativos pela outra fonte
                    if ($cancelled) {
                        $cnpj = $result['dados_gerais']['doc_fundo'];
                        $result['demonstracao_contabil'] = $this->getCancelledFundAuditorsReports($cnpj);
                    }
                } elseif ($dadosLink['type'] === self::FUNDO_IMOBILIARIO) {
                    $result = $this->parseRealStateFundData($html, $dadosLink['doc']);
                }
            } catch (Exception $e) {
                continue;
            }

            // Valor será utilizado no front-end para saber qual partes da view devem ser carregadas
            $result["tipo"] = $dadosLink['type'];

            $response[] = $result;

            if ($limit >= $key) {
                break;
            }
        }

        return $response;
    }

    /**
     * Passado o resultado da busca do formulário de busca,
     * são retornados os links das páginas que contém as informações dos fundos, no formato:
     *
     * Formato de cada entrada do Retorno:
     * [
     *      "doc" => cnpj do fundo,
     *      "__EVENTTARGET" => target do asp.net para a página de resultados do fundo,
     *      "type" => inteiro informando o tipo de fundo
     * ]
     *
     * @version 1.0.0
     *
     * <AUTHOR> Santana de Andrade - 25/09/2018
     *
     * @param string $html
     * @param string $critery
     *
     * @return array
     */
    public function getResultLinks($html, $critery)
    {
        $cancelled = false;
        $links = [];

        //verifica se foi encontrado algum fundo
        if ($this->checkExistResults($html)) {
            if ($this->debug) {
                print PHP_EOL . "Verificando se é um fundo cancelado" . PHP_EOL;
            }

            // Verifica se é um fundo cancelado
            $html = $this->requestForm($critery, true);

            if ($this->checkExistResults($html)) {
                return [];
            }

            $cancelled = true;

            // Cookie utilizado para obter dados de sessão da pesquisa de fundos cancelados
            $this->cancelledFundCookie = "";
        }

        try {
            $links = $this->getLinksRequest($html, $critery);
        } catch (Exception $e) {
            var_dump($e->getMessage());
        }

        return array($links, $html, $cancelled);
    }

    /**
     * Obtém os pareceres do auditor de fundos cancelados.
     *
     * @version 1.0.0
     *
     * <AUTHOR> Santana de Andrade - 17/09/2018
     *
     * @param string $cnpj
     *
     * @return void
     */
    public function getCancelledFundAuditorsReports($cnpj)
    {
        $response = [];

        // Verifica se o cookie da página de pesquisa dos fundos cancelados foi setado
        if (empty($this->cancelledFundCookie)) {
            // Cria um novo arquivo de cookie e o coloca em uso pelo curl
            $uniqid = md5(uniqid(rand(), true));
            $this->cancelledFundCookie = '/tmp/cookie_' . $uniqid . '.txt';
            curl_setopt($this->ch, CURLOPT_COOKIEJAR, $this->cancelledFundCookie);

            $this->getResponse("sistemas.cvm.gov.br", "GET", [], [], false, true);
            $this->getResponse("sistemas.cvm.gov.br/?AdminCart", "GET", [], [], false, true);
            $this->getResponse("cvmweb.cvm.gov.br/SWB/Sistemas/SCW/CPublica/CPublica.asp", "GET", [], [], false, true);
            $this->getResponse("sistemas.cvm.gov.br/port/fundos/fundmob/menu.asp", "GET", [], [], false, true);
        } else {
            curl_setopt($this->ch, CURLOPT_COOKIEJAR, $this->cancelledFundCookie);
        }

        $html = $this->makeRequest($this->urlCancelFundsFinancialStatement);

        // Definindo parâmetros do Asp.net
        $postParams = $this->getViewState($html);
        $postParams["__EVENTTARGET"] = "";
        $postParams["__EVENTARGUMENT"] = "";

        $crawler = new Crawler($html);

        // Definindo parâmetros de busca
        $postParams["txtCNPJNome"] = $cnpj;

        $getParams = array(
            "CNPJNome" => $postParams["txtCNPJNome"]
        );

        // Data máxima de consulta de documentos neste formulário
        // Outros documentos devem ser obtidos por outro página
        $postParams["dataRefIni:ddDia"] = "1";
        $postParams["dataRefIni:ddMes"] = "1";
        $postParams["dataRefIni:ddAno"] = "2000";
        $postParams["dataRefFim:ddDia"] = "1";
        $postParams["dataRefFim:ddMes"] = "7";
        $postParams["dataRefFim:ddAno"] = "2018";
        $postParams["btConsulta"] = "Buscar";

        $getParams["DT_REF_INI"] = "1/1/2000";
        $getParams["DT_REF_FIM"] = "1/7/2018";

        $url = $this->urlCancelFundsFinancialStatement . "?" . http_build_query($getParams);
        $htmlResult = $this->getResponse($url, "POST", $postParams);

        $fSCrawler = new Crawler($htmlResult);
        $trCount = $fSCrawler->filter("#dgFatRelev tr")->count();
        if ($trCount) {
            $trCount--;

            // Recarrega os parâmetros do Asp.net
            $postParamsAux = $this->getViewState($htmlResult);
            $postParams = array_merge($postParams, $postParamsAux);

            // Precisa ser removido para realizar o download dos arquivos
            unset($postParams["btConsulta"]);

            $fSCrawler->filter("#dgFatRelev tr")->each(
                function ($node, $i) use (&$response, $url, &$postParams, $trCount) {
                    $financialStatement = [];

                    // Verifica se não é o cabeçalho ou o rodapé da tabela
                    if ($i === 0 || $i >= $trCount) {
                        return true;
                    }

                    try {
                        $tds = $node->children();

                        // Obtém o link para o pdf
                        $href = $tds->eq(2)->children()->first()->attr("href");
                        if (preg_match_all("/_PostBackOptions\\(\"(.*?)\"/is", $href, $match)) {
                            $eventTarget = $match[1][0];
                        } else {
                            throw new Exception(
                                "Erro ao montar a url para download do demonstrativo financeiro!",
                                3
                            );
                        }

                        // Defino o parâmetro target e obtém o PDF
                        $postParams["__EVENTTARGET"] = $eventTarget;
                        $htmlResult = $this->getResponse($url, "POST", $postParams, [], true, true);

                        $financialStatement = $this->downloadAndGetFilePath($htmlResult, "demonstracao_contabil");

                        $financialStatement["periodo"] = Str::encoding(trim($tds->eq(1)->text()));
                    } catch (Exception $e) {
                        $financialStatement = array(
                            "pdf" => "",
                            "periodo" => "",
                            "msg" => $e->getMessage()
                        );
                    }

                    $response[] = $financialStatement;
                }
            );
        }

        if (empty($response)) {
            $response = [
                [
                    "pdf" => "",
                    "data_envio" => "",
                    "periodo" => "",
                    "msg" => "Nenhum demonstrativo financeiro encontrado!"
                ]
            ];
        }

        // Volta para o cookie original
        curl_setopt($this->ch, CURLOPT_COOKIEJAR, $this->cookie);
        return $response;
    }

    /**
     * Verifica se o documento tem algum fundo
     *
     * @version 1.0.0
     * <AUTHOR> Melo - 18/06/2018 - Verifica se o documento tem algum fundo
     *
     * @param string $html
     *
     * @return boolean
     *
     */
    private function checkExistResults($html)
    {
        return preg_match("/Nenhum\\s*fundo\\s*foi\\s*encontrado/", $html);
    }

    /**
     * Captura os dados de um fundo Imobiliário.
     *
     * @version 1.0.0
     *
     * <AUTHOR> Santana de Andrade - 17/09/2018
     *
     * @param string $html
     * @param array $parameters
     *
     * @return array
     */
    public function parseRealStateFundData($html, $cnpj)
    {
        if ($this->debug) {
            print(__METHOD__ . PHP_EOL);
        }
        $response = [];
        $crawler = new Crawler($html);

        // As tags não possuem Id, então obtenho pela posição na tabela
        $entries = array(
            "nome_fundo",
            "administrador",
            "diretor",
            "telefone",
            "fax",
            "email",
            "endereco",
            "valor_patrimonio",
            "data_constituicao",
            "n_cotas",
            "valor_cota",
            "situacao_atual"
        );

        $response['dados_gerais']['doc_fundo'] = $cnpj;
        $response['dados_gerais']['data_inicio'] = "";

        $crawler->filter("table#TbMain tr")->each(function ($node, $i) use (&$response, &$entries) {
            if ($i === 0) {
                return true;
            }
            $td = $node->filter("td")->last();
            $response['dados_gerais'][$entries[($i - 1)]] = Str::encoding(strip_tags($td->html(), "<br>"));
        });

        if (!empty($response['dados_gerais']["email"])) {
            $response['dados_gerais']["email"] = explode("<br>", $response['dados_gerais']["email"]);
            // Em alguns casos, o último índice fica vazio, o código abaixo remove a entrada vazia
            $last = (count($response['dados_gerais']["email"]) - 1);
            if (empty($response['dados_gerais']["email"][$last])) {
                unset($response['dados_gerais']["email"][$last]);
            }
        }

        $response['dados_gerais']['endereco'] = strip_tags($response['dados_gerais']['telefone']);
        $response['dados_gerais']['telefone'] = strip_tags($response['dados_gerais']['telefone']);
        $response['dados_gerais']['fax'] = strip_tags($response['dados_gerais']['telefone']);

        //Verifica se existe o botão para a página de demonstrativo financeiro
        $financialStatementLink = $crawler->filter("table#Table2 a#Hyperlink3");

        if ($financialStatementLink->count()) {
            // Corrigir formatação do url
            $financialStatementLink = str_replace("../", "", $financialStatementLink->attr('href'));
            $financialStatementHtml = $this->getResponse("{$this->urlBase}/{$financialStatementLink}");
            $fSCrawler = new Crawler($financialStatementHtml);

            $response['dados_gerais']['doc_administrador'] = Str::removeSpecialCharacters(
                $this->getElementText($fSCrawler, "#LbCNPJAdm")
            );
            $response['dados_gerais']['cpf_diretor'] = Str::removeSpecialCharacters(
                $this->getElementText($fSCrawler, "#LbCNPJDir")
            );
            $response['dados_gerais']['dta_constituicao'] = $this->getElementText($fSCrawler, "#LbDtConst");
            $response['dados_gerais']['eleicao_representante'] = Str::removeSpecialCharacters(
                $this->getElementText($fSCrawler, "#LbElRepres")
            );
            $response['dados_gerais']['publicacao_demonstrativo'] = Str::removeSpecialCharacters(
                $this->getElementText($fSCrawler, "#LbDemnstFinanc")
            );
            //Verifica se foi informada uma data de constituição do FII e se ela é superior a 01/06/2016
            if (
                !empty($response['dados_gerais']['data_constituicao']) &&
                $this->verificaDataDeEnvioLimite($response['dados_gerais']['data_constituicao'])
            ) {
                $bovespaFundsManagerHtml = $this->getResponse($this->urlBovespaManager . $cnpj['doc']);
                $response['demonstracao_contabil'] = $this->parseBovespaManager($bovespaFundsManagerHtml);
            } else {
                $response['demonstracao_contabil'] = $this->getAuditorsReport($fSCrawler);
            }
        }

        return $response;
    }

    /**
     * Verifica se a data de um fundo imobiliário é anterior ou posterior a data limite de 01/06/2016
     * utilizando strtotime para comparar seus unixtimes.
     *
     * @version 1.0.0
     *
     * <AUTHOR> Andrade <<EMAIL>>
     *
     * @param string $data - Data no formato dd\/mm\/aaaa
     *
     * @return boolean
     */
    public function verificaDataDeEnvioLimite($data)
    {
        // Esta data é utilizada para verificar de qual fonte se deve obter os demonstrativos financeiros
        $dataFinal = strtotime("2016/06/01");

        // Abre a data passada em um array, inverte e transforma em uma string novamente
        // 01/02/2018 => 2018/02/01
        $data = implode("/", array_reverse(explode("\\/", $data)));

        if (Date::validateDate($data, 'Ymd')) {
            return strtotime($data) >= $dataFinal;
        }
        return false;
    }

    /**
     * Captura e retorna os demonstrativos financeiros de um dado CNPJ no Fundos.Net
     *
     * @version 1.0.0
     *
     * <AUTHOR> Andrade <<EMAIL>>
     *
     * @param string $html
     *
     * @return array
     */
    public function parseBovespaManager($html)
    {
        $response = [];
        $crawler = new Crawler($html);
        //Obtenho primeiro o corpo da tabela e depois seu conteúdo
        if (
            preg_match_all(
                "#<tbody[^>]*?>(?<tbody>.*?)</tbody>#is",
                $html,
                $matches
            ) && preg_match_all(
                "#<tr[^>]*>\\s*?<td>.*?</td>\\s*?<td>.*?</td>\\s*?<td>Demonstra\\S*?es Financeiras</td>\\s*?<td>" .
                    ".*?</td>\\s*?<td>(?<data_referencia>.*?)</td>\\s*?<td>(?<data_entrega>.*?)</td>\\s*?<td.*?>" .
                    "(?<status>.*?)</td>\\s*?<td>.*?</td>\\s*?<td>.*?</td>\\s*?<td[^>]*>\\s*<a.*?href=\".*?id=" .
                    "(?<id>.*?)\\&#i",
                $matches['tbody'][0],
                $rows
            )
        ) {
            foreach ($rows[0] as $i => $row) {
                $result = [];
                try {
                    // Requisição
                    $url = $this->urlBovespaDownload . $rows['id'][$i];
                    $financialStatementHtml = $this->makeRequest($url);

                    // Download
                    $result = $this->downloadAndGetFilePath($financialStatementHtml, "demonstracao_contabil");

                    // Informações adicionais
                    $result["periodo"] = $rows['data_referencia'][$i];
                    $result["msg"] = "";
                } catch (Exception $e) {
                    $response[] = array(
                        "periodo" => "",
                        "url" => "",
                        "msg" => $e->getMessage()
                    );
                }
                $response[] = $result;
            }
        }
        return $response;
    }


    /**
     * Busca e baixa o demonstrativo financeiro de um fundo imobiliário.
     *
     * <AUTHOR> Santana de Andrade - 18/09/2018
     * @version 1.0.0
     *
     * @param string $html
     *
     * @return array
     */
    public function getAuditorsReport($crawler)
    {
        $response = [];
        if ($crawler->filter('#ddComptc option')->count()) {
            $months = array(
                'jan' => '01',
                'fev' => '02',
                'mar' => '03',
                'abr' => '04',
                'mai' => '05',
                'jun' => '06',
                'jul' => '07',
                'ago' => '08',
                'set' => '09',
                'out' => '10',
                'nov' => '11',
                'dez' => '12'
            );
            $crawler->filter('#ddComptc option')->each(function ($node, $i) use (&$response, $crawler) {
                try {
                    //Preparando parâmetros do asp.net
                    $param = $this->getViewState($crawler->html());
                    $param['__EVENTARGUMENT'] = "";
                    $param['__EVENTTARGET'] = $this->getEventTarget($crawler->filter('#HLParAudInd')->attr('href'));
                    if ($crawler->filter('#Form1')->count() == 0) {
                        throw new Exception("Não foi possível obter a Url para baixar o demonstrativo financeiro!");
                    }
                    $param['ddComptc'] = $node->attr('value');
                    $action = str_replace("./", "", $crawler->filter('#Form1')->attr("action"));

                    $url = "{$this->urlBase}/InfoSemFII/{$action}";

                    // Requisições para obter id do arquivo que será baixado
                    $financialStatementHtml = $this->getResponse($url, "POST", $param);
                    $result = $this->downloadAndGetFilePath($financialStatementHtml, "demonstracao_contabil");

                    $result['periodo'] = $param['ddComptc'];
                    $response[] = $result;
                } catch (Exception $e) {
                    $response[] = array(
                        "msg" => $e->getMessage()
                    );
                }
            });
        }

        if (empty($response)) {
            $response = array(array(
                "msg" => "Nenhum parecer encontrado!"
            ));
        }

        return $response;
    }

    public function getEventTarget($href)
    {
        if (preg_match("/__doPostBack\\('(?<__EVENTTARGET>.*?)'/", $href, $result)) {
            return $result['__EVENTTARGET'];
        }
        throw new Exception("Não foi encontrada a URL para download do PDF!");
    }

    /**
     * Busca e organiza os dados
     *
     * @version 1.2.1
     *
     * <AUTHOR> - ??
     * Maximiliano Minucelli - 17/05/2018 - Ajuste dta_vigencia_regulamento
     * Alan Santana de Andrade - 17/09/2018 - Adicionado download de demonstração contábil
     * Alan Santana de Andrade - 18/09/2018 - Adicionada captura das informações por DOM Crawler
     * Alan Santana de Andrade - 19/09/2018 - Adicionado método genérico para download de arquivos
     *
     * @param string $html
     *
     * @return array
     */
    private function parseData($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $months = array(
            'jan' => '01',
            'fev' => '02',
            'mar' => '03',
            'abr' => '04',
            'mai' => '05',
            'jun' => '06',
            'jul' => '07',
            'ago' => '08',
            'set' => '09',
            'out' => '10',
            'nov' => '11',
            'dez' => '12'
        );

        $crawler = new Crawler($html);
        $response = [];

        //Verifica se a página contém o PK_PARTIC no action do Form
        $action = $this->getElement($crawler, "#Form1", 'action');

        if (!preg_match('/PK_PARTIC=(?<pk>\d+)/i', $action, $match)) {
            throw new Exception("Key não encontrada", 3);
        }
        $pk = $match['pk'];


        // Verifica se o fundo foi cancelado
        $response['dados_gerais'] = [
            'data_cancelado' => $this->getElementText($crawler, "#lbInfAdc4"),
            'nome_fundo' => $this->getElementText($crawler, "#lbNmDenomSocial"),
            'doc_fundo' => $this->getElementText($crawler, "#lbNrPfPj"),
            'administrador' => $this->getElementText($crawler, "#lbNmDenomSocialAdm"),
            'doc_administrador' => $this->getElementText($crawler, "#lbNrPfPjAdm"),
            'situacao_atual' => $this->getElementText($crawler, "#lbSitDesc"),
            'dta_inicio_atividades' => $this->getElementText($crawler, "#lbInfAdc1"),
            'dta_constituicao' => $this->getElementText($crawler, "#lbInfAdc2"),
            'dta_vigencia_regulamento' => '',
        ];

        $response['caracterizacao'] = [
            'classe' =>  $this->getElementText($crawler, "#lbClasse"),
            'Forma de Condominio' =>  $this->getElementText($crawler, "#lbFormaCondmn"),
            'indicador_desempenho' =>  $this->getElementText($crawler, "#lbIndicDesemp"),
            'fundo_exclusivo' =>  $this->getElementText($crawler, "#lbExclv"),
            'fundo_cotas' =>  $this->getElementText($crawler, "#lbTpInvest"),
            'tratamento_tributario_longo_prazo' =>  $this->getElementText($crawler, "#lbTributLPrazo"),
            'dest_exc_invest_qualific' =>  $this->getElementText($crawler, "#lbInvestQualif"),

        ];

        //Dados Diários
        $href = $this->getElement($crawler, '#Hyperlink2', 'href');
        if (preg_match('/[^\/]*(?<url>[^\/]*\/InfDiario\/CPublicaInfDiario[^"]*)/is', $html, $match)) {
            $response['dados_diarios'] =
                $this->getDailyData($this->getResponse($this->urlBase . $match['url']));
        }

        //Regulamentos
        $href = $this->getElement($crawler, '#Hyperlink6', 'href');
        if (preg_match('/[^\/]*(?<url>[^\/]*\/Regul\/CPublicaRegulFI[^"]*)/is', $href, $match)) {
            $htmlRegulamento = $this->getResponse($this->urlBase . $match['url']);
            $response['regulamento'] = $this->getRegulamento($htmlRegulamento, $this->urlBase . $match['url']);

            if (empty($response['regulamento']['regulamento']['error'])) {
                $crawlerRegulamento = new Crawler($htmlRegulamento);
                if ($crawlerRegulamento->filter("#COMPTC option[selected]")->count()) {
                    $dta['data'] = $this->getElement($crawlerRegulamento, "#COMPTC option[selected]", 'value');

                    if (!empty($dta['data'])) {
                        $dta = explode('/', $dta['data']);

                        if (!preg_match('/\d+/si', $dta[1])) {
                            $dta[1] = $months[$dta[1]];
                        }

                        $response['dados_gerais']['dta_vigencia_regulamento'] = implode('/', $dta);
                    }
                }
            }
        }

        //Balancete
        $href = $this->getElement($crawler, '#Hyperlink5', 'href');
        if (preg_match('/[^\/]*(?<url>[^\/]*\/Balancete\/CPublicaBalancete[^"]*)/is', $href, $match)) {
            $htmlBalancete = $this->makeRequest($this->urlBase . $match['url']);
            $response['balancete'] = $this->getBalanceSheet($htmlBalancete);
            try {
                // Inserir no S3
                $fileId = 'balancete_' . uniqid() . '.pdf';
                $filePath = '/tmp/' . $fileId;
                $s3Path = 'cvm/' . $fileId;

                (new Pdf())->saveHtmlToPdf($htmlBalancete, $filePath);

                if ((new S3(new StaticUplexisBucket()))->save($s3Path, $filePath)) {
                    $response['regulamento']['balancete']['pdf'] = S3_STATIC_BUCKET . '/' . $s3Path;
                    $response['regulamento']['balancete']['error'] = '';
                }
            } catch (Exception $e) {
                $response['regulamento']['balancete']['pdf'] = '';
                $response['regulamento']['balancete']['error'] = 'Não foi possível baixar o balancete!';
            }
        }

        // Demonstração contábeis com parecer do auditor
        $href = $this->getElement($crawler, '#hlInfDemContabeis', 'href');
        if (preg_match('/(?<url>\/DemContabeis\/CPublicaDemContabeisFI.*)/', $href, $match)) {
            if (empty($this->financialStatement)) {
                $uniqid = md5(uniqid(rand(), true));
                $this->financialStatement = '/tmp/cookie_' . $uniqid . '.txt';
            }

            // Obtém a página de pareceres do auditor
            $url = $this->urlBase . $match['url'];
            $htmlFS = $this->getResponse($url);
            $crawlerFS = new Crawler($htmlFS);

            // Verifíca se a lista de períodos está disponível
            if ($crawlerFS->filter("#COMPTC option")->count()) {
                $response["demonstracao_contabil"] = [];

                // Monta os parâmetros do asp.net
                $params = $this->getViewState($htmlFS);
                $params['__EVENTTARGET'] = "hl1";
                $params['__EVENTARGUMENT'] = "";

                // Obtém todos os pareceres de cada período disponível
                $crawlerFS->filter("#COMPTC option")->each(
                    function ($node, $i) use (&$response, &$months, &$params, $url) {
                        $value = $node->attr("value");

                        // Verifica se é a última entrada da lista
                        if ($value === "Anteriores") {
                            return true;
                        }

                        // Realiza as requisições e baixa o pdf, retornando o caminho
                        // e alguma mensagem de erro se houver um.
                        $params['COMPTC'] = $node->attr("value");
                        $pdfFinancialStatement = $this->getResponse($url, "POST", $params);
                        $pdfFinancialStatement = $this->getResponse($this->urlPdf_2);
                        $result = $this->downloadAndGetFilePath($pdfFinancialStatement, "demonstracao_contabil");

                        // Verifica se o download foi concluído e adiciona o período em
                        // que o arquivo foi postado no site
                        if (!empty($result)) {
                            $date = explode('/', $params['COMPTC']);
                            $date[1] = $months[$date[1]];
                            $result['periodo'] = implode("/", $date);
                            $response["demonstracao_contabil"][] = $result;
                        }
                    }
                );
            }
        }

        if (empty($response["demonstracao_contabil"])) {
            $response["demonstracao_contabil"] = [
                [
                    "msg" => "Nenhum demonstrativo contábil encontrado!"
                ]
            ];
        }

        return $response;
    }

    /**
     *  Busca as informações sobre o balancete
     *
     *  @version 1.0.0
     *
     *  <AUTHOR> - ??
     *  Maximiliano Minucelli - 08/05/2018 - Revisão
     *
     *  @param string $html
     *
     *  @return array
     */
    private function getBalanceSheet($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (!preg_match("#<select[^>]*?name='COMPTC'[^>]*?>\\s*<option>(.*?)<\\/option>#is", $html, $competence)) {
            $competence = '';
        } else {
            $competence = $competence[1];
        }

        $trsFaefca = Util::queryXPath($html, '//*[@id="Table1"]/tr[@bgcolor="#faefca"]', true);
        $trsFFF8DC = Util::queryXPath($html, '//*[@id="Table1"]/tr[@bgcolor="#FFF8DC"]', true);

        $trs = array_merge($trsFaefca, $trsFFF8DC);

        $response = [];
        foreach ($trs as $key => $tr) {
            list($conta, $accountDescription, $balanceAmount) = Util::queryXPath($tr, '//td');

            $response[] = array(
                'competencia' => $competence,
                'conta' => $conta,
                'descricao_conta' => $accountDescription,
                'valor_saldo' => $balanceAmount,
            );
        }

        return $response;
    }

    /**
     *  Busca as informações sobre dados diários
     *
     *  @version 1.0.0
     *
     *  <AUTHOR> - ??
     *  Maximiliano Minucelli - 08/05/2018 - Revisão
     *
     *  @param string $html
     *
     *  @return array
     */
    private function getDailyData($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (
            !preg_match(
                '#<select\s*name="ddComptc"[^>]*?>\s*<option\s*selected="selected"[^>]*?>(.*?)<\/option>#is',
                $html,
                $competence
            )
        ) {
            $competence = '';
        } else {
            $competence = $competence[1];
        }

        $trs = Util::queryXPath($html, '//*[@id="dgDocDiario"]/tr', true);

        $response = [];
        foreach ($trs as $key => $tr) {
            if ($key == 0) {
                continue;
            }

            list(
                $dia,
                $quota,
                $dayCaptation,
                $dayRansom,
                $liquidPatrician,
                $walletTotal,
                $shareholdersCount,
                $nextDateInformLiquidPratician
            ) = Util::queryXPath($tr, '//td', true);

            if (
                empty(str_replace(array(chr(194), chr(160), "\u{c2a0}"), '', $quota)) &&
                empty(str_replace(array(chr(194), chr(160), "\u{c2a0}"), '', $liquidPatrician))
            ) {
                continue;
            }

            $response[] = array(
                'competencia' => $competence,
                'dia' => $dia,
                'quota' => $quota,
                'captacao_dia' => $dayCaptation,
                'resgate_dia' => $dayRansom,
                'patrimanio_liquido' => $liquidPatrician,
                'total_carteira' => $walletTotal,
                'total_cotistas' => $shareholdersCount,
                'data_proxima_informacao_pl' => $nextDateInformLiquidPratician,
            );
        }

        return $response;
    }

    /**
     *  Busca as informações sobre o regulamento
     *
     *  @version 1.0.0
     *
     *  <AUTHOR> - ??
     *  Maximiliano Minucelli - 08/05/2018 - Revisão
     *
     *  @param string $html
     *  @param string $ref
     *
     *  @return array
     */
    private function getRegulamento($html, $ref)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $state = $this->getViewState($html);

        $dataArray = array(
            'regulamento' => array(
                'target' => preg_match('/Consulta\s*de\s*Documentos\s*Eventuais\s*FIDC/is', $html) ? 'hl8' : 'hl1'
            ),
            'prospecto' => array(
                'target' => 'hl2'
            ),
        );

        $optionsArray = array(
            'regulamento' => '246',
            'prospecto' => '247'
        );

        foreach ($optionsArray as $key => $option) {
            $dados = array(
                'url' => $ref,
                '__EVENTTARGET' => 'DDCdTpDoc',
                '__EVENTARGUMENT' => $state['__EVENTARGUMENT'],
                '__LASTFOCUS' => $state['__LASTFOCUS'],
                '__VIEWSTATE' => $state['__VIEWSTATE'],
                '__VIEWSTATEGENERATOR' => $state['__VIEWSTATEGENERATOR'],
                '__EVENTVALIDATION' => $state['__EVENTVALIDATION'],
                'DDCdTpDoc' => $option,
                'COMPTC' => '',
            );

            $response = $this->getResponseTroughBinaryWithDados($dados);

            if (preg_match_all("/(N.o\\sfoi\\sencontrado\\snenhum\\s\\w+)/is", $response, $output)) {
                $dataArray[$key]['error'] = utf8_encode($output[1][0]);
                continue;
            }

            $dataArray[$key]['state'] = $this->getViewState($response);
            $dataArray[$key]['DDCdTpDoc'] = $option;

            if (
                preg_match_all(
                    "/id=\"COMPTC\"\\D*?<option\\s*selected=\"selected\"\\s*value=\"(.*?)\">/is",
                    $response,
                    $output
                )
            ) {
                $dataArray[$key]['COMPTC'] = $output[1][0];
            } else {
                $dataArray[$key] = array(
                    'error' => 'Não foi encontrado nenhum Prospecto'
                );
            }
        }

        curl_setopt($this->ch, CURLOPT_REFERER, $ref);
        $id = uniqid();
        foreach ($dataArray as $key => $data) {
            if (isset($data['error'])) {
                $finalResponse[$key]['pdf'] = '';
                $finalResponse[$key]['error'] = $data['error'];
                continue;
            }

            $pdf = $this->getPdf($data['state'], $ref, $data['target'], $data['DDCdTpDoc'], $data['COMPTC']);

            // inserir no S3
            $fileId = $key . '_' . $id . '.' . $pdf['ext'];
            $filePath = '/tmp/' . $fileId;
            $s3Path = 'cvm/' . $fileId;

            file_put_contents($filePath, $pdf['pdf']);

            if ((new S3(new StaticUplexisBucket()))->save($s3Path, $filePath)) {
                $dataArray[$key]['pdf'] = S3_STATIC_BUCKET . '/' . $s3Path;
                $finalResponse[$key]['pdf'] = S3_STATIC_BUCKET . '/' . $s3Path;
                $finalResponse[$key]['error'] = '';
            }
        }

        return $finalResponse;
    }

    /**
     *  Pega os dados binários para requisição de download de arquivos
     *
     *  @version 1.0.0
     *
     *  <AUTHOR> - ??
     *  Maximiliano Minucelli - 08/05/2018 - Revisão
     *
     *  @param array $dados
     *
     *  @return string
     */
    private function getResponseTroughBinaryWithDados($dados)
    {
        $eol = "\r\n"; //default line-break for mime type

        //random boundaryid, is a separator for each param on my post curl function
        $BOUNDARY = 'WebKitFormBoundary' . md5(rand());

        //init my curl body
        $BODY = "";
        //#############################{param 1}################################
        //start param header
        $BODY .= '------' . $BOUNDARY . $eol;
        // last Content with 2 $eol, in this case is only 1 content.
        $BODY .= 'Content-Disposition: form-data; name="__EVENTTARGET"' . $eol . $eol;
        //param data in this case is a simple post data and 1 $eol for the end of the data
        $BODY .= $dados['__EVENTTARGET'] . $eol;
        //#############################{param 1}################################
        //
        //#############################{param 2}################################
        // start 2nd param,
        $BODY .= '------' . $BOUNDARY . $eol;
        // last Content with 2 $eol, in this case is only 1 content.
        $BODY .= 'Content-Disposition: form-data; name="__EVENTARGUMENT"' . $eol . $eol;
        //param data in this case is a simple post data and 1 $eol for the end of the data
        $BODY .= "{$dados['__EVENTARGUMENT']}" . $eol;
        //#############################{param 2}################################
        //
        //#############################{param 3}################################
        // start 3nd param,
        $BODY .= '------' . $BOUNDARY . $eol;
        // last Content with 2 $eol, in this case is only 1 content.
        $BODY .= 'Content-Disposition: form-data; name="__LASTFOCUS"' . $eol . $eol;
        //param data in this case is a simple post data and 1 $eol for the end of the data
        $BODY .= "{$dados['__LASTFOCUS']}" . $eol;
        //#############################{param 3}################################
        //
        //#############################{param 4}################################
        // start 4nd param,
        $BODY .= '------' . $BOUNDARY . $eol;
        // last Content with 2 $eol, in this case is only 1 content.
        $BODY .= 'Content-Disposition: form-data; name="__VIEWSTATE"' . $eol . $eol;
        //param data in this case is a simple post data and 1 $eol for the end of the data
        $BODY .= "{$dados['__VIEWSTATE']}" . $eol;
        //#############################{param 4}################################
        //
        //#############################{param 5}################################
        // start 5nd param,
        $BODY .= '------' . $BOUNDARY . $eol;
        // last Content with 2 $eol, in this case is only 1 content.
        $BODY .= 'Content-Disposition: form-data; name="__VIEWSTATEGENERATOR"' . $eol . $eol;
        //param data in this case is a simple post data and 1 $eol for the end of the data
        $BODY .= "{$dados['__VIEWSTATEGENERATOR']}" . $eol;
        //#############################{param 5}################################
        //
        //#############################{param 6}################################
        // start 6nd param,
        $BODY .= '------' . $BOUNDARY . $eol;
        // last Content with 2 $eol, in this case is only 1 content.
        $BODY .= 'Content-Disposition: form-data; name="__EVENTVALIDATION"' . $eol . $eol;
        //param data in this case is a simple post data and 1 $eol for the end of the data
        $BODY .= "{$dados['__EVENTVALIDATION']}" . $eol;
        //#############################{param 6}################################
        //
        //#############################{param 7}################################
        // start 7nd param,
        $BODY .= '------' . $BOUNDARY . $eol;
        // last Content with 2 $eol, in this case is only 1 content.
        $BODY .= 'Content-Disposition: form-data; name="DDCdTpDoc"' . $eol . $eol;
        //param data in this case is a simple post data and 1 $eol for the end of the data
        $BODY .= $dados['DDCdTpDoc'] . $eol;
        //#############################{param 7}################################
        //
        if (!empty($dados['COMPTC'])) {
            //#############################{param 8}################################
            // start 8nd param,
            $BODY .= '------' . $BOUNDARY . $eol;
            // last Content with 2 $eol, in this case is only 1 content.
            $BODY .= 'Content-Disposition: form-data; name="COMPTC"' . $eol . $eol;
            //param data in this case is a simple post data and 1 $eol for the end of the data
            $BODY .= $dados['COMPTC'] . $eol;
            //#############################{param 8}################################
        }

        // we close the param and the post width "--" and 2 $eol at the end of our boundary header.
        $BODY .= '------' . $BOUNDARY . '--' . $eol . $eol;

        //setting our mime type for make it work on $_FILE variable
        curl_setopt(
            $this->ch,
            CURLOPT_HTTPHEADER,
            array(
                "Content-Type: multipart/form-data; boundary=----" . $BOUNDARY
            )
        );
        curl_setopt(
            $this->ch,
            CURLOPT_USERAGENT,
            'Mozilla/1.0 (Windows NT 6.1; WOW64; rv:28.0) Gecko/20100101 Firefox/28.0'
        );
        curl_setopt($this->ch, CURLOPT_URL, $dados['url']); //setting our api post url
        curl_setopt($this->ch, CURLOPT_REFERER, $dados['url']);
        curl_setopt($this->ch, CURLOPT_COOKIEJAR, $this->cookie); //saving cookies just in case we want
        curl_setopt($this->ch, CURLOPT_RETURNTRANSFER, 1); // call return content
        curl_setopt($this->ch, CURLOPT_FOLLOWLOCATION, 1); //navigate the endpoint
        curl_setopt($this->ch, CURLOPT_POST, true); //set as post
        curl_setopt($this->ch, CURLOPT_POSTFIELDS, $BODY); // set our $BODY

        $response = curl_exec($this->ch);

        return $response; // start curl navigation
    }

    /**
     *  Faz o download do arquivo
     *
     *  @version 1.0.0
     *
     *  <AUTHOR> - ??
     *  Maximiliano Minucelli - 08/05/2018 - Revisão
     *  Alan Santana de Andrade - 25/09/2018 - Alterado o nome do primeiro parâmetro de $dados para $params
     *
     *  @param array $params
     *  @param string $url
     *  @param string $target
     *  @param string $ddcdtpdoc
     *  @param string $comptc
     *
     *  @return array
     */
    private function getPdf($params, $url, $target, $ddcdtpdoc, $comptc)
    {
        $params = array(
            'url' => $url,
            '__EVENTTARGET' => $target,
            '__EVENTARGUMENT' => $params['__EVENTARGUMENT'],
            '__LASTFOCUS' => $params['__LASTFOCUS'],
            '__VIEWSTATE' => $params['__VIEWSTATE'],
            '__VIEWSTATEGENERATOR' => $params['__VIEWSTATEGENERATOR'],
            '__EVENTVALIDATION' => $params['__EVENTVALIDATION'],
            'DDCdTpDoc' => $ddcdtpdoc,
            'COMPTC' => $comptc,
        );

        $response = $this->getResponseTroughBinaryWithDados($params);

        if (curl_error($this->ch)) {
            $httpCode = curl_getinfo($this->ch, CURLINFO_HTTP_CODE);
            throw new Exception("Response error: {$httpCode}, Url: {$url}", 3);
        }

        $info = curl_getinfo($this->ch);
        if (preg_match('/word/', $info['content_type'])) {
            $ext = 'doc';
        } else {
            $ext = 'pdf';
        }

        return array('pdf' => $response, 'ext' => $ext);
    }

    /**
     *  Realiza o request do form principal
     *
     *  @version 1.0.1
     *
     *  <AUTHOR> - ??
     *  Maximiliano Minucelli - 08/05/2018 - Revisão
     *  Alan Santana de Andrade - 17/09/2018 - Adicionado parâmetro para verificar se o fundo foi cancelado
     *
     *  @param string $cnpj
     *  @param boolean $checkIsCancelled
     *
     *  @return string
     */
    private function requestForm($critery, $checkIsCancelled = false)
    {
        $this->getImageCaptcha();
        $this->breakCaptcha();

        $params = array(
            'CNPJNome' => $critery,
            'TpPartic' => 0,
            'Adm' => 'false',
            'numRandom' => $this->captcha,
            'SemFrame' => ''
        );

        if (!$checkIsCancelled) {
            $url = $this->urlResult;
        } else {
            $url = $this->urlCancelFundsResult;
        }

        $html = $this->makeRequest($url, 'GET', $params);

        $this->isProxyError($html);

        return $html;
    }

    /**
     *  Realiza requests com looping para verifição de captcha
     *
     *  @version 1.0.0
     *
     *  <AUTHOR> - ??
     *  Maximiliano Minucelli - 08/05/2018 - Revisão
     *
     *  @param string $url
     *  @param string $method
     *  @param array $getParams
     *  @param array $postParams
     *  @param int $try
     *
     *  @return string
     */
    private function makeRequest($url, $method = 'GET', $getParams = [], $postParams = [], $try = 0)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $html = $this->getResponse(
            $url . (!empty($getParams) ? http_build_query($getParams) : ''),
            $method,
            $postParams
        );

        if (
            preg_match(
                '#<td\s*colspan="2">\s*Digite\s*abaixo\s*o\s*n.{1,8}mero\s*que\s*' .
                    'aparece\s*ao\s*lado\s*:\s*<\/td>#is',
                $html
            )
            && isset($getParams['numRandom'])
            && $try < 3
        ) {
            $this->getImageCaptcha();
            $this->breakCaptcha();

            $getParams['numRandom'] = $this->captcha;

            $html = $this->makeRequest($url, $method, $getParams, $postParams, ++$try);
        }

        return $html;
    }

    /**
     *  Faz download da imagem do captcha e salva no path correto
     *
     *  @version 1.0.0
     *
     *  <AUTHOR> - ??
     *  Maximiliano Minucelli - 08/05/2018 - Revisão
     *
     *  @return void
     */
    private function getImageCaptcha()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        file_put_contents($this->captcha_path, $this->getResponse($this->urlCaptcha));
    }

    /**
     *  Pega todos os fundos encontrados para o critério
     *
     *  @version 2.0.1
     *
     *  <AUTHOR> - ??
     *  Maximiliano Minucelli - 08/05/2018 - Revisão
     *  Revisão: Alan Santana de Andrade - 17/09/2018 - Adicionado verificação do tipo de fundo
     *  Revisão: Alan Santana de Andrade - 18/09/2018 - Alterado o algoritmo para utilizar DOM Crawler
     *
     *  @param string $html
     *
     *  @return array
     */
    private function getLinksRequest($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $links = [];

        $crawler = new Crawler($html);

        $crawler->filter("table#ddlFundos tr")->each(function ($node, $i) use (&$links) {
            if ($node->filter("td")->count()) {
                $tds = $node->filter("td");

                $links[$i] = Str::urldecodeRecursive(
                    Util::parseDados(
                        array(
                            "__EVENTTARGET" => array("#href=\"javascript:__doPostBack\\('(.*?)',#is", null)
                        ),
                        $tds->eq(1)->html()
                    )
                );

                $links[$i]['doc'] = Str::removeSpecialCharacters($tds->eq(0)->text());

                if ($tds->eq(2)->text() === "F.I.I.") {
                    $links[$i]['type'] = self::FUNDO_IMOBILIARIO;
                } else {
                    $links[$i]['type'] = self::FUNDO_INVESTIMENTO;
                }
            }
        });

        $crawler->filter("table#ddlAdm tr")->each(function ($node, $i) use (&$links) {
            $admFundo = [];

            if ($node->filter("td")->count()) {
                $tds = $node->filter("td");

                $admFundo['CNPJ'] = Str::removeSpecialCharacters($tds->eq(0)->text());
                $admFundo['razaoSocial'] = strip_tags($tds->eq(1)->html());
                $admFundo['classificacao'] = Str::withOutSpace($tds->eq(2)->html());
                $admFundo['type'] = self::ADM_FUNDO;
            }

            $links[] = $admFundo;
        });

        return $links;
    }

    /**
     *  Pega os dados referentes ao viewState
     *
     *  @version 1.0.0
     *
     *  <AUTHOR> - ??
     *  Maximiliano Minucelli - 08/05/2018 - Revisão
     *
     *  @param string $html
     *
     *  @return array
     */
    private function getViewState($html)
    {
        $patterns = array(
            '__VIEWSTATE' => array('#<input[^>]*?name="__VIEWSTATE"\s*id="__VIEWSTATE"\s*value="(.*?)"[^>]*>#is', null),
            '__VIEWSTATEGENERATOR' => array(
                '#<input[^>]*?name="__VIEWSTATEGENERATOR"\s*id="__VIEWSTATEGENERATOR"\s*value="(.*?)"[^>]*>#is',
                null
            ),
            '__EVENTVALIDATION' => array(
                '#<input[^>]*?name="__EVENTVALIDATION"\s*id="__EVENTVALIDATION"\s*value="(.*?)"[^>]*>#is',
                null
            ),
        );

        return Util::parseDados($patterns, $html);
    }

    /**
     *  Verifica se a página possui erro de proxy
     *
     *  @version 1.0.0
     *
     *  <AUTHOR> - ??
     *  Maximiliano Minucelli - 08/05/2018 - Revisão
     *
     *  @param string $html
     *
     *  @return void
     */
    private function isProxyError($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (preg_match("#Proxy\\s*Error#is", $html)) {
            throw new Exception("Proxy Error, tente novamente", 3);
        }
    }

    private function getElement($crawler, $id, $attr)
    {
        return $crawler->filter($id)->count() > 0 ? $crawler->filter($id)->first()->attr($attr) : '';
    }

    private function getElementText($crawler, $id, $position = "first")
    {
        return $crawler->filter($id)->count() > 0 ? $crawler->filter($id)->$position()->text() : '';
    }
}
