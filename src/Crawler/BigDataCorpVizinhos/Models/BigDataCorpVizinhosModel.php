<?php

namespace App\Crawler\BigDataCorpVizinhos\Models;

class BigDataCorpVizinhosModel
{
    public $DOCUMENTO;
    public $NOME;
    public $IDADE;
    public $ENDERECO;
    public $BAIRRO;
    public $CIDADE;
    public $ESTADO;
    public $CEP;

    public function __construct($pessoa, $endereco)
    {
        $this->DOCUMENTO = $pessoa->RelatedEntityTaxIdNumber;
        $this->NOME = $pessoa->RelatedEntityName;
        $this->ENDERECO = "{$endereco->Typology} {$endereco->AddressMain} {$endereco->Number}";
        $this->BAIRRO = $endereco->Neighborhood;
        $this->CIDADE = $endereco->City;
        $this->ESTADO = $endereco->State;
        $this->CEP = $endereco->ZipCode;
    }
}
