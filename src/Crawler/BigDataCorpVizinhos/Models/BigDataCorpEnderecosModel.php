<?php

namespace App\Crawler\BigDataCorpVizinhos\Models;

class BigDataCorpEnderecosModel
{
    public $Typology;
    public $Title;
    public $AddressMain;
    public $Number;
    public $Complement;
    public $Neighborhood;
    public $ZipCode;
    public $City;
    public $State;
    public $Country;
    public $Type;
    public $HouseholdCode;
    public $BuildingCode;
    public $HouseholdTotalPassages;
    public $HouseholdBadPassages;
    public $HouseholdCrawlingPassages;
    public $HouseholdValidationPassages;
    public $HouseholdQueryPassages;
    public $HouseholdMonthAveragePassages;
    public $HouseholdNumberOfEntities;
    public $BuildingTotalPassages;
    public $BuildingBadPassages;
    public $BuildingCrawlingPassages;
    public $BuildingValidationPassages;
    public $BuildingQueryPassages;
    public $BuildingMonthAveragePassages;
    public $BuildingNumberOfHouseholds;
    public $Priority;
    public $IsMain;
    public $IsRecent;
    public $IsActive;
    public $IsRatified;
    public $FirstPassageDate;
    public $LastPassageDate;
    public $CreationDate;
    public $LastUpdateDate;
    public $HasOptIn;
    public $Latitude;
    public $Longitude;

    public function __construct($dados)
    {
        foreach (array_keys(get_object_vars($this)) as $att) {
            $this->{$att} = $dados->{$att} ??  null;
        }
    }
}
