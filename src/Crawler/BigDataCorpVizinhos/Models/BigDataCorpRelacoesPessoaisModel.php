<?php

namespace App\Crawler\BigDataCorpVizinhos\Models;

class BigDataCorpRelacoesPessoaisModel
{
    public $RelatedEntityTaxIdNumber;
    public $RelatedEntityTaxIdType;
    public $RelatedEntityTaxIdCountry;
    public $RelatedEntityName;
    public $RelationshipType;
    public $RelationshipLevel;
    public $RelationshipStartDate;
    public $RelationshipEndDate;
    public $CreationDate;
    public $LastUpdateDate;
    public $Addresses = [];

    public function __construct($dados)
    {
        foreach (array_keys(get_object_vars($this)) as $att) {
            $this->{$att} = $dados->{$att} ??  null;
        }
    }
}
