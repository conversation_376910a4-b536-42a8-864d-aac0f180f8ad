<?php

namespace App\Crawler\BigDataCorpVizinhos;

use Exception;
use Illuminate\Support\Arr;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\BigDataCorpManager;
use App\Crawler\BigDataCorpVizinhos\Models\BigDataCorpVizinhosModel;
use App\Crawler\BigDataCorpVizinhos\Models\BigDataCorpEnderecosModel;
use App\Crawler\BigDataCorpVizinhos\Models\BigDataCorpRelacoesPessoaisModel;
use App\Crawler\VizinhosGeneric\Models\VizinhoGenericModel;
use App\Crawler\VizinhosGeneric\Models\VizinhosGenericModel;
use App\Crawler\GenericInterface;

class BigDataCorpVizinhos extends Spider implements GenericInterface
{
    private $allDatasets;
    private $allData;

    protected function validateAndSetCrawlerAttributes()
    {
        if (isset($this->param['documento']) && Document::validarCpfOuCnpj($this->param['documento'])) {
            return true;
        }

        throw new Exception('Parâmetro inválido');
    }

    protected function start()
    {
        $this->config();

        $this->allData = $this->getDados($this->allDatasets);

        $relatedAddresses = $this->getRelatedAddresses();
        if (empty($relatedAddresses)) {
            throw new Exception("Não encontrado nenhum endereço!", 2);
        }

        $relatedPeople = $this->getRelatedPeople();
        if (empty($relatedPeople)) {
            throw new Exception("Nenhum resultado encontrado!", 2);
        }

        $results = [];
        foreach ($relatedPeople as $related) {
            foreach ($relatedAddresses as $address) {
                if (false !== strpos($address->Type, $related->RelatedEntityTaxIdNumber)) {
                    $results[] = new BigDataCorpVizinhosModel($related, $address);
                }
            }
        }

        return ['aVizinhos' => $results];
    }

    private function config()
    {
        $documento = $this->param['documento'];
        $this->allDatasets = 'related_people_addresses,related_people.filter(relationshiptype=neighbor)';
    }

    public function getRelatedAddresses()
    {
        $results = [];
        foreach ($this->allData->Result as $value) {
            foreach ($value->RelatedPeopleAddresses as $relatedPeopleAddress) {
                $results[] = new BigDataCorpEnderecosModel($relatedPeopleAddress);
            }
        }

        return $results;
    }

    public function getRelatedPeople()
    {
        $results = [];
        foreach ($this->allData->Result as $value) {
            foreach ($value->RelatedPeople->PersonalRelationships as $personalRelationship) {
                $results[] = new BigDataCorpRelacoesPessoaisModel($personalRelationship);
            }
        }

        return $results;
    }

    private function getDados($url)
    {
        $bigDataManager = new BigDataCorpManager($this->idUser);
        $class = 'BigDataCorpVizinhos';
        $result = $bigDataManager->getDataPersonApi($url, $this->param['documento'], $class);
        $data = json_decode($result);
        return $data;
    }

    public function parseToGeneric(array $data, string $sourceName): VizinhosGenericModel
    {
        $result = [];
        $data = Arr::first($data);

        $vizinhos = new VizinhosGenericModel($sourceName);

        foreach ($data as $item) {
            $vizinho = new VizinhoGenericModel();
            $vizinho->DOCUMENTO = $item->DOCUMENTO;
            $vizinho->NOME = $item->NOME;
            $vizinho->IDADE = $item->IDADE;
            $vizinho->ENDERECO = $item->ENDERECO;
            $vizinho->BAIRRO = $item->BAIRRO;
            $vizinho->CIDADE = $item->CIDADE;
            $vizinho->ESTADO = $item->ESTADO;
            $vizinho->CEP = $item->CEP;

            $vizinhos->setVizinhos($vizinho);
        }

        return $vizinhos;
    }
}
