<?php

namespace App\Crawler\ReceitaFederalPf;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Str;
use App\Manager\DynamoManager;
use App\Factory\PostgresDB;
use App\Helper\Util;
use App\Manager\ElasticsearchManager;
use Aws\Sdk;
use Exception;

class ReceitaFederalPf extends Spider
{
    protected function validateAndSetCrawlerAttributes()
    {
        return false;
    }

    protected function start()
    {

        if (isset($this->param['nome']) && !empty($this->param['nome'])) {
            $response = $this->receitaFederalNome();
        } else {
            switch ($this->param['fornecedor']) {
                case 'receita_federal_api':
                    $response = $this->receitaFederal();
                    //API redirecionada para o site enquanto a api estiver fora do ar
                    //$response = $this->receitaFederalApi();
                    break;

                default:
                    $response = $this->receitaFederal();
                    break;
            }
        }

        if (empty($response['nome'])) {
            throw new Exception('Receita retornou todos os campos vazios favor reprocessar o lote', 3);
        }

        $this->updateSpine('receitaFederalPf', $response);

        return $response;
    }

    private function getDataDeNascimento()
    {
        $doc = $this->param['cpf'];
        $db = new PostgresDB();
        $results =  $db->connectCaptura()
        ->select('data_nascimento')
        ->from('receita_federal.pf')
        ->where("cpf = '$doc'")
        ->execute()
        ->fetchAll();
        $db->disconnect();

        // se não encontrar no postgre, busca no elastic
        if (empty($results)) {
            $results = $this->searchElasticInfo($doc);
            $results = $results['hits']['hits'][0]['_source'];

            if (empty($results)) {
                throw new Exception('DataNascimentoDivergente', 1);
            }

            if (empty($results['data_nascimento'])) {
                throw new Exception('DataNascimentoDivergente', 1);
            }
        }

        if (!empty($results[0])) {
            $dtNasc = str_replace("-", "", $results[0]['data_nascimento']);
        } else {
            $dtNasc = $results['data_nascimento'];
        }

        return  substr($dtNasc, 6, 2) . '/' .
                substr($dtNasc, 4, 2) . '/' .
                substr($dtNasc, 0, 4);
    }

    private function receitaFederalNome()
    {
        $params_originais = $this->param;

        try {
            switch ($this->param['fornecedor']) {
                case 'receita_federal_site':
                    $response = $this->receitaFederal();
                    break;

                default:
                    $response = $this->receitaFederalApi();
                    break;
            }
        } catch (Exception $e) {
            // Caso de erro de data e foi passada uma data, tenta novamente com a data da nossa base
            if ($e->getCode() == 1 and !empty($params_originais['data_nascimento'])) {
                $this->param['data_nascimento'] = $this->getDataDeNascimento();

                switch ($this->param['fornecedor']) {
                    case 'receita_federal_site':
                        $response = $this->receitaFederal();
                        break;

                    default:
                        $response = $this->receitaFederalApi();
                        break;
                }
            } else {
                throw $e;
            }
        }

        $response['data']['cod_resultado'] = '';

        // Verifica se a data de nascimento da resposta foi diferente da passada
        if (
            preg_replace(
                '/\D*/isu',
                '',
                $response['data']['nascimento']
            ) != preg_replace(
                '/\D*/isu',
                '',
                $params_originais['data_nascimento']
            )
        ) {
            $response['data']['cod_resultado'] = 1; // 'Data de nascimento divergente da real';
        }

        // Verifica se o nome da resposta foi diferente da passada
        if (strtolower(trim($response['data']['nome'])) != strtolower(trim($params_originais['nome']))) {
            $response['data']['cod_resultado'] = ($response['data']['cod_resultado'] == 1) ? 3 : 2;
        } else {
            // $response['data']['similaridade_nome'] = '0%';
        }

        switch (strtolower($response['data']['situacao'])) {
            case 'regular':
                $response['data']['cod_situacao'] = 1;
                break;
            case 'cancelado':
            case '':
            case 'suspenso':
            case 'inativo':
                $response['data']['cod_situacao'] = 2;
                break;
            default:
                $response['data']['cod_situacao'] = 3;
                break;
        }

        return $response;
    }

    private function receitaFederalApi()
    {
        // $this->setProxy();

        //Gerar Token
        $id = str_replace('.', '', uniqid(mt_rand(), true));
        $url = "https://movel01.receita.fazenda.gov.br/servicos-rfb/v2/Util/obterToken";
        $param = [
            'aplicativo' => 'pessoafisica',
            'idNuvem' => $id,
            'sandbox' => 'false',
            'so' => 'ios'
        ];
        $headers = [
            'Host: movel01.receita.fazenda.gov.br',
            'Connection: keep-alive',
            'Accept: application/json',
            'Accept-Language: fr-ca',
        ];

        $response = $this->getResponse($url, 'POST', json_encode($param), $headers);

        //Obter Token
        $url = "https://movel01.receita.fazenda.gov.br/servicos-rfb/v2/EnviarMensagemNuvem/recuperarMensagens";
        $param = [
            'idDispositivo' => $id
        ];
        $token = null;
        $headers = [
            'Host: movel01.receita.fazenda.gov.br',
            'aplicativo: Pessoa F?sica',
            'Accept: */*',
            'versao_app: 4.3',
            'plataforma: iOS',
            'Accept-Language: fr-ca',
            'token: ' . $token,
            'versao: 10.2.1',
            'dispositivo: iPhone',
            'charset: utf-8',
            'Connection: keep-alive'
        ];

        $response = $this->getResponse($url, 'POST', $param, $headers);


        //Consulta
        $token = json_decode($response, true)[0]['token'];
        $tokenAuth = json_decode(json_decode($response, true)[0]['mensagemEnviada'], true)['aps']['tokenJWT'];

        $url = "https://movel01.receita.fazenda.gov.br:443/servicos-rfb/v2/IRPF/cpf/consultar";

        $param = [
            'cpf' => $this->param['cpf'],
            'dataNascimento' => $this->param['data_nascimento']
                ? str_replace('/', '', $this->param['data_nascimento'])
                : str_replace('/', '', $this->getDataDeNascimento()),
            'tokenAuth' => $tokenAuth
        ];

        $headers = [
            'Host: movel01.receita.fazenda.gov.br',
            'aplicativo: Pessoa Física',
            'Accept: */*',
            'versao_app: 4.3',
            'plataforma: iOS',
            'Accept-Language: fr-ca',
            'token: ' . $token,
            'versao: 10.2.1',
            'dispositivo: iPhone',
            'idioma: pt',
            'Connection: keep-alive'
        ];


        $response = $this->getResponse($url, 'POST', json_encode($param), $headers);

        $response = json_decode($response, true);

        if ($response['codigoRetorno'] != '00') {
            throw new Exception($response['mensagemRetorno'], 1);
        }

        return [
            'cpf'                   => $this->param['cpf'],
            'nome'                  => $response['nome'],
            'nascimento'            => $response['dataNascimento'],
            'situacao'              => $response['descSituacaoCadastral'],
            'inscricao'             => $response['dataIsncricao'],
            'digito_verificador'    => $response['digitoVerificador'],
            'obito'                 => $response['mensagemObito'] != null ? $response['mensagemObito'] : '',
            'hora'                  => $response['horaConsulta'],
            'data'                  => $response['dataConsulta'],
            'chave'                 => $response['codigoControle']
        ];
    }

    private function getSiteHeaders($method = 'GET', $cookies = [], $hResponse = '')
    {
        $options = [
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
            ]
        ];

        if ($method === 'POST') {
            $options['http'] = [
                'method'  => 'POST',
                'header' => 'Cookie: ' . implode(';', $cookies),
                'content' => http_build_query([
                    'idCheckedReCaptcha' => 'false',
                    'txtCPF' => Document::formatCpf($this->param['cpf']),
                    'txtDataNascimento' => $this->param['data_nascimento']
                        ? $this->param['data_nascimento']
                        : $this->getDataDeNascimento(),
                    'h-captcha-response' => $hResponse,
                    'Enviar' => 'Consultar'
                ])
            ];
        }

        return stream_context_create($options);
    }

    private function receitaFederal()
    {
        $url = "https://servicos.receita.fazenda.gov.br/Servicos/CPF/ConsultaSituacao/ConsultaPublica.asp";

        $response = Str::encoding(file_get_contents($url, false, $this->getSiteHeaders()));

        $cookies = [];

        foreach ($http_response_header as $hdr) {
            if (preg_match('/^Set-Cookie:\s*([^;]+)/', $hdr, $matches)) {
                $cookies[] = $matches[1];
            }
        }

        preg_match('/data-sitekey="(.*?)"/isu', $response, $match);

        if (empty($match[1])) {
            throw new Exception('Erro ao pegar a chave do catpcha', 3);
        }

        if (isset($this->param['captchaMonster']) && $this->param['captchaMonster']) {
            $hResponse = $this->solveHCaptchaWithCapMonster($match[1], $url);
        } else {
            $hResponse = $this->solveHCaptcha($match[1], $url);
        }

        $urlPost = "https://servicos.receita.fazenda.gov.br/Servicos/CPF/ConsultaSituacao/ConsultaPublicaExibir.asp";

        $response = file_get_contents(
            $urlPost,
            false,
            $this->getSiteHeaders('POST', $cookies, $hResponse)
        );

        if (
            preg_match(
                '#A.valida.*?o.anti-rob.*?.n.*?o.foi.realizada.corretamente#i',
                $response
            )
        ) {
            throw new Exception('Captcha invalido', 3);
        }

        if (preg_match('#Data\s*de\s*nascimento\s*informada.*?est.*?\s*divergente#i', $response)) {
            throw new Exception('DataNascimentoDivergente', 1);
        }

        if (
            preg_match(
                '#Informe\s*a\s*data\s*de\s*nascimento\s*do\s*titular\s*do\s*CPF\s*a\s*ser\s*consultado#i',
                $response
            )
        ) {
            throw new Exception('DataNascimentoDivergente', 1);
        }

        $patterns = [
            'cpf' => ['#CPF:\s*<b>(.*?)</b>#i', null],
            'nome' => ['#Nome:\s*<b>(.*?)</b>#i', null],
            'nascimento' => ['#Data\s*de\s*Nascimento:\s*<b>(.*?)</b>#i', null],
            'situacao' => ['#Situa.*?o\s*Cadastral:\s*<b>(.*?)</b>#i', null],
            'inscricao' => ['#Data\s*da\s*Inscri.*?o:\s*<b>(.*?)</b>#i', null],
            'digito_verificador' => ['#Digito\s*Verificador:\s*<b>(.*?)</b>#i', null],
            'obito' => ['#Ano\s*de\s*.*?bito:\s*<b>(.*?)</b>#i', null],
            'hora' => ['#Comprovante\s*emitido\s*.*s:\s*<b>(.*?)</b>#i', null],
            'data' => ['#do\s*dia\s*<b>(.*?)</b>#i', null],
            'chave' => ['#controle\s*do\s*comprovante:\s*<b>(.*?)<\/b>#i', null],
        ];
        return Util::parseDados($patterns, $response);
    }

    /**
     * Retorna o resultado da spine_pf
     *
     * <AUTHOR> Guilherme Sório - 19/07/2021
     * @param string $document
     * @return array
     */
    private function searchElasticInfo($document)
    {
        return (new ElasticsearchManager())->search($this->getQueryParams($document));
    }

    /**
     * Retorna a query para a busca no elastic search
     *
     * <AUTHOR> Guilherme Sório - 19/07/2021
     * @param string $document
     * @return array
     */
    private function getQueryParams($document)
    {
        return [
            'index' => 'spine_pf',
            'type' => 'pf',
            'body' => [
                'query' => [
                    'match' => [
                        'cpf' => $document
                    ]
                ]
            ]
        ];
    }
}
