<?php

namespace App\Crawler\FacebookPf;

use App\Helper\Document;
use App\Crawler\Spider;
use Exception;

class FacebookPf extends Spider
{
    /**
     * Estava fonte encontra-se inativada nessa atualização
     *
     * <AUTHOR> - 06/10/2021
     *
     */
    protected function start()
    {
        $spider = new FacebookPfSpider();
        $spinePfData = $spider->getSpinePf($this->param['documento'], $this->param['data_nascimento']);
        if (empty($spinePfData['cpf'])) {
            $bigDataCorpData = $spider->getSpinePfBigDataCorp($this->param['documento']);
            $response = $this->parseBigDataCorpToResponse($bigDataCorpData);
        } else {
            $spinePfEnderecos = $spider->getSpinePfEnderecos($this->param['documento']);
            $response = $this->parseSpineToResponse($spinePfData, $spinePfEnderecos);
        }
        return $response;
    }

    protected function parseSpineToResponse($spinePfData, $spinePfEnderecos)
    {
        $data = [
            'dados_pessoais' => [
                'data_situacao' =>  $spinePfData['data_situacao'] ?? null,
                'situacao' =>  $spinePfData['situacao'] ?? null,
                'orgao_emissor' =>  $spinePfData['orgao_emissor'] ?? null,
                'sexo' =>  $spinePfData['sexo'] ?? null,
                'estado_civil' =>  $spinePfData['estado_civil'] ?? null,
                'cbo' =>  $spinePfData['cbo'] ?? null,
                'hash_rf' =>  $spinePfData['hash_rf'] ?? null,
                'mae' =>  $spinePfData['mae'] ?? null,
                'classe_social' =>  $spinePfData['classe_social'] ?? null,
                'rg' =>  $spinePfData['rg'] ?? null,
                'renda_estimada' =>  $spinePfData['renda_estimada'] ?? null,
                'cpf' =>  $spinePfData['cpf'] ?? null,
                'data_nascimento' =>  $spinePfData['data_nascimento'] ?? null,
                'data_alt' =>  $spinePfData['data_alt'] ?? null,
                'hora_situacao' =>  $spinePfData['hora_situacao'] ?? null,
                'nome' =>  $spinePfData['nome'] ?? null,
                'uf_emissao' =>  $spinePfData['uf_emissao'] ?? null,
            ],
            'enderecos' => []
        ];
        foreach ($spinePfEnderecos as $endereco) {
            if (empty($endereco['logradouro'])) {
                continue;
            }
            $data['enderecos'][] = [
                'numero' => $endereco['numero'] ?? null,
                'complemento' => $endereco['complemento'] ?? null,
                'cidade' => $endereco['cidade'] ?? null,
                'cep' => $endereco['cep'] ?? null,
                'tipo_logradouro' => $endereco['tipo_logradouro'] ?? null,
                'bairro' => $endereco['bairro'] ?? null,
                'uf' => $endereco['uf'] ?? null,
                'logradouro' => $endereco['logradouro'] ?? null,
                'longitude' => $endereco['longitude'] ?? null,
                'ibge_setor' => $endereco['ibge_setor'] ?? null,
                'data_log' => $endereco['data_log'] ?? null,
                'latitude' => $endereco['latitude'] ?? null,
            ];
        }
        return $data;
    }

    protected function parseBigDataCorpToResponse($bigDataCorpData)
    {
        if (empty($bigDataCorpData['dados_cadastrais']->CPF)) {
            throw new Exception('Nenhum registro encontrado', 2);
        }
        $data = [
            'dados_pessoais' => [
                'data_situacao' =>  null,
                'situacao' =>  $bigDataCorpData['dados_cadastrais']->SITUACAO_RECEITA ?? null,
                'orgao_emissor' =>  null,
                'sexo' =>  $bigDataCorpData['dados_cadastrais']->SEXO ?? null,
                'estado_civil' =>  null,
                'cbo' =>  null,
                'hash_rf' =>  null,
                'mae' =>  $bigDataCorpData['dados_cadastrais']->NOME_MAE ?? null,
                'classe_social' =>  null,
                'rg' =>  null,
                'renda_estimada' =>  null,
                'cpf' =>  $bigDataCorpData['dados_cadastrais']->CPF ?? null,
                'data_nascimento' =>  $bigDataCorpData['dados_cadastrais']->DATANASC ?? null,
                'data_alt' =>  null,
                'hora_situacao' =>  null,
                'nome' =>  $bigDataCorpData['dados_cadastrais']->NOME ?? null,
                'uf_emissao' =>  null,
            ],
            'enderecos' => []
        ];
        foreach ($bigDataCorpData['aEnderecos'] as $endereco) {
            if (empty($endereco->AddressMain)) {
                continue;
            }

            $data['enderecos'][] = [
                'numero' => $endereco->Number ?? null,
                'complemento' => $endereco->Complement ?? null,
                'cidade' => $endereco->City ?? null,
                'cep' => $endereco->ZipCode ?? null,
                'tipo_logradouro' => $endereco->Typology ?? null,
                'bairro' => $endereco->Neighborhood ?? null,
                'uf' => $endereco->State ?? null,
                'logradouro' => $endereco->AddressMain ?? null,
                'longitude' => $endereco->Longitude ?? null,
                'ibge_setor' => $endereco->IbgeCode ?? null,
                'data_log' => null,
                'latitude' => $endereco->Latitude ?? null
            ];
        }

        return $data;
    }

    protected function validateAndSetCrawlerAttributes()
    {
        $this->param['documento'] = preg_replace('/\D/isu', '', $this->param['documento']);
        if (!Document::validarCpf($this->param['documento'])) {
            throw new Exception('CPF inválido', 1);
        }

        $this->param['data_nascimento'] = $this->param['data_nascimento'] ?? null;
    }
}
