<?php

namespace App\Crawler\FacebookPf;

use App\Crawler\BigDataCorpLocalizaPessoa\BigDataCorpLocalizaPessoa;
use App\Manager\DynamoManager;
use App\Manager\SnsManager;

class FacebookPfSpider
{
    private $dynamoManager;
    private $snsManager;

    public function __construct()
    {
        $this->dynamoManager = new DynamoManager();
        $this->snsManager = new SnsManager([
            'access_key' => AWS_ACCESS_KEY,
            'secret_key' => AWS_SECRET_KEY
        ]);
    }

    public function getSpinePf($cpf, $dataNascimento)
    {
        $spine = $this->dynamoManager->getItem('spine_pf', [
            'cpf' => $cpf
        ]);

        if (empty($spine['data_nascimento']) && !empty($dataNascimento)) {
            $spine['data_nascimento'] = $dataNascimento;
        }
        $this->requestUpdateSpine($spine['cpf']);
        return $spine;
    }

    public function getSpinePfEnderecos($cpf)
    {
        $conditions = 'cpf = :cpf';
        $expression = [
            ':cpf' => $cpf
        ];
        return $this->dynamoManager->getQuery('spine_pf_enderecos', $conditions, $expression);
    }

    public function getSpinePfBigDataCorp($cpf)
    {
        $data = (new BigDataCorpLocalizaPessoa([
            'documento' => $cpf
        ], []))->run();
        return $data['data'];
    }

    private function requestUpdateSpine($cpf)
    {
        $target = 'arn:aws:sns:us-east-1:' . ACCOUNT_ID . ':ReceitaFederalLambda';
        $subject = 'Atualização SPINE PF';
        $message = json_encode([
            "retry" => "1",
            "source" => "ReceitaFederalPfGeneric",
            "param" => [
                "documento" => $cpf
            ]
        ]);
        $this->snsManager->sendMessage($target, $subject, $message);
    }
}
