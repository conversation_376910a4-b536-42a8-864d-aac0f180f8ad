<?php

namespace App\Crawler\SemaMtAreaEmbargada;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Util;
use Exception;

class SemaMtAreaEmbargada extends Spider
{
    private const BASE_URL = 'http://monitoramento.sema.mt.gov.br/simlam';
    private const URL_EMBARGADA = '/ListarAreasEmbargadas.aspx';
    private const URL_PROCESS = '/ListarProcessoPublico.aspx';
    private $cpf_cnpj_name = "";
    private $isName = false;
    private $process = [];
    private $areas_embargadas = [];
    private $pagina = 1;
    private $count = 0;
    private $limit = 10;


    public function start()
    {
        $this->setAlternativeProxy();

        $html = $this->makeRequestAreasEmbargadas();
        $process = $this->getNumberProcess($html, 1);

        foreach ($process as $infoProcess) {
            $this->makeRequestProcess($infoProcess);
        }

        return $this->process;
    }

    /**
     * Faz a requisição da página de Áreas Embargadas.
     * <AUTHOR>
     * @param null
     * @return string
     */
    private function makeRequestAreasEmbargadas()
    {
        $html = $this->getResponse(self::BASE_URL . self::URL_EMBARGADA);

        preg_match('/id="__VIEWSTATE"\svalue="([\s\S]*?)"/', $html, $this->viewState);
        preg_match('/__VIEWSTATEGENERATOR"\svalue="(.*)"/', $html, $this->viewStateGenerator);
        preg_match('/__EVENTVALIDATION"\svalue="(.*)"/', $html, $this->eventValuation);

        $busca = $this->isName ? 3 : 4;

        $params = [
            'v_hidden_ScrollTop' => 0,
            'v_hidden_ScrollLeft' => 0,
            '__EVENTTARGET' =>  'ddlBuscar',
            '__EVENTARGUMENT' =>  '',
            '__LASTFOCUS' => '',
            '__VIEWSTATE' => $this->viewState[1],
            '__VIEWSTATEGENERATOR' => $this->viewStateGenerator[1],
            '__SCROLLPOSITIONX' => 0,
            '__SCROLLPOSITIONY' => 0,
            '__EVENTVALIDATION' => $this->eventValuation[1],
            'ddlBuscar' => $busca,
        ];

        $html = $this->getResponse(self::BASE_URL . self::URL_EMBARGADA, 'POST', $params);

        preg_match('/id="__VIEWSTATE"\svalue="([\s\S]*?)"/', $html, $this->viewState);
        preg_match('/__EVENTVALIDATION"\svalue="(.*)"/', $html, $this->eventValuation);

        $params = [
            'v_hidden_ScrollTop' => 0,
            'v_hidden_ScrollLeft' => 0,
            '__EVENTTARGET' =>  '',
            '__EVENTARGUMENT' =>  '',
            '__LASTFOCUS' => '',
            '__VIEWSTATE' => $this->viewState[1],
            '__VIEWSTATEGENERATOR' => $this->viewStateGenerator[1],
            '__SCROLLPOSITIONX' => 0,
            '__SCROLLPOSITIONY' => 0,
            '__EVENTVALIDATION' => $this->eventValuation[1],
            'ddlBuscar' => $busca,
            'txtBusca' => $this->cpf_cnpj_name,
            'btnLocalizar.x' => 17,
            'btnLocalizar.y' => 8,
        ];

        $html = $this->getResponse(self::BASE_URL . self::URL_EMBARGADA, 'POST', $params);

        return $html;
    }

    /**
     * Adiciona todas as informações das áreas embargadas consultados, no array.
     * <AUTHOR> Pereira
     * @param string $html
     * @return array
     */
    private function getNumberProcess($html, $count)
    {
        preg_match('/(\d+)\socorrências/', $html, $totalOcorrencias);

        if ((int)$totalOcorrencias[1] == 0) {
            throw new Exception("Nenhum resultado encontrado.", 2);
        }

        $totalOcorrencias = (int)$totalOcorrencias[1];

        for ($i = 1; $i <= $totalOcorrencias; $i++) {
            if ($this->count == $this->limit) {
                return $this->areas_embargadas;
            }

            $element = $count + $i;

            $patterns = [
                'numero_area_embargada' => ["@dgListagem__ctl{$element}_fitNumero\"\stitle=\"([\s\S]*?)\"@"],
                'numero_processo' => ["@dgListagem__ctl{$element}_fitProcesso\"\stitle=\"([\s\S]*?)\"@"],
                'modelo' => ["@dgListagem__ctl{$element}_Fitlabel1\"\stitle=\"([\s\S]*?)\"@"],
                'municipio' => ["@dgListagem__ctl{$element}_Fitlabel3\"\stitle=\"([\s\S]*?)\"@"],
            ];

            $infoProcess = Util::parseDados($patterns, $html, false, false);

            if (empty($infoProcess)) {
                $this->getNumberProcess($html, 2);
            }

            if (empty($infoProcess['numero_processo'])) {
                continue;
            }

            $this->count++;

            $this->areas_embargadas[] = $infoProcess;

            if ($element == 12) {
                preg_match('/id="__VIEWSTATE"\svalue="([\s\S]*?)"/', $html, $this->viewState);
                preg_match('/__EVENTVALIDATION"\svalue="(.*)"/', $html, $this->eventValuation);
                preg_match('/__VIEWSTATEGENERATOR"\svalue="(.*)"/', $html, $this->viewStateGenerator);

                $params = [
                    'v_hidden_ScrollTop' => 0,
                    'v_hidden_ScrollLeft' => 0,
                    '__EVENTTARGET' =>  'dgListagem$_ctl14$_ctl' . $this->pagina,
                    '__EVENTARGUMENT' =>  '',
                    '__LASTFOCUS' => '',
                    '__VIEWSTATE' => $this->viewState[1],
                    '__VIEWSTATEGENERATOR' => $this->viewStateGenerator[1],
                    '__SCROLLPOSITIONX' => 0,
                    '__SCROLLPOSITIONY' => 0,
                    '__EVENTVALIDATION' => $this->eventValuation[1],
                    'ddlBuscar' => $this->isName ? 3 : 4,
                    'txtBusca' => $this->cpf_cnpj_name,
                ];

                $html = $this->getResponse(self::BASE_URL . self::URL_EMBARGADA, 'POST', $params);

                $this->pagina++;
                $this->getNumberProcess($html, $count);
            }
        }

        return $this->areas_embargadas;
    }

    /**
     * Faz a requisição da página de processos, consultando cada processo capturado.
     * <AUTHOR> Pereira
     * @param array $infoProcess
     * @return null
     */
    private function makeRequestProcess($infoProcess)
    {
        $html = $this->getResponse(self::BASE_URL . self::URL_PROCESS);

        preg_match('/id="__VIEWSTATE"\svalue="([\s\S]*?)"/', $html, $this->viewState);
        preg_match('/__VIEWSTATEGENERATOR"\svalue="(.*)"/', $html, $this->viewStateGenerator);
        preg_match('/__EVENTVALIDATION"\svalue="(.*)"/', $html, $this->eventValuation);

        $params = [
            '__EVENTTARGET' => 'ddlBusca',
            '__EVENTARGUMENT' =>  '',
            '__LASTFOCUS' => '',
            '__VIEWSTATE' => $this->viewState[1],
            '__VIEWSTATEGENERATOR' => $this->viewStateGenerator[1],
            '__SCROLLPOSITIONX' => 0,
            '__SCROLLPOSITIONY' => 0,
            '__EVENTVALIDATION' => $this->eventValuation[1],
            'hdnTexto' => '',
            'coluna' => '',
            'ordem' => '',
            'valor' => '',
            'ddlBusca' => 1,
            'ddlEstado' => 11,
            'ddlMunicipio' => 'Acorizal',
        ];

        $html = $this->getResponse(self::BASE_URL . self::URL_PROCESS, 'POST', $params);

        preg_match('/id="__VIEWSTATE"\svalue="([\s\S]*?)"/', $html, $this->viewState);
        preg_match('/__EVENTVALIDATION"\svalue="(.*)"/', $html, $this->eventValuation);

        $params = [
            '__EVENTTARGET' => '',
            '__EVENTARGUMENT' =>  '',
            '__LASTFOCUS' => '',
            '__VIEWSTATE' => $this->viewState[1],
            '__VIEWSTATEGENERATOR' => $this->viewStateGenerator[1],
            '__SCROLLPOSITIONX' => 0,
            '__SCROLLPOSITIONY' => 0,
            '__EVENTVALIDATION' => $this->eventValuation[1],
            'hdnTexto' => '',
            'coluna' => '',
            'ordem' => '',
            'valor' => '',
            'ddlBusca' => 1,
            'btnOK.x' => 14,
            'btnOK.y' => 12,
            'txtBusca' => $infoProcess['numero_processo'],
        ];

        $html = $this->getResponse(self::BASE_URL . self::URL_PROCESS, 'POST', $params);

        preg_match('/id="__VIEWSTATE"\svalue="([\s\S]*?)"/', $html, $this->viewState);
        preg_match('/__EVENTVALIDATION"\svalue="(.*)"/', $html, $this->eventValuation);
        preg_match('/onclick="AbrirTela\([\s\S]*?;(\d+)&#/', $html, $numberPopup);
        preg_match("/\.aspx\?Id='\+id,'([\s\S]*?)'/", $html, $visualizerPopup);

        $url = $this->makeUrlProcess($numberPopup[1], $visualizerPopup[1]);

        $html = $this->getResponse($url);

        preg_match("/'(VisualizarProcesso.*?)'/", $html, $urlProcesso);

        $html = $this->getResponse(self::BASE_URL . "/{$urlProcesso[1]}");

        $this->parseDataProcess($html, $infoProcess, self::BASE_URL . "/{$urlProcesso[1]}");
    }

    /**
     * Monta a url do popup do processo.
     * <AUTHOR> Pereira
     * @param string $popup, $visualize
     * @return string
     */
    private function makeUrlProcess($popup, $visualize)
    {
        $params = [
            'Id' => $popup,
            'idRetorno' => $visualize,
            'acao' => 'empreendimento',
        ];

        $params = http_build_query($params);

        return self::BASE_URL . "/WindowOpen.aspx?WindowOpen=VisualizarProcesso.aspx?" . $params;
    }

    /**
     * Faz o parse dos dados do processo.
     * <AUTHOR> Pereira
     * @param string $html
     * @return null
     */
    private function parseDataProcess($html, $infoProcess, $urlPopup)
    {
        $patterns = [
            'tipo_processo' => ['@lblTipo"\sclass="texto">(.*?)<@'],
            'numero_processo' => ['@lblNumero"\sclass="texto">(.*?)<@'],
            'interessado' => ['@lblInterassado"\sclass="texto">(.*?)<@'],
            'empreendimento' => ['@fitEmpreendimento"\sclass="texto">(.*?)<@'],
            'atividades_licenciadas' => ['@lblAtividades"\sclass="texto">(.*?)<@'],
            'situacao_processo' => ['@lblSituacao"\sclass="texto">(.*?)<@'],
            'data_criacao' => ['@lblData"\sclass="texto">(.*?)<@'],
            'responsaveis_tecnicos' => ['@lblRespTec"\sclass="texto">(.*?)<@'],
            'arquivos' => ['@lblArquivosExistem"\sclass="texto">(.*?)<@'],
            'titulos_emitidos' => ['@fitTitulos"\sclass="texto">(.*?)<@'],
            'pendencias' => ['@fitPendencias"\sclass="texto">(.*?)<@'],
        ];

        $data = Util::parseDados($patterns, $html, false, false);

        if (!isset($data['arquivos'])) {
            $data['arquivos'] = $urlPopup;
        }

        $data['url_empreendimento'] = $this->makeUrlEmpreendimento($html);
        $data['numero_area_embargada'] = $infoProcess['numero_area_embargada'];
        $data['numero_processo'] = $infoProcess['numero_processo'];
        $data['modelo'] = $infoProcess['modelo'];
        $data['municipio'] = $infoProcess['municipio'];

        $this->process[] = $data;
    }

    /**
     * Monta a url do popup do empreendimento.
     * <AUTHOR> Pereira
     * @param string $html
     * @return string
     */
    private function makeUrlEmpreendimento($html)
    {
        preg_match_all("/,'(.*)','empreendimento/", $html, $stateEmpreendimento);
        preg_match('/PopupEmpreendimento\(\s(.*)\s\)/', $html, $popupEmpreendimento);

        $params = [
            'idEmpreendimento' => $popupEmpreendimento[1],
            'idRetorno' => $stateEmpreendimento[1][0],
            'acao' => 'empreendimento',
        ];

        $params = http_build_query($params);

        $url = self::BASE_URL . "/WindowOpen.aspx?WindowOpen=VisualizarEmpreendimento.aspx|" . $params;

        $html = $this->getResponse($url);

        preg_match("/'(Visualizar.*?)'/", $html, $urlEmpreendimento);

        return 'http://monitoramento.sema.mt.gov.br/simlam/' . $urlEmpreendimento[1];
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCpfOuCnpj($this->param['cpf_cnpj_name'])) {
            $this->isName = true;
            $this->cpf_cnpj_name = trim($this->param['cpf_cnpj_name']);
        } else {
            $this->cpf_cnpj_name = Document::formatCpfOrCnpj($this->param['cpf_cnpj_name']);
        }

        if ($this->param['cpf_cnpj_name'] == '') {
            throw new Exception("Parametro inválido", 1);
        }

        if (!empty($this->param['limit'])) {
            $this->limit = $this->param['limit'];
        }
    }
}
