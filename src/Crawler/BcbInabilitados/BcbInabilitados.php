<?php

namespace App\Crawler\BcbInabilitados;

use Exception;
use Carbon\Carbon;
use App\Crawler\Spider;
use App\Factory\MongoDB;
use App\Helper\Document;

/**
 * Classe de consulta dos dados na BCB Inabilitados
 *
 * @version 0.1.0
 * <AUTHOR> - 28/09/2020
 */
class BcbInabilitados extends Spider
{
    protected $name_document;

    protected $table = 'bcb.inabilitados';

    private $datesToParse = [
        'data_publicacao' => false, // sem hora
        'prazo_final' => false,
        'data_carga' => true,
        'data_emissao' => true
    ];

    protected function start(): array
    {

        $results = $this->search();

        if (empty($results)) {
            throw new Exception('Nenhum registro encontrado', 2);
        }

        return $this->parseDates($results);
    }

    protected function search(): array
    {
        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('banco_central_brasil')
            ->setCollection('lista_inabilitados');

        $result = $manager->textSearch($this->name_document);
        $result = json_decode(json_encode($result->toArray(), true), true);

        return $result;
    }

    /**
     *  Enviar para o banco com a data formatada
     */
    private function parseDates(array $results): array
    {
        $return = [];

        foreach ($results as $result) {
            foreach ($result as $key => $item) {
                if (isset($this->datesToParse[$key])) {
                    $time = $this->datesToParse[$key] ? ' H:i:s' : '';
                    $date = Carbon::createFromFormat('Y-m-d' . $time, $item);
                    $result[$key] = $date->format('d-m-Y' . $time);
                }
            }

            $return[] = $result;
        }

        return $return;
    }

    protected function validateAndSetCrawlerAttributes(): void
    {

        if (empty($this->param['nome_cpf'])) {
            throw new Exception('Parâmetro invalido', 1);
        }

        $this->name_document = $this->param['nome_cpf'];

        if (Document::validarCpfOuCnpj($this->param['nome_cpf'])) {
            $this->name_document = Document::removeMask($this->param['nome_cpf']);
        }
    }
}
