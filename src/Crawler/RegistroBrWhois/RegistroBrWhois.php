<?php

namespace App\Crawler\RegistroBrWhois;

use App\Crawler\Spider;
use Exception;
use App\Helper\Str;
use App\Helper\Document;

class RegistroBrWhois extends Spider
{
    private const CAPTCHA_TOKEN = '6Le1MiMUAAAAAIgsWOl8yEQf_P9Yi_y4J0J_fgpo';
    private const REGEX_DOMINIOS = '#usu.*rio\sn.*o\spossu.*\sdom.*nios\sregistrados#isu';
    private const RETRY = 15;

    private $url = 'https://200.160.2.3/v2/ajax/whois/?qr=<criterio>&recaptchaResponse=';
    public $debug = false;
    private $retry = 1;

    /**
     *  Executa pesquisa
     *
     *  <AUTHOR> - 20/07/2018
     *  @version 1.0.0
     *  @version 1.0.1 - Jefferson Mesquita 05/06/2019
     *                  Buscar os dados direto na API WHOIS, 5 tentativas pois tem bloqueio
     *                  de uso após chamar algumas vezes
     *
     *  @return array
     */
    protected function start()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $this->setCurlOpt([
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        do {
            $exception = null;
            try {
                $this->setProxy();
                $token = $this->getXsrfToken();

                ## Validação do captcha não está funcionando.
                ## Caso volte a funcionar, descomente a linha abaixo.
                // $this->captcha = $this->resolveCaptcha();

                $response = $this->getResponse(
                    str_replace('<criterio>', $this->document, $this->url) . $this->captcha,
                    'GET',
                    [],
                    [
                        'Cookie: XSRF-TOKEN=' . $token,
                        'X-XSRF-TOKEN: ' . $token,
                        'User-Agent: Twitterbot'
                    ]
                );
                $response = json_decode($response);

                $this->checkResponse($response);

                return $this->parseResult($response);
            } catch (Exception $e) {
                $exception = $e;
                $this->retry++;
                if ($e->getCode() == 2) {
                    break;
                }

                sleep(5);
                curl_close($this->ch);
                $this->openCurl();
            }
        } while (self::RETRY > $this->retry);

        if ($exception) {
            throw $exception;
        }
    }

    /**
     *  Validação dos parâmetros
     *
     *  <AUTHOR> Minucelli - 20/07/2018
     *  @version 1.0.0
     *
     *  @return string
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (!Document::validarCpfOuCnpj($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }

        $this->document = Document::formatCpfOrCnpj($this->param['cpf_cnpj']);
    }

    /**
     *  Resolver ReCaptcha
     *
     *  <AUTHOR> Minucelli - 20/07/2018
     *  @version 1.0.0
     *  @version 1.0.1 - Jefferson Mesquita 05/06/2019
     *                  Colocar token do captcha do site, pois não da para pegar no html
     *
     *  @param string $html
     *
     *  @return string
     */
    private function resolveCaptcha()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $captcha =  $this->solveReCaptcha(self::CAPTCHA_TOKEN, $this->url, 3, 30);

        if ($captcha == 'CAPCHA_NOT_READY' || empty($captcha)) {
            throw new Exception("Erro ao recuperar o captcha.", 3);
        }

        return $captcha;
    }

    /**
     *  Passa o resultado para array
     *
     *  <AUTHOR> Minucelli - 20/07/2018
     *  @version 1.0.0
     *  @version 1.0.1 - Jefferson Mesquita 05/06/2019
     *                  Parse dos dados via API WHOIS
     *
     *  <AUTHOR> Vidal - 17/06/2021
     *                  Retornar mensagem quando a API não retornar os domínios.
     *
     *  @param array $results
     *
     *  @return array
     */
    private function parseResult($response)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $telefone = '';
        $endereco = '';

        if (is_array($response->Entity->Phones)) {
            $telefone = $response->Entity->Phones[0];
        }

        if (is_array($response->Entity->Addresses)) {
            $endereco = $response->Entity->Addresses[0];
        }

        $result = array(
            'documento' => $response->Entity->OwnerID,
            'titular' => $response->Entity->Owner,
            'contato_titular' => $response->Entity->OwnerHandle,
            'responsavel' => $response->Entity->Responsible,
            'telefone' => $telefone,
            'endereco' => $endereco,
            'pais' => $response->Entity->Country,
            'criado' => $this->formatDate($response->Entity->CreatedAt),
            'alterado' => $this->formatDate($response->Entity->UpdatedAt)
        );

        $result['contato']['nome'] = '';
        $result['contato']['email'] = '';
        $result['contato']['pais'] = '';
        $result['contato']['criado'] = '';
        $result['contato']['alterado'] = '';

        if (is_array($response->Entity->Contacts)) {
            foreach ($response->Entity->Contacts as $contact) {
                $result['contato']['nome'] = $contact->Persons[0];
                $result['contato']['email'] = $contact->Emails[0] ??  '';
                $result['contato']['pais'] = $contact->Country;
                $result['contato']['criado'] = $this->formatDate($contact->CreatedAt);
                $result['contato']['alterado'] = $this->formatDate($contact->UpdatedAt);
            }
        }

        $patterns = array(
            'nome' => array('#<label>\s*Nome:\s*</label>\s*</div>\s*<div\s*class=".*?">(.*?)</div>#isu', null),
            'email' => array('#<label>\s*Email:\s*</label>\s*</div>\s*<div\s*class=".*?">(.*?)</div>#isu', null)
        );

        if (is_array($response->Entity->Domains)) {
            foreach ($response->Entity->Domains as $domain) {
                $result['dominios'][] = $domain;
            }
        }

        if (empty($result['dominios']) && empty($result['contato'])) {
            throw new Exception("Nenhum resultado encontrado", 3);
        }

        if (empty($result['dominios']) && !empty($result['contato'])) {
            $result['dominios'] = [
                'A Api do RegistroBR Whois não retornou os domínios para a entidade pesquisada'
            ];
        }

        return Str::encoding($result);
    }

    /**
     *  Formata Data
     *
     *  <AUTHOR> Mesquita 15/06/2019
     *  @version 1.0.0
     *
     *  @param array $date
     */
    private function formatDate($date)
    {
        $date = substr($date, 0, 10);
        return date(
            'd/m/Y',
            strtotime(substr($date, 0, 10))
        );
    }

    /**
     *  Verifica erro ou
     *
     *  <AUTHOR> Minucelli - 20/07/2018
     *  @version 1.0.0
     *  @version 1.0.1 - Jefferson Mesquita 05/06/2019
     *                  Validar os dados via API WHOIS
     *
     *  @param array $results
     */
    private function checkResponse($result)
    {
        if (isset($result->messages)) {
            if ($result->messages[0]->code == 'whois:not-found') {
                throw new Exception("Nenhum dado encontrado", 2);
            }

            throw new Exception($result->messages[0]->code . ': ' . $result->messages[0]->message, 3);
        }
    }

    private function getXsrfToken()
    {
        $this->setCurlOpt([
            CURLOPT_HEADER => 1
        ]);
        $response = $this->getResponse($this->url);
        preg_match_all('/^Set-Cookie:\s*([^;]*)/mi', $response, $matches);
        $cookies = array();
        foreach ($matches[1] as $item) {
            parse_str($item, $cookie);
            $cookies = array_merge($cookies, $cookie);
        }

        $this->setCurlOpt([
            CURLOPT_HEADER => 0
        ]);

        return $cookies['XSRF-TOKEN'];
    }

    /**
     * Método adicionado para forçar renovação de conexão
     *
     * <AUTHOR> Vidal - 10/02/2022
     */
    private function openCurl()
    {
        $uniqid = md5(uniqid(rand(), true));
        $this->cookie = '/tmp/cookie_' . $uniqid . '.txt';
        $this->ch = curl_init();

        curl_setopt($this->ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($this->ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($this->ch, CURLOPT_COOKIESESSION, 1);
        curl_setopt($this->ch, CURLOPT_COOKIEJAR, $this->cookie);
        curl_setopt($this->ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($this->ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($this->ch, CURLOPT_TIMEOUT, 600);
    }
}
