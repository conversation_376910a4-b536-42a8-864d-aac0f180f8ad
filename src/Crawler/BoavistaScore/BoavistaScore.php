<?php

namespace App\Crawler\BoavistaScore;

use App\Crawler\Spider;
use App\Helper\Str;
use App\Helper\Document;
use App\Manager\BoaVista\BoaVistaManager;
use Exception;

class BoavistaScore extends Spider
{
    private $type;
    private $urlBase = 'https://www.bvsnet.com.br/cgi-bin/db2www/netpo028.mbr/string?consulta=';  //String  # PROD

    private $user = '00610494';
    private $pass = '78VE5T';
    private $source;

    protected function start()
    {
        $string = $this->getDadosBoaVista();

        $this->checkResponse($string);

        $dados = $this->parseFinal($string);

        (new BoaVistaManager())->saveBoaVistaScore(
            $dados,
            $this->source,
            $this->param['cpf_cnpj']
        );

        return Str::encoding($dados);
    }


    private function getDadosBoaVista()
    {
        $retry = 5;
        while ($retry >= 0) {
            try {
                $dados = $this->getResponse($this->prepareBoaVistaString());
                $pattern = '/NET.DATA.Error:.CGI CONTENT_TYPE.of.APPLICATION\/JSON.' .
                    'for.data.that.was.submitted.with.HTTP.request.is.not.supported./is';
                if (preg_match($pattern, $dados)) {
                    throw new Exception("Erro ao obter dados do Boa Vista.", 2);
                }
                return $dados;
            } catch (Exception $e) {
                if ($retry == 0) {
                    throw new Exception($e->getMessage(), 2);
                }
                $retry--;
            }
        }
    }

    /**
     * ----
     *
     * <AUTHOR>
     *
     * @version 1.0.1 Jefferson Mesquita 26/12/2019 - colocar senha default direto, sem pegar do auth
     * Foi alterado devido a fonte ser paga e não restrita
     *
     * @return string
     */
    private function prepareBoaVistaString()
    {

        // String de Consulta
        //1   #8   = 8        #01 transação
        $string = str_pad('CSR60', 8);
        //9   #10  = 2        #02 versão
        $string .= str_pad('01', 2);
        //11  #20  = 10       #03 reservado solicitante
        $string .= str_pad('', 10);
        //21  #40  = 20       #04 reservado Boa Vsta Serviços
        $string .= str_pad('', 20);
        //41  #48  = 08       #05 codigo
        $string .= sprintf('%08d', $this->user);
        //49  #56  = 08       #06 senha
        $string .= str_pad($this->pass, 8);
        //57  #64  = 08       #07 consulta
        $string .= str_pad('SCORE', 8);
        //65  #66  = 02       #08 versão da consulta
        $string .= str_pad('04', 02);
        //67  #67  = 01       #09 tipo de resposta
        $string .= str_pad('2', 1);
        // C or T #68  #68  = 01       #10 tipo de transmissão da resposta
        $string .= str_pad('T', 1);
        //69  #69  = 01       #11 tipo de documento
        $string .= $this->type;
        //70  #83  = 14       #12 documento
        $string .= str_pad($this->param['cpf_cnpj'], 14, '0', STR_PAD_LEFT);
        //84  #85  = 02       #13 modelo do score
        $string .= str_pad($this->param['modelo_score'], 2);
        //86  #93  = 08       #14 cep de origem
        $string .= str_pad('', 8);
        //94  #101 = 08       #15 facilitador
        $string .= str_pad('', 8);
        //102 #102 = 01       #16 indicador de fim de texto
        $string .= 'X';

        return $this->urlBase . urlencode($string);
    }

    private function parseFinal($string)
    {
        $dados = $this->parseDados($string);

        return $this->parseDadosDetails($dados);
    }

    private function checkResponse($string)
    {
        if (preg_match("#CODIGO\\s*NAO\\s*AUTORIZADO\\s*A\\s*ESTE\\s*SERVICO#is", $string)) {
            throw new \Exception('CODIGO NAO AUTORIZADO A ESTE SERVICO', 3);
        }

        if (preg_match("#CODIGO\\s*DE\\s*SERVICO\\s*NAO\\s*AUTORIZADO\\s*A\\s*ESTA\\s*CONSULTA#is", $string)) {
            throw new \Exception('CODIGO NAO AUTORIZADO A ESTE SERVICO', 3);
        }

        if (preg_match("#NAO\\s*AUTORIZADO#is", $string)) {
            throw new \Exception('CODIGO NAO AUTORIZADO A ESTE SERVICO', 3);
        }

        if (preg_match("#AUTORIZACAO\\s*DE\\s*ACESSO\\s*INIBIDA\\s*TEMPORARIAMENTE#is", $string)) {
            throw new \Exception('CODIGO NAO AUTORIZADO A ESTE SERVICO', 3);
        }
    }

    private function parseDados($string)
    {
        $response = [];

        $fieldSequence = array(8, 2, 10, 20, 8, 8, 2, 1, 1, 7, 4, 3, 3, 50000000);

        list(
            $response['transacao'],
            $response['versao'],
            $response['reservado_solicitante'],
            $response['reservado_boa_vista_servicos'],
            $response['codigo'],
            $response['consulta'],
            $response['versao_da_consulta'],
            $response['tipo_de_resposta'],
            $response['codigo_de_retorno'],
            $response['numero_da_consulta'],
            $response['tamanho_do_texto'],
            $response['tamanho_do_registro'],
            $response['tipo_do_registro'],
            $response['dados_do_registro']
        ) = $this->splitByLengths(
            (string) trim(strip_tags($string)),
            $fieldSequence
        );

        return $response;
    }

    private function parseDadosDetails($dados)
    {
        switch ((int) $dados['tipo_do_registro']) {
            case 259: // Score
                return $this->score($dados);
            case 260: // score (Plano de Execução)
                return $this->scorePlanoExecucao($dados);
            case 997: // Fechamento da consulta
                return $this->fechamentoConsulta($dados);
        }
    }

    private function score($dados)
    {
        $fieldSequence = array(3, 3, 1, 1, 14, 115, 4, 20);

        $string = $this->getStringResult($dados);

        list(
            $response['tamanho_registro'],
            $response['tipo_registro'],
            $response['registro'],
            $response['tipo_documento'],
            $response['numero_documento'],
            $response['nome'],
            $response['resultado'],
            $response['descricao']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function scorePlanoExecucao($dados)
    {
        $fieldSequence = array(3, 3, 1, 1, 14, 115, 4, 20);

        $string = $this->getStringResult($dados);

        list(
            $response['tamanho_registro'],
            $response['tipo_registro'],
            $response['registro'],
            $response['tipo_documento'],
            $response['numero_documento'],
            $response['nome'],
            $response['resultado'],
            $response['descricao']
        ) = $this->splitByLengths((string) trim($string), $fieldSequence);

        return $response;
    }

    private function getStringResult($dados)
    {
        return (string) trim($dados['tamanho_do_registro'] . $dados['tipo_do_registro'] . $dados['dados_do_registro']);
    }

    private function fechamentoConsulta($dados)
    {
        $fieldSequence = array(3, 3, 1, 95, 8, 6);

        $string = $this->getStringResult($dados);

        list(
            $response['tamanho_registro'],
            $response['tipo_registro'],
            $response['registro'],
            $response['mensagem'],
            $response['data_consulta'],
            $response['hora_consulta']
        ) = $this->splitByLengths((string) trim($string), $fieldSequence);

        return $response;
    }

    private function splitByLengths($string, $arrayLengths)
    {
        $output = [];
        foreach ($arrayLengths as $oneLength) {
            $output[] = substr($string, 0, $oneLength);
            $string = substr($string, $oneLength);
        }

        return ($output);
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if (!isset($this->param['cpf_cnpj']) or empty($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro de documento inválido');
        }

        $this->param['cpf_cnpj'] = preg_replace('/[^0-9]/', '', (string) $this->param['cpf_cnpj']);

        if (Document::validarCpf($this->param['cpf_cnpj'])) {
            $this->param['cpf_cnpj'] = str_pad($this->param['cpf_cnpj'], 11, '0', STR_PAD_LEFT);
            $this->type = 1;
        } elseif (Document::validarCnpj($this->param['cpf_cnpj'])) {
            $this->param['cpf_cnpj'] = str_pad($this->param['cpf_cnpj'], 14, '0', STR_PAD_LEFT);
            $this->type = 2;
        } else {
            throw new Exception('Parâmetro de documento inválido');
        }

        if (!isset($this->param['modelo_score']) or empty($this->param['modelo_score'])) {
            throw new Exception('Parâmetro de score inválido');
        }

        $this->scoreModel($this->param['modelo_score']);
    }

    /**
     * Método responsável por retornar o tipo do Score que está sendo consultado
     *
     * <AUTHOR> Guilherme Sório
     * @param $scoreModel
     *
     */
    private function scoreModel($scoreModel)
    {
        switch ($scoreModel) {
            case '48':
                $this->source = 'Segmentação de Cobrança';
                break;
            case '23':
                $this->source = 'Faturamento Presumido Faixa';
                break;
            case '14':
                $this->source = 'SCORE ALERTA DE FRAUDE STRING';
                break;
            case '52':
                $this->source = 'SCORE RECUPERACAO STRING (45 DIAS)';
                break;
            case '28':
                $this->source = 'SCORE RECUPERACAO STRING (60 DIAS)';
                break;
            case '81':
                $this->source = 'SCORE PF PROP CONS STRING';
                break;
            case '37':
                $this->source = 'SCORE CREDITO PJ WEB';
                break;
            case '11':
                $this->source = 'SCORE ATACADISTA STRING';
                break;
            case '79':
                $this->source = 'Score de Crédito';
                break;
            case '09':
                $this->source = 'SCORE PF 12 MESES SEGM STRING';
                break;
            case '08':
                $this->source = 'SCORE RECUPERACAO PJ BATCH';
                break;
            case '51':
                $this->source = 'SCORE PF FRAUDE STRING';
                break;
            default:
                $this->source = '';
        }
    }
}
