<?php

namespace App\Crawler\Aml;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\Aml\AmlManager;
use Exception;

class Aml extends Spider
{
    /**
     * @var string $document
     */
    private $document;

    public function start()
    {
        $amlManager = new AmlManager();
        $response = json_decode($amlManager->searchPerson($this->document));

        if (empty($response->cadastros)) {
            throw new Exception('Nenhum cadastro encontrado', 2);
        }

        $this->parseFinalData($response);
        return $response;
    }

    private function parseFinalData(object &$response): void
    {
        foreach ($response->cadastros as &$cadastro) {
            foreach ($cadastro as $key => $item) {
                if ($key == 'pseudonimos') {
                    continue;
                }
                $cadastro->$key = str_replace('|', '<br /><br />', $item);
            }
        }
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty(trim($this->param['document']))) {
            throw new Exception('Parametro inválido', 1);
        }

        $this->document =  Document::formatCpfOrCnpj($this->param['document']);
    }
}
