<?php

namespace App\Crawler\IbamaCnd;

use Exception;
use App\Helper\Pdf;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\Bucket\StaticUplexisBucket;

/**
 * @class IbamaCdn
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 */
class IbamaCnd extends Spider
{
    private const SPINE_RECEITA = LAMBDA_LUMEN  . 'captura-';

    private $urlSistema = 'https://servicos.ibama.gov.br/sicafiext/sistema.php';
    private $urlEmitir = 'https://servicos.ibama.gov.br/sicafiext/modulos/sisarr/relatorio/emitir_certidao.php';

    private $certificateName;
    private $certificatePath;
    private $certificatePathS3;
    private $maskedDocument;
    private $tempPath;
    private $headers = [];

    private $s3;

    private const HAVE_DEBITS = 'Certificamos existir(em) débito(s) registrado(s) em nome da pessoa acima identificada';

    private const HAVE_NO_DEBITS = 'Nada consta';

    /**
     * Verifica se os parametros estão corretos
     *
     * @return void
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (empty($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro de documento inválido');
        }

        if (!Document::validarCpfOuCnpj($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro de documento inválido');
        }

        $this->param['cpf_cnpj'] = preg_replace('/[^0-9]/', '', $this->param['cpf_cnpj']);
        $pattern = (strlen($this->param['cpf_cnpj']) > 11) ? "##.###.###/####-##" : "###.###.###-##";
        $this->maskedDocument = $this->mask($this->param['cpf_cnpj'], $pattern);
    }

    protected function start()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->tempPath = '/tmp/';
        $this->certificatePath = $this->tempPath . "{$this->certificateName}";
        $this->certificatePathS3 = S3_STATIC_URL .
            "captura/ibama_certidao_negativada/{$this->certificateName}";
        $this->headers = [
            'Referer'       => $this->urlSistema,
            'User-Agent'    => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/536.11 (KHTML, like Gecko)' .
                ' Ubuntu/12.04 Chromium/20.0.1132.47 Chrome/20.0.1132.47 Safari/536.11'
        ];

        $this->setProxy();
        $this->setCurlOpt([
            CURLOPT_SSL_VERIFYHOST  => 0,
            CURLOPT_SSL_VERIFYPEER  => 0,
            CURLOPT_USERAGENT       => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko)' .
                'Chrome/27.0.1453.110 Safari/537.36',
            CURLOPT_RETURNTRANSFER  => true,
            CURLOPT_FOLLOWLOCATION  => true,
            CURLOPT_TIMEOUT         => 120000
        ]);

        return $this->search();
    }

    private function search()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $this->getResponse($this->urlSistema, 'GET');

        $retry = 3;
        while ($retry >= 0) {
            try {
                $nome = $this->getNameFromServices($this->param['cpf_cnpj']);
                if ($nome) {
                    break;
                }
            } catch (Exception $e) {
                if ($retry == 0) {
                    throw new Exception($e->getMessage(), 3);
                }
                $retry--;
            }
        }

        $params = $this->postData([
            'p_num_cpf_cnpj'            => $this->maskedDocument,
            'num_cpf_cnpj_formatado'    => $this->maskedDocument,
            'num_cpf_cnpj'              => $this->param['cpf_cnpj']
        ]);

        $first = $this->getResponse($this->urlSistema, 'POST', $params, $this->headers);

        if ($this->checkIfNeedRegister($first)) {
            $data = $this->registerCertificate($nome);

            return array_merge([
                'documento'     => $this->param['cpf_cnpj'],
                'nome'          => $nome,
                'municipio'     => '',
                'uf'            => '',
                'endereco'      => '',
                'certidao'      => '',
                'pdf'           => $this->certificatePathS3
            ], $data);
        }


        $data = $this->hasRegister($first, $nome);

        return array_merge([
            'documento'     => $this->param['cpf_cnpj'],
            'nome'          => $nome,
            'municipio'     => '',
            'uf'            => '',
            'endereco'      => '',
            'certidao'      => '',
            'pdf'           => $this->certificatePathS3
        ], $data);
    }

    /**
     * Undocumented function
     *
     * @param string $html
     * @param string $nome
     * @return array
     */
    private function hasRegister($html, $nome)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        /**
         * Tentantiva de registro para gerar mensagem enviada de resposta do Certificado
         */
        /*$params = $this->postData([
            'formDinAba'                => 'aba03',
            'formDinAcao'               => 'Confirmar',
            'formDinPosHScroll'         => 0,
            'formDinPosVScroll'         => 40,
            'modulo'                    => 'sisarr/cons_emitir_certidao',
            'p_num_cpf_cnpj'            => $this->maskedDocument,
            'cad_num_cpf_cnpj'          => $this->maskedDocument,
            'cad_nom_pessoa'            => $nome,
            'cad_end_pessoa'            => $nome,
            'cad_des_bairro'            => $nome,
            'cad_cod_uf'                => 12,
            'cad_cod_municipio'         => 1200013,
        ]);*/

        /**
         * Informa ao sistema para cadastrar e iniciar permissão de impressão
         */
        //$registered = $this->getResponse($this->urlSistema, 'POST', $params, $this->headers);

        //$data = $this->parseData($registered);

        /**
         * Se não tiver permissão de impressão pelo IBAMA,
         * gera um PDF da página ontem tem está informação e mostra para o usuario
         */
        if (!$this->canPrint($html, $nome)) {
            $pattern = "/btngdImprimir.*NUM_CPF_CNPJ.*NUM_CPF_CNPJ_FORMATADO.*NOM_PESSOA.*NUM_PESSOA.*\'.*\|(.*?)\'/i";
            preg_match($pattern, $html, $match);
            list($select, $num_pessoa) = $match;

            $params = $this->postData([
                'formDinAcao'               => 'btngdImprimir',
                'num_pessoa'                => $num_pessoa,
                'nom_pessoa'                => $nome,
                'p_num_cpf_cnpj'            => $this->maskedDocument,
                'num_cpf_cnpj_formatado'    => $this->maskedDocument,
                'num_cpf_cnpj'              => $this->param['cpf_cnpj']
            ]);

            $html = $this->getResponse($this->urlSistema, 'POST', $params, $this->headers);

            if (!$this->html4pdf($html, $this->certificateName)) {
                throw new Exception("Erro ao gravar PDF!", 1);
            }

            $data = $this->parseData($html);

            $pdfPreview = $this->getPdfPreview($this->certificatePath);

            $this->saveS3File();

            $data['certidao'] = $pdfPreview . $this->getHtmlPdfLink($this->certificatePathS3);

            return $data;
        }

        $pattern = "/btngdImprimir.*NUM_CPF_CNPJ.*NUM_CPF_CNPJ_FORMATADO.*NOM_PESSOA.*NUM_PESSOA.*\'.*\|(.*?)\'/i";

        preg_match($pattern, $html, $match);

        list($select, $num_pessoa) = $match;

        $params = $this->postData([
            'formDinAcao'               => 'btngdImprimir',
            'num_pessoa'                => $num_pessoa,
            'nom_pessoa'                => $nome,
            'p_num_cpf_cnpj'            => $this->maskedDocument,
            'num_cpf_cnpj_formatado'    => $this->maskedDocument,
            'num_cpf_cnpj'              => $this->param['cpf_cnpj'],
            'gd_paginacao_pagina'       => 1,
            'gd_coluna_ordenar'         => ''
        ]);

        //$pdfHtml = $this->getResponse($this->urlSistema, 'POST', $params, $this->headers);

        $pdf = $this->getResponse($this->urlEmitir, 'GET', $this->headers);

        file_put_contents($this->certificatePath, $pdf);

        $pdfPreview = $this->getPdfPreview($this->certificatePath);

        $this->saveS3File();

        $data['certidao'] = $pdfPreview . $this->getHtmlPdfLink($this->certificatePathS3);

        return $data;
    }

    public function getHtmlPdfLink($href)
    {
        return "<br><a href=\"" . $href . "\" target=\"_blank\">Ver PDF</a>";
    }

    public function getPdfPreview($path)
    {
        $text = (new Pdf())->getTextFromPdf($path, [
            'nopgbrk',
            'layout',
            'fixed 4'
        ]);

        $pattern = '/Certificamos existir\(em\) d.*?bito\(s\) registrado\(s\) em nome da pessoa acima identificada/m';
        $pattern2 = '/Consta d.*?bito na/m';

        if (preg_match($pattern, $text) || preg_match($pattern2, $text)) {
            return  self::HAVE_DEBITS;
        }

        return self::HAVE_NO_DEBITS;
    }

    /**
     * Verifica se é permitido fazer a impressão da certidão negativa
     *
     * @param string $html
     * @param string $nome
     * @return boolean
     */
    private function canPrint($html, $nome)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $pattern = "/btngdImprimir.*NUM_CPF_CNPJ.*NUM_CPF_CNPJ_FORMATADO.*NOM_PESSOA.*NUM_PESSOA.*\\'.*\\|(.*?)\\'/i";

        if (!preg_match($pattern, $html, $match)) {
            return false;
        }

        list($select, $num_pessoa) = $match;

        $params = $this->postData([
            'formDinAcao'               => 'btngdImprimir',
            'num_pessoa'                => $num_pessoa,
            'nom_pessoa'                => $nome,
            'p_num_cpf_cnpj'            => $this->maskedDocument,
            'num_cpf_cnpj_formatado'    => $this->maskedDocument,
            'num_cpf_cnpj'              => $this->param['cpf_cnpj']
        ]);

        $tryPrint = $this->getResponse($this->urlSistema, 'POST', $params, $this->headers);

        $pattern = "/Certid.o\\s*n.o\\s*pode\\s*ser\\s*emitida\\s*pela\\s*internet.\\s*/i";

        if (!preg_match($pattern, $tryPrint, $match)) {
            return true;
        }

        return false;
    }

    private function html4pdf($html, $pdf)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }
        (new Pdf())->saveHtmlToPdf($html, $this->certificatePath);

        $res = false;
        if (file_exists($this->certificatePath)) {
            // Remove o arquivo temporário
            $res = true;
        }
        return $res;
    }

    /**
     * Undocumented function
     *
     * @param string $nome
     * @return array
     */
    private function registerCertificate($nome)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $params = $this->postData([
            'formDinAba'                => 'aba03',
            'formDinAcao'               => 'Confirmar',
            'formDinPosHScroll'         => 0,
            'formDinPosVScroll'         => 40,
            'modulo'                    => 'sisarr/cons_emitir_certidao',
            'p_num_cpf_cnpj'            => $this->maskedDocument,
            'cad_num_cpf_cnpj'          => $this->maskedDocument,
            'cad_nom_pessoa'            => $nome,
            'cad_end_pessoa'            => $nome,
            'cad_des_bairro'            => $nome,
            'cad_cod_uf'                => 12,
            'cad_cod_municipio'         => 1200013,
        ]);

        /**
         * Informa ao sistema para cadastrar e iniciar permissão de impressão
         */
        $registered = $this->getResponse($this->urlSistema, 'POST', $params, $this->headers);

        $data = $this->parseData($registered);

        /**
         * Bloco que adiciona ao cookie informações de que já foi emitido
         */
        $params = $this->postData([
            'formDinAcao'               => 'btngdImprimir',
            'modulo'                    => 'sisarr/cons_emitir_certidao',
            'formDinPosVScroll'         => 30,
            'formDinAba'                => 'aba01',
            'gd_paginacao_pagina'       => 1,
            'num_cpf_cnpj_formatado'    => $this->maskedDocument,
            'p_num_cpf_cnpj'            => $this->maskedDocument,
            'num_cpf_cnpj'              => $this->param['cpf_cnpj'],
            'nom_pessoa'                => $nome
        ]);

        $this->getResponse($this->urlSistema, 'POST', $params, $this->headers);

        $params = [
            'formDinAcao'   => 'JaEmitiu',
            'modulo'        => 'sisarr/cons_emitir_certidao'
        ];

        $this->getResponse($this->urlSistema, 'POST', $params, $this->headers);

        $pdf = $this->getResponse($this->urlEmitir, 'GET', $this->headers);

        $this->saveFile($this->certificatePath, $pdf);

        $pdfPreview = $this->getPdfPreview($this->certificatePath);

        $this->saveS3File();

        $data['certidao'] = $pdfPreview . $this->getHtmlPdfLink($this->certificatePathS3);

        return $data;
    }

    private function saveS3File()
    {
        (new S3(new StaticUplexisBucket()))->save($this->getS3Path(), $this->certificatePath);
    }

    private function getS3Path()
    {
        return 'captura/ibama_certidao_negativada/' . $this->certificateName;
    }

    /**
     * Pega os campos do resultado da busca
     *
     * @param string $html
     * @return array
     */
    private function parseData($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $data = [];
        $pregs = array(
            'municipio'     => "/<input type=\"hidden\" id=\"nom_municipio\" name=\"nom_municipio\" value=\"(.*?)\">/i",
            'uf'            => "/<input type=\"hidden\" id=\"sig_uf\" name=\"sig_uf\" value=\"(.*?)\">/i",
            'endereco'      => "/<input type=\"hidden\" id=\"end_pessoa\" name=\"end_pessoa\" value=\"(.*?)\">/i",
            'certidao'      => "/<td*.class=[\"|\\']formDinMensagem[\"|\\']>([\\s\\S]*?)<\\/td>/i"
        );

        foreach ($pregs as $key => $value) {
            preg_match($value, $html, $match);

            $alvo = $match[1] ??  null; //Validação do valor a ser atribuído para evitar erros
            $dado = trim(strip_tags($alvo));

            if ($key === 'certidao') {
                $data[$key] = empty($dado) ? self::HAVE_NO_DEBITS : self::HAVE_DEBITS;
                continue;
            }

            if ($key === 'documento') {
                $data[$key] = preg_replace('/(\.)|(\-)|(\/)/isu', '', $dado);
                continue;
            }

            $data[$key] = utf8_encode($dado);
        }

        return $data;
    }

    /**
     * Verifica se precisa registrar
     *
     * @param string $result
     * @return bool
     */
    private function checkIfNeedRegister($result)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $notRegister = '/pessoas\s*ou\s*empresas\s*n.o\s*cadastradas/i';
        $canPrint = '/>Imprimir<\/a>/i';

        if (preg_match($notRegister, $result) and !preg_match($canPrint, $result)) {
            return true;
        }

        return false;
    }

    /**
     * Pega o nome da pessoa ou empresa nos dados cadastrais
     *
     * @param string $document
     * @return string
     */
    private function getNameFromServices($document)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $documento = preg_replace("@\\D+@is", '', $document);

        if (strlen($document) > 11) {
            $params = array(
                'retry' => '1',
                'source' => 'SpinePj',
                'param' => array(
                    'documento' => $documento
                )
            );
        } else {
            $params = array(
                'retry' => '1',
                'source' => 'ReceitaFederalPf',
                'param' => array(
                    'fornecedor' => 'receita_federal',
                    'cpf' => $documento,
                    'captchaMonster' => true
                )
            );
        }

        if (ENV == 'dev' || ENV == 'qa') {
            $spineURL = self::SPINE_RECEITA . "qa-256";
        } else {
            $spineURL = self::SPINE_RECEITA . "prd-256";
        }

        $params = json_encode($params);
        $ch = curl_init($spineURL);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt(
            $ch,
            CURLOPT_HTTPHEADER,
            array(
                'Content-Type: application/json',
                'Content-Length: ' . strlen($params)
            )
        );

        $dados = curl_exec($ch);
        $dados = (object) $dados;
        $dados = json_decode($dados->scalar);

        if (isset($dados->data->razao_social) && !empty($dados->data->razao_social)) {
            return $dados->data->razao_social;
        }

        if (isset($dados->data->nome) && !empty($dados->data->nome)) {
            return $dados->data->nome;
        }
        throw new Exception('Nome necessário para a consulta, Não existe na nossa base', 3);
    }

    /**
     * $val = "741124555465444";
     * $mask = mask($val, "###.##-###.####.##");
     *
     * @param string $val
     * @param string $mask
     * @return string
     */
    private function mask($val, $mask)
    {
        $maskared = '';
        $k = 0;

        for ($i = 0; $i <= strlen($mask) - 1; $i++) {
            if ($mask[$i] == '#' && isset($val[$k])) {
                $maskared .= $val[$k++];
            } elseif (isset($mask[$i])) {
                $maskared .= $mask[$i];
            }
        }

        return $maskared;
    }

    /**
     * Retorna os campos de POST preenchidos
     *
     * @param array $data
     * @return array
     */
    private function postData(array $data)
    {
        $defaults = [
            'formDinAcao' => 'Pesquisar',
            'formDinPosVScroll' => 30,
            'formDinPosHScroll' => 0,
            'modulo' => 'sisarr/cons_emitir_certidao',
            'formDinAba' => 'aba01',
            'p_num_cpf_cnpj' => '',
            'cad_num_cpf_cnpj' => '',
            'cad_nom_pessoa' => '',
            'cad_end_pessoa' => '',
            'cad_des_bairro' => '',
            'cad_cod_uf' => '',
            'cad_cod_municipio' => '',
            'cad_num_cep' => '',
            'cad_num_caixa_postal' => '',
            'num_pessoa' => '',
            'nom_pessoa' => '',
            'end_pessoa' => '',
            'des_bairro' => '',
            'sig_uf' => '',
            'num_cep' => '',
            'nom_municipio' => '',
            'num_cpf_cnpj_formatado' => '',
            'num_cpf_cnpj' => ''
        ];

        return array_merge($defaults, $data);
    }

    /**
     *  Salva arquivos verificando se o path existe antes
     *
     *  <AUTHOR> Hugo R. <<EMAIL>>
     *  @version 1.0.0
     *  @param string $contents
     */
    private function saveFile($path, $contents, $format = 'pdf')
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        if (empty($contents)) {
            throw new Exception("Falha ao acessar o {$format}", 3);
        }

        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0777);
        }

        if (!@file_put_contents($path, $contents)) {
            throw new Exception("Erro ao gravar {$format}!", 1);
        }
        if (!@chmod($path, 0777)) {
            throw new Exception("Erro ao mudar permissão do {$format}!", 1);
        }
    }
}
