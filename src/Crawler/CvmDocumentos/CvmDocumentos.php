<?php

namespace App\Crawler\CvmDocumentos;

use App\Factory\MongoDB;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Str;
use DateTime;
use Exception;

/**
 *  Clase de consulta da fonte CVM - Consulta de documentos
 *
 *  <AUTHOR> Mesquita 12/08/2019
 *
 *  @version 1.0.0
 *
 */
class CvmDocumentos extends Spider
{
    private const INDEX_MONGODB = 'name';
    private const LIMIT = 50;
    /**
     *  Pesquisa documentos por tipo
     *
     *  <AUTHOR> Mesquita 12/08/2019
     *  <AUTHOR> 09/09/2021 - Mudança para base
     *
     *  @version 1.1.0
     */
    protected function start()
    {
        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('common')
            ->setCollection('cvm_documents');

        $fields = ['nome'];
        $search = [$fields[0] => $this->param['name']];
        $limit = ['$limit' => self::LIMIT];

        $result = json_decode(
            json_encode(
                $manager
                    ->getConnection()
                    ->find($search, $limit)
                    ->toArray(),
                true
            ),
            true
        );

        if (empty($result)) {
            throw new Exception('Nenhum resultado encontrado!', 2);
        }

        return $this->parseDate($result);
    }

    /**
     *  Valida critério
     *
     *  <AUTHOR> Mesquita 12/08/2019
     *  <AUTHOR> Minucelli 09/09/2021 - Mudança para base
     *
     *  @version 1.1.0
     *
     *  @return string
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['name'])) {
            throw new Exception("Parâmetro de busca inválido!", 6);
        }

        $this->param['name'] = strtoupper(Str::removerAcentos($this->param['name']));
    }

    private function parseDate($results)
    {
        foreach ($results as $key => $result) {
            $data[$key] = $result;
            $data[$key]['data'] = ( new DateTime($result['data']))->format('d-m-Y');
        }
        return $data;
    }
}
