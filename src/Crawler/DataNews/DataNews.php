<?php

namespace App\Crawler\DataNews;

use App\Crawler\Spider;
use Exception;
use GuzzleHttp\Client;
use Carbon\Carbon;

class DataNews extends Spider
{
    private const DATA_NEWS_URL = "https://newsdata.io/api/1/archive";

    private $criterio;

    public function start()
    {
        return $this->searchNews();
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['criterio'])) {
            throw new Exception('Parametro inválido', 3);
        }

        $this->criterio = $this->param['criterio'];
    }

    private function searchNews()
    {
        $fromDate = Carbon::now()->subDays(177)->toDateString();
        $client = new Client();
        $response = $client->request('GET', self::DATA_NEWS_URL, [
            "query" => [
                "apikey" => DATA_NEWS_API_KEY,
                "q" => "'" . $this->criterio . "'",
                "country" => "br",
                "from_date" => $fromDate,
            ]
        ])->getBody()->getContents();
        $response = json_decode($response, true);
        return $response['results'];
    }
}
