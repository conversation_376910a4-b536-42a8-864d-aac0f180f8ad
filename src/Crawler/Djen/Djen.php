<?php

namespace App\Crawler\Djen;

use App\Crawler\Spider;
use App\Helper\Str;
use App\Helper\Document;
use App\Helper\Client;
use App\Helper\Date;
use Exception;

class Djen extends Spider
{
    private const URL = 'https://comunicaapi.pje.jus.br/api/v1/comunicacao?pagina=1&itensPorPagina=';

    private $client;

    public function start()
    {
        $response = $this->getResponseFromSite();
        return $this->parseResponse($response->items);
    }

    public function validateAndSetCrawlerAttributes()
    {
        if ($this->param['nome'] == '') {
            throw new Exception("Parametro inválido", 2);
        }
    }

    private function getResponseFromSite()
    {
        $this->setProxy();

        $this->configAlternativeClient();


        $limit = $this->param['limit'];
        if ($limit == 0 || $limit == null) {
            $limit = '15';
        }

        $url = self::URL . $limit . '&nomeParte=' . $this->param['nome'];

        $this->client->request('GET', $url);
        $response = $this->client->getResponse();
        $data = $response->getContent();
        $data = json_decode($data);
        if ($data->count == 0) {
            throw new Exception('Nenhum registro encontrado', 2);
        }

        return $data;
    }

    private function parseResponse($response)
    {
        foreach ($response as $key => $value) {
            foreach ($response[$key]->destinatarios as $chave => $pessoaDado) {
                $destinatario[] = [
                    'nome' => $pessoaDado->nome
                ];
            }
            foreach ($response[$key]->destinatarioadvogados as $indice => $advogados) {
                $advogado[] = [
                    'nomeAdvogado' => $advogados->advogado->nome,
                    'numeroOab' => $advogados->advogado->numero_oab,
                    'ufOab' => $advogados->advogado->uf_oab
                ];
            }

            $data[] = [
                'data' => $response[$key]->datadisponibilizacao,
                'sigla' => $response[$key]->siglaTribunal,
                'tipoComunicacao' => $response[$key]->tipoComunicacao,
                'nomeOrgao' => $response[$key]->nomeOrgao,
                'texto' => $response[$key]->texto,
                'numero_processo' => $response[$key]->numero_processo,
                'link' => $response[$key]->link,
                'tipoDocumento' => $response[$key]->tipoDocumento,
                'nomeClasse' => $response[$key]->nomeClasse,
                'meiocompleto' => $response[$key]->meiocompleto,
                'numeroprocessocommascara' => $response[$key]->numeroprocessocommascara,
                'destinatarios' => $destinatario,
                'destinatarioadvogados' => $advogado
            ];

            foreach ($destinatario as $pessoa) {
                array_shift($destinatario);
            }

            foreach ($advogado as $adv) {
                array_shift($advogado);
            }
        }

        return $data;
    }

    private function configAlternativeClient()
    {
        $this->client = (new Client())->createGoutteClient((new Client())->createHttpClient([], 'curl'));
    }
}
