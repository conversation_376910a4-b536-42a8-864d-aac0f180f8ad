<?php

namespace App\Crawler\CommonPepBtg;

use App\Crawler\Spider;
use App\Factory\MongoDB;
use App\Helper\Document;
use Exception;

class CommonPepBtg extends Spider
{
    private const INDEX_MONGODB = 'name_document';
    private const LIMIT = 50;

    public function start()
    {
        return $this->searchByNameOrCpf();
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->param['cpf_nome'] = trim($this->param['cpf_nome']);

        if (empty($this->param['cpf_nome'])) {
            throw new Exception('Parâmetro inválido', 1);
        }
        if (Document::validarCpf($this->param['cpf_nome'])) {
            $this->param['cpf_nome'] = Document::removeMask($this->param['cpf_nome']);
        }
    }

    private function searchByNameOrCpf()
    {
        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('common')
            ->setCollection('pep_btg');
        $fields = ['nome','cpf'];
        $results = $manager
                    ->atlasSearch(
                        $this->param['cpf_nome'],
                        self::INDEX_MONGODB,
                        self::LIMIT,
                        $fields
                    );

        return $this->parseResults($results);
    }

    private function parseResults($results)
    {
        $parsedResults = [];
        foreach ($results as $result) {
            $parsedResults[] = [
                'cpf' => $result['cpf'],
                'nome' => $result['nome'],
                'sigla_funcao' => $result['sigla_funcao'],
                'descricao_funcao' => $result['descricao_funcao'],
                'nivel_funcao' => $result['nivel_funcao'],
                'nome_orgao' => $result['nome_orgao'],
                'dt_inicio_exercicio' => $result['dt_inicio_exercicio'],
                'dt_fim_exercicio' => $result['dt_fim_exercicio'],
                'dt_final_carencia' => $result['dt_final_carencia'],
            ];
        }

        if (empty($parsedResults)) {
            throw new Exception('Nada encontrado!', 2);
        }

        return $parsedResults;
    }
}
