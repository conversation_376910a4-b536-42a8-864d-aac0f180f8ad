<?php

namespace App\Crawler\BigDataCorpPerfilSocial;

use Exception;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\BigDataCorpManager;
use App\Crawler\BigDataCorpPerfilSocial\Models\BigDataCorpPerfilSocialModel;
use App\Crawler\BigDataCorpPerfilSocial\Models\BigDataCorpSocioDemograficosModel;
use App\Crawler\BigDataCorpPerfilSocial\Models\BigDataCorpDadosProfissionaisModel;

class BigDataCorpPerfilSocial extends Spider
{
    private $allDatasets;
    private $allData;

    protected function validateAndSetCrawlerAttributes()
    {
        if (isset($this->param['documento']) && Document::validarCpf($this->param['documento'])) {
            return true;
        }

        throw new Exception('Parâmetro inválido');
    }

    protected function start()
    {
        $this->config();

        $this->getAll();
        $dadosSocioDemograficos = $this->getDadosSocioDemograficos();
        if (empty($dadosSocioDemograficos)) {
            throw new Exception("Nenhum resultado encontrado!", 2);
        }

        $dadosProfissionais = $this->getDadosProfissionais();
        if (empty($dadosProfissionais)) {
            throw new Exception("Nenhum resultado encontrado!", 2);
        }

        $results = [];

        foreach ($dadosProfissionais as $key => $v) {
            $results[] = new BigDataCorpPerfilSocialModel($dadosSocioDemograficos[$key], $dadosProfissionais[$key]);
        }

        return $results;
    }

    private function config()
    {
        $documento = $this->param['documento'];
        $this->allDatasets =  'occupation_data,demographic_data';
    }

    private function getAll()
    {
        $this->allData = $this->getDados($this->allDatasets);
    }

    public function getDadosSocioDemograficos()
    {
        $results = [];
        foreach ($this->allData->Result as $value) {
            foreach ($value->DemographicData as $demographicDAta) {
                $results[] = new BigDataCorpSocioDemograficosModel($demographicDAta);
            }
        }

        return $results;
    }

    public function getDadosProfissionais()
    {
        $results = [];
        foreach ($this->allData->Result as $Result) {
            foreach ($Result->ProfessionData->Professions as $value) {
                $results[] = new BigDataCorpDadosProfissionaisModel($value);
            }
        }

        return $results;
    }

    private function getDados($dataset)
    {
        $bigDataManager = new BigDataCorpManager($this->idUser);
        $class = 'BigDataCorpPerfilSocial';
        $result = $bigDataManager->getDataPersonApi($dataset, $this->param['documento'], $class);
        $data = json_decode($result);
        return $data;
    }
}
