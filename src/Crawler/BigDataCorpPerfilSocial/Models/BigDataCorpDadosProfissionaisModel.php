<?php

namespace App\Crawler\BigDataCorpPerfilSocial\Models;

class BigDataCorpDadosProfissionaisModel
{
    public $Sector;
    public $Country;
    public $CompanyIdNumber;
    public $CompanyName;
    public $Area;
    public $Level;
    public $Status;
    public $IncomeRange;
    public $Income;
    public $StartDate;
    public $EndDate;
    public $CreationDate;
    public $LastUpdateDate;

    public function __construct($dados)
    {
        foreach (array_keys(get_object_vars($this)) as $att) {
            $this->{$att} = !empty($dados->{$att}) ? $dados->{$att} : null;
        }
    }
}
