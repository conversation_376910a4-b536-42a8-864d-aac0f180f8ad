<?php

namespace App\Crawler\BigDataCorpPerfilSocial\Models;

class BigDataCorpPerfilSocialModel
{
    public $CLASSE_SOCIAL;
    public $RENDA_PRESUMIDA;
    public $ESCOLARIDADE;
    public $OCUPACAO;
    public $ESTADO_CIVIL;
    public $CLUSTER_UNITFOUR;
    public $SUSPEITA_OBITO;
    public $POSSUI_VEICULOS;

    public function __construct($dadosDemograficos, $dadosProfissionais)
    {
        $this->CLASSE_SOCIAL = $dadosDemograficos->SocialClass;
        $this->ESCOLARIDADE = $dadosDemograficos->EstimatedInstructionLevel;
        $this->RENDA_PRESUMIDA = $dadosProfissionais->Income;
        $this->OCUPACAO = $dadosProfissionais->Level;
    }
}
