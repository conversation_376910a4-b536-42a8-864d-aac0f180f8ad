<?php

namespace App\Crawler\BigDataCorpPerfilSocial\Models;

class BigDataCorpSocioDemograficosModel
{
    public $DataOrigin;
    public $DataAgregationLevel;
    public $DataCountry;
    public $EstimatedIncomeRange;
    public $EstimatedInstructionLevel;
    public $SocialClass;
    public $CreationDate;
    public $LastUpdateDate;

    public function __construct($dados)
    {
        foreach (array_keys(get_object_vars($this)) as $att) {
            $this->{$att} = !empty($dados->{$att}) ? $dados->{$att} : null;
        }
    }
}
