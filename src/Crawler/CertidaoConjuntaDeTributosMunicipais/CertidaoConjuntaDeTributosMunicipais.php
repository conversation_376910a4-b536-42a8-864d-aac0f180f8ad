<?php

namespace App\Crawler\CertidaoConjuntaDeTributosMunicipais;

use Exception;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\InfoSimplesManager;

class CertidaoConjuntaDeTributosMunicipais extends Spider
{
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const CERTIDAOCONJUNTA_S3_PATH = 'captura/certidao_conjunta_tributos_municipais/';

    protected function start()
    {
        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::CERTIDAOCONJUNTA_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;


        $manager = new InfoSimplesManager();

        $data = $manager->searchCertidaoTributariaMobiliaria(
            $this->param['documento']
        );

        if (!$data['data']['conseguiu_emitir_certidao_negativa']) {
            return $this->parseFailedIssue($data);
        }

        $pdf = $this->getResponse($data['receipt']['sites_urls'][0]);

        $responsePdf = $this->savePdfAndReturnText($pdf);
        $result = $this->parseResult($responsePdf);

        return $result;
    }

    protected function validateAndSetCrawlerAttributes()
    {
        $this->param['documento'] = preg_replace('/[^0-9]/isu', '', $this->param['documento']);

        if (!Document::validarCpfOuCnpj($this->param['documento'])) {
            throw new Exception("Critério de busca inválido.", 6);
        }
    }


    protected function parseResult($text)
    {
        $textDelimiter = str_replace("\n", 'delimiter', $text);
        $textDelimiter = Str::encoding($textDelimiter);
        $textEncoding =  Str::encoding($text);
        preg_match('/Contribuinte:\s*(.*?)\n/m', $text, $contribuinte);
        preg_match('/Liberação:\s*(.*?)\n/m', $text, $liberacao);
        preg_match('/Validade:\s*(.*?)\n/m', $text, $validade);
        preg_match('/Tributos Abrangidos:(.*?)delimiterdelimiterdelimiterdelimiterU/m', $textDelimiter, $tributos);
        preg_match('/Unidades Tributárias: (.*?)\sRessalvado/m', $textEncoding, $unidadesTributarias);
        preg_match('/por esta certidão, até a presente data é: (.*?)\. /m', $textEncoding, $situacao);
        $unidadesTributarias = str_replace(') ', '), ', $unidadesTributarias[1]);
        $tributos = str_replace("delimiter", ",", $tributos[1]);

        $dados = [
            'certidao_negativa' => true,
            'contribuinte' => $contribuinte[1],
            'liberacao' => $liberacao[1],
            'validade' => $validade[1],
            'situacao' => $situacao[1],
            'tributos' => $tributos,
            'unidadesTributarias' => $unidadesTributarias,
            'pdf' => $this->pdf
        ];
        return $dados;
    }

    protected function savePdfAndReturnText($pdf)
    {
        file_put_contents($this->certificateLocalPath, $pdf);

        $text = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'layout',
            'nopgbrk'
        ]);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        $this->pdf = $this->certificateUrl;
        return $text;
    }

    protected function parseFailedIssue($data)
    {
        (new Pdf())->saveHtmlToPdf(
            $this->getResponse($data['receipt']['sites_urls'][0]),
            $this->certificateLocalPath
        );

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);

        $dados = [
            'certidao_negativa' => false,
            'mensagem' => $data['data']['mensagem'],
            'pdf' => $this->certificateUrl
        ];

        return $dados;
    }
}
