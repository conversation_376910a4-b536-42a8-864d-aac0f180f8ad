<?php

namespace App\Crawler\CertidaoCeatTrtCampinasRegiao;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Manager\S3\S3;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Helper\Util;
use Exception;

class CertidaoCeatTrtCampinasRegiao extends Spider
{
    private const BASE_URL = "https://ceat.trt15.jus.br";
    private const REQUEST_URL = "/ceat/certidaoAction.seam";
    private const PATH_STATIC_S3 = S3_STATIC_URL;
    private const PATH_CERTIDAO_S3 = 'captura/certidao_ceat_trt15_campinas_regiao/';

    private $documento;
    private $nome;
    private $tipoPesquisa;
    private $processosArquivados = "off";
    private $headers = ['User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0'];


    public function start()
    {
        $data = $this->makeRequest();
        $file = $this->makePdf($data);
        $pdf = $this->savePdfAndGetText($file);
        $result = $this->parseText($pdf['textPdf']);

        return [
            'pdf' => $pdf['urlPdf'],
            'info' => $result
        ];
    }

    /**
     * @throws Exception
     */
    private function makeRequest()
    {
        if ($this->debug) {
            print(__METHOD__ . PHP_EOL);
        }

        $retry = 10;
        do {
            $html = $this->getResponse(self::BASE_URL . self::REQUEST_URL, 'GET', [], $this->headers);

            preg_match('/ViewState"\svalue="([\s\S]*?)"/', $html, $viewState);
            preg_match('/<img\ssrc="(.*?)"/', $html, $captcha);

            $params = [
                "certidaoActionForm" => "certidaoActionForm",
                "certidaoActionForm:decTipoPesquisa:radiotipopesquisa" => $this->tipoPesquisa,
                "certidaoActionForm:j_id23:doctoPesquisa" => $this->documento,
                "certidaoActionForm:j_id33:nomePesquisa" => $this->nome,
                "certidaoActionForm:j_id42:j_id46" => $this->processosArquivados,
                "certidaoActionForm:j_id51:verifyCaptcha" => $this->validateCaptcha($captcha[1]),
                "certidaoActionForm:certidaoActionEmitir" => "Emitir Certidão",
                "javax.faces.ViewState" => $viewState[1]
            ];

            $data = $this->getResponse(self::BASE_URL . self::REQUEST_URL, "POST", $params, $this->headers);

            preg_match('/Certid&atilde;o\sgerada\scom\so\sc&oacute/', $data, $result);
            if (!empty($result)) {
                return $data;
            }
            $retry--;
        } while ($retry > 0);
        throw new Exception('Não foi possível capturar as informações da página.', 6);
    }

    /** Função que faz requisição e retorna o arquivo pdf
     * @param $data
     * @return mixed
     * @throws Exception
     */
    private function makePdf($data)
    {
        preg_match('/ViewState"\svalue="([\s\S]*?)"/', $data, $viewState);
        $params = [
            "certidaoActionForm" => "certidaoActionForm",
            "certidaoActionForm:certidaoActionImprimir" => "Imprimir Certidão",
            "javax.faces.ViewState" => $viewState[1],
        ];

        return $this->getResponse(self::BASE_URL . self::REQUEST_URL, 'POST', $params, $this->headers);
    }

    /** Salva o pdf e pega o seu conteúdo
     * @param $file
     * @return array
     * @throws Exception
     */
    private function savePdfAndGetText($file)
    {
        $uniqid = md5(uniqid(rand(), true));
        $certificateName = "$uniqid.pdf";
        $certificateLocalPath = "/tmp/{$certificateName}";
        $certificateS3Path = self::PATH_CERTIDAO_S3 . $certificateName;
        $certificateUrl = self::PATH_STATIC_S3 . $certificateS3Path;

        file_put_contents($certificateLocalPath, $file);

        $text = (new Pdf())->getTextFromPdf($certificateLocalPath, ['layout', 'nopgbrk']);

        (new S3(new StaticUplexisBucket()))->save($certificateS3Path, $certificateLocalPath);

        return ["urlPdf" => $certificateUrl, "textPdf" => $text];
    }

    /** Faz o parse do texto
     * @param $text
     * @return array
     * @throws Exception
     */
    private function parseText($text)
    {
        $patterns = [
            'numCertidao' => ['@Certid.o\sN.\s([\s\S]*?)\\n@u'],
            'situacao' => ['@(CERTIFICA-SE[\s\S]*?)+\sA\sconfer.ncia\sdos\sdados@u'],
            'descricao' => ['@((O\sandamento|A\sconfer.ncia)[\s\S]*?\(30\sdias\)\.)@u'],
            'descricao2' => ['@(Os\sdados[\s\S]*?Resolu..o[\s\S]*?\.)@u'],
            'codVerificador' => ['@C.digo\sverificador:\s([\s\S]*?)\\n@u'],
            'validade' => ['@v.lida\sat.:\s([\s\S]*?)\\n@u'],
            'dataEmissao' => ['@Certid.o\semitida\sem(.*).@u'],
        ];
        $data = Util::parseDados($patterns, $text);
        $data = array_map("utf8_decode", $data);
        $descricao = preg_replace('/\\n\n/', '', $data['descricao']);
        $descricao2 = preg_replace('/\\n\n/', '', $data['descricao2']);

        $data['descricao'] = $this->removeHeaders($descricao);
        $data['descricao2'] = $this->removeHeaders($descricao2);

        if (preg_match('/foram\sencontrados/', $text)) {
            preg_match('@(CERTIFICA-SE[\s\S]*?\s+seguintes\sprocessos.*)@u', $text, $situacao);
            preg_match('@\n(CPF|CNPJ).*(\\n[\s\S]*)\s+O\sandamento\sprocessual@', $text, $processos);
            $data['situacao'] = $this->removeHeaders($situacao[1]);
            $data['processos'] = $this->removeHeaders($processos[2]);
        }

        return $data;
    }

    /** Função que remove headers do texto.
     * @param $text
     * @return array|string|string[]|null
     */
    private function removeHeaders($text)
    {
        $patterns = array(
            '/\s+PODER\sJUDICIÁRIO\sFEDERAL/u',
            '/\s+JUSTIÇA\sDO\sTRABALHO/u',
            '/\s+TRIBUNAL\sREGIONAL\sDO\sTRABALHO\sDA\s15.\sREGIÃO/u',
            '/\s+Pág.\s\d+\sde\s\d+/u'
        );
        return preg_replace($patterns, '', $text);
    }


    /** Valida o captcha
     * @param $url
     * @return mixed
     */
    private function validateCaptcha($url)
    {
        if ($this->debug) {
            print(__METHOD__ . PHP_EOL);
        }

        $this->getImageAndBreakCaptcha(self::BASE_URL . $url, $this->headers);

        return $this->captcha;
    }


    public function validateAndSetCrawlerAttributes()
    {
        $this->documento = trim($this->param['documento']);

        if ($this->param['processos_arquivados'] == "S") {
            $this->processosArquivados = 'on';
        }

        if (Document::validarCpfOuCnpj($this->documento)) {
            $this->documento = Document::formatCpfOrCnpj($this->documento);
            $this->tipoPesquisa = 'D';
        } elseif (preg_match("/^[a-zA-Z]/", $this->documento)) {
            $this->nome = $this->documento;
            $this->documento = '';
            $this->tipoPesquisa = 'N';
        } else {
            throw new Exception('Parâmetro Inválido', 6);
        }
    }
}
