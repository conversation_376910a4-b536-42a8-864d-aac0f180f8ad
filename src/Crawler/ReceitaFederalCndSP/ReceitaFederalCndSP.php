<?php

namespace App\Crawler\ReceitaFederalCndSP;

use Exception;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Helper\Util;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\Bucket\StaticUplexisBucket;

/**
 *  Classe da fonte (200)- Receita Federal CND SP
 *
 *  <AUTHOR> Mesquita - 15/10/2019
 */
class ReceitaFederalCndSP extends Spider
{
    private const BASE_URL = 'https://www10.fazenda.sp.gov.br/CertidaoNegativaDeb/';
    private const MAIN_URL = 'Pages/EmissaoCertidaoNegativa.aspx';
    private const CAPTCHA_URL = 'Container/Captcha.ashx?';
    private const GENERATE_CERTIFICATE = 'Pages/ImpressaoCertidaoNegativa.aspx';

    private const TYPE_CNPJ = 1;
    private const TYPE_CPF = 2;
    private const TYPE_POST = 1;
    private const TYPE_PDF = 2;

    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const MFP_S3_PATH = 'captura/receita_federal_cnd_sp/';

    private $certificateName;
    private $certificateLocalPath;
    private $certificateS3Path;
    private $certificateUrl;
    private $pdf;

    protected $captcha = '';

    /**
     *  Valida e retorna os parametros
     *
     *  <AUTHOR> Mesquita - 15/10/2019
     *
     *  @version 1.0.0
     *
     *  @return array
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (!Document::validarCpfOuCnpj($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }

        $this->document = preg_replace('#\D#isu', '', $this->param['cpf_cnpj']);

        $this->type = (Document::validarCpf($this->param['cpf_cnpj'])) ? self::TYPE_CPF : self::TYPE_CNPJ;
    }

    /**
     *  Iniciar emissão da certidão
     *
     *  <AUTHOR> Mesquita - 15/10/2019
     *
     *  @version 1.0.0
     *
     *  @return array
     */
    protected function start()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::MFP_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;

        $this->setAlternativeProxy();

        $firstHtml = $this->getResponse(self::BASE_URL . self::MAIN_URL);

        $this->resolveCaptcha($firstHtml);

        $postHtml = $this->postDocument($firstHtml);

        $parseText = $this->parseAndGetHml($postHtml);

        return $this->parseData($parseText);
    }

    protected function addExtraDataToResponse($response)
    {
        if ($this->pdf) {
            $response["pdf"] = $this->pdf;
        }

        return $response;
    }

    /**
     *  Monta regras para saber qual o último html que deve ir para o parse
     *
     *  <AUTHOR> Mesquita - 15/10/2019
     *
     *  @version 1.0.0
     *
     *  @return string
     *
     */
    private function parseAndGetHml($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (preg_match('/N..o.foi.poss..vel.emitir.a.certid..o/i', $html)) {
            $this->saveHtmlToPdf($html);
            return $html;
        }

        $this->checkResult($html);

        $responsePdf = $this->postPdf($html);
        return $this->savePdfAndReturnText($responsePdf);
    }

    /**
     *  Checar resultado
     *
     *  <AUTHOR> Mesquita - 15/10/2019
     *
     *  @version 1.0.0
     *
     */
    private function checkResult($htmlResult)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (preg_match('/Escreva.os.caracteres.da.imagem.no.campo.ao.lado/i', $htmlResult)) {
            throw new Exception('Erro ao recuperar o conteúdo da certidão, captcha pode não ter sido resolvido', 3);
        }
    }

    /**
     *  Resolve o captcha
     *
     *  <AUTHOR> Mesquita - 15/10/2019
     *
     *  @version 1.0.0
     *
     */
    private function resolveCaptcha($firstHtml)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $pattern = '/var\s+your_site_key.*?=.*?(\'|")(.*?)\1/m';

        preg_match($pattern, $firstHtml, $key);

        if (empty($key)) {
            throw new Exception("Não foi possível recuperar a key do captcha", 3);
        }

        $url = self::BASE_URL . self::MAIN_URL;

        $this->captcha = $this->solveReCaptcha($key[2], $url);

        if (empty($this->captcha)) {
            throw new Exception("Não foi possível resolver o captcha", 3);
        }
    }


    /**
     *  Envia post de emissão da certidão
     *
     *  <AUTHOR> Mesquita - 15/10/2019
     *
     *  @version 1.0.0
     *
     *  @return string
     */
    private function postDocument($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        return $this->getResponse(
            self::BASE_URL . self::MAIN_URL,
            'POST',
            $this->getParams($html, self::TYPE_POST)
        );
    }

    /**
     *  Envia o post para pegar o PDF
     *
     *  <AUTHOR> Mesquita - 15/10/2019
     *
     *  @version 1.0.0
     *
     *  @return string
     */
    private function postPdf($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }
        return $this->getResponse(
            self::BASE_URL . self::GENERATE_CERTIFICATE,
            'POST',
            $this->getParams($html, self::TYPE_PDF)
        );
    }


    /**
     *  Salva e retorna o texto do PDF
     *
     *  <AUTHOR> Mesquita - 15/10/2019
     *
     *  @version 1.0.0
     *
     *  @return string
     */
    private function savePdfAndReturnText($pdfResponse)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        file_put_contents($this->certificateLocalPath, $pdfResponse);
        $text = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'layout',
            'nopgbrk'
        ]);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        $this->pdf = $this->certificateUrl;
        return utf8_decode($text);
    }

    /**
     *  Converter HTML para PDF e salvar no S3
     *
     *  <AUTHOR> Mesquita - 11/10/2019
     *
     *  @version 1.0.0
     */
    private function saveHtmlToPdf($response)
    {
        $response = Str::removerAcentos(Str::encoding($response));
        (new Pdf())->saveHtmlToPdf($response, $this->certificateLocalPath);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);

        $this->pdf = $this->certificateUrl;
    }

    /**
     *  Monta os parametros utilizados no POST
     *
     *  <AUTHOR> Mesquita - 15/10/2019
     *
     *  @version 1.0.0
     *
     *  @return array
     */
    private function getParams($html, $postType)
    {
        $paramsPost = $this->getValuesParamsPostFromHtml($html);

        $result = [
            '__EVENTTARGET' =>    '',
            '__EVENTARGUMENT' => '',
            '__VIEWSTATE' => $paramsPost['viewState'],
            '__VIEWSTATEGENERATOR' => $paramsPost['viewStateGenerator'],
            '__EVENTVALIDATION' => $paramsPost['eventValidation']
        ];

        if ($postType == self::TYPE_POST) {
            $result['ctl00$MainContent$grupoDocumento'] = 'cpfradio';
            $result['ctl00$MainContent$txtDocumento'] = $this->document;
            $result['ctl00$MainContent$hdgrecaptcha'] = $this->captcha;
            $result['g-recaptcha-response'] = $this->captcha;
            $result['ctl00$MainContent$btnPesquisar'] = '+Emitir+';

            if ($this->type == self::TYPE_CNPJ) {
                $result['ctl00$MainContent$grupoDocumento'] = 'cnpjradio';
            }
        }

        if ($postType == self::TYPE_PDF) {
            $result['__EVENTTARGET'] = 'ctl00$MainContent$btnImpressao';
        }

        return $result;
    }

    /**
     *  Recupera valores contidos no html para os parametros utilizados no post
     *
     *  <AUTHOR> Mesquita - 15/10/2019
     *
     *  @version 1.0.0
     *
     *  @return array
     */
    private function getValuesParamsPostFromHtml($html)
    {
        $result = [
            'viewState' => '',
            'viewStateGenerator' => '',
            'eventValidation' => ''
        ];

        $matches = [];

        if (!preg_match('/<input[^>]*?__VIEWSTATEGENERATOR[^>]*?value="([^>]*)"./i', $html, $matches)) {
            throw new Exception('ViewStateGenerator não encontrado no html', 3);
        }

        $result['viewStateGenerator'] = trim($matches[1]);

        if (!preg_match('/<input[^>]*?__EVENTVALIDATION[^>]*?value="([^>]*)"./i', $html, $matches)) {
            throw new Exception('EventValidator não encontrado no html', 3);
        }

        $result['eventValidation'] = trim($matches[1]);

        if (!preg_match('/<input[^>]*?__VIEWSTATE[^>]*?value="([^>]*)"./i', $html, $matches)) {
            throw new Exception('ViewsState não encontrado no html', 3);
        }

        $result['viewState'] = trim($matches[1]);

        return $result;
    }


    /**
     *  Parse dos dados
     *
     *  <AUTHOR> Mesquita - 15/10/2019
     *
     *  @version 1.0.0
     *
     *  @return array
     */
    private function parseData($text)
    {
        $arrPatterns = [
            'documento' => ['/[CNPJ|IE|cpf]:\s([^>]*?)\s?Ressalvado/i', null],
            'conteudo' => ['/(Ressalvado[^>]*)Certid.o\sn.\s/i', null],
            'certidao' => ['/Certid.o.n..([^>]*?)Data/i', null],
            'data_emissao' => ['/data.e.hora.da.emiss.o.([^>]*?)\svalidade/i', null],
            'validade' => ['/validade.([^>]*),/i', null]
        ];


        if (preg_match('/N..o.foi.poss..vel.emitir.a.certid..o/i', $text)) {
            $text = utf8_decode(
                strip_tags(
                    $this->cleanText($text)
                )
            );
            $arrPatterns['conteudo'] = ['/(n.o.foi.poss.vel.*?correio.eletr.nico\.)/i', null];
            $arrPatterns['certidao'] = [];
        }

        $result = Util::parseDados($arrPatterns, $text);
        $result['conteudo'] = $this->cleanText($result['conteudo']);
        $result['pdf'] = $this->certificateUrl;
        return $result;
    }

    /**
     *  Limpa quebras da string
     *
     *  @param string $text
     *
     *  <AUTHOR> Mesquita - 15/10/2019
     *
     *  @version 1.0.0
     *
     *  @return string
     */
    public static function cleanText($text)
    {
        $text = preg_replace('/\n/', " ", $text);
        $text = preg_replace('/\s{2,}/', " ", $text);
        $text = str_replace('&nbsp;', '', $text);
        return preg_replace('/\t/', " ", $text);
    }
}
