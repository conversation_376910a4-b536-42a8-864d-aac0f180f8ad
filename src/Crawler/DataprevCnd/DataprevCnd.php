<?php

namespace App\Crawler\DataprevCnd;

use Exception;
use Carbon\Carbon;
use App\Helper\Pdf;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\Bucket\StaticUplexisBucket;

/**
 * Classe de da fonte Dataprev CND
 *
 * <AUTHOR>
 * <AUTHOR> - 29/05/2018 - Correção das URLs de consulta
 */
class DataprevCnd extends Spider
{
    private const HOME_URL = 'http://cnd.dataprev.gov.br/cws/contexto/cnd/cnd.html';
    private const MAIN_URL = 'http://cnd.dataprev.gov.br/CWS/BIN/cws_mv2.asp?CONTEXTO/CND/ACNT1004';
    private const ENTRIES_URL = 'http://cnd.dataprev.gov.br/CWS/BIN/cws_mv2.asp';
    private const PDF_URL = 'http://cnd.dataprev.gov.br/' .
        'CWS/BIN/cws_mv2.asp?COMS_BIN/SIW_Contexto=CND/SIW_Transacao_Web=CONSULTA2/<CERTIDAO>';

    private const SERVICO_INDISPONIVEL_REGEX = '/servi.o\s*indispon.vel\s*temporariamente/isu';
    private const ENTRIES_REGEX = '/detalhe' .
        '\("\s*(.*?)\s*","\s*(.*?)\s*","\s*(.*?)\s*","\s*(.*?)\s*","\s*(.*?)\s*","\s*(.*?)\s*","\s*(.*?)\s*"/isu';

    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const DATAPREV_S3_PATH = 'captura/dataprev_cnd/';

    private $cnpj = '';
    private $certificateName = '';
    private $certificateLocalPath = '';
    private $certificateS3Path = '';
    private $certificateUrl = '';
    private $pdf = null;

    /**
     * Valida e retorna os parâmetros
     *
     * @version 1.0.0
     * <AUTHOR> Favoreto Makhoul - 04/12/2018
     *
     * @return string
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCnpj($this->param['cnpj'])) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }

        $this->cnpj = preg_replace('/\D/', '', $this->param['cnpj']);
    }

    /**
     * Busca os dados da DataprevCnd
     *
     * @version 1.0.0
     * <AUTHOR> Favoreto Makhoul - 04/12/2018
     *
     * @return array
     */
    protected function start()
    {
        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::DATAPREV_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;

        $mainPageHtml = $this->getMainPage();

        $this->checkMainPageError($mainPageHtml);

        $entriesHtml = $this->getEntriesHtml($this->cnpj);

        $response = $this->parseEntriesHtmlToResponse($entriesHtml);

        return $response;
    }
    /**
     * Converte um html para um pdf
     *
     * @version 1.0.0
     * <AUTHOR> Favoreto Makhoul - 04/12/2018
     *
     * @param string $html
     *
     * @return void
     */
    private function htmlToPdf($html)
    {
        (new Pdf())->saveHtmlToPdf($html, $this->certificateLocalPath);

        if (!file_exists($this->certificateLocalPath)) {
            throw new Exception('Falha ao salvar o PDF', 3);
        }
    }
    /**
     * Busca o PDF da Certidão
     *
     * @version 1.0.0
     * <AUTHOR> Favoreto Makhoul - 04/12/2018
     *
     * @param string $certidao
     *
     * @return string
     */
    private function getPdfByCertidao($certidao)
    {
        $certidao = preg_replace('/\D/isu', '', $certidao);
        $url = str_replace('<CERTIDAO>', $certidao, self::PDF_URL);
        $response = $this->getResponse($url, 'GET', [], [
            'Referer' => self::ENTRIES_URL
        ]);
        $this->htmlToPdf($response);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        return $this->certificateUrl;
    }

    /**
     * Parseia o HTML das certidões para o retorno
     *
     * @version 1.0.0
     * <AUTHOR> Favoreto Makhoul - 04/12/2018
     *
     * @param string $entriesHtml
     *
     * @return array
     */
    private function parseEntriesHtmlToResponse($entriesHtml)
    {
        preg_match(self::ENTRIES_REGEX, $entriesHtml, $matches);
        if (empty($matches)) {
            $this->htmlToPdf($entriesHtml);
            (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
            throw new Exception('Não há certidões para o documento informado', 2);
        }
        $matches;
        if ($matches[1] !== '-000000') {
            $certidao = $matches[1];
        } else {
            $certidao = preg_replace('/\D/isu', '', $matches[4]) . '-' . preg_replace('/\D/isu', '', $matches[5]);
        }

        $dataValidade = Carbon::createFromFormat('d/m/Y', $matches[2])
            ->addMonth(3)
            ->format('d/m/Y');

        $pdf = $this->getPdfByCertidao($certidao);

        $response = [
            'certidao' => $certidao,
            'dataEmissao' => $matches[2],
            'fin' => $matches[3],
            'dataValidade' => $dataValidade,
            'dataCancelamento' => $matches[6],
            'horaBrasilia' => $matches[7],
            'pdf' => $pdf
        ];

        return $response;
    }
    /**
     * Busca o HTML das certidões
     *
     * @version 1.0.0
     * <AUTHOR> Favoreto Makhoul - 04/12/2018
     *
     * @param string $cnpj
     *
     * @return string
     */
    private function getEntriesHtml($cnpj)
    {
        $response = $this->getResponse(self::ENTRIES_URL, 'POST', [
            'tipo' => '1',
            'num' => $cnpj,
            'SIW_Contexto' => 'CND',
            'SIW_Transacao_Web' => 'LISTA',
            'SIW_Layout' => '1,14'
        ], [
            'Referer' => self::MAIN_URL
        ]);
        return utf8_encode($response);
    }

    private function getMainPage()
    {
        $response = $this->getResponse(self::MAIN_URL, 'GET', [], [
            'Referer' => self::HOME_URL
        ]);
        return $response;
    }
    /**
     * Verifica se o serviço está indisponivel
     *
     * @version 1.0.0
     * <AUTHOR> Favoreto Makhoul - 04/12/2018
     *
     * @param string $mainPageHtml
     *
     * @return void
     */
    private function checkMainPageError($mainPageHtml)
    {
        if (preg_match(self::SERVICO_INDISPONIVEL_REGEX, $mainPageHtml)) {
            throw new Exception("Site indisponível", 3);
        }
    }
}
