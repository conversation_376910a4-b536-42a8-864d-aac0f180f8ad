<?php

namespace App\Crawler\CvmFundo;

use Exception;
use App\Helper\Str;
use App\Helper\Pdf;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\Bucket\StaticUplexisBucket;

class CvmFundo extends Spider
{
    private $url;
    private $baseUrl = 'https://cvmweb.cvm.gov.br';

    private $try = 10;
    private $urlCaptcha = null;

    private $cnpj = '';

    private $result = [];

    private $form = [];

    protected function start(): array
    {
        $this->config();

        $this->form();

        // Removido por não estar usando captcha neste momento
        //$this->setCaptcha();

        $this->startCrawler();

        return $this->result;
    }

    private function form(): bool
    {
        if ($this->debug) {
            print(__METHOD__ . PHP_EOL);
        }

        $result = $this->getResponse($this->url, 'GET', [], ['Referer' => $this->url]);

        try {
            # recupera VIEWSTATE
            $this->form['viewstate'] = $this->getViewState($result);
            if ($this->form['viewstate'] === false) {
                return false;
            }
            # recupera __EVENTVALIDATION
            $this->form['eventvalidation'] = $this->getEventValidation($result);
            if ($this->form['eventvalidation'] === false) {
                return false;
            }

            # recupera __EVENTVALIDATION
            $this->form['viewStateGenerator'] = $this->getViewStateGenerator($result);
            if ($this->form['viewStateGenerator'] === false) {
                return false;
            }

            // Removido por não estar usando captcha neste momento
            // if (preg_match('#<img\s*src=["\']\.+/(.*?)["\']>\s*<br>#is', $result, $match)) {
            //     $this->urlCaptcha = $match[1];
            // } else {
            //     throw new Exception('Erro ao identificar url do Captcha');
            // }
        } catch (Exception $e) {
            throw $e;
        }

        return true;
    }

    private function startCrawler(): void
    {
        if ($this->debug) {
            print(__METHOD__ . PHP_EOL);
        }

        $post = array(
            '__EVENTTARGET'        => '',
            '__EVENTARGUMENT'      => '',
            '__VIEWSTATE'          => $this->form['viewstate'],
            '__EVENTVALIDATION'    => $this->form['eventvalidation'],
            'txtCNPJNome'          => $this->cnpj,
            'ddlTpFdo'             => 95,
            // 'numRandom'            => $this->captcha,
            'btnContinuar'         => urlencode('Continuar >')
        );

        try {
            $result = $this->getResponse($this->url, 'POST', $post, ['Referer' => $this->url]);

            $this->ondeEstou($result);
        } catch (Exception $e) {
            throw $e;
        }
    }

    private function setCaptcha(): void
    {
        if ($this->debug) {
            print(__METHOD__ . PHP_EOL);
        }

        $url = $this->baseUrl . '/SWB/Sistemas/SCW/CPublica/' . $this->urlCaptcha;

        $this->getImageAndBreakCaptcha($url);
    }

    private function ondeEstou(string $result): void
    {
        if ($this->debug) {
            print(__METHOD__ . PHP_EOL);
        }

        if (
            preg_match('#Ocorreu\s*um\s*erro\s*de\
            s*processamento\s*no\s*servidor#is', $result)
        ) {
            throw new Exception("Ocorreu um erro de processamento no servidor", 3);
        }

        /* Removido por não estar usando captcha neste momento
        if (
            preg_match('#<td[^>]*?>\s*digite\s*abaixo\s*o\
            s*n.{1,8}mero\s*que\s*aparece\s*ao\s*lado#is', $result)
        ) {
            throw new Exception("Captcha inválido: {$this->captcha}", 6);
        }
        */

        # busca por resultado nao encontrado
        if (preg_match('#<b>Nenhum\s*registro\s*encontrado!</b>#is', $result)) {
            throw new Exception("Nada foi encontrado.", 1);
        }

        # Lista de Fundos / Administradores encontrados
        // \s*e\s*\/?\s*ou\s*administradores\s*
        $pattern = '#os\s*seguintes\s*fundos.*?foram\s*encontrados\s*a\s*partir\s*da\s*sua\s*pesquisa#is';
        if (
            preg_match($pattern, $result)
        ) {
            $this->stepListaFundos($result);
            return;
        }

        # CVM encontrado
        if (preg_match('#<B>\s*Dados\s*Gerais\s*</B>#is', $result)) {
            $this->stepFinalParse($result);
            return;
        }

        #Nenhum fundo foi encontrado com o critério de busca especificado.
        $pattern = '#Nenhum\s*fundo\s*foi\s*encontrado\s*com\s*o\s*crit.*?rio\s*de\s*busca\s*especificado#is';
        if (preg_match($pattern, $result)) {
            throw new Exception("Nenhum fundo foi encontrado com 
                o critério de busca especificado.", 2);
        }

        // var_dump($result);

        throw new Exception("Erro na requisicao HTTP do Metodo '
            ondeEstou': Nao sei onde estou!");
    }

    private function stepListaFundos($result)
    {
        if ($this->debug) {
            print(__METHOD__ . PHP_EOL);
        }

        $pattern = '#<form\s*name="Form\d+"\s*method="(?:post|get)"\s*action="(.*?)"[^>]*?>([\s\S]+.*?)</form>#is';

        if (preg_match_all($pattern, $result, $forms)) {
            $base = 'https://cvmweb.cvm.gov.br/SWB/Sistemas/SCW/CPublica/CConsolFdo';

            foreach ($forms[1] as $i => $link) {
                $link = preg_replace('#&amp;#is', '&', $base . trim($link, '.'));

                $forms[2][$i] = htmlspecialchars_decode($forms[2][$i], ENT_QUOTES);
                $this->form['viewstate'] = $this->getViewState($forms[2][$i]);
                if ($this->form['viewstate'] === false) {
                    throw new Exception("Erro ao identificar viewstate na Lista de Fundos, Registro ($i)!", 4);
                }
                $this->form['eventvalidation'] = $this->getEventValidation($forms[2][$i]);
                if ($this->form['viewstate'] === false) {
                    throw new Exception("Erro ao identificar eventvalidation na Lista de Fundos, Registro ($i)!", 4);
                }
                if (!preg_match('#<a.*?__doPostBack\(\'(.*?)\',\'\'\).*?<\/a>#is', $forms[2][$i], $botao)) {
                    throw new Exception("Erro ao identificar Nome do Botao na Lista de Fundos, Registro ($i)!", 4);
                }
                $post = array(
                    '__EVENTTARGET'     => $botao[1],
                    '__EVENTARGUMENT'   => '',
                    '__VIEWSTATE'       => $this->form['viewstate'],
                    '__EVENTVALIDATION' => $this->form['eventvalidation']
                );

                $result = $this->getResponse($link, 'POST', $post, ['Referer' => $link]);

                $this->ondeEstou($result);
            }
        } else {
            throw new Exception("Erro ao identificar links na Lista de Fundos!", 4);
        }
    }

    private function stepFinalParse($result)
    {
        if ($this->debug) {
            print(__METHOD__ . PHP_EOL);
        }

        $result = $this->removeStrangeChars(
            html_entity_decode(
                utf8_encode($result),
                ENT_QUOTES,
                'UTF-8'
            )
        );

        $aErro = [];
        $aObjetos = [
            'CvmDadosGerais' => [
                'nome_fundo'         => ['/>\s?nome\s+?do\s+?fundo[\s\S]+?<span.*?>(.*?)<\/span/mi'],
                'cnpj_fundo'         => ['/>\s?nome\s+?do\s+?fundo[\s\S]+?cnpj:[\s\S]+?<span.*?>(.*?)<\/span>/mi'],
                'administrador'      => ['/>\s*administrador.*?<span[^>]*?>(.*?)<\/span>/is'],
                'cnpj_administrador' => ['/>\s*administrador.*?CNPJ.*?<span[^>]*?>(.*?)<\/span>/is'],
                'situacao'           => ['/>\s*situa.{2,16}o.*?<span[^>]*?>(.*?)<\/span>/is', null],
                'data_inicio'        => [
                    '/>\s*data\s*in.{1,8}cio\s*de\s*atividades.*?<span[^>]*?>(.*?)<\/span>/is',
                    null
                ],
                'data_constituicao'  => ['/>\s*data\s*de\s*constitui.{2,16}o.*?<span[^>]*?>(.*?)<\/span>/is', null],
                'web_site'           => ['/>\s*web\s*site.*?<span[^>]*?>(.*?)<\/span>/is', null]
            ],
            'CvmCaracterizacao' => [
                'classe'                => ['/>\s*classe.*?<span[^>]*?>(.*?)<\/span>/is', null],
                'forma_condominio'      => ['/>\s*forma\s*de\s*condom.{1,8}nio.*?<span[^>]*?>(.*?)<\/span>/is', null],
                'indicador_desempenho'  => ['/>\s*indicador\s*de\s*desempenho.*?<span[^>]*?>(.*?)<\/span>/is', null],
                'fundo_exclusivo'       => ['/>\s*fundo\s*exclusivo.*?<span[^>]*?>(.*?)<\/span>/is', null],
                'fundo_cotas'           => ['/>\s*fundo\s*de\s*cotas.*?<span[^>]*?>(.*?)<\/span>/is', null],
                'tratamento_tributario' => [
                    '/>\s*tratamento\s*tribut.{1,8}rio\s*de\s*longo\s*prazo.*?<span[^>]*?>(.*?)<\/span>/is',
                    null
                ],
                'destino_exclusivo'     => [
                    '/>\s*destinado\s*exclusivamente\s*a\s*investidores\s*qualificados.*?<span[^>]*?>(.*?)<\/span>/is',
                    null
                ]
            ]
        ];

        $oCvmDadosGerais = [];
        $oCvmCaracterizacao = [];
        foreach ($aObjetos as $classe => $aFields) {
            foreach ($aFields as $desc => $aRegExp) {
                foreach ($aRegExp as $sRegExp) {
                    if ($sRegExp == null) {
                        //eval("\$o{$classe}->set_$desc(NULL);");
                        ${"o$classe"}[$desc] = null;
                        unset($aErro["$classe|$desc"]);
                    } else {
                        if (preg_match($sRegExp, $result, $matches)) {
                            $argumento = str_replace("'", "", $matches[1]);
                            //eval("\$o{$classe}->set_$desc(trim(\$this->cleanString('{$argumento}')));");
                            ${"o$classe"}[$desc] = trim(Str::cleanString($argumento));
                            unset($aErro["$classe|$desc"]);
                            break;
                        } else {
                            echo "\nnot preg match\n";
                            $aErro["$classe|$desc"] = "$classe ->$desc";
                        }
                    }
                }
            }
        }

        if (count($aErro) > 0) {
            throw new Exception("Spider Broken! - " . implode(",", $aErro), 1);
        }
        $aCvmDocumento = $this->stepListaDocumentos($result);

        $oCvm = [
            'oDadosGerais' => $oCvmDadosGerais,
            'oCaracterizacao' => $oCvmCaracterizacao,
            'aDocumento' => $aCvmDocumento
        ];

        $this->result[] = $oCvm;
    }

    private function stepListaDocumentos($result)
    {
        if ($this->debug) {
            print(__METHOD__ . PHP_EOL);
        }

        // A fonte não trata esses documentos na view
        // estou comentando essa parte pra fonte ser mais ráṕida
        // e pra não fazer processos desnecessários
        // caso a view começe a apresentar esses documentos
        // basta remover/comentar esse retorno abaixo
        return [];

        try {
            $aCvmDocumento = [];

            $docsPattern = '/<a\s*id="HyperLink\d+"\s*href="\.+\/(.*?)"[^>]*?>(.*?)<\/a>/is';

            if (preg_match_all($docsPattern, $result, $links)) {
                foreach ($links[1] as $i => $link) {
                    $url = 'https://cvmweb.cvm.gov.br/SWB/Sistemas/SCW/CPublica/' . $link;

                    if ($filename = $this->gerarPdf($url)) {
                        $aCvmDocumento[] = [
                            'label' => strip_tags($links[2][$i]),
                            'label_interno' => $filename
                        ];
                    } else {
                        throw new Exception('Erro ao converter arquivo para PDF');
                    }
                }
                // $this->limparHtmlFiles();
            }
            return $aCvmDocumento;
        } catch (Exception $e) {
            throw $e;
        }
    }

    private function gerarPdf($url)
    {

        if ($this->debug) {
            print(__METHOD__ . PHP_EOL);
            print($url . PHP_EOL);
        }

        $nome     = date('YmdHis');
        $path     = $this->getDefaultPath();
        $fileHtml = $path . "/$nome.html";
        $filePdf  = $path . "/$nome.pdf";
        $output   = $path . "/output";
        $versao   = '';

        try {
            $result = $this->getResponse($url, 'GET', [], ['Referer' => $url]);

            # Caso tenha algum link de arquivo na pagina
            if (preg_match('/<a[^>]*?>\s*exemplar\s*do\s*regulamento\s*<\/a/is', $result)) {
                $filepath = $this->downloadFile($result);

                return $this->sendToS3('CmvFundo/' . uniqid() . '.doc', $filepath);
            }
        } catch (Exception $e) {
            throw new Exception("Erro na requisicao HTTP do Metodo 'pdf Creator'", 100);
        }

        return $this->htmlToPdfAndSaveInS3($result);
    }


    private function downloadFile(string $result): string
    {
        if ($this->debug) {
            print(__METHOD__ . PHP_EOL);
        }
        $this->form['viewstate'] = $this->getViewState($result);
        preg_match('/id="COMPTC"[^>]*?>\s*<option\s*selected="selected"\s*value="(.*?)"\s*>/is', $result, $match);
        preg_match('/action="(.*?)"/is', $result, $url);
        $post = array(
            '__EVENTTARGET'   => 'hl1',
            '__EVENTARGUMENT' => '',
            '__VIEWSTATE'     => $this->form['viewstate'],
            'DDCdTpDoc'       => '246',
            'COMPTC'          => $match[1]
        );
        $url = "https://cvmweb.cvm.gov.br/SWB/Sistemas/SCW/CPublica/Regul/{$url[1]}";
        $filename = 'CvmFundo' . uniqid() . '.doc';
        $fileDir = '/tmp/';
        $filePath = $fileDir . $filename;

        $response = $this->getResponse($url, 'POST', $post, ['Referer' => $url]);

        if (!@file_put_contents($filePath, $response)) {
            throw new Exception("Erro ao gravar DOC!", 1);
        }
        if (!@chmod($filePath, 0777)) {
            throw new Exception("Erro ao mudar permissão do DOC!", 1);
        }

        return $filePath;
    }

    private function htmlToPdfAndSaveInS3($result)
    {
        $fileId = uniqid() . '.pdf';
        $filePath = '/tmp/CvmFundo' . $fileId;
        $s3Path = 'CvmFundo/' . $fileId;

        $this->htmlToPdf($result, $filePath);

        return $this->sendToS3($s3Path, $filePath);
    }

    private function htmlToPdf(string $html, string $filePath): void
    {
        (new Pdf())->saveHtmlToPdf(utf8_decode($html), $filePath);
    }

    private function sendToS3(string $s3Path, $filePath): string
    {
        return S3_STATIC_URL . $s3Path;

        if ((new S3(new StaticUplexisBucket()))->save($s3Path, $filePath)) {
            return S3_STATIC_URL . $s3Path;
        }

        throw new Exception('Erro ao salvar o pdf', 6);
    }

    private function getViewState(string $result): string
    {
        if ($this->debug) {
            print(__METHOD__ . PHP_EOL);
        }

        $pattern = '@name="__VIEWSTATE".*?value="(.*?)"\s*/>@is';

        $count = preg_match($pattern, $result, $matches);

        if (!$count) {
            throw new Exception("Não Encontrou Viewstate");
        }

        return html_entity_decode($matches[1]);
    }

    private function getEventValidation(string $result): string
    {
        if ($this->debug) {
            print(__METHOD__ . PHP_EOL);
        }

        $pattern = '@name="__EVENTVALIDATION".*?value="(.*?)"\s*/>@is';

        if (!preg_match($pattern, $result, $matches)) {
            throw new Exception("Não Encontrou EventValidation");
        }

        return html_entity_decode($matches[1]);
    }

    private function getViewStateGenerator(string $result): string
    {
        if ($this->debug) {
            print(__METHOD__ . PHP_EOL);
        }

        $pattern = '/name="__VIEWSTATEGENERATOR"[\s\S]+?value="(.*?)"/mi';

        if (!preg_match($pattern, $result, $matches)) {
            throw new Exception("Não Encontrou EventValidation");
        }

        return $matches[1];
    }

    private function removeStrangeChars(string $text): string
    {
        for ($i = 1; $i < 32; $i++) {
            if ($i == 10 || $i == 9 || $i == 13) {
                continue;
            }

            $text = str_replace(chr($i), "", $text);
        }

        return $text;
    }

    private function getDefaultPath(): string
    {
        return __DIR__ . "/temp";
    }

    private function config(): void
    {
        $pathUrl = '/SWB/Sistemas/SCW/CPublica/CConsolFdo/FormBuscaParticFdo.aspx';
        $this->url = $this->baseUrl . $pathUrl;
    }

    protected function validateAndSetCrawlerAttributes(): void
    {
        $this->param['limite'] = $this->param['limite'] ?? 1;

        if (empty($this->param['cnpj'])) {
            throw new Exception("Nenhum critério de busca informado!", 3);
        }

        if (!Document::validarCnpj($this->param['cnpj'])) {
            throw new Exception("Critério de busca inválido!", 3);
        }

        $this->cnpj = preg_replace('/[^\d]/', '', $this->param['cnpj']);
    }
}
