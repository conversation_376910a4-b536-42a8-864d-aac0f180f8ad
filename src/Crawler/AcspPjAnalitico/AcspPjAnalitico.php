<?php

namespace App\Crawler\AcspPjAnalitico;

use Exception;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\Bucket\StaticUplexisBucket;

/**
 * Class AcspPjAnalitico[88] - Pesquisa "PJ - Relatórios Analíticos" na boa vista
 *
 * <AUTHOR> Mesquita - 13/04/2018
 */
class AcspPjAnalitico extends Spider
{
    private const URL_LOGIN_FORM = 'https://www.bvsnet.com.br/cgi-bin/db2www/NETPO101.mbr/loginSI';
    private const URL_LOGIN = 'https://www.bvsnet.com.br/cgi-bin/db2www/NETPO001.mbr/senha?';
    private const URL_MENU = 'https://www.bvsnet.com.br/cgi-bin/db2www/NETPO101.mbr/menuLU';
    private const URL_SEARCH = 'https://www.scpcnet.com.br/ACSPNET/Programas/CPJPH002.php';

    private const URL_RECAPTCHA = 'https://web2.bvsnet.com.br/transacional/login.php';

    private const S3_DOMAIN = S3_STATIC_BUCKET . '/';
    private const CAPTURA_PATH = 'captura/acsp_pj_analitico/';

    /**
     * Realiza parse do resultado final
     *
     * <AUTHOR> Mesquita - 01/13/2018
     * @version 1.0
     * @return mixed
     * @throws Exception
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print __METHOD__ . PHP_EOL;
        }

        $this->document = preg_replace("/\\D/", "", $this->param['cnpj']);
        $this->user = $this->auth['usuario'];
        $this->password = $this->auth['senha'];

        if (
            !Document::validarCpfOuCnpj($this->document)
            || empty($this->user)
            || empty($this->password)
        ) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }
    }

    /**
     * @return array
     */
    protected function start()
    {
        if ($this->debug) {
            print __METHOD__ . PHP_EOL;
        }

        $formData = $this->login();
        $params = $this->parseParams($formData);

        return $this->search($params);
    }

    /**
     * Monta parâmetros de pesquisa
     *
     * <AUTHOR> Mesquita - 01/13/2018
     * @version 1.0
     * @param $formData
     * @param $cnpj
     * @return array
     */
    private function parseParams($formData)
    {
        if ($this->debug) {
            print __METHOD__ . PHP_EOL;
        }

        $date = \DateTime::createFromFormat('U.u', microtime(true));

        return  [
            'lk_docto' => $this->document,
            'lk_tdoc'  => '2',
            'lk_codig' => $formData['lk_codig'],
            'lk_opera' => $formData['lk_codig'],
            'lk_codpr' => '',
            'ws_contr' => $formData['ws_contr'],
            'lk_acess' => $formData['lk_acess'],
            'lk_ipnum' => long2ip(rand(0, "4294967295")),
            'lk_horac' => $date->format('H:i:s.u'),
            'lk_tcons' => 'ACN',
            'lk_mscor' => '',
            'lk_razao' => '',
            'lk_divnm' => 'div1'
        ];
    }

    private function resolveCaptcha($html)
    {
        preg_match('#g\W*recaptcha[^>*]*?sitekey\W+(?<sitekey>.*?)[\'\"]#i', $html, $match);

        if (isset($match['sitekey']) && !empty($match['sitekey'])) {
            return $this->solveReCaptcha($match['sitekey'], self::URL_LOGIN_FORM, 20, 5);
        }
        throw new Exception("Erro ao localizar dados do captcha na página.", 1);
    }

    /**
     * Realiza login
     *
     * @version 1.0
     *
     * <AUTHOR> Mesquita - 01/13/2018
     *
     * @return array
     */
    private function login()
    {
        if ($this->debug) {
            print __METHOD__ . PHP_EOL;
        }

        $captcha = $this->resolveCaptcha($this->getResponse(self::URL_RECAPTCHA));

        $params = [
            'lk_codig' => $this->user,
            'lk_senha' => $this->password,
            'RN_nrCPF' => '',
            'lk_rightnow' => '',
            'lk_width' => '123',
            'lk_consu' => 'SENHA',
            'lk_suaft' => '',
            'g-recaptcha-response' => $captcha,
            'lk_manut' => 'https://www.servicodeprotecaoaocredito.com.br/bvs_login.htm',
            'lk_urlesquecisenha' => 'https://www.bvsnet.com.br/cgi-bin/db2www/NETPO101.mbr/RecuperaSenha'
        ];
        $header = ['Referer' => self::URL_LOGIN_FORM];
        $this->checkResponse($this->getResponse(self::URL_LOGIN_FORM));

        $response = $this->getResponse(self::URL_LOGIN, 'POST', $params, $header);
        $this->checkResponse($response);
        $formData = $this->parseFormData($response);
        $header = ['Referer' => 'https://www.bvsnet.com.br/cgi-bin/db2www/NETPO001.mbr/senha?'];
        $this->checkResponse($this->getResponse(self::URL_MENU, 'POST', $formData, $header));

        return $formData;
    }

    /**
     * Faz parse de params a serem enviados via POST durante pesquisa
     *
     * <AUTHOR> Mesquita - 01/13/2018
     * @version 1.0
     * @param $response
     * @return array
     * @throws Exception
     */
    private function parseFormData($response)
    {
        if ($this->debug) {
            print __METHOD__ . PHP_EOL;
        }

        if (
            preg_match_all(
                '#<\s*?input\s*?.*?name="\s*?(.*?)\s*?"\s*?\s*?value="(.*?)"#isu',
                utf8_encode($response),
                $matches
            )
        ) {
            return array_combine($matches[1], $matches[2]);
        }

        throw new Exception('Não foi possível capturar os parâmetros.', 3);
    }

    /**
     * Realiza pesquisa
     *
     * <AUTHOR> Mesquita - 01/13/2018
     * @version 1.0
     * @param $params
     * @return array
     */
    private function search($params)
    {
        if ($this->debug) {
            print __METHOD__ . PHP_EOL;
        }

        $header = ['Referer' => 'www.scpcnet.com.br/ACSPNET/Programas/CPJPH001.php'];
        $response = $this->getResponse(self::URL_SEARCH, 'POST', $params, $header);
        $this->checkResponse($response);

        return $this->parseResponse($response);
    }

    /**
     * Valida response
     *
     * <AUTHOR> Mesquita - 01/13/2018
     * @version 1.0
     * @param $response
     * @throws Exception
     */
    private function checkResponse($response)
    {
        if ($this->debug) {
            print __METHOD__ . PHP_EOL;
        }

        if (preg_match('@identifica[^o]*?o\s*?do\s*?associado\s*?inv[^l]*?lida@isu', $response)) {
            throw new Exception('Usuário e senha inválidos.', 1);
        }

        if (preg_match('@informa[^s]*s\s*?cadastrais\s*?inexistentes@isu', $response)) {
            throw new Exception('Não existe resultado para o critério informado', 2);
        }

        if (preg_match('@AUTORIZACAO\s*DE\s*ACESSO\s*INIBIDA\s*TEMPORARIAMENTE@i', $response)) {
            throw new Exception("Autorização de acesso inibida temporariamente.", 6);
        }

        if (preg_match('@SERVICO\s*NAO\s*DISPONIVEL\s*TEMPORARIAMENTE@i', $response)) {
            throw new Exception("Serviço não disponível temporariamente.", 6);
        }

        if (preg_match('@BANCO\s*INVALIDO@i', $response)) {
            throw new Exception('Banco inválido.', 3);
        }

        if (preg_match('@CODIGO\s*DE\s*SERVICO\s*INVALIDO@i', $response)) {
            throw new Exception('Código de serviço inválido', 3);
        }

        if (preg_match('@CODIGO\s*DE\s*SERVICO\s*NAO\s*AUTORIZADO\s*A\s*ESTA\s*CONSULTA@i', $response)) {
            throw new Exception('Código de serviço não autorizado', 3);
        }
    }

    /**
     * Realiza parse do resultado final
     *
     * <AUTHOR> Mesquita - 01/13/2018
     * @version 1.0
     *
     * <AUTHOR> Vidal - 06/04/2021 - Adicionei verificação se o resultado está vazio
     *
     * @param $response
     * @return array
     */
    private function parseResponse($response)
    {
        if ($this->debug) {
            print __METHOD__ . PHP_EOL;
        }

        $patterns = [
            'consulta'    => ['#<span[^>]*?>\s*CONSULTA:\s*</span>.*?<span[^>]*?>(.*?)</span></td>#isu'],
            'solicitante' => ['#<span[^>]*?>\s*SOLICITANTE:\s*</span>.*?<span[^>]*?>(.*?)</span></td>#isu'],
            'nome'        => ['#<span[^>]*?>\s*NOME:\s*<\/span>.*?<span[^>]*?>(.*?)<\/span><\/td>#isu'],
            'razao_social' => ['#raz[^o]*o social[\s\S]+?<span[^>]*?>\s*?(.*?)\s*?<\/span>#isu'],
            'documento'   => ['#<span[^>]*?>\s*DOCUMENTO:\s*</span>.*?<span[^>]*?>(.*?)</span></td>#isu'],
        ];

        $result = Util::parseDados($patterns, $response);

        if (empty($result)) {
            throw new Exception('O site não retornou a consulta solicitada, verifique as credenciais de acesso.', 6);
        }

        $result['pdf'] = $this->convertAndSavePdf($response);
        $result['html'] = '';

        return $result;
    }

    /**
     * Invoca métodos p/ converter e salvar pdf no S3
     *
     * <AUTHOR> Mesquita - 01/13/2018
     * @version 1.0
     * @param $response
     * @return string
     */
    private function convertAndSavePdf($response)
    {
        if ($this->debug) {
            print __METHOD__ . PHP_EOL;
        }

        list($path, $uniqId) = $this->convertHtmlToPdfAndReturnPath($response);

        return $this->uploadToS3AndReturnPdfLink($uniqId, $path);
    }

    /**
     * Salva no S3
     *
     * <AUTHOR> Mesquita - 01/13/2018
     * @version 1.0
     * @param $fileId
     * @param $filePath
     * @return string
     */
    private function uploadToS3AndReturnPdfLink($fileId, $filePath)
    {
        if ($this->debug) {
            print __METHOD__ . PHP_EOL;
        }

        $s3Path = self::CAPTURA_PATH . $fileId . '.pdf';
        (new S3(new StaticUplexisBucket()))->save($s3Path, $filePath);

        return self::S3_DOMAIN . $s3Path;
    }

    /**
     * Converte para PDF
     *
     * <AUTHOR> Mesquita - 01/13/2018
     * @version 1.0
     * @param string $response
     * @return array
     * @throws Exception
     */
    private function convertHtmlToPdfAndReturnPath($html = '')
    {
        if ($this->debug) {
            print __METHOD__ . PHP_EOL;
        }

        try {
            $uniqid = md5(uniqid(rand(), true));
            $pdfPath = '/tmp/acsp_' . $uniqid . '.pdf';

            (new Pdf())->saveHtmlToPdf(utf8_decode($html), $pdfPath);

            return [$pdfPath, $uniqid];
        } catch (\Exception $e) {
            throw new Exception($e->getMessage(), 3, $e);
        }
    }
}
