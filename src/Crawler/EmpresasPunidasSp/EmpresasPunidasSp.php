<?php

namespace App\Crawler\EmpresasPunidasSp;

use App\Crawler\Spider;
use App\Factory\MongoDB;
use App\Helper\Document;
use Exception;

class EmpresasPunidasSp extends Spider
{
    private const INDEX_MONGODB = 'document';
    private const LIMIT = 100;

    public function start()
    {
        $data = $this->getResults();
        return $data;
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCpfOuCnpj($this->param['documento'])) {
            throw new Exception('Parâmetro invalido', 1);
        }
        if (empty($this->param['limite'])) {
            $this->param['limite'] = self::LIMIT;
        }
        $this->documento = Document::removeMask($this->param['documento']);
    }

    private function getResults()
    {
        $documento = $this->documento;
        $limit = $this->param['limite'];
        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('common')
            ->setCollection('empresas_punidas_sp');

        $fields = ['documento'];
        $result = json_decode(
            json_encode(
                $manager
                    ->atlasSearch(
                        $documento,
                        self::INDEX_MONGODB,
                        $limit,
                        $fields
                    )
                    ->toArray(),
                true
            ),
            true
        );

        return $result;
    }
}
