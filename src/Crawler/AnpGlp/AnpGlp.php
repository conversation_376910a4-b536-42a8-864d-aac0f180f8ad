<?php

namespace App\Crawler\AnpGlp;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Str;
use App\Helper\Util;
use Exception;

class AnpGlp extends Spider
{
    private const BASE_URL = 'https://cdp.anp.gov.br/ords';
    private const URL_FORM = '/r/cdp_apex/consulta-dados-publicos-cdp/consulta-de-revenda-glp-lista';
    private const URL_DETALHE = '/r/cdp_apex/consulta-dados-publicos-cdp/consulta-de-revenda-glp-detalhe';
    private $html;
    private $cnpj;
    private mixed $result;

    protected function validateAndSetCrawlerAttributes()
    {
        $this->cnpj = $this->param['cnpj'];

        if (empty($this->cnpj) || !Document::validarCnpj($this->cnpj)) {
            throw new Exception('Campo cnpj obrigatório', 1);
        }

        $this->cnpj = Document::removeMask($this->cnpj);
    }


    protected function start()
    {
        return $this->makeRequest();
    }

    private function makeRequest()
    {
        $this->setProxy();

        $this->html = html_entity_decode(
            $this->getResponse(self::BASE_URL . self::URL_FORM)
        );

        $this->sendForm();

        if ($this->noResults()) {
            throw new Exception('Nenhum resultado encontrado!', 2);
        }

        $this->getDetails();

        if ($this->noHasCredentials()) {
            throw new Exception(
                'Revenda de GLP Descredenciada por Distribuidor ou Com Autorização Revogada!',
                6
            );
        }

        $result = $this->parseResult();

        if (!isset($result['autorizacao'])) {
            $this->makeRequest();
        }
        return $result;
    }


    private function getDetails(): void
    {
        preg_match('/consulta-de-revenda-glp-detalhe(.*?)&#x27;/is', $this->result, $queryString);
        $queryString = str_replace('\u0026', '&', $queryString[1]);

        $headers = [
            "User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0",
            "Referer: https://cdp.anp.gov.br/",
            "Upgrade-Insecure-Requests: 1",
        ];

        $this->result = html_entity_decode($this->getResponse(
            self::BASE_URL . self::URL_DETALHE . $queryString,
            'GET',
            [],
            $headers
        ));
    }


    private function sendForm(): void
    {
        preg_match('@<div id="anp_p30_captcha">(.*?)</div>@is', $this->html, $matches);
        preg_match_all('@src="(.*?)"@is', $matches[1], $imgs);
        $captcha = $this->solveCaptcha($imgs[1]);

        $patterns = [
            'action' => ['@<form.*?action="(.*?)"@is'],
            'p_flow_id' => ['@name="p_flow_id".*?value="(.*?)"@is'],
            'p_flow_step_id' => ['@name="p_flow_step_id".*?value="(.*?)"@is'],
            'p_instance' => ['@name="p_instance".*?value="(.*?)"@is'],
            'p_page_submission_id' => ['@name="p_page_submission_id".*?value="(.*?)"@is'],
            'p_reload_on_submit' => ['@name="p_reload_on_submit".*?value="(.*?)"@is'],
            'salt' => ['@pContext.*?<input.*?value="(.*?)".*?id="pSalt"@is'],
            'P30_LABEL_FILTRAR' => ['@name="P30_LABEL_FILTRAR".*?value="(.*?)"@is'],
            'P30_LABEL_FILTRAR_ck' => ['@data-for="P30_LABEL_FILTRAR".*?value="(.*?)"@is'],
            'P30_LINHA_2' => ['@name="P30_LINHA_2".*?value="(.*?)"@is'],
            'P30_LINHA_2_ck' => ['@data-for="P30_LINHA_2".*?value="(.*?)"@is'],
            'protected' => ['@id="pPageItemsProtected".*?value="(.*?)"@is']
        ];

        $data = Util::parseDados($patterns, Str::cleanString($this->html));

        $params = [
            "p_flow_id" => $data['p_flow_id'],
            "p_flow_step_id" => $data['p_flow_step_id'],
            "p_instance" => $data['p_instance'],
            "p_page_submission_id" => $data['p_page_submission_id'],
            "p_request" => 'Buscar',
            "p_reload_on_submit" => $data['p_reload_on_submit'],
            "p_json" => json_encode([
                "salt" => $data['salt'],
                "pageItems" => [
                  "itemsToSubmit" => [
                    [
                      "n" => "P30_LABEL_FILTRAR",
                      "v" => "",
                      "ck" => $data['P30_LABEL_FILTRAR_ck']
                    ],
                    [
                      "n" => "P30_CONSULTA",
                      "v" => ""
                    ],
                    [
                      "n" => "P30_CNPJ",
                      "v" => $this->cnpj
                    ],
                    [
                      "n" => "P30_NOME_REVENDA",
                      "v" => ""
                    ],
                    [
                      "n" => "P30_ESTADO",
                      "v" => ""
                    ],
                    [
                      "n" => "P30_LOCALIDADE",
                      "v" => ""
                    ],
                    [
                      "n" => "P30_DISTRIBUIDORA",
                      "v" => ""
                    ],
                    [
                      "n" => "P30_CAPTCHA",
                      "v" => $captcha
                    ],
                    [
                      "n" => "P30_LINHA_2",
                      "v" => "",
                      "ck" => $data['P30_LINHA_2_ck']
                    ]
                  ],
                  "protected" => $data['protected'],
                  "rowVersion" => "",
                  "formRegionChecksums" => []
                ]
            ]),
        ];

        $headers = [
            "User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0",
            "Origin: https://cdp.anp.gov.br",
            "Referer: https://cdp.anp.gov.br/",
            "Upgrade-Insecure-Requests: 1",
        ];

        $this->result = $this->getResponse(self::BASE_URL . '/' . $data['action'], 'POST', $params, $headers);
        $this->result = html_entity_decode($this->result);

        if (preg_match('/error has occurred.*?a-Notification-link">(.*?)<\/a>/is', $this->result, $erros)) {
            throw new Exception($erros[1], 2);
        }
    }

    private function parseResult(): array
    {
        $patterns = [
            'autorizacao' => ['@P20_REGISTRO_POSTO_DISPLAY.*?>(.*?)</span>@is'],
            'cnpj' => ['@P20_CNPJ_DISPLAY.*?>(.*?)</span>@u'],
            'razaoSocial' => ['@P20_RAZAO_SOCIAL_DISPLAY.*?>(.*?)</span>@u'],
            'nomeFantasia' => ['@P20_NOME_FANTASIA_DISPLAY.*?>(.*?)</span>@u'],
            'endereco' => ['@P20_ENDERECO_DISPLAY.*?>(.*?)</span>@u'],
            'complemento' => ['@P20_TXT_COMPLEMENTO_ENDERECO_DISPLAY.*?>(.*?)</span>@u'],
            'bairro' => ['@P20_BAIRRO_DISPLAY.*?>(.*?)</span>@u'],
            'cep' => ['@P20_CEP_DISPLAY.*?>(.*?)</span>@u'],
            'municipio' => ['@P20_ESTADO_DISPLAY.*?>(.*?)</span>@u'],
            'numeroDespacho' => ['@P20_NR_DESPACHO_DISPLAY.*?>(.*?)</span>@u'],
            'dataPublicacao' => ['@P20_DATA_PUBLICACAO_DISPLAY.*?>(.*?)</span>@u'],
            'classeArmazenamento' => ['@P20_TXT_CLASSE_ARMAZENAMENTO_DISPLAY.*?>(.*?)</span>@u'],
            'distribuidora' => ['@P20_NOME_FANTASIA_DISPLAY.*?>(.*?)</span>@u'],
        ];

        $data = Util::parseDados($patterns, Str::cleanString($this->result));
        $data = array_map("utf8_decode", $data);

        preg_match_all('/<td.*?headers="SOCIO".*?>(.*?)<\/td>/is', $this->result, $socios);

        preg_match('/aria-label="Hist.*?rico de credenciamento">(.*?)<\/table>/is', $this->result, $historico);

        $data['socios'] = $socios[1];
        $data['historico'] = $this->parseHistorico($historico[1]);

        return Str::stripTagsRecursive($data);
    }

    private function parseHistorico($html)
    {
        if (is_null($html)) {
            return [];
        }

        $result = [];
        $cols = Util::queryXPath($html, '//tbody/tr');

        foreach ($cols as $col) {
            $lines = Util::queryXPath($col, '//td');
                $result[] = [
                    'credencial' => strip_tags($lines[0]) ?? '',
                    'inicio' => strip_tags($lines[1]) ?? '',
                    'fim' => strip_tags($lines[2]) ?? '',
                ];
        }

        return $result;
    }


    private function solveCaptcha($imgs): string
    {
        $captcha = '';
        foreach ($imgs as $img) {
            $captchaUrl = self::BASE_URL . '/' . $img;
            $captchaUrl = str_replace('%2F', '/', $captchaUrl);

            $this->captcha = '';

            $retry = 2;
            while ($retry >= 0) {
                try {
                    $this->getImageAndBreakCaptcha($captchaUrl);
                    if (!empty($this->captcha) && strlen($this->captcha) == 1) {
                        break;
                    }
                } catch (Exception $e) {
                    if ($retry == 0) {
                        throw new Exception($e->getMessage(), 3);
                    }
                }
                $retry--;
            }

            $captcha = $captcha . $this->captcha;
        }

        if (empty($captcha)) {
            throw new Exception('Não foi possível recuperar o captcha', 2);
        }
        return $captcha;
    }

    private function noResults(): bool
    {
        return (bool)preg_match('/Dados\sn.*?o\sencontrados/is', $this->result);
    }

    private function noHasCredentials(): bool
    {
        if (
            preg_match('/Revenda\sde\sGLP\sDescredenciada\spor\sDistribuidor/u', $this->result) ||
            preg_match('/Revenda\sde\sGLP\scom\sautorização\srevogada/isu', $this->result)
        ) {
            return true;
        }
        return false;
    }
}
