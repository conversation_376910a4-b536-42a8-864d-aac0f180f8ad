<?php

namespace App\Crawler\ReceitaFederalOperacaoDeflagrada;

use Exception;
use App\Crawler\Spider;
use App\Factory\PostgresDB;
use App\Helper\Document;
use App\Helper\Util;

 /**
 * Classe de consulta dos dados na Transparência PEP
 *
 * <AUTHOR>
 */
class ReceitaFederalOperacaoDeflagrada extends Spider
{
    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['pesquisa'])) {
            throw new Exception('Parâmetro invalido', 1);
        }
    }

    protected function start()
    {
        if (Document::validarCpfOuCnpj($this->param['pesquisa'])) {
            $results = $this->searchByDoc();
        } else {
            $results = $this->searchByNome();
        }

        if (empty($results)) {
            throw new Exception('Nenhum registro encontrado', 2);
        }

        return $this->parseResulsts($results);
    }

    private function searchByDoc()
    {
        $doc = Document::removeMask($this->param['pesquisa']);
        $db = new PostgresDB();
        $result =  $db->connectCaptura()
            ->select('*')
            ->from('receita_federal.operacao_deflagrada')
            ->where("documento = '$doc'")
            ->execute()
            ->fetchAll();
        $db->disconnect();
        return $result;
    }

    private function searchByNome()
    {
        $nome = $this->param['pesquisa'];
        $db = new PostgresDB();
        $result =  $db->connectCaptura()
            ->select('*')
            ->from('receita_federal.operacao_deflagrada')
            ->where("unaccent(contribuinte) ILIKE unaccent ('$nome')")
            ->execute()
            ->fetchAll();
        $db->disconnect();
        return $result;
    }

    private function parseResulsts($results)
    {
        $contribuinte = $this->parseResponse($results);
        $i = 0;
        foreach ($results as $key => $value) {
            $dataFormatada = $this->dataFormat($value['referencia']);
            $data = [
                $dataFormatada => $contribuinte
            ];

            if ($i == 0) {
                $data1 = $data;
            } else {
                $data1 = array_merge($data1, $data);
            }
            $i++;
        }

        return $data1;
    }

    private function parseResponse($results)
    {
        $i = 0;
        foreach ($results as $key => $value) {
            $dados = json_decode($value['dado'], true);
            $responsaveis = $this->responsaveis($dados);
            $data[] = [
                'contribuinte' => $dados['contribuinte'],
                'documento' => $dados['documento'],
                'numero_processo' => $dados['numero_processo'],
                'area' => $dados['area'],
                'unidade_rf' => $dados['unidade_rf'],
                'encaminhamento' => $dados['encaminhamento'],
                'data_encaminhamento' => $dados['data_encaminhamento'],
                'tipificacao' => $dados['tipificacao'],
                'responsaveis' => $responsaveis
            ];
        }

        return $data;
    }

    private function responsaveis($dados)
    {
        foreach ($dados['responsaveis'] as $key => $value) {
            $responsaveis[] = [
                'documento' => $value['documento'],
                'cargo' => $value['cargo'],
                'nome' => $value['nome']
            ];
        }

        return $responsaveis;
    }

    private function dataFormat($date)
    {
        $d = new \DateTime($date);
        $dateFormat = $d->format('m/Y');
        return $dateFormat;
    }
}
