<?php

namespace App\Crawler\Cade;

use App\Crawler\Spider;
use App\Helper\Str;
use DOMDocument;
use DOMXPath;
use Exception;

class Cade extends Spider
{
    private const SEI_URL = 'https://sei.cade.gov.br/';
    private const URL_BASE = 'https://sei.cade.gov.br/';
    private const URL_COMPLEMENT = 'sei/modulos/pesquisa/md_pesq_processo_pesquisar.php';
    private const URL_CAPTCHA_DEFAULT = self::SEI_URL . 'infra_js/infra_gerar_captcha.php?codetorandom=119-113';
    private const URL_CAPTCHA = self::SEI_URL . 'infra_js/infra_gerar_captcha.php?codetorandom=';
    private const URL_PROCESSO = self::SEI_URL . 'sei/modulos/pesquisa/';
    private const MAX_RESULTS = 70;
    private const MAX_CAPTCHA_BYPASS_RETRIES = 10;

    private $qtdProcessos = 0;

    protected function start()
    {
        $this->setAlternativeProxy();
        $searchUrl = $this->mountSearchUrl();

        // gerando o html para pegar o recaptcha do google
        $html = $this->getResponse($searchUrl, 'GET');
        $this->captcha = $this->resolveCaptcha($html);

        $requestParams = $this->getRequestParameters();
        $resultPageHtml = $this->getResponse($searchUrl, 'POST', $requestParams);

        $this->checkPageHasResults($resultPageHtml);
        $result = $this->parseResultsByPage($resultPageHtml);

        return $result;
    }

    protected function validateAndSetCrawlerAttributes()
    {
        $limit = $this->param['limit'] ??  self::MAX_RESULTS;

        $name = $this->param['name'];

        $target = false;
        if (!empty($this->param['target'])) {
            $target = (bool) $this->param['target'];
        }

        if (empty($name)) {
            throw new Exception("Nenhum critério de pesquisa informado!", 6);
        }

        if (in_array($this->param['search'], ['processos', 'gerados', 'externos'])) {
            throw new Exception("Tipo de pesquisa selecionado é inválido: {$this->param['pesquisa']}!", 6);
        }

        $this->validatedParams = [
            'searchTypes' => $this->param['search'],
            'name' => $name,
            'target' => $target,
            'limit' => $limit
        ];
    }

    private function resolveCaptcha($html)
    {
        preg_match('#g\W*recaptcha[^>*]*?sitekey\W+(?<sitekey>.*?)[\'\"]#i', $html, $match);
        if (isset($match['sitekey']) && !empty($match['sitekey'])) {
            return $this->solveReCaptcha($match['sitekey'], self::URL_BASE . self::URL_COMPLEMENT, 20, 5);
        }
        throw new Exception("Erro ao localizar dados do captcha na página.", 1);
    }

    private function breakAndResolveCaptcha($html = '')
    {
        $captchaUrl = self::URL_CAPTCHA_DEFAULT;
        if (!empty($html)) {
            $captcha = $this->getCaptchaUrl($html);

            if (!empty($captcha)) {
                $captchaUrl = self::URL_CAPTCHA . $captcha;
            }
        }
        $this->getImageAndBreakCaptcha($captchaUrl);
    }

    private function mountSearchUrl($extraParams = [])
    {
        $queryString = [
            'acao_externa' => 'protocolo_pesquisar',
            'acao_origem_externa' => 'protocolo_pesquisar_paginado',
            'id_orgao_acesso_externo' => 0
        ];

        if (!empty($extraParams)) {
            foreach ($extraParams as $paramName => $paramValue) {
                $queryString[$paramName] = $paramValue;
            }
        }

        return self::URL_BASE . self::URL_COMPLEMENT . '?' . http_build_query($queryString);
    }

    /**
     * Monta os parâmetros da busca.
     * OBS.: O captcha wqtt parece ser aceito na primeira consulta,
     * caso não funcione será preciso quebrar o captcha da página inicial antes de iniciar a consulta
     *
     * @version 1.0.0
     *
     * <AUTHOR> Santana de Andrade - 13/09/2019
     *
     * @return array
     */
    private function getRequestParameters()
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }
        $criteria = $this->validatedParams['name'];
        $target = $this->validatedParams['target'];

        if ($target === true) {
            $criteria = '"' . utf8_decode($criteria) . '"';
        }

        $requestParams = [
            'txtProtocoloPesquisa' => '',
            'q' => $criteria,
            'txtParticipante' => '',
            'hdnIdParticipante' => '',
            'txtUnidade' => '',
            'hdnIdUnidade' => '',
            'selTipoProcedimentoPesquisa' => '',
            'selSeriePesquisa' => '',
            'txtDataInicio'         => '01/01/2012',
            'txtDataFim'            => date('d/m/Y'),
            'g-recaptcha-response' => $this->captcha,
            'sbmPesquisar' => 'Pesquisar',
            'txtNumeroDocumentoPesquisa' => '',
            'txtAssinante' => '',
            'hdnIdAssinante' => '',
            'txtDescricaoPesquisa' => '',
            'txtAssunto' => '',
            'hdnIdAssunto' => '',
            'txtSiglaUsuario1' => '',
            'txtSiglaUsuario2' => '',
            'txtSiglaUsuario3' => '',
            'txtSiglaUsuario4' => '',
            'hdnSiglasUsuarios' => '',
            'hdnSiglasUsuarios' => '',
            'requiredfields' => '',
            'as_q' => '',
            'hdnFlagPesquisa' => '1'
        ];

        $partialfields = [];
        if (in_array('processos', $this->validatedParams['searchTypes'])) {
            $partialfields[] = 'P';
            $requestParams['chkSinProcessos'] = 'P';
        }
        if (in_array('gerados', $this->validatedParams['searchTypes'])) {
            $partialfields[] = 'G';
            $requestParams['chkSinDocumentosGerados'] = 'G';
        }
        if (in_array('externos', $this->validatedParams['searchTypes'])) {
            $partialfields[] = 'R';
            $requestParams['chkSinDocumentosRecebidos'] = 'R';
        }

        $localTime = "T00:00:00Z";
        $partialfieldsResult = 'sta_prot:' . implode(';', $partialfields);
        $partialfieldsResult .= ' AND dta_ger:[2012-01-01' . $localTime . ' TO ';
        $partialfieldsResult .= date('Y-m-d') . $localTime . ']';

        $requestParams['partialfields'] = $partialfieldsResult;

        return $requestParams;
    }

    private function checkPageHasResults($pageHtml)
    {
        if (
            preg_match('/<div\sclass=.sem-resultado.>Sua\spesquisa\spelo\stermo/is', $pageHtml)
        ) {
            throw new Exception(
                'Sua pesquisa pelo termo não encontrou nenhum processo 
                ou documento correspondente.',
                2
            );
        }
    }

    /**
     * Obtém os resultados e muda de página enquanto encontrar o botão "próxima página"
     *
     * @version 1.0.0
     *
     * <AUTHOR> Santana de Andrade - 13/09/2019
     *
     * @version 1.0.1
     *
     * <AUTHOR> Vidal - 10/09/2020 - Adicionado requisição do reCaptcha para cada página.
     *
     * @param $html
     *
     * @return array
     */
    public function parseResultsByPage($html)
    {
        $results = [];

        // A fonte conta as páginas a partir de um parâmetro chamado ínicio,
        // inicia em 0 e vai incrementando de dez em dez a cada página;
        $pageNumber = 0;

        $nextPageHash = $this->getPageHash($html);

        $params = $this->getRequestParameters();

        do {
            $pageResults = $this->parseResults($html);

            $results = array_merge($results, $pageResults);

            if (!isset($this->validatedParams['limit']) || $this->validatedParams['limit'] == '') {
                $this->validatedParams['limit'] = self::MAX_RESULTS;
            }

            if ($this->qtdProcessos >= $this->validatedParams['limit']) {
                break;
            }

            $pageNumber += 10;

            if (!$nextPageHash) {
                break;
            }

            $url = $this->mountSearchUrl([
                'inicio' => $pageNumber,
                'hash' => $nextPageHash
            ]);

            //Para cada paginação, é necessário captcha
            $this->captcha = $this->resolveCaptcha($html);
            $params = $this->getRequestParameters();

            $html = $this->getResponse($url, 'POST', $params);

            $nextPageHash = $this->getPageHash($html);
        } while ($nextPageHash);

        return $results;
    }

    /**
     * Parse de todos os dados necessários para resposta
     *
     * @param String $result Html da página de lista de processos
     *
     * @version 1.0.0
     * @version 1.1.0 - Jefferson Mesquita - 06/06/2019
     *              Retirar código que pega documento com critério pois não terá mais
     *
     * <AUTHOR>
     *
     * <AUTHOR> Mesquita 18/04/2019
     *
     * @return Array
     */
    private function parseResults($html)
    {
        $links = $this->getProcessesLinks($html);

        $processes = $this->getProcessesNumber($html);

        $data = [];

        foreach ($links[1] as $index => $link) {
            $this->setProxy();

            if ($this->qtdProcessos >= $this->validatedParams['limit']) {
                break;
            }

            $process = $processes[1][$index];

            $details = $this->getDetailData($link, $index, $process);

            $data = array_merge($data, $details);

            $this->qtdProcessos++;
        }

        return $data;
    }

    private function getProcessesLinks($html)
    {
        $linkRegex = '/<td[^>]*?class="resTituloDireita">\s*<a[^>]*?href="(.*?)"/is';

        // $regexError = '/<textarea\sid="txaInfraValidacao"\s.*>(.*?)</is';

        if (!preg_match_all($linkRegex, $html, $matchesLinks)) {
            throw new Exception('Nenhum link encontrado!.', 3);
        }

        return $matchesLinks;
    }

    private function getProcessesNumber($html)
    {
        $processesRegex = '/<td\s*class="resTituloDireita">\s*<a\s*href[^>]*?class="protocoloNormal">' .
            '(.*?)<\/a><\/td>\s*/is';

        if (!preg_match_all($processesRegex, $html, $matchesProcesso)) {
            throw new Exception("Nenhum processo encontrado!", 3);
        }


        return $matchesProcesso;
    }

    /**
     * Verifica se esta na página do processo, se não estiver tenta quebrar o captcha e retornar para ela.
     *
     * @param string $link
     *
     * @return array
     */
    private function getDetailData($link, $index, $processNumber)
    {
        $url = self::URL_PROCESSO . $link;

        $response =  $this->getResponse($url);

        /**
         * Existe uma página de captcha onde o md5 do captcha não está presente.
         * Nela só existe o campo para digitar o captcha.
         * É possível que ela apareça sempre que tentarmos ver uma página de resultado de um processo.
         */
        if (empty($this->getCaptchaMd5($response))) {
            $response = $this->bypassCaptchaPage($url);
        }

        $data[$index][$processNumber]["autuacao"] = Str::toUtf8($this->parseDadosAutuacao($response));
        $data[$index][$processNumber]["protocolos"] = Str::toUtf8($this->parseDadosProtocolos($response));
        $data[$index][$processNumber]["andamentos"] = Str::toUtf8($this->parseDadosAndamentos($response));

        return $data;
    }


    private function bypassCaptchaPage($url)
    {
        $i = 0;
        $this->setCurlOpt([
            CURLOPT_ENCODING => '',
            CURLOPT_TIMEOUT => 60
        ]);
        do {
            $res = $this->getResponse($url);

            $this->breakAndResolveCaptcha($res);

            $html =  $this->getResponse(
                $url,
                "POST",
                [
                'txtCaptcha' => $this->captcha,
                'btnEnviarCaptcha' => 'Enviar'
                ],
                [
                    'Accept: */*',
                    'Content-Type: application/x-www-form-urlencoded',
                    'Accept-Encoding: gzip, deflate, br',
                    'Referer: ' . $url,
                    'Origin: ' . self::URL_BASE,
                    'Connection: Keep-Alive'
                ]
            );

            $i++;

            if ($i > self::MAX_CAPTCHA_BYPASS_RETRIES) {
                throw new Exception(
                    "Máximo de tentativas de quebrar o captcha excedidas. Tente processar novamente!",
                    6
                );
            }
        } while (empty($this->getCaptchaMd5($html)));
        $validaRegex = '/<textarea\sid="txaInfraValidacao"\s.*?>(.*?)</is';

        if (preg_match($validaRegex, $html)) {
            throw new Exception('Falha ao quebrar o captcha entre as consultas.', 3);
        }

        return $html;
    }

    private function getCaptchaUrl($html)
    {
        if (preg_match('/src=.\/infra_js\/infra_gerar_captcha.php\?codetorandom=(\d+-\d+)./s', $html, $matches)) {
            return $matches[1];
        }

        return false;
    }

    public function getCaptchaMd5($html)
    {
        if (preg_match_all('/<input[^>]*?id="hdnCaptchaMd5"[^>]*?value="(.*?)"/s', $html, $matches)) {
            return $matches[1];
        }

        return false;
    }

    /**
     * Parse dos dados de Autuação
     *
     * @param String $result - HTML da página de detalhe
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     *
     * <AUTHOR> Mesquita 18/04/2019
     *
     * @return Array
     */
    private function parseDadosAutuacao($result)
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        $patterns = array(
            'processo' => '/<td.*Processo:<\/td><td>([^>]*)<\/td>/isU',
            'tipo' => '/<td.*tipo:<\/td><td>([^>]*)<\/td>/isU',
            'data_registro' => '/<td.*data\sde\sregistro:<\/td><td>([^>]*)<\/td>/isU',
            'interessados' => '/<td.*interessados:<\/td><td>(.*)<\/td>/isU'
        );

        $dados = [];
        foreach ($patterns as $patternName => $patternValue) {
            try {
                $dados[$patternName] = $this->getParseAutuacao(
                    $patternName,
                    $patternValue,
                    $result
                );

                if (is_string($dados[$patternName])) {
                    $dados[$patternName] = utf8_encode($dados[$patternName]);
                }
            } catch (Exception $e) {
                if ($e->getCode() === 2) {
                    continue;
                }
            }
        }

        return $dados;
    }

    /**
     * Execução do parse de autuação
     *
     * @param String $patternName  Nome
     * @param String $patternValue Regex
     * @param String $result       Html do resultado
     *
     * @version 1.0.0
     * @version 1.0.1 - Jefferson Mesquita - 06/06/2019
     *                  Quando não tiver interessados ou o nome pesquisado não estiver
     *                  entre os interessados mostrar que não teve resultados
     *
     * <AUTHOR>
     *
     * <AUTHOR> Mesquita 18/04/2019
     *
     * @return Array
     */
    private function getParseAutuacao($patternName, $patternValue, $result)
    {
        preg_match($patternValue, $result, $match);

        if (empty($match[1])) {
            throw new Exception('Não foi possível encontrar o ' . $patternName);
        }

        $match = $match[1];

        if ($patternName == 'interessados') {
            $match = explode('<br />', $match);
            foreach ($match as $key => $value) {
                $match[$key] = trim(utf8_encode($value));
                $value = trim($value);
                if (empty($value)) {
                    unset($match[$key]);
                }
            }
        }
        return $match;
    }

    /**
     * Verifica se o critério está entre os interessedos
     *
     * @param String $interessados
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita - 06/06/2019
     *
     * @return Array
     */
    private function isMatchedName($interessados)
    {
        if (preg_match('/' . utf8_encode($this->validatedParams['name']) . '/i', $interessados)) {
            return true;
        }

        $criterio = preg_replace('/[\.-\/\-]/i', '', $this->validatedParams['name']);

        if (preg_match("/{$criterio}/i", utf8_encode($interessados))) {
            return true;
        }

        if (preg_match('/' . $this->validatedParams['name'] . '/i', $interessados)) {
            return true;
        }

        $interessadosEncode = Str::removerAcentos(utf8_encode($interessados));
        $criterio = Str::removerAcentos($this->validatedParams['name']);

        if (preg_match('/' . $criterio . '/i', utf8_encode($interessadosEncode))) {
            return true;
        }

        $interessados = preg_replace('/[\W]/i', '', $interessados);
        $criterio = preg_replace('/[\W]/i', '', $this->validatedParams['name']);
        $criterio = Str::removerAcentos($criterio);
        $interessados = Str::removerAcentos($interessados);

        if (preg_match("/{$criterio}/i", $interessados)) {
            return true;
        }

        if (preg_match("/{$criterio}/i", utf8_encode($interessados))) {
            return true;
        }
        return false;
    }

    /**
     * Parse dos dados de protocolo
     *
     * @param String $result Html do resultado
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     *
     * <AUTHOR> Mesquita 18/04/2019
     *
     * @return Array
     */
    private function parseDadosProtocolos($result)
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        $trs = $this->queryXPath($result, '//*[@id="tblDocumentos"]/tr');

        $protocolos = [];
        foreach ($trs as $key => $tr) {
            if ($key == 0) {
                continue;
            }

            list($td1, $td2, $td3, $td4, $td5, $td6) = $this->queryXPath(
                $tr,
                '//td'
            );

            $pdf_original = $this->getPdfLink($td2);

            $protocolos[] = array(
                'doc_processo' => strip_tags($td2),
                'tipo_doc' => strip_tags(utf8_encode($td3)),
                'data_documento' => strip_tags($td4),
                'data_registro' => strip_tags($td5),
                'unidade' => strip_tags($td6),
                'link_pdf' => $pdf_original
            );

            if (self::MAX_RESULTS == count($protocolos)) {
                break;
            }
        }

        return $protocolos;
    }

    /**
     * Parse dos dados de protocolo
     *
     * @param String $html        Html do resultado
     * @param String $xPath       Query de elementos
     * @param String $forceResult Forçar Resultado
     * @param String $msg         Mensagem
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     *
     * <AUTHOR> Mesquita 18/04/2019
     *
     * @return Array
     */
    protected function queryXPath($html, $xPath, $forceResult = false, $msg = null)
    {
        $doc = new DOMDocument();
        $doc->preserveWhiteSpace = false;
        $doc->loadHTML($html);
        $domXpath = new DOMXPath($doc);

        $entries = $domXpath->query($xPath);
        $response = [];

        if ($entries->length > 0) {
            foreach ($entries as $entry) {
                if ($entry instanceof DOMDocument) {
                    $node = $entry;
                } else {
                    $node = $entry->ownerDocument;
                }

                $innerHTML = '';
                foreach ($entry->childNodes as $child) {
                    $innerHTML .= preg_replace(
                        '/\s+/',
                        ' ',
                        $node->saveHTML($child)
                    );
                }

                $response[] = $innerHTML;
            }
        } else {
            if ($forceResult) {
                file_put_contents("./queryXPath.html", $html);
                $msg = !$msg ? "Erro ao obter tag {$xPath}" : $msg;
                throw new \Exception($msg, 3);
            }
        }
        return $response;
    }

    /**
     * Parse dos dados de Andamentos
     *
     * @param String $result Html do resultado
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     *
     * <AUTHOR> Mesquita 18/04/2019
     *
     * @return Array
     */
    private function parseDadosAndamentos($result)
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        $trs = $this->queryXPath($result, '//*[@id="tblHistorico"]/tr');

        $andamentos = [];
        foreach ($trs as $key => $tr) {
            if ($key == 0) {
                continue;
            }

            list($td1, $td2, $td3) = $this->queryXPath($tr, '//td');

            $andamentos[] = array(
                'data_hora' => strip_tags($td1),
                'unidade' => strip_tags($td2),
                'descricao' => strip_tags(utf8_encode($td3))
            );

            if (self::MAX_RESULTS == count($andamentos)) {
                break;
            }
        }

        return $andamentos;
    }

    /**
     * Pega o Link dos arquivos de cada linha
     *
     * @param String $td Td com o link do Arquivo
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     *
     * <AUTHOR> Mesquita 18/04/2019
     *
     * @return String
     */
    private function getPdfLink($td)
    {
        if (!preg_match('#onclick="window\.open\(\'(.*?)\'\)#is', $td, $link)) {
            throw new Exception('erro ao obter link', 3);
        }

        return $this->getFileLink($link[1]);
    }

    /**
     * Monta URL para arquivo do processo
     *
     * @param String $link link para arquivo do processo
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     *
     * <AUTHOR> Mesquita 18/04/2019
     *
     * @return String
     */
    private function getFileLink($link)
    {
        return self::URL_PROCESSO . $link;
    }

    private function getPageHash($html)
    {
        if (preg_match('@<span\s*class=.pequeno.><a\s*[^>]+hash=(\w+)\'\)">Pr.xima</a>@', $html, $matches)) {
            return $matches[1];
        }

        if (preg_match('@<div[^>]class=.paginas.>.*?<a[^>]+hash=(\w+)\'@', $html, $matches)) {
            return $matches[1];
        }

        return false;
    }
}
