<?php

namespace App\Crawler\AnttRn3;

use Exception;
use App\Helper\Pdf;
use App\Crawler\Spider;
use App\Helper\Document;

class AnttRn3 extends Spider
{
    private const BASE_URL = 'https://consultapublica.antt.gov.br';
    private const FORM_URL = '/Site/ConsultaRNTRC.aspx/ConsultaPublica';

    private $url;
    private $pdfPath = '';
    private $siteKey = '';
    private $cpf_cnpj = '';
    private $captchaToken = '';

    protected function start(): array
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $this->pdfPath = '/tmp/' . uniqid() . '.pdf';

        $this->url = self::BASE_URL . self::FORM_URL;

        $finalResult = $this->search();

        return $finalResult;
    }

    private function search(): array
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $response = $this->loadFirstPage();

        $this->setSiteKey($response);

        $params = $this->getParamsConsulta($response);

        $headers = ['Referer', $url];

        $responseConsulta = $this->getResponse(
            $this->url,
            'POST',
            $params,
            $headers
        );

        if (!$this->resultExists($responseConsulta)) {
            throw new Exception('Nada foi encontrado', 2);
        }

        $arrData = $this->parseData($responseConsulta);

        if ($this->debug) {
            var_dump($arrData);
        }

        $this->downloadPdf($responseConsulta);

        $otherInfos = $this->extractInfosFromPdf();

        return array_merge(
            $arrData,
            $otherInfos
        );
    }

    private function parseData(string $responseConsulta): array
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $rgx = '/<table .*?Corpo_gvResultadoPesquisa[\s\S]+?<tr>([\s\S]+?)<\/tr>/mi';

        if (!preg_match($rgx, $responseConsulta, $match)) {
            throw new Exception("Erro ao pegar os resultados", 3);
        }

        $rgx = '/<td.*?>(.*?)<\/td>/mi';

        preg_match_all($rgx, $match[1], $results);

        if (empty($results[1])) {
            throw new Exception("Erro desconhecido", 1);
        }

        list($municipio, $uf) = explode('/', $results[1][6]);

        $mensagem = '';

        $msg = '/Corpo_lblMsg"><span style="color.*?>([\s\S]+?)<\/span>/mi';
        if (preg_match($msg, $responseConsulta, $match)) {
            $mensagem = $match[1];
        }

        return [
            'transportador' => $results[1][0],
            'cpf_cnpj' => $results[1][1],
            'uf' => $uf,
            'municipio' => $municipio,
            'rntrc' => $results[1][2],
            'situacao' => $results[1][3],
            'data_emissao' => $results[1][4],
            'data_validade' => $results[1][5],
            'mensagem' => strip_tags($mensagem),
        ];
    }

    private function loadFirstPage(): string
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $this->setCurlOpt([CURLOPT_USERAGENT => "Mozilla/4.0 (compatible;)"]);

        return $this->getResponse($this->url, 'GET', [], ['Referer' => $url]);
    }

    private function getParamsConsulta(string $response): array
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $this->captchaToken = $this->getCaptcha();

        $main = 'ctl00$Corpo$updPnlTipoConsulta|ctl00$Corpo$btnConsulta';
        $params = [
            'ctl00$ScriptManagerMain' => $main,
            '__EVENTTARGET' => $this->getInputValue('__EVENTTARGET', $response),
            '__EVENTARGUMENT' => $this->getInputValue('__EVENTARGUMENT', $response),
            '__LASTFOCUS' => $this->getInputValue('__LASTFOCUS', $response),
            '__VIEWSTATE' => $this->getInputValue('__VIEWSTATE', $response, true),
            '__VIEWSTATEGENERATOR' => $this->getInputValue('__VIEWSTATEGENERATOR', $response, true),
            '__EVENTVALIDATION' => $this->getInputValue('__EVENTVALIDATION', $response, true),
            'ctl00$bMostraAlerta' => true,
            'ctl00$Corpo$hfPnlConsulta' => 1,
            'ctl00$Corpo$rbTipoConsulta' => 1,
            'ctl00$Corpo$txtRNTRC' => '',
            'ctl00$Corpo$txtCpfCnpj' => $this->cpf_cnpj,
            'g-recaptcha-response' => $this->captchaToken,
            '__ASYNCPOST' => true,
            'ctl00$Corpo$btnConsulta' => 'Consultar',
        ];

        if ($this->debug) {
            // var_dump($params);
        }

        return $params;
    }

    private function downloadPdf(string $responseConsulta): void
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $main = 'ctl00$Corpo$updPnlTipoConsulta|ctl00$Corpo$btnProtocolo';

        // $this->captchaToken = $this->getCaptcha();

        $params = [
            'ctl00$ScriptManagerMain' => $main,
            'ctl00$bMostraAlerta' => true,
            'ctl00$Corpo$hfPnlConsulta' => 1,
            'ctl00$Corpo$rbTipoConsulta' => 1,
            'ctl00$Corpo$txtRNTRC' => '',
            'ctl00$Corpo$txtCpfCnpj' => '', //$this->cpf_cnpj,
            'g-recaptcha-response' => '', //$this->captchaToken,
            '__EVENTTARGET' => $this->getInputValue('__EVENTTARGET', $responseConsulta),
            '__EVENTARGUMENT' => $this->getInputValue('__EVENTARGUMENT', $responseConsulta),
            '__LASTFOCUS' => $this->getInputValue('__LASTFOCUS', $responseConsulta),
            '__VIEWSTATE' => $this->getInputValue('__VIEWSTATE', $responseConsulta),
            '__VIEWSTATEGENERATOR' => $this->getInputValue('__VIEWSTATEGENERATOR', $responseConsulta),
            '__EVENTVALIDATION' => $this->getInputValue('__EVENTVALIDATION', $responseConsulta),
            '__ASYNCPOST' => true,
            'ctl00$Corpo$btnProtocolo' => 'Imprimir Protocolo'
        ];

        $this->setCurlOpt([CURLOPT_USERAGENT => "Mozilla/4.0 (compatible;)"]);

        $urlPdf = 'https://consultapublica.antt.gov.br/Site/AbrePDF.aspx';

        $response = $this->getResponse(
            $this->url,
            'POST',
            $params,
            ['Referer' => $this->url]
        );

        $pdfResponse = $this->getResponse($urlPdf, 'GET', [], $this->getPdfRequestHeaders());

        @file_put_contents($this->pdfPath, $pdfResponse);
    }

    private function extractInfosFromPdf(): array
    {
        $infos = [
            'categoria' => '',
            'codigo_protocolo' => '',
            'data_hora_consulta' => '',
        ];

        if (!file_exists($this->pdfPath)) {
            return $infos;
        }

        $pdf = new Pdf();

        $text = $pdf->getTextFromPdf($this->pdfPath, [
            'layout',
            'nopgbrk'
        ]);

        @unlink($this->pdfPath);

        $categoria = '/dados do transportador[\s\S]+?Categoria: ?(\S*)/mi';

        if (preg_match($categoria, $text, $categ)) {
            $infos['categoria'] = $categ[1];
        }

        $codigoProtocoloConsulta = '/C.*?digo do Protocolo da Consulta: ?(\S*)/mi';

        if (preg_match($codigoProtocoloConsulta, $text, $codProto)) {
            $infos['codigo_protocolo'] = $codProto[1];
        }

        $dataHoraConsulta = '/Dat(a|e) e Hora da Consulta: ?([\d\/]+? [\d:]+)?/mi';

        if (preg_match($dataHoraConsulta, $text, $matches)) {
            $infos['data_hora_consulta'] = $matches[2];
        }

        return $infos;
    }

    private function getPdfRequestHeaders(): array
    {
        return [
            'Accept' => '*/*',
            'Accept-Encoding' => 'gzip, deflate, br',
            'Accept-Language' => 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7,la;q=0.6',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'Content-Length' => '10291',
            'Content-Type' => 'application/x-www-form-urlencoded; charset=UTF-8',
            //'Cookie' => 'ASP.NET_SessionId=wjniwo4kuuvki4b0qcbjh35q;
             //BIGipServer~externa~pool_appweb_ext=1493326346.0.0000',
            'Host' => 'consultapublica.antt.gov.br',
            'Origin' => self::BASE_URL,
            'Referer' => $this->url,
            'Sec-Fetch-Dest' => 'empty',
            'Sec-Fetch-Mode' => 'cors',
            'Sec-Fetch-Site' => 'same-origin',
            // 'User-Agent' => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36
            // (KHTML, like Gecko) Chrome/86.0.4240.111 Safari/537.36',
            'X-MicrosoftAjax' => 'Delta=true',
            'X-Requested-With' => 'XMLHttpRequest',
        ];
    }

    private function resultExists(string $result): bool
    {
        $pattern = '/Corpo_lblTituloPesquisa.*?Dados do transportador/mi';

        return preg_match($pattern, $result);
    }

    private function setSiteKey(string $response): void
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $pattern = '/class="g-recaptcha".*?data-sitekey="(.*?)">/mi';

        if (!preg_match($pattern, $response, $match)) {
            throw new Exception("Não foi possível recuperar a chave do Captcha", 3);
        }

        $this->siteKey = $match[1];
    }

    private function getCaptcha(): string
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        return $this->solveReCaptcha(
            $this->siteKey,
            self::BASE_URL . self::FORM_URL,
            3
        );
    }

    private function getInputValue(
        string $inputName,
        string $html,
        bool $throwError = false
    ): string {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $regex = '/<input.*?' . $inputName . '.*?value="(.*?)"/mi';

        if (!preg_match($regex, $html, $match)) {
            if ($throwError) {
                throw new Exception($inputName . ' Não encontrado', 3);
            }

            return '';
        }

        return $match[1];
    }

    /**
     *  Valida critério
     *
     *  <AUTHOR>
     *  @version 1.0.0
     *
     *  @return string
     */
    protected function validateAndSetCrawlerAttributes(): void
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (empty($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro de documento inválido');
        }

        $cpf_cnpj = preg_replace(
            '/[^\d]/',
            '',
            (string) $this->param['cpf_cnpj']
        );

        if (!Document::validarCpfOuCnpj($cpf_cnpj)) {
            throw new Exception('Parâmetro de documento inválido');
        }

        $this->cpf_cnpj = $cpf_cnpj;
    }
}
