<?php

namespace App\Crawler\CertidaoCeatTrtCE;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

class CertidaoCeatTrtCE extends Spider
{
    private const BASE_URL = 'https://portaldeservicos.trt7.jus.br';
    private const URL_REQUEST = '/portalservicos/certidaoNegativa/emissaoCertidaoNegativa.jsf';
    private const URL_CAPTCHA = 'https://portaldeservicos.trt7.jus.br/portalservicos/simpleCaptcha.jpg?22';
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const CERTIDAO_S3_PATH = 'captura/certidao_ceat_trt7_ce/';

    public function start()
    {
        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::CERTIDAO_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;

        $pdf = $this->makeRequest();
        $text = $this->savePdfAndReturnText($pdf);
        $data = $this->parseData($text);
        $data['pdf'] = $this->pdf;

        return $data;
    }

    /**
     * Valida o captcha da fonte
     * <AUTHOR> Pereira
     * @return array
     */
    private function validateCaptcha($url)
    {
        $count = false;

        while ($count == false) {
            $this->getImageAndBreakCaptcha($url);

            if (strlen($this->captcha) == 5) {
                return $this->captcha;
            }
        }
    }

    /**
     * Faz a requisição e retorna o pdf
     * <AUTHOR> Pereira
     * @return string
     */
    private function makeRequest()
    {
        $this->getResponse(self::BASE_URL . self::URL_REQUEST);
        $html = $this->getResponse(self::BASE_URL . self::URL_REQUEST);

        preg_match('/ViewState"\svalue="([\s\S]*?)"/', $html, $viewState);

        $params = [
            'AJAXREQUEST' => '_viewRoot',
            'form1' => 'form1',
            'form1:j_id54' => '',
            'form1:pesquisaPor' => 'D',
            $this->form => '',
            'form1:captchaInput' => '',
            'form1:panelStatusAjaxOpenedState' => '',
            'javax.faces.ViewState' => $viewState[1],
            'form1:tipoPesquisa' => $this->tipoPessoa,
            'ajaxSingle' => 'form1:tipoPesquisa',
            'form1:j_id90' => 'form1:j_id90',
        ];

        $html = $this->getResponse(self::BASE_URL . self::URL_REQUEST, 'POST', $params);
        preg_match('/ViewState"\svalue="([\s\S]*?)"/', $html, $viewState);

        $params = [
            'form1' => 'form1',
            'form1:j_id54' => '',
            'form1:pesquisaPor' => 'D',
            'form1:tipoPesquisa' => $this->tipoPessoa,
            $this->form => $this->cpf_cnpj,
            'form1:captchaInput' => $this->validateCaptcha(self::URL_CAPTCHA),
            'form1:emitirCertidao' => 'Emitir Certidão',
            'form1:panelStatusAjaxOpenedState' => '',
            'javax.faces.ViewState' => $viewState[1],
        ];

        $pdf = $this->getResponse(self::BASE_URL . self::URL_REQUEST, 'POST', $params);
        return $pdf;
    }

    /**
     * Retorna o texto do PDF
     * <AUTHOR> Pereira
     * @return string
     */
    private function savePdfAndReturnText($pdf)
    {
        try {
            file_put_contents($this->certificateLocalPath, $pdf);

            $text = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
                'layout',
                'nopgbrk'
            ]);

            (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
            $this->pdf = $this->certificateUrl;
        } catch (Exception $e) {
            throw new Exception("Erro ao capturar a certidão", 6);
        }

        return $text;
    }

    private function parseData($text)
    {
        $patterns = [
            'numCertidao' => ['@Nº:\s(.*)\n@'],
            'codValidacao' => ['@VALIDAÇÃO:\s(.*)\n@'],
            'dataEmissao' => ['@EMISSÃO:\s(.*)\n@'],
            'validacao' => ['@ATÉ:\s(.*)\n@'],
            'descricao' => ['@(Certifica-se[\s\S]*?)\n+OBSER@'],
            'observacoes' => ['@OBSERVAÇÕES:\n+([\s\S]*?)\n+\s+Para\svalidar@']
        ];

        $data = Util::parseDados($patterns, $text);
        $data = array_map("utf8_decode", $data);

        $data['observacoes'] = preg_replace('/(Página.*\d)/', '', $data['observacoes']);

        return $data;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->cpf_cnpj = trim($this->param['cpf_cnpj']);

        if (!Document::validarCpfOuCnpj($this->cpf_cnpj)) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        if (Document::validarCnpj($this->cpf_cnpj)) {
            $this->cpf_cnpj = Document::formatCnpj($this->cpf_cnpj);
            $this->tipoPessoa = 'J';
            $this->form = 'form1:cnpj';
        } elseif (Document::validarCpf($this->cpf_cnpj)) {
            $this->cpf_cnpj = Document::formatCpf($this->cpf_cnpj);
            $this->tipoPessoa = 'F';
            $this->form = 'form1:cpf';
        }
    }
}
