<?php

namespace App\Crawler\MteCertidaoDebito;

use App\Manager\InfoSimplesManager;
use Exception;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Helper\Util;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\Bucket\StaticUplexisBucket;

class MteCertidaoDebito extends Spider
{
    private const BASE_URL = 'http://cdcit.mte.gov.br';
    private const MAIN_URL = '/inter/cdcit/emitir.seam';
    private const DETAIL_URL = '/inter/cdcit/detalhe.seam?cid=';
    private const RETRIES = 2;

    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const MTE_CERTIDAO_DEBITO_S3_PATH = 'captura/mte_certidao_debito/';
    private const MTE_LAMBDA = 'https://lambda.uplexis.com/captura-mte-certidao-debito-prd';
    private const RETRY = 3;

    private $certificateName = '';
    private $certificateLocalPath = '';
    private $certificateS3Path = '';
    private $certificateUrl = '';
    private $manager;

    /**
     *  Valida parâmetros da fonte
     *
     *  <AUTHOR>
     *  Revisão - Maximiliano Minucelli 18/05/2018 - remoção do log
     *  @version 1.0.0
     *
     *  @return string
     *
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        if (
            !isset($this->param['cpf_cnpj']) ||
            empty($this->param['cpf_cnpj']) ||
            !Document::validarCpfOuCnpj($this->param['cpf_cnpj'])
        ) {
            throw new Exception("Critério não informado ou inválido.", 1);
        }

        $this->document = preg_replace('/\D/isu', '', $this->param['cpf_cnpj']);
    }

    /**
     *  Início da execução da fonte MTE - Certidão de Débito (175)
     *
     *  <AUTHOR>
     *  Revisão - Maximiliano Minucelli 18/05/2018 - remoção do log
     *  @version 1.0.0
     *
     *  @return array
     *
     */
    protected function start()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::MTE_CERTIDAO_DEBITO_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;
        $this->manager = new InfoSimplesManager();
        $exception = null;

        $response = $this->callPublicMteProvider($this->document, 0);

        if ($response['status'] == 'success') {
            return $response['data'];
        }

        print PHP_EOL . "Não foi possível recuperar dados através do site, chamando api" . PHP_EOL;

        try {
            $response = $this->manager->searchMteDebitosTrabalhista($this->document);
        } catch (Exception $exception) {
            throw new Exception("Erro ao acessar API, tente reprocessar", 3);
        }

        $pdf = file_get_contents($response['site_receipt']);

        $this->savePdf($pdf);

        return $this->parseDados($response);
    }

    public function callPublicMteProvider($document, $try)
    {
        $headers = ['Content-Type: application/json'];
        $body = [
            "retry" => "1",
            "source" => "MteCertidaoDebito",
            "param" => [
                "criterio" => $document
            ]
        ];

        try {
            print PHP_EOL . "tentativa " . $try . PHP_EOL;
            $this->setCurlOpt([
                CURLOPT_TIMEOUT => 200
            ]);
            $response = $this->getResponse(self::MTE_LAMBDA, 'POST', json_encode($body), $headers);
            if (json_decode($response, true)['status'] == "error") {
                throw new Exception("Fonte publica retornou erro", 3);
            } else {
                return json_decode($response, true);
            }
        } catch (Exception $e) {
            $try++;
            if ($try == self::RETRY) {
                return false;
            }
            return $this->callPublicMteProvider($this->document, $try);
        }
    }

    /**
     *  Pegar os dados do PDF e transformar em array
     *
     *  <AUTHOR>
     *  Revisão - Maximiliano Minucelli 18/05/2018 - remoção do log
     *  @version 1.0.0
     *  Atualização - Pedro Medeiros 07/11/2022 - receber resultado da api como parametro.
     *
     *  @param $apiResponse array
     *  @return array
     *
     */
    private function parseDados($apiResponse)
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }
        $result = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'layout'
        ]);

        $response['nome'] = $apiResponse['empregador'];
        $response['documento'] = $apiResponse['cnpj'];
        $response['expedicao'] = $apiResponse['emissao_datahora'];
        $response['pdf'] = $this->certificateUrl;

        if (empty($apiResponse['cnpj'])) {
            $response['documento'] = $apiResponse['cpf'];
        }

        $patterns = [];

        if (preg_match('@\s*(?P<processo>PROCESSO:.*)@is', $result)) {
            $patterns['conteudo'] = array('@\s*(?P<consta>CERTIF.*?identificado)@is');
        } else {
            $patterns['conteudo'] = array('@\s*(?P<naoconsta>CERTIF.*?identificado)@is');
        }

        $response['conteudo'] = Util::parseDados($patterns, $result)['conteudo'];

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);

        return $response;
    }

    /**
     *  Salva o pdf
     *
     *  <AUTHOR>
     *  Revisão - Maximiliano Minucelli 18/05/2018 - remoção do log
     *  @version 1.0.0
     *
     *  @param string $pdf
     *
     */
    private function savePdf($pdf)
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__ . PHP_EOL;
        }

        if (empty($pdf)) {
            throw new Exception('Falha ao acessar o PDF', 3);
        } elseif (substr($pdf, 0, 4) != '%PDF') {
            throw new Exception('A fonte gerou um formato inválido de certidão.', 3);
        }

        if (!@file_put_contents($this->certificateLocalPath, $pdf)) {
            throw new Exception("Erro ao gravar PDF!", 3);
        }
        if (!@chmod($this->certificateLocalPath, 0777)) {
            throw new Exception("Erro ao mudar permissão do PDF!", 3);
        }
    }
}
