<?php

namespace App\Crawler\ReceitaFederalRestituicaoIRPF;

use Exception;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\ElasticsearchManager;

class ReceitaFederalRestituicaoIRPF extends Spider
{
    private const BASE_URL = 'https://www.restituicao.receita.fazenda.gov.br';
    private const URL_SEARCH = '/servicos-rfb-apprfb-restituicao/apprfb-restituicao/consulta-restituicao';
    private $cpf;

    protected function start()
    {
        $retry = 5;
        while ($retry >= 0) {
            try {
                $result = $this->checkResultEncode($this->search());

                return $result;
            } catch (Exception $e) {
                if (in_array($e->getCode(), array(3, 6, 4, 5))) {
                    throw $e;
                }
                if ($this->debug) {
                    print $e->getMessage() . "\n";
                }
            }

            if ($retry == 0) {
                throw new Exception('Não foi possível recuperar os dados: ' . $e->getMessage());
            }

            $retry--;
        }
    }

    private function checkResultEncode($result): array
    {
        if (!json_encode($result)) {
            foreach ($result as $key => $value) {
                if ($key == "descricao_situacao") {
                    $result[$key] = utf8_encode($value);
                }
            }
        }
        return $result;
    }

    private function search(): array
    {
        date_default_timezone_set('America/Sao_Paulo');
        $exercicio = $this->getYear();
        $birthDate = $this->getBirthDate($this->cpf);

        $params = '/' . $this->cpf . '/' . $exercicio . '/' . $birthDate;
        $url = self::BASE_URL . self::URL_SEARCH . $params;
        $captcha = $this->solveHCaptchaWithCapMonster('1e7b8462-5e38-4418-9998-74210d909134', self::BASE_URL);

        $headers = [
            'Accept: */*',
            'Accept-Encoding: gzip, deflate, br',
            'Accept-Language: pt-BR,pt;q=0.9',
            'aplicativo: RESTITUICAO',
            'Connection: keep-alive',
            'H-Captcha-Response: ' . $captcha,
            'Host: www.restituicao.receita.fazenda.gov.br',
            'origem: web',
            'Referer: ' . 'https://www.restituicao.receita.fazenda.gov.br/',
            'servico: consultar_restituicao',
            'so: WE',
            'token',
            'token_esuite',
            'token_fcm',
            'User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
            'versao_app: 1.0',
            'X-Firebase-AppCheck'
        ];

        $result = $this->chuckNorrisProxy(self::BASE_URL, self::URL_SEARCH . $params, $headers, [], 'GET', true);
        try {
            $this->validaResultado($result);

            $patternHasRestitution = '@dados.*?libera.*?o.*?restitui.*?descritos.*?abaixo@is';

            if (preg_match($patternHasRestitution, $result)) {
                return $this->getDadosComRestituicao($result);
            }

            return $this->getDadosSemRestituicao($result);
        } catch (Exception $e) {
            throw $e;
        }
    }

    private function validaResultado(string $result): void
    {
        # resultado final
        # verifica se consulta excedeu limite de usuarios
        $pattern = '@Consulta\s*n.*?o\s*processada\s*devido\s*ao\s*excesso\s*de\s*usu.*?rios@i';
        $int = preg_match($pattern, $result, $matches);
        if (count($matches) > 0) {
            throw new Exception("Limite de Usuários atingido!");
        }

        # verifica se consulta excedeu limite de usuarios
        $pattern = '@No\s*momento\s*os\s*servidores\s*para\s*consulta\s*est.*?o\s*ocupados.@i';
        $int = preg_match($pattern, $result, $matches);
        if (count($matches) > 0) {
            throw new Exception("Os servidores da Receita estão ocupados. Tente mais tarde.", 6);
        }

        #Código de segurança inválido!
        $pattern = '@C.*?digo\s*de\s*seguran.*?a\s*inv.*?lido@i';
        if (preg_match($pattern, $result, $matches)) {
            throw new Exception("Canal Fechado!", 100);
        }

        # retorno bugado
        if (trim($result) == "!" or trim($result) == "") {
            throw new Exception("Bug!", 100);
        }

        # Erro tela Inicial
        $pattern = '@Imagem\s*com\s*os\s*caracteres\s*anti\s*rob@i';
        if (preg_match($pattern, $result)) {
            throw new Exception("Erro - Volta Tela Inicial!", 100);
        }

        #Erro: CPF inválido. O campo CPF deve ter 11 caracteres e ser numérico
        $pattern = '@CPF\s*inv.*?lido@i';
        if (preg_match($pattern, $result)) {
            throw new Exception("O campo CPF deve ter 11 caracteres e ser numérico", 2);
        }

        # verifica se acesso foi bloqueado
        $pattern = '@seu\s*acesso\s*foi\s*bloqueado@i';
        $int = preg_match($pattern, $result, $matches);
        if (count($matches) > 0) {
            throw new Exception("Acesso bloqueado!", 2);
        }

        # hCaptcha expirado ou inválido.
        $pattern = '@hCaptcha\s*expirado\s*ou\s*inv.*?lido@i';
        $int = preg_match($pattern, $result, $matches);
        if (count($matches) > 0) {
            throw new Exception("hCaptcha expirado ou inválido!", 2);
        }
    }

    private function getDadosComRestituicao(string $html): array
    {
        $result = json_decode($html, true);

        $situacao = $result['restituicao']['situacaoRestituicao'];

        if (!preg_match('/<p>(.*?)\./is', $situacao, $matches)) {
            throw new Exception("Erro ao obter situação", 2);
        }

        $objResult = [
            'contribuinte_cpf' => $this->cpf,
            'contribuinte_nome' => $result['nomeContribuinte'],
            'descricao_situacao' => html_entity_decode($matches[1], null, 'UTF-8'),
            'data_consulta' => date('d/m/Y'),
            'hora_consulta' => date('H:i:s'),
            'banco' => $result['dadosBancarios']['banco'],
            'agencia' => $result['dadosBancarios']['agencia'],
            'lote' => $result['restituicao']['lote'],
            'data_disponibilidade' => $result['restituicao']['dataDisponibilidade'],
            'has_restitution' => 1,
            'html' => base64_encode(gzcompress($html, 9))
        ];

        return $objResult;
    }

    private function getDadosSemRestituicao(string $html): array
    {
        $result = json_decode($html, true);

        $situacao = trim(strip_tags(html_entity_decode($result['situacao'], null, 'UTF-8')));

        $objResult = [
            'contribuinte_cpf' => $this->cpf,
            'contribuinte_nome' => $result['nomeContribuinte'],
            'descricao_situacao' => $situacao,
            'data_consulta' => date('d/m/Y'),
            'hora_consulta' => date('H:i:s'),
            'banco' => '',
            'agencia' => '',
            'lote' => '',
            'data_disponibilidade' => '',
            'has_restitution' => 0,
            'html' => base64_encode(gzcompress($html, 9))
        ];

        return $objResult;
    }

    private function getYear(): int
    {
        $currentMonth = intval(date("m"));

        if ($currentMonth <= 4) {
            return date("Y") - 1;
        }

        return date("Y");
    }

    private function getBirthDate(): string
    {
        $elasticManager = new ElasticsearchManager();

        $data = $elasticManager->search($this->getQueryParams($this->cpf));

        $data = isset($data['hits']['hits'][0])
            ? $data['hits']['hits'][0]['_source']
            : null;

        if (empty($data)) {
            throw new Exception('DataNascimentoDivergente', 1);
        }

        if (empty($data['data_nascimento'])) {
            throw new Exception('DataNascimentoDivergente', 1);
        }

        return $this->parseBirthday($data['data_nascimento']);
    }

    /**
     * Retorna a query para efetuar a busca no elastic search
     *
     * <AUTHOR> Guilherme Sório
     * @param $document
     * @return array
     */
    private function getQueryParams(string $document): array
    {
        return [
            'index' => 'spine_pf',
            'type' => 'pf',
            'body' => [
                'query' => [
                    'match' => [
                        'cpf' => $document
                    ]
                ]
            ]
        ];
    }

    /**
     * Retorna a data de nascimento formatada
     *
     * <AUTHOR> Guilherme Sório
     * @param $birthday
     * @return string
     */
    private function parseBirthday(string $birthday): string
    {
        $year = substr($birthday, 0, 4);
        $month = substr($birthday, 4, 2);
        $day = substr($birthday, 6, 2);

        return "{$year}{$month}{$day}";
    }

    protected function validateAndSetCrawlerAttributes(): void
    {
        if (empty($this->param['cpf'])) {
            throw new Exception('Campo cpf obrigatório', 1);
        }

        if (!Document::validarCpf($this->param['cpf'])) {
            throw new Exception('Cpf inválido', 1);
        }

        $this->cpf = preg_replace('/[^\d]/', '', $this->param['cpf']);
    }
}
