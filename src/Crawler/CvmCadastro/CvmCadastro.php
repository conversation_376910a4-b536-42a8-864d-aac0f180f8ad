<?php

namespace App\Crawler\CvmCadastro;

use App\Helper\Util;
use App\Helper\Document;
use App\Crawler\Spider;
use App\Crawler\CvmCadastro\Models\CvmModel;
use Exception;

class CvmCadastro extends Spider
{
    private const URL_BASE = 'http://sistemas.cvm.gov.br/';
    private const CAPTCHA_URL = 'http://sistemas.cvm.gov.br/asp/cvmwww/captcha/aspcaptcha.asp';

    private $documento;
    private $cnpj;
    private $cpf;
    private $nome;
    private $url;
    private $razao_social;
    private $count = 15;
    private $html;
    private const RETRY = 5;

    public function start()
    {
        $result = [];
        $this->setProxy();
        $result = $this->requestForm();
        return $result;
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['cpf_cnpj'])) {
            throw new Exception("Nenhum critério de busca informado!", 1);
        }

        if (!Document::validarCpfOuCnpj($this->param['cpf_cnpj'])) {
            throw new Exception('Documento não é um CPF ou CNPJ válido', 1);
        }

        $this->documento = Document::removeMask($this->param['cpf_cnpj']);
    }

    private function requestForm()
    {
        $params = [
            'NrPfPj' => $this->documento,
            'Rzsoc' => '',
            'Tipo_Partic' => '',
            'strCAPTCHA' => ''
        ];

        $retry = 0;

        do {
            try {
                $exception = null;

                $this->setProxy();
                $this->convertImageBmpToPngFormatAndBreakCaptcha(self::CAPTCHA_URL);

                $params['strCAPTCHA'] = $this->captcha;
                $response = $this->getResponse(
                    self::URL_BASE . 'asp/cvmwww/cadastro/CadListPartic.asp?' . http_build_query($params)
                );

                $this->checkResponse($response);
                $link = $this->getLinkEmpresa($response);
                $details = $this->getDetails($link);
                $obj = $this->getDados($details);

                break;
            } catch (Exception $e) {
                $exception = $e->getMessage();
                $retry++;
                if ($e->getCode() == 2) {
                    throw $e;
                }
                continue;
            }
        } while ($retry < self::RETRY);

        if ($exception) {
            throw new Exception($exception, 3);
        }

        if (!($obj instanceof CvmModel)) {
            throw new Exception("Não foi possível finalizar a consulta, tente novamente", 1);
        }

        return $obj;
    }

    private function getDados($details)
    {
        if (preg_match("@500\s\-\sInternal\sserver\serror@is", $details)) {
            return -1;
        }

        $cvmObject = new CvmModel();
        $cvmObject->html = $details;
        $cvmObject->cnpj = $this->cnpj;
        $cvmObject->razao_social = utf8_encode(trim($this->razao_social));
        $result_final = array();

        $pattern = '@DADOS\s*CADASTRAIS\s*DE\s*PREST\.\s*SERVI.*?OS\s*DE\s*ADMINISTRA.*?O\s*DE\s*
                    CARTEIRAS|DADOS\s*CADASTRAIS\s*DE\s*CIAS\s*ABERTAS@is';

        $patternPf = '@DADOS\s*CADASTRAIS\s*DE\s*AGENTES\s*AUT\W+NOMOS@is';

        $testPj = (preg_match($pattern, $details));
        $testPf = (preg_match($patternPf, $details));

        if ($testPf || $testPj) {
            $type = $testPj == 1 ? 'pj' : 'pf';
            $result_final['cvm_cadastro'] = $this->parsePadrao($details, $cvmObject, $type);

            return $this->parseResultToObject($result_final['cvm_cadastro'], $cvmObject);
        }

        $result_final = $this->verifyPattern($details);

        if (!empty($result_final)) {
            return $this->parseResultToObject($result_final, $cvmObject);
        }

        throw new Exception('Não existe resultado para o critério informado', 2);
    }

    private function verifyPattern($details)
    {
        $result_final = array();

        if (preg_match('@DADOS CADASTRAIS\s*DE\s*FUNDOS\s*DE\s*INVESTIMENTO@is', $details)) {
            $result_final['fundo_investimento'] = $this->parseFundosInvestimentos($details);

            $arrLinks = array(
                'custodiante' => 'custodiantes_link',
                'gestor_carteira' => 'gestor_carteira_link',
                'prestador_servico_page' => 'link_administrador',
                'auditores_independentes' => 'auditores_independentes_link',
                'distribuidor_cotas' => 'distrbuidor_cotas_link'
            );

            foreach ($arrLinks as $key => $link) {
                if (
                    isset($result_final['fundo_investimento'][$link])
                    && !empty($result_final['fundo_investimento'][$link])
                ) {
                    $result_final[$key] = $this->getSubResultData(
                        self::URL_BASE . $result_final['fundo_investimento'][$link],
                        $key
                    );
                }
            }
        }

        if (preg_match('@<table\s*id="TbMain"[^>]*>@is', $details)) {
            $result_final['fundo_investimento'] = $this->parseTabMain($details);
            $result_final['fundo_investimento']['endereco'] = strip_tags(
                $result_final['fundo_investimento']['endereco']
            );
        }

        if (preg_match('@<table\s*id="tabAtivos"[^>]*>@is', $details)) {
            $result_final['fundo_investimento'] = $this->parseTabAtivos($details);
            $result_final['fundo_investimento']['endereco'] = strip_tags(
                $result_final['fundo_investimento']['endereco']
            );
        }

        return $result_final;
    }

    private function getDetails($link)
    {
        $query_string = $link;

        if (!preg_match('@strCAPTCHA@is', $link)) {
            $query_string = "strCAPTCHA={$this->captcha}&$link";
        }

        $url = self::URL_BASE . "/asp/cvmwww/cadastro/RedirCad.asp?$query_string";
        $this->url = $url;

        $response = $this->getResponse($url);
        $this->html = $response;
        return $response;
    }

    private function getLinkEmpresa($result)
    {
        $pattern = '@<p.*?Msg.*?>(.*?)</p>@is';

        if (preg_match_all($pattern, $result, $matches)) {
            throw new Exception(utf8_encode(trim($matches[1][0])), 2);
        }

        $pattern = "@CLASS='MenuItemP'>(.*?)<TR\s*CLASS@is";
        if (!preg_match($pattern, $result, $matches)) {
            throw new Exception('Erro ao recuperar nome', 100);
        }

        if (strlen($this->documento) > 11) {
            $this->cnpj = $this->documento;
            $this->razao_social = $matches[1];
        } else {
            $this->cpf = $this->documento;
            $this->nome = $matches[1];
        }

        $pattern = '@<A\s*HREF=\'.*?(Tipo_Partic=.*?)\'[^>]*?>(.*?)<TR[^>]*?><TD[^>]*?>@';
        $cancelado = true;

        if (preg_match_all($pattern, $result, $matches)) {
            foreach ($matches[2] as $key => $desc) {
                if (!preg_match('@CANCELADA@', $desc)) {
                    $cancelado = false;
                    return str_replace(' ', '', $matches[1][$key]);
                }
            }

            if ($cancelado == true) {
                return str_replace(' ', '', $matches[1][0]);
            }
        }

        return false;
    }

    private function getImageCaptcha()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        file_put_contents($this->captcha_path, $this->getResponse(self::CAPTCHA_URL));
    }

    private function parseResultToObject($arrResult, $cvmObject)
    {
        foreach ($arrResult as $key => $value) {
            $cvmObject->{$key} = preg_replace('@\s+@', ' ', $value);
        }

        if (is_object($arrResult)) {
            $arrResult = $arrResult->html;
        }

        $cvmObject->url = $this->url;
        $cvmObject->html = $this->compactaHtml($this->html);

        return $cvmObject;
    }

    private function compactaHtml($html)
    {
        return base64_encode(gzcompress($html, 9));
    }

    private function parsePadrao($result, $cvmObject, $type)
    {
        $aErro = array();

        $patterns = array(
            'razao_social' => ['@<tr.CLASS=.BodyPB.><td[^>]*>(.*?)<\/td>@is'],
            'nome_comercial' => ['@<td>Denominaç.*?\s?\w+[^>]*><td>[^>]*><td>(.*?)<\/td>@is', null],
            'cnpj' => ['@<td>CNPJ[^>]*><td>[^>]*><td>(.*?)<\/td>@is'],
            'endereco' => ['@<td>Endere.*?o[^>]*><td>[^>]*>.[^>]*><td>(.*?)<\/td>@is'],
            'bairro' => ['@<td>Bairro[^>]*><td><b>.<\/b>[^>]*><td>(.*?)<\/td>@is',null],
            'cidade' => ['@<td>Cidade<\/td><td>[^>]*><td>(.*?)<\/td>@is'],
            'uf' => ['@<td>Uf<\/td><td>[^>]*><td>(.*?)<\/td>@is'],
            'cep' => ['@<td>Cep<\/td><td>[^>]*><td>(.*?)<\/td>@is'],
            'ddd' => ['@<td>ddd<\/td><td>[^>]*><td>(.*?)<\/td>@is'],
            'telefone' => ['@<td>tel<\/td><td>[^>]*><td>(.*?)<\/td>@is'],
            'fax' => ['@<td>fax<\/td><td>[^>]*><td>(.*?)<\/td>@is'],
            'data_registro' => ['@<td>Data\sde\sRegistro<\/td><td>[^>]*><td>(.*?)<\/td>@is'],
            'codigo_cvm' => ['@<td>C.?digo CVM<\/td><td>[^>]*><td>(.*?)<\/td>@is',null],
            'diretor_relacoes' => ['@<td><font>Diretor[^>]*><td>.*?[^>]+>.*?<font>(.*?)<\/td>@is',null],
            'endereco_diretor' => ['@<td>Endere.?o\s?\(\w+\)[^>]*><td>[^>]*><td>(.*?)<\/td>@is',null],
            'uf_diretor' => ['@<td>Uf\s?\(\w+\)[^>]*><td>[^>]*><td>(.*?)<\/td>@is',null],
            'cep_diretor' => ['@<td>Cep\s?\(\w+\)[^>]*><td>[^>]*><td>(.*?)<\/td>@is',null],
            'ddd_diretor' => ['@<td>ddd\s?\(\w+\)[^>]*><td>[^>]*><td>(.*?)<\/td>@is',null],
            'telefone_diretor' => ['@<td>tel\s?\(\w+\)[^>]*><td>[^>]*><td>(.*?)<\/td>@is',null],
            'fax_diretor' => ['@<td>fax\s?\(\w+\)[^>]*><td>[^>]*><td>(.*?)<\/td>@is',null],
            'mercado' => ['@<td>mercado[^>]*><td>[^>]*><td>(.*?)<\/td>@is',null],
            'atividade' => ['@<td>atividade[^>]*><td>[^>]*><td>(.*?)<\/td>@is',null],
            'auditor' => ['@<td>Auditor<\/td>.*?<a\s.*?>(.*?)<\/a>@is',null],
            'cgc_auditor' => ['@<td>CGC\s*do\s*Auditor[^>]*><td>[^>]*><td>(.*?)<\/td>@is',null],
            'controle_acionario' => ['@<td>Controle\s?\w+?[^>]*><td>[^>]*><td>(.*?)<\/td>@is',null],
            'situacao' => ['@<td>Situ\W+o[^>]*><td>[^>]*><td>(.*?)<\/td>@is'],
            'data_situacao' => ['@<td>Data\s?Situa.*?o[^>]*><td>[^>]*><td>(.*?)<\/td>@is', null]
        );

        if ($type == 'pf') {
            $patterns['situacao'] = [
                '@>Situ[\W\d\w]+<b>:</b></TD><TD><FONT\s*FACE=[\'\w]+\s*SIZE=2>([\w\s]*)</td>@is',
                null
            ];
            $patterns['data_registro'] = [
                '@>Data\s+de\s+Registro\<\/td><td><FONT[\s\W\d\w]+><b>:<\/b></TD><TD><FONT[\s\W\d\w]+>([\d\/]+)@is',
                null
            ];

            $cvmObject->nome = $this->nome;
            $cvmObject->cpf = $this->cpf;
        }

        if (preg_match('@Prestador\s*de\s*servi.*?os@', $result)) {
            $patterns['prestador_servicos'] = array(
                '@<TR><TD>Prestador\s*de\s?ser\w+?</TD><TD>:</TD><TD><[A|a][^>]*?>(.*?)\s*</[A|a]></TD></TR>@is'
            );
        }

        $dados = Util::parseDados($patterns, $result);

        foreach ($dados as $key => $value) {
            $cvmObject->$key = $value;
        }

        $cvmObject->url = $this->url;

        return $cvmObject;
    }

    private function parseFundosInvestimentos($result)
    {
        $patterns = array(
            'razao_social' => ['@<td .*? class=.BodyPB.>(.*?)<\/td>@is', null],
            'cnpj' => ['@<td>CNPJ[^>]*><td>.[^>]*><td>(.*?)<\/td>@is', null],
            'situacao' => ['@<td>Situa.*?o\s?\w+[^>]*><td>.[^>]*><td>(.*?)<\/td>@is'],
            'data_situacao' => ['@<td>Data de in.*?[\s\w+][^>]*><td>.[^>]*><td>(.*?)<\/td>@is'],
            'patrimonio_liquido' => ['@<td>Patri.*?[^>]*><td>.[^>]*><td>(.*?)<\/td>@is', null],
            'data_patrimonio_liquido' => ['@<td>data\s?Patri.*?[^>]*><td>.[^>]*><td>(.*?)<\/td>@is'],
            'classe' => ['@Classe[<td><\/td>:\str]+<td>(.*?)<\/td>@is'],
            'indicador_desempenho' => ['@Indicador de Desempenho[<td><\/td>:\str]+<td>(.*?)<\/td>@is'],
            'fundo_condominio' => ['@Forma\s*?de\s*?Condom[^>]nio[<td><\/td>:\str]+<td>\s*(.*?)\s*<\/td>@is'],
            'fundo_exclusivo' => ['@fundo[^>]*?cotas[<td><\/td>:\str]+<td>\s*(.*?)\s*<\/td>@is'],
            'tratamento_tributario' => ['@tratamento[^>]*?prazo[<td><\/td>:\str]+<td>\s*(.*?)\s*<\/td>@is'],
            'destinado_investidores_qualificados' => ['@<td>destinado\s?\w+?[^>]*><td>.[^>]*><td>(.*?)<\/td>@is'],
            'administrador' => ['@administrador[<td><\/td>:\str]+<td>(?:<a[^>]*[\s*]?>\s*(.*?)|)<\/a>@is'],
            'diretor_responsavel' => ['@diretor\s*Respons[^>]*?vel[<td><\/td>:\str]+<td>\s*(.*?)\s*<\/td>@is'],
            'link_administrador'    => ['@administrador[<>\/\A-ÿ:\s\w]*?a\s*href=[\'"](.*?)[\'"]@is', null],
            'auditores_independentes' => ['@auditores[<td><\/td>:\str]+<td>(?:<a[^>]*[\s*]?>\s*(.*?)|)<\/a>@is', null],
            'auditores_independentes_link' => ['@auditores[<>\/\A-ÿ:\s\w]*?a\s*href=[\'"](.*?)[\'"]@is', null],
            'distribuidor_cotas' => ['@<td>distribuidor\s?\w+?[^>]*><td><a.*?>(.*?)<\/a>@is', null],
            'distrbuidor_cotas_link' => ['@<td>DISTRIBUIDOR\s*?\w+?[<>\/\A-ÿ:\s\w]*?a\s*href=[\'"](.*?)[\'"]@is', null],
            'custodiantes' => ['@<td>Custodiantes\s.*?<\/td><td><a.*?>(.*?)<\/a>@is', null],
            'custodiantes_link' => ['@<td>Custodiantes\s.*?<\/td><td><a href=[\'"](.*?)[\'"]@is', null],
            'servico_tesouraria' => ['@<td>servi.*?\s?\w+[^>]*><i><font\s.*?>(.*?)<\/font>.*?<td>(.*?)<\/td>@is', null],
            'controle_valores_imobiliarios' => ['@<td>controle\s?\w+[^>]*>.*?<td>(.*?)<\/td>@is', null],
            'gestor_carteira' => ['@<td>gestor\s?da\s?cart\w+?[^>]*>.*?<a.*?>(.*?)<\/a>@is', null],
            'gestor_carteira_link' => ['@gestor\s*?da\s*?carteira[<>\/\A-ÿ:\s\w]*?a\s*href=[\'"](.*?)[\'"]@is', null]
        );

        $response = Util::parseDados($patterns, $result);
        return array_map("strip_tags", $response);
    }

    private function parseAuditores($result)
    {
        $patterns = array(
            'razao_social' => array('@<font><b>(.*?)<\/b>@is', null),
            'endereco' => array('@<font>Endere[^>]*?o<\/td>.*?<font>(.*?)<\/td>@is', null),
            'cidade' => array('@cidade\s*?[\w:<>\/]*>\s*(.*?)\s*?<@is', null),
            'uf' => array('@<font>uf\s*?[<td><\/td><b><font>:\s]*(.*?)<\/td>@is', null),
            'cep' => array('@<font>cep\s*?[<td><\/td><b><font>:\s]*(.*?)<\/td>@is', null),
            'ddd' => array('@<font>ddd\s*?[<td><\/td><b><font>:\s]*(.*?)<\/td>@is', null),
            'tel' => array('@<font>tel\s*?[<td><\/td><b><font>:\s]*(.*?)<\/td>@is', null),
            'fax' => array('@<font>fax\s*?[<td><\/td><b><font>:\s]*(.*?)<\/td>@is', null),
            'cnpj' => array('@<font>cnpj\s*?[<td><\/td><b><font>:\s]*(.*?)<\/td>@is', null),
            'data_registro' => array('@<font>data\s?de\s?\w+?.*?<font>(.*?)<\/td>@is', null),
            'patrimonio_liquido' => array(
                '@(?:<td>|<font>)\s*patrimonio l[^<]*quido\s*?[<td><\/td><b><font>:\s]*(.*?)\s*?<@is',
                null
            ),
            'dt_patrimonio_liquido' => array('@<font>patrimonio\s.*?<\/td>.*?<font>(.*?)<\/td>@is'),
            'email' => array('@e[^m]mail[\s=\'"%\d\w<tr>:\/]*?<font>\s*?(.*?)\s*?<\/td>@is'),
            'dt_registro' => array('@data[^r]*registro[\s=\'"%\d\w<tr>:\/]*?<font>(.*?)<@is'),
            'codigo_cvm' => array('@(?:<td>|<font>)\s*?c[^cv]*?digo\s*cvm\s*?[<td><\/td><b><font>:\s]*(.*?)\s*?<@is')
        );

        return Util::parseDados($patterns, $result);
    }

    private function parsePrestadorServico($result)
    {
        $patterns = array(
            'razao_social' => array(
                '@><\/TABLE><\/TD><\/TR><\/TABLE><TABLE[\s=\'"%\d\w<tr>]+>\s*(.*?)\s*<\/td>@is',
                null
            ),
            'nome_comercial' => array('@<TR><TD>Denomina.*?o\s*Comercial</TD><TD>:</TD><TD>(.*?)</TD></TR>@is', null),
            'cnpj' => array('@CNPJ[\s=\'"%\d\w<tr>:\/]+<td>\s*(.*?)<\/td>@is', null),
            'endereco' => array('@Endere[^>]*?o[\s=\'"%\d\w<tr>:\/]+<td>\s*(.*?)\s*<\/td>@is', null),
            'cidade' => array('@cidade[\s=\'"%\d\w<tr>:\/]+<td>\s*(.*?)\s*<\/td>@is'),
            'uf' => array('@<td>\s*?uf.*?[\s<TD>:<\/TD>]+<td>\s*(.*?)\s*<\/td>@is'),
            'cep' => array('@<td>\s*?cep[\s=\'"%\d\w<tr>:\/]+<td>\s*(.*?)\s*<\/td>@is'),
            'ddd' => array('@<td>\s*?ddd[\s<td>:\/]+<td>\s*(.*?)\s*<\/td>@is'),
            'telefone' => array('@<td>\s*?tel[\s<td>:\/]+<td>\s*(.*?)\s*<\/td>@is'),
            'fax' => array('@<td>\s*?fax[\s<td>:\/]+<td>\s*(.*?)\s*<\/td>@is'),
            'data_registro' => array('@<td>\s*?Data\s*?de\s*?Registro[\s<td>:\/]+<td>\s*(.*?)\s*<\/td>@is'),
            'codigo_cvm' => array('@(?=cvm\s*\d)cvm\s*(\d.*?)\s*<\/@is',null),
            'diretor_administracao' => array(
                '@<td>\s*?diretor\s*?administra[^<]*?[\s<td>:\/]+<td>\s*(.*?)\s*<\/td>@is',
                null
            ),
            'diretor_distribuicao' => array(
                '@<td>\s*?diretor\s*?DISTRIBUI[^<]*?[\s<td>:\/]+<td>\s*(.*?)\s*<\/td>@is',
                null
            ),
            'situacao' => array('@<td>\s*?situa[^<]*?[\s<td>:\/]+<td>\s*(.*?)\s*<\/td>@is'),
            'categoria' => array('@<td>\s*?categoria[^<]*?[\s<td>:\/]+<td>\s*(.*?)\s*<\/td>@is'),
            'data_situacao' => array(
                '@<TR><TD>(?:Data\s*Situa.*?o)</TD><TD>:</TD><TD>(.*?)</TD></TR>@is',
                null
            ),
            'website' => array('@<td>\s*?Website[^<]*?[\s<td>:\/]+<td>\s*(.*?)\s*<\/td>@is', null)
        );

        return Util::parseDados($patterns, $result);
    }

    private function parseCustodiantes($result)
    {
        $patterns = array(
            'razao_social' => array('@<font>\s*?<h3>\s*?(.*?)\s*<\/h3>@is', null),
            'endereco' => array('@<font>Endere.*?o.*?<font>(.*?)<\/td>@is', null),
            'bairro' => array('@bairro\s*?[\w:<>\/]*>\s*(.*?)\s*?<@is', null),
            'nome_comercial' => array('@<TR><TD>Denomina.*?o\s?w+?</TD><TD>:</TD><TD>(.*?)<@is', null),
            'cidade' => array('@cidade\s*?[\w:<>\/]*>\s*(.*?)\s*?<@is', null),
            'uf' => array('@<font>uf\s*?[<td><\/td><b><font>:\s]*(.*?)<\/td>@is', null),
            'cep' => array('@<font>cep\s*?[<td><\/td><b><font>:\s]*(.*?)<\/td>@is', null),
            'ddd' => array('@<font>ddd\s*?[<td><\/td><b><font>:\s]*(.*?)<\/td>@is', null),
            'tel' => array('@<font>tel\s*?[<td><\/td><b><font>:\s]*(.*?)<\/td>@is', null),
            'fax' => array('@<font>fax\s*?[<td><\/td><b><font>:\s]*(.*?)<\/td>@is', null),
            'cnpj' => array('@<font>cnpj\s*?[<td><\/td><b><font>:\s]*(.*?)<\/td>@is', null),
            'data_registro' => array('@<font>data\s.*?<font>(.*?)<\/td>@is', null),
            'diretor' => array('@<font>Diretor.*?<font>(.*?)<\/td>@is', null),
            'patrimonio_liquido' => array('@<font>patrimonio l[\W\w]+?<font>(.*?)<\/td>@is', null),
            'dt_patrimonio_liquido' => array('@<font>data patrimonio l[\W\w]+?<font>(.*?)<\/td>@is'),
            'situacao' => array('@<font>situa.*?o.*?<font>(.*?)<\/td>@is', null)
        );

        return Util::parseDados($patterns, $result);
    }

    private function parsePrestadoresServicoAndAdmCarteiras($result)
    {
         $patterns = array(
            'razao_social' => array('@<td.*?class=.BodyPB.>(.*?)<\/td>@is', null),
            'nome_comercial' => array('@<td>Denomina.*?o\s?\w+?<\/td><td>[^>]*><td>(.*?)<\/td>@is', null),
            'cnpj' => array('@<td>cnpj[<\/td>:]*(.*?)<\/td>@is'),
            'endereco' => array('@<td>Endere.*?o[<\/td>:]*(.*?)<\/td>@is', null),
            'cidade' => array('@<td>Cidade[<\/td>:]*(.*?)<\/td>@is'),
            'uf' => array('@<td>uf[<\/td>:]*(.*?)<\/td>@is'),
            'cep' => array('@<td>cep[<\/td>:]*(.*?)<\/td>@is'),
            'ddd' => array('@<td>ddd[<\/td>:]*(.*?)<\/td>@is'),
            'telefone' => array('@<td>tel[<\/td>:]*(.*?)<\/td>@is'),
            'fax' => array('@<td>fax[<\/td>:]*(.*?)<\/td>@is'),
            'data_registro' => array('@<td>data\s?de\s?\w+[<\/td>:]*(.*?)<\/td>@is'),
            'codigo_cvm' => array('@(?=cvm\s*\d)cvm\s*(\d.*?)\s*<\/@is',null),
            'diretor_administracao' => array('@<td>\s*?diretor\s*?\w+?[\s<td>:\/]+<td>\s*(.*?)\s*<\/td>@is', null),
            'diretor_distribuicao' => array('@<td>\s*?diretor\s*?\w+?[^<]*?[\s<td>:\/]+<td>\s*(.*?)\s*<\/td>@is', null),
            'situacao' => array('@<td>\s*?situa[^<]*?[\s<td>:\/]+<td>\s*(.*?)\s*<\/td>@is'),
            'categoria' => array('@<td>\s*?categoria[^<]*?[\s<td>:\/]+<td>\s*(.*?)\s*<\/td>@is'),
            'data_situacao' => array('@<TR><TD>(?:Data\s*Situa.*?o)</TD><TD>:</TD><TD>(.*?)</TD></TR>@is', null),
            'website' => array('@<td>\s*?Website[^<]*?[\s<td>:\/]+<td>\s*(.*?)\s*<\/td>@is', null),
            'diretor_gestor_carteira' => array('@<td>Diretor[\s<td>:\/]+<td>\s*(.*?)\s*<\/td>@is')
         );

         return Util::parseDados($patterns, $result);
    }

    private function parseTabMain($result)
    {
        $patterns = array(
            'razao_social' => array('@<td[^>]*>\s*raz.o\s*social\s*do\s*fundo\s*</td>\s*<td[^>]*>(.*?)<\/td>@is', null),
            'situacao' => array('@<td[^>]*>\s*raz.o\s*social\s*da\s*administradora\s*<td[^>]*>(.*?)<\/td>@is'),
            'administrador' => array('@<td[^>]*>\s*diretor\s*do\s*fundo\s*</td>\s*<td[^>]*>(.*?)<\/td>@is'),
            'diretor_responsavel' => array('@<td[^>]*>\s*diretor\s*do\s*fundo\s*</td>\s*<td[^>]*>(.*?)<\/td>@is'),
        );

        return Util::parseDados($patterns, $result);
    }

    private function parseTabAtivos($result)
    {
        $patterns = array(
            'razao_social' => array('@<span\s*id="lbNmDenomSocial"\s*>(.*?)<\/span>@is', null),
            'cnpj' => array('@<span\s*id="lbNrPfPj"\s*>(.*?)<\/span>@is', null),
            'situacao' => array('@<span\s*id="lbSitDesc"\s*>(.*?)<\/span>@is'),
            'data_situacao' => array('@<span\s*id="lbDtFunc"\s*>(.*?)<\/span>@is'),
            'patrimonio_liquido' => array('@<span\s*id="lbVlPl"\s*>(.*?)<\/span>@is', null),
            'data_patrimonio_liquido' => array('@<span\s*id="lblDtPl"[^>]*>(.*?)<\/span>@is'),
            'administrador' => array('@<span\s*id="lbNmDenomSocialAdm"[^>]*>(.*?)<\/span>@is'),
            'diretor_responsavel' => array('@<span\s*id="lbDirFdo"[^>]*>(.*?)<\/span>@is'),
            'gestor_carteira' => array('@<span\s*id="lbNmGestFdo"[^>]*>(.*?)<\/span>@is'),
        );

        return Util::parseDados($patterns, $result);
    }

    private function getSubResultData($url, $indexResult)
    {
        do {
            $response = $this->getResponse($url);
            if ($captcha = $this->checkIfTheresCaptchaInResponse($response)) {
                $url = preg_replace('/CAPTCHA=[\d]*/i', "CAPTCHA={$captcha}", $url);
                $response = $this->getResponse($url);
                sleep(.5);
            }
        } while ($captcha && $this->count-- > 0);

        switch ($indexResult) {
            case 'prestador_servico_page':
                return $this->parsePrestadorServico($response);
            break;
            case 'custodiante':
                return $this->parseCustodiantes($response);
            break;
            case 'gestor_carteira':
                return $this->parsePrestadoresServicoAndAdmCarteiras($response);
            break;
            case 'auditores_independentes':
                return $this->parseAuditores($response);
            break;
            case 'distribuidor_cotas':
                return $this->parseCustodiantes($response);
            break;
        }
    }

    private function checkIfTheresCaptchaInResponse($response)
    {
        if (preg_match('@digite\s*?o\s*?n[^me]*?mero\s*?que\s*?aparece@is', $response)) {
            return $this->getImageAndBreakCaptcha(self::CAPTCHA_URL);
        }

        return false;
    }

    private function checkResponse($response)
    {
        $pattern = '@Digite\s*o\s*n.*?mero\s*que\s*aparece\s*abaixo@is';
        if (preg_match($pattern, $response, $matches)) {
            throw new Exception('Captcha incorreto', 100);
        }
    }

    private function convertImageBmpToPngFormatAndBreakCaptcha($url)
    {
        $img = $this->getResponse($url);
        $this->captcha_path = $this->changeExtensionCaptchaPath($this->captcha_path, 'bmp');
        file_put_contents($this->captcha_path, $img);
        $img = imagecreatefrombmp($this->captcha_path);
        $this->captcha_path = $this->changeExtensionCaptchaPath($this->captcha_path, 'png');
        if (imagepng($img, $this->captcha_path)) {
            try {
                $this->breakCaptcha();
            } catch (\Exception $e) {
                throw new Exception('Erro ao quebrar captcha: ' . $e->getMessage(), 2);
            }
        }
    }

    private function changeExtensionCaptchaPath($captchaPath, $ext)
    {
        $newPath = explode('.', $captchaPath);
        $newPath[1] = $ext;
        return implode('.', $newPath);
    }
}
