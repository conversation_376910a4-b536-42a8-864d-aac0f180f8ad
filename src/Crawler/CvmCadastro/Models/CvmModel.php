<?php

namespace App\Crawler\CvmCadastro\Models;

class CvmModel
{
    public $razao_social;
    public $nome_comercial;
    public $cnpj;
    public $cpf;
    public $nome;
    public $prestador_servicos;
    public $endereco;
    public $bairro;
    public $cidade;
    public $uf;
    public $cep;
    public $ddd;
    public $telefone;
    public $fax;
    public $data_registro;
    public $codigo_cvm;
    public $diretor_relacoes;
    public $endereco_diretor;
    public $uf_diretor;
    public $cep_diretor;
    public $ddd_diretor;
    public $telefone_diretor;
    public $fax_diretor;
    public $mercado;
    public $atividade;
    public $auditor;
    public $cgc_auditor;
    public $controle_acionario;
    public $situacao;
    public $data_situacao;
    public $url;
    public $html;
    public $fundo_investimento;
    public $custodiante;
    public $gestor_carteira;
    public $prestador_servico_page;
    public $distribuidor_cotas;
    public $auditores_independentes;

    public function __set($name, $value)
    {
        if (property_exists($this, $name)) {
            return $this->$name = $value;
        }
    }

    public function __get($name)
    {
        if (property_exists($this, $name)) {
            return $this->$name;
        }
    }
}
