<?php

namespace App\Crawler\InfracoesTrabalhistas;

use Exception;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Helper\Util;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\Bucket\StaticUplexisBucket;

class InfracoesTrabalhistas extends Spider
{
    public $debug = true;
    private $s3StaticPath = S3_STATIC_URL;
    private $sourceS3Path = 'captura/infracoes_trabalhistas/';
    private $pdfS3Path = '';

    private $uniqid = '';

    private $html = '';
    private $baseUrl = 'http://cdcit.mte.gov.br';
    protected $mainUrl = 'http://cdcit.mte.gov.br/inter/cdcit/pages/infracoes/';
    private $urlCaptcha = 'http://cdcit.mte.gov.br/inter/cdcit/seam/resource/captcha?';
    private $detailUrl = 'http://cdcit.mte.gov.br/inter/cdcit/pages/infracoes/detalhe.seam?cid=';
    private $actualUrl = '';
    private $pdfUrl = '';
    private $pdfPath = '/tmp/pdf_{uniqid}.pdf';

    private $data = [];

    private $formParams = [];
    private $ids = [];

    /**
     * Valida os parametros passados (obrigatórios)
     *
     * @return void
     * <AUTHOR> Machado - 2019-03-11
     * Revisão
     * @version 1.0.0
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print __METHOD__ . "\n";
        }

        if (!empty($this->param['documento']) && Document::validarCnpj($this->param['documento'])) {
            $this->param['tipo'] = 'CNPJ';
        } elseif (!empty($this->param['documento']) && Document::validarCpf($this->param['documento'])) {
            $this->param['tipo'] = 'CPF';
        } else {
            throw new Exception('Parâmetro documento é inválido', 1);
        }
    }

    /**
     * Método que inicia a captura
     *
     * @return array
     * <AUTHOR> Machado - 2019-03-11
     * Revisão
     * @version 1.0.0
     */
    protected function start()
    {
        if ($this->debug) {
            print __METHOD__ . "\n";
        }

        $this->actualUrl = $this->mainUrl;
        $this->config();

        $this->getFormParams();
        $this->setDetailUrl();

        $result = $this->sendForm();

        $this->checkResponseErrors($result);

        $this->html = '';
        $this->actualUrl = $this->detailUrl;
        $this->setPdfUrl();
        $this->parsePdf();
        $this->savePdf();

        return $this->data;
    }

    /**
     *  Checking response errors
     */
    public function checkResponseErrors($result)
    {
        if (preg_match('/CPF\/CNPJ inv.*?lido/', $result)) {
            throw new Exception("CPF/CNPJ inválido", 6);
        }
    }

    /**
     * Seta os parâmetros a serem enviados na requisição
     *
     * <AUTHOR> Prates 27-08-2019
     *
     * @version 1.0.1 Jefferson Mesquita - 17/09/2019
     * Retirar true de slEmenta, para trazer todos os dados
     *
     * @return void
     */
    private function getFormParams()
    {
        //Pega os j_ids dinamicamente
        $this->getIds();

        $this->formParams = [
            'frmConsultaCertidao' => 'frmConsultaCertidao',
            $this->ids['tipoUsuario'] => $this->param['tipo'],
            'frmConsultaCertidao:decGrupoInfracoes:slEmenta' => 'org.jboss.seam.ui.NoSelectionConverter' .
                '.noSelectionValue',
            $this->ids['slEmenta'] => '',
            'frmConsultaCertidao:decorateCaptcha:verifyCaptcha' => '',
            $this->ids['emitir'] => 'Emitir',
            'javax.faces.ViewState' => 'j_id4',
            $this->ids['documento'] => Document::formatCpfOrCnpj($this->param['documento'])
        ];
    }

    /**
     * Pega os j_ids dinamicamente
     *
     * <AUTHOR> Prates 27-08-2019
     *
     * @param string $html
     * @return void
     */
    private function getIds($html = '')
    {
        $html = $this->getResponse($this->mainUrl);
        $this->checkSiteStatus($html);

        $allRegex = [
            'tipoUsuario' => '/<input type="radio" checked="checked" name="(.*)" id/i',
            'slEmenta' => '/<select id="frmConsultaCertidao:j_id.*:slEmenta" name="(.*)" size/i',
            'emitir' => '/<input type="submit" name="(.*)" value="Emitir"/i',
            'documento' => '/<input type="text" name="(.*)" class="(cnpj|cpf)"/i',
            'ajaxClick' => "/onchange\\=\"A4J\\.AJAX\\.Submit\\(\\'frmConsultaCertidao\\'\\," .
                "event\\,\\{\\'control\\'\\:this\\,\\'similarityGroupingId\\'\\:\\'(.*)\\',\\'parameters/i"
        ];

        foreach ($allRegex as $key => $regex) {
            if (!preg_match_all($regex, $html, $output_array)) {
                throw new Exception('Não foi encontrar os ids na página principal', 3);
            }
            $this->ids[$key] = $output_array[1][0];
        }

        //CPF tem que fazer uma requisição a mais para o sistema identificar o tipo do documento
        //e para pegar o j_id documento tipo cpf
        if ($this->param['tipo'] == 'CPF') {
            $html = $this->ajaxClickCpf();
            if (!preg_match_all($allRegex['documento'], $html, $output_array)) {
                throw new Exception('Não foi encontrar os ids na página principal', 3);
            }
            $this->ids['documento'] = $output_array[1][0];
        }
    }

    private function checkSiteStatus($html)
    {
        $pattern = "/The\s+requested\s+resource\s+is\s+not\s+available/is";
        if (preg_match($pattern, $html)) {
            throw new Exception('O site da fonte encontra-se fora do ar', 6);
        }
    }

    /**
     * Configurações da fonte
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     *
     * @version 1.0.1 Jefferson Mesquita - 17/09/2019
     * Troca para o Alternative Proxy, o outro range de proxy da muitos erros e mesmo utilizando retry
     * a fonte demora e continua dando erro
     *
     * @return $[type]
     */
    private function config()
    {
        $this->setAlternativeProxy();
        $this->setCurlOpt([
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_USERAGENT => 'Mozilla/5.0 Windows NT 6.1 WOW64 rv 8.0 Gecko/20100101 Firefox/8.0',
            CURLOPT_FOLLOWLOCATION => true
        ]);
    }

    private function ajaxClickCpf()
    {
        try {
            $params = [
                'AJAXREQUEST:' => '_viewRoot',
                'frmConsultaCertidao' => 'frmConsultaCertidao',
                $this->ids['documento'] => '',
                'frmConsultaCertidao:decGrupoInfracoes:slEmenta' => 'org.jboss.seam.ui.NoSelectionConverter' .
                    '.noSelectionValue',
                $this->ids['slEmenta'] => '',
                'frmConsultaCertidao:decorateCaptcha:verifyCaptcha' => '',
                'javax.faces.ViewState' => $this->getViewState(),
                $this->ids['tipoUsuario'] => 'CPF',
                $this->ids['ajaxClick'] => $this->ids['ajaxClick'],
                'ajaxSingle' => $this->ids['tipoUsuario']
            ];


            $header = [
                'Content-Type' => 'application/x-www-form-urlencoded;charset=UTF-8',
                'Accept-Encoding' => 'gzip,deflate',
                'Referer' => $this->mainUrl
            ];

            return $this->getResponse($this->mainUrl, 'POST', $params, $header);
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), 3);
        }
    }

    /**
     * Envia o formulário com as informações necessárias
     *
     * @return void
     * <AUTHOR> Machado - 2019-03-11
     * Revisão
     * @version 1.0.0
     *
     * @version 1.0.1 Jefferson Mesquita - 17/09/2019
     * Retirar true de slEmenta, para trazer todos os dados
     */
    private function sendForm()
    {
        try {
            if ($this->debug) {
                print __METHOD__ . "\n";
            }
            $this->formParams['javax.faces.ViewState'] = $this->getViewState();

            $this->getImageCaptcha();
            $this->breakCaptcha();
            $this->formParams['frmConsultaCertidao:decorateCaptcha:verifyCaptcha'] = $this->captcha;
            $this->formParams['frmConsultaCertidao:j_id118:slEmenta'] = '';
            $header = ['Referer' => $this->mainUrl];
            $this->setCurlOpt([CURLOPT_HEADER => 1]);

            return $this->getResponse($this->mainUrl, 'POST', $this->formParams, $header);
        } catch (Exception $e) {
            throw new Exception('Não possível enviar os dados', 3);
        }
    }

    /**
     * Gera a url de detalhes
     *
     * @return void
     * <AUTHOR> Machado - 2019-03-12
     * Revisão
     * @version 1.0.0
     */
    private function setDetailUrl()
    {
        try {
            $cidPattern = '/cid\=([^\"]*)\"/isU';
            $dados = Util::parseDados(
                ['cid' => [$cidPattern, null]],
                $this->getHtml()
            );
            if (empty($dados['cid'])) {
                throw new Exception('Não foi encontrar o cid na página principal', 3);
            }
            $this->detailUrl = $this->detailUrl . $dados['cid'];
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * Pega a url do pdf do html da url detailUrl
     *
     * @return void
     * <AUTHOR> Machado - 2019-03-11
     * Revisão
     * @version 1.0.0
     */
    private function setPdfUrl()
    {
        try {
            $pattern = '/<object[^>]*id=\"frmDetalheAcoes:outputPDF\"\s*data=\"([^>]*)\"/isU';
            $dados = Util::parseDados(
                ['pdfPath' => [$pattern, null]],
                $this->getHtml()
            );
            if (empty($dados['pdfPath'])) {
                throw new Exception('Não foi possível encontrar o caminho do pdf', 3);
            }

            $this->pdfUrl = $dados['pdfPath'];
            $this->pdfUrl = $this->baseUrl . $this->pdfUrl;
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), 3);
        }
    }

    /**
     * Pega o viewState do html da baseUrl
     *
     * @return void
     * <AUTHOR> Machado - 2019-03-11
     * Revisão
     * @version 1.0.0
     */
    private function getViewState()
    {
        try {
            $patternViewState = '/<input[^>]*id="javax\.faces\.ViewState"\s*value="([^>]*)"\s[^>]*>/isU';
            $dados = Util::parseDados(
                ['viewState' => [$patternViewState, null]],
                $this->getHtml()
            );
            if (empty($dados['viewState'])) {
                throw new Exception('Não foi possível encontrar o viewState no html', 3);
            }
            return $dados['viewState'];
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), 3);
        }
    }

    /**
     * Salva a imagem do captcha
     *
     * @return void
     * <AUTHOR> Machado - 2019-11-03
     * Revisão
     * @version 1.0.0
     */
    private function getImageCaptcha()
    {
        if ($this->debug) {
            print __METHOD__ . "\n";
        }

        $captchaUrl = $this->urlCaptcha . time();
        file_put_contents($this->captcha_path, $this->getResponse($captchaUrl));
    }

    /**
     * Formata as informações do pdf
     *
     * @return array
     * <AUTHOR> Machado - 2019-03-12
     * Revisão
     * @version 1.0.0
     */
    private function parsePdf()
    {
        $this->setPdfPath();
        file_put_contents($this->pdfPath, $this->getResponse($this->pdfUrl));
        $pdfText = (new Pdf())->getTextFromPdf($this->pdfPath, [
            'layout'
        ]);

        $arrPatterns = [
            'empregador' => ['/empregador\:\s([^\n]*).\n/isU', null],
            'documento' => ['/[cnpj|cpf]\:\s([^\n]*)\n/isU', null],
            'data_emissao' => ['/data[^\:]*\:\s([^\n]*)\n/isU', null],
            'dispositivo_legal' => ['/dispositivo\slegal\sconsultado\:\s([^\n]*)\n./isU', null],
            'processos_com_reincidencia' => ['/procedentes\sc[^\:]*\:\s([^\n]*)\n/isU', null],
            'processos_sem_reincidencia' => ['/procedentes\ss[^\:]*\:\s([^\n]*)\n/isU', null],
            'processos_demais' => ['/todos\sos\sdemais\:\s([^\n]*)\n/isUi', null],
            'conteudo' => ['/(.*)p..gina\s1\sde/isU', null]
        ];

        $this->data = Util::parseDados($arrPatterns, $pdfText);
        $this->data = Str::encoding($this->data);
    }

    /**
     * Salva o pdf no s3
     *
     * @return void
     * <AUTHOR> Machado - 2019-03-12
     * Revisão
     * @version 1.0.0
     */
    private function savePdf()
    {
        $s3Path = $this->sourceS3Path . $this->uniqid . '.pdf';
        $this->pdfS3Path = $this->s3StaticPath . $s3Path;
        $this->data['pdf'] = $this->pdfS3Path;

        (new S3(new StaticUplexisBucket()))->save($s3Path, $this->pdfPath);
    }

    /**
     * Seta o caminho do pdf
     *
     * @return void
     * <AUTHOR> Machado - 2019-03-12
     * Revisão
     * @version 1.0.0
     */
    private function setPdfPath()
    {
        $this->uniqid = md5(uniqid(rand(), true));
        $this->pdfPath = str_replace('{uniqid}', $this->uniqid, $this->pdfPath);
    }

    /**
     * Seta o html se necessário
     *
     * @return void
     * <AUTHOR> Machado - 2019-03-12
     * Revisão
     * @version 1.0.0
     */
    private function getHtml()
    {
        if (empty($this->html)) {
            if (empty($this->actualUrl)) {
                throw new Exception('actualUrl is empty or not defined!');
            }
            $this->html = $this->getResponse($this->actualUrl, 'GET');
        }
        return $this->html;
    }
}
