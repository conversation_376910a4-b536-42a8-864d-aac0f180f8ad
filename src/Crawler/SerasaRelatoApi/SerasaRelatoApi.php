<?php

namespace App\Crawler\SerasaRelatoApi;

use App\Crawler\Spider;
use Exception;
use App\Manager\S3\S3;
use App\Manager\S3\Bucket\StaticUplexisBucket;

/**
 * Classe de pesquisa da Serasa Relato Api
 *
 * @version 1.0.1
 * <AUTHOR>
 */
class SerasaRelatoApi extends Spider
{
    private $production_url = 'https://sitenet43.serasa.com.br/Prod/consultahttps';
    private $homolog_url = 'https://mqlinuxext.serasa.com.br/Homologa/consultahttps';
    private $serasa_service_url = '';
    private $application_env = 'prod';
    private $start_output_position = 400;
    private $strlen_current = 115;

    /**
     * Checa a resposta da API
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Makhoul - 25/06/2018 - Correção da PSR e adicionado catch de erro
     *
     * @param string $response
     *
     * @return string
     */
    private function checkResponse($response)
    {
        $this->validateLoginErrors($response);

        if (preg_match("#USUARIO\\/SENHA\\s*INVALIDO#is", $response)) {
            throw new Exception("USUARIO SENHA INVALIDO", 3);
        }

        if (preg_match("#AUTORIZACAO\\s*CANCELADA#is", $response)) {
            throw new Exception("AUTORIZACAO CANCELADA", 3);
        }

        if (preg_match("#NOVA\\s*PASSWORD\\s*INVALIDA#is", $response)) {
            throw new Exception("USUÁRIO OU SENHA FORA DO PADRÂO", 3);
        }

        if (preg_match("#INCONSISTENCIAS\\s*NOS\\s*DADOS\\s*ENVIADOS#is", $response)) {
            throw new Exception("INCONSISTENCIAS NOS DADOS ENVIADOS", 3);
        }

        if (preg_match("#USUARIO\\s*NAO\\s*AUTORIZADO#is", $response)) {
            throw new Exception("USUARIO NAO AUTORIZADO", 3);
        }

        return $response;
    }

    /**
     * Função principal de execução
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @return array
     */
    protected function start()
    {
        if ($this->application_env == 'prod') {
            $this->serasa_service_url = $this->production_url;
        } else {
            $this->serasa_service_url = $this->homolog_url;
        }

        list($str_header, $str_body) = $this->splitString($this->getDados());

        do {
            $tipo = $this->checkTypeResponse($str_header);

            $endPositionOfString = $this->lookingForEndPositionOfString($str_body);

            $strlen = strlen($str_body);

            for ($i = 0; $i <= $strlen; $i += $this->strlen_current) {
                if ($i >= $endPositionOfString) {
                    break;
                }

                $str_current = substr($str_body, $i, $this->strlen_current);

                if (method_exists($this, $method_name = $this->getMethod($this->getKeyResponse($str_current)))) {
                    $codKey = $this->getCodKey($this->getKeyResponse($str_current));
                    $response[$codKey][] = $this->{$method_name}($str_current);
                }
            }

            if ($tipo == 'CON') {
                list($str_header, $str_body) = $this->splitString($this->getDados($str_header)); //PASSAR O CON
            }
        } while ($tipo != 'FIM');

        return self::encoding($response);
    }

    /**
     * Corrige o encoding
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string|array $str
     *
     * @return string|array
     */
    public static function encoding($str)
    {
        $cleanString = function ($string) {
            $string = preg_replace('/\n/', " ", $string);
            $string = preg_replace('/\s{2,}/', " ", $string);
            $string = preg_replace('/\t/', " ", $string);
            $string = trim($string);
            return $string;
        };

        if (!is_array($str) and !is_object($str)) {
            return $cleanString($str);
        }

        return array_map('self::encoding', $str);
    }

    /**
     * Prepara a string de parametrização
     *
     * @version 1.0.1
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Retirado urlencode da string parametrizada
     *
     * @param string $string_con
     *
     * @return string
     */
    private function prepareSerasaStringContinue($string_con)
    {
        //AUTH
        $auth = str_pad($this->auth['usuario'], 8);
        $auth .= str_pad($this->auth['senha'], 8);
        $auth .= str_pad('', 8);

        $string = $auth . $string_con;

        // T999
        $string .= 'T999';
        $string .= str_pad('', 111);

        return $string;
    }

    /**
     * Prepara a string de parametrização de início
     *
     * @version 1.0.1
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Retirado urlencode da string parametrizada
     *
     * @return string
     */
    private function prepareSerasaString()
    {
        //AUTH
        $auth = str_pad($this->auth['usuario'], 8);
        $auth .= str_pad($this->auth['senha'], 8);
        $auth .= str_pad('', 8);

        // B49C
        $string = 'B49C';
        //$string .= sprintf('%-6s', '')
        $string .= str_pad('', 6);
        $string .= sprintf('%015d', preg_replace("@\\D+@i", "", $this->param['cpf_cnpj']));
        $string .= 'J';
        //$string .= sprintf('%-6s', 'C')
        $string .= str_pad('C', 6);
        $string .= 'FI';
        //$string .= sprintf('%07d', 0)
        $string .= sprintf('%07d', 0);
        //string .= sprintf('%-12s', '')
        $string .= str_pad('', 12);
        $string .= 'S';
        $string .= '99';
        $string .= 'S';
        $string .= 'INI';
        $string .= 'A';
        $string .= 'N';
        //string .= sprintf('%-30s', '')
        $string .= str_pad('', 30);
        $string .= 'D';
        $string .= 'N';
        //string .= sprintf('%-10s', '')
        $string .= str_pad('', 10);
        $string .= '00';
        $string .= 'N';
        //$string .= sprintf('%-8s', '');
        $string .= str_pad('', 8);
        $string .= sprintf('%015d', 0);
        $string .= 'S';
        //$string .= sprintf('%-9s', '');
        $string .= str_pad('', 9);
        $string .= '1';
        //$string .= sprintf('%-259s', '');
        $string .= str_pad('', 259);

        // P002
        $string .= 'P002';
        $string .= 'IP20';
        $string .= 'QPKHPJ8';
        //$string .= sprintf('%-104s', '');
        $string .= str_pad('', 100);

        // T999
        $string .= 'T999';
        //$string .= sprintf('%-111s', '');
        $string .= str_pad('', 111);

        return $auth . $string;
    }

    /**
     * Busca os dados na API
     *
     * @version 1.0.1
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Mudado o pedido de GET para POST
     *
     * @param string|null $con
     *
     * @return string
     */
    private function getDados($con = null)
    {
        $data = [];
        if (!$con) {
            $data['p'] = $this->prepareSerasaString();
        } else {
            $data['p'] = $this->prepareSerasaStringContinue($con);
        }

        $str = $this->getResponseByFileGet($this->serasa_service_url, 'POST', $data);

        $this->saveStringS3($str, $data);

        $str = $this->checkResponse($str);

        return $str;
    }

    /**
     * Salva a string no S3
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $data
     * @param string $url
     *
     * @return void
     */
    private function saveStringS3($data, $url)
    {
        $uniqid = $this->auth['usuario'] . '_' . $this->auth['senha'] . '_' . md5(uniqid(rand(), true));

        $file = preg_replace("@\\D+@i", "", $this->param['cpf_cnpj']) . '_' . $uniqid . '.txt';

        $serasa_relato_api_request = '/tmp/serasa_relato_api_request_' . $file;
        $serasa_relato_api_response = '/tmp/serasa_relato_api_response_' . $file;

        file_put_contents($serasa_relato_api_request, $url);
        file_put_contents($serasa_relato_api_response, $data);

        (new S3(new StaticUplexisBucket()))->save(
            "captura/serasa_relato_api/request_" . $file,
            $serasa_relato_api_request
        );
        (new S3(new StaticUplexisBucket()))->save(
            "captura/serasa_relato_api/response_" . $file,
            $serasa_relato_api_response
        );
    }

    /**
     * Função de separar string por tamanhos
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     * @param array $arrayLengths
     *
     * @return array
     */
    private function splitByLengths($string, $arrayLengths)
    {
        $output = [];
        foreach ($arrayLengths as $oneLength) {
            $output[] = substr($string, 0, $oneLength);
            $string = substr($string, $oneLength);
        }

        return ($output);
    }

    /**
     * Função de separar string
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $str
     *
     * @return array
     */
    private function splitString($str)
    {
        $str_header = substr($str, 0, $this->start_output_position);

        $str_body = substr($str, $this->start_output_position);

        return array($str_header, $str_body);
    }

    /**
     * Função de checar tipo da resposta
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $str
     *
     * @return string
     */
    private function checkTypeResponse($str)
    {
        return substr($str, 57, 3); // tipo de resposta FIM ou CON
    }

    /**
     * Função de busca de fim da string
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $str
     *
     * @return string
     */
    private function lookingForEndPositionOfString($str)
    {
        return stripos($str, 'T999'); // procurando posição final da string
    }

    /**
     * Função de busca do método
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $key
     *
     * @return string
     */
    private function getMethod($key)
    {
        $key = preg_replace('/^_/', '', $key);

        return 'parse' . $key;
    }

    /**
     * Função de busca do método
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $str
     *
     * @return string
     */
    private function getKeyResponse($str)
    {
        return substr(ltrim($str), 0, 4);
    }

    /**
     * Função de validação dos parâmetros
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @return void
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (!isset($this->param['cpf_cnpj']) or empty($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro de documento inválido', 1);
        }

        if (
            !isset($this->auth['usuario']) ||
            empty($this->auth['usuario']) ||
            strlen($this->auth['usuario']) > 8
        ) {
            throw new Exception('Parâmetro de usuário inválido', 1);
        }

        if (
            !isset($this->auth['senha']) ||
            empty($this->auth['senha']) ||
            strlen($this->auth['senha']) > 8
        ) {
            throw new Exception('Parâmetro de senha inválido', 1);
        }
    }

    /**
     * Função de parseamento da chave
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $key
     *
     * @return string
     */
    private function getCodKey($key)
    {
        $cod = array(
            'A900' => 'mensagem',
            'B381' => 'grafia',
            'R102' => 'endereco',
            'R103' => 'localizacao',
            'R104' => 'atividade',
            'R105' => 'filiais',
            'R106' => 'principais_produtos',
            'R107' => 'controle_societario',
            'R108' => 'detalhe_controle_societario',
            'R109' => 'quadro_administrativo',
            'R110' => 'detalhe_quadro_administrativo',
            'R111' => 'detalhe_cont_quadro_administrativo',
            'R112' => 'participacao',
            'R113' => 'detalhe_participacao_participada',
            'R114' => 'detalhe_participacao_participante',
            'R115' => 'detalhe_cont_participacao_participante',
            'R119' => 'antecessora',
            'R200' => 'principais_fontes',
            'R011' => 'principais_fontes_analitico',
            'R012' => 'relacionamento_fornecedores',
            'R013' => 'relacionamento_fornecedores_periodo',
            'R201' => 'relacionamento_fornecedores_antigo',
            'R205' => 'perfil_pagamentos',
            'R206' => 'evolucao_compromissos_fornecedores',
            'R202' => 'potencial_negocios',
            'R203' => 'historico_pagamento',
            'R301' => 'controle_consulta',
            'R302' => 'ultimas_consultas',
            'R410' => 'recheque',
            'R412' => 'resumo_concentre',
            'R303' => 'frases_alerta',
            'R402' => 'informacoes_riskscoring',
            'R403' => 'mensagem_riskscoring',
            'R404' => 'riskscoring_6meses',
            'B358' => 'detalhe_pendencia_pagamento',
            'B389' => 'detalhe_pendencia_pagamento_refin',
            'B360' => 'detalhe_cheques_sem_fundos',
            'B362' => 'detalhe_protestos',
            'B364' => 'detalhe_acoes_judiciais',
            'B366' => 'detalhe_participacao_falencias',
            'B368' => 'detalhe_convem_devedores',
            'B383' => 'detalhe_falencia_concordata'
        );

        if (!isset($cod[$key])) {
            return '';
        }

        return $cod[$key];
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseA900($string)
    {
        $response = [];

        $fieldSequence = array(4, 6, 32, 70);

        list(
            $response['tipo_de_registro'],
            $response['codigo'],
            $response['msg_reduzida'],
            $response['msg_completa']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB381($string)
    {
        $response = [];

        $fieldSequence = array(4, 70, 8, 8, 13, 1, 1, 1);

        list(
            $response['tipo_de_registro'],
            $response['razao_social'],
            $response['cnpj'],
            $response['data_fundacao'],
            $response['filler'],
            $response['situacao'], //tabSituacao
            $response['filler2'],
            $response['nova_situacao'] //tabSituacao
        ) = $this->splitByLengths($string, $fieldSequence);

        $response['situacao'] = $this->tabSituacao($response['situacao']);
        $response['nova_situacao'] = $this->tabSituacao($response['nova_situacao']);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR010($string)
    {
        $response = [];

        $fieldSequence = array(4, 60, 8, 8, 2, 24, 8, 1);

        list(
            $response['tipo_de_registro'],
            $response['confidencial'],
            $response['data'],
            $response['hora'],
            $response['moeda'],
            $response['cnpj'],
            $response['data_atualizacao'],
            $response['origem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR102($string)
    {
        $response = [];

        $fieldSequence = array(4, 60, 51);

        list(
            $response['tipo_de_registro'],
            $response['ereco'],
            $response['fantasia']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR103($string)
    {
        $response = [];

        $fieldSequence = array(4, 30, 2, 9, 4, 9, 9, 4, 44);

        list(
            $response['tipo_de_registro'],
            $response['cidade'],
            $response['uf'],
            $response['cep'],
            $response['ddd'],
            $response['telefone'],
            $response['fax'],
            $response['cod_embratel'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR104($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 8, 54, 7, 5, 3, 3, 6, 7, 10);

        list(
            $response['tipo_de_registro'],
            $response['data_fundacao'],
            $response['data_cnpj'],
            $response['ramo_atividade'],
            $response['cod_serasa'],
            $response['qtde_empregados'],
            $response['perc_compras'],
            $response['perc_vas'],
            $response['qtde_filiais'],
            $response['cod_cnae'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR105($string)
    {
        $response = [];

        $fieldSequence = array(4, 30, 4, 77);

        list(
            $response['tipo_de_registro'],
            $response['nome_filial'],
            $response['cod_embratel'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR106($string)
    {
        $response = [];

        $fieldSequence = array(4, 60, 51);

        list(
            $response['tipo_de_registro'],
            $response['princ_produtos'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR107($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 13, 13, 13, 12, 12, 12, 1, 27);

        list(
            $response['tipo_de_registro'],
            $response['data_atualizacao'],
            $response['valor_capital_s'],
            $response['valor_capital_r'],
            $response['valor_capital_a'],
            $response['nacionalidade'],
            $response['origem'],
            $response['natureza'],
            $response['junta'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR108($string)
    {
        $response = [];

        $fieldSequence = array(4, 1, 9, 2, 4, 64, 12, 4, 8, 1, 4, 2);

        list(
            $response['tipo_de_registro'],
            $response['tipo_pessoa'],
            $response['documento'],
            $response['digito_documento'],
            $response['tipo_doc'],
            $response['nome'],
            $response['nacionalidade'],
            $response['perc_capital'],
            $response['data_entrada'],
            $response['restricao'],
            $response['perc_cap_volante'],
            $response['situacao'] //tabSituacao
        ) = $this->splitByLengths($string, $fieldSequence);


        $response['situacao'] = $this->tabSituacao($response['situacao']);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR109($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 1, 102);

        list(
            $response['tipo_de_registro'],
            $response['data_atualizacao'],
            $response['junta'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR110($string)
    {
        $response = [];

        $fieldSequence = array(4, 1, 9, 2, 58, 12, 12, 17);

        list(
            $response['tipo_de_registro'],
            $response['tipo_pessoa'],
            $response['documento'],
            $response['digito_documento'],
            $response['nome'],
            $response['cargo'],
            $response['nacionalidade'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR111($string)
    {
        $response = [];

        $fieldSequence = array(4, 1, 9, 2, 4, 9, 8, 8, 1, 3, 2, 63);


        list(
            $response['tipo_de_registro'],
            $response['tipo_pessoa'],
            $response['documento'],
            $response['digito_documento'],
            $response['tipo_doc'],
            $response['estado_civil'],
            $response['data_entrada'],
            $response['data_final'],
            $response['restricao'],
            $response['cargo'],
            $response['situacao'],
            $response['filler'] //tabSituacao
        ) = $this->splitByLengths($string, $fieldSequence);

        $response['filler'] = $this->tabSituacao($response['filler']);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR112($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 1, 102);

        list(
            $response['tipo_de_registro'],
            $response['data_atualizacao'],
            $response['origem'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR113($string)
    {
        $response = [];

        $fieldSequence = array(4, 1, 9, 2, 4, 60, 1, 2, 24);

        list(
            $response['tipo_de_registro'],
            $response['tipo_pessoa'],
            $response['documento'],
            $response['digito_documento'],
            $response['tipo_doc'],
            $response['empresa'],
            $response['restricao'],
            $response['situacao'],
            $response['filler'] // tabSituacao
        ) = $this->splitByLengths($string, $fieldSequence);

        $response['filler'] = $this->tabSituacao($response['filler']);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR114($string)
    {
        $response = [];

        $fieldSequence = array(4, 1, 9, 2, 67, 32);

        list(
            $response['tipo_de_registro'],
            $response['tipo_pessoa'],
            $response['documento'],
            $response['digito_documento'],
            $response['nome'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR115($string)
    {
        $response = [];

        $fieldSequence = array(4, 1, 9, 2, 4, 9, 4, 30, 2, 5, 1, 2, 41);

        list(
            $response['tipo_de_registro'],
            $response['tipo_pessoa'],
            $response['documento'],
            $response['digito_documento'],
            $response['tipo_doc'],
            $response['vinculo'],
            $response['cod_embratel'],
            $response['municipio'],
            $response['uf'],
            $response['perc_participacao'],
            $response['restricao'],
            $response['situacao'], //tabSituacao
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        $response['situacao'] = $this->tabSituacao($response['situacao']);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR119($string)
    {
        $response = [];

        $fieldSequence = array(4, 70, 9, 9, 22);

        list(
            $response['tipo_de_registro'],
            $response['razao_social'],
            $response['data_mandato'],
            $response['data_atualizacao'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR200($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 3, 99);

        list(
            $response['tipo_de_registro'],
            $response['data_atualizacao'],
            $response['origem'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR011($string)
    {
        $response = [];

        $fieldSequence = array(4, 70, 9, 4, 2, 3, 22);

        list(
            $response['tipo_de_registro'],
            $response['fonte'],
            $response['cnpj'],
            $response['filial'],
            $response['digito'],
            $response['origem'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR012($string)
    {
        $response = [];

        $fieldSequence = array(4, 4, 4, 4, 4, 4, 3, 4, 83);

        list(
            $response['tipo_de_registro'],
            $response['qtde_consult'],
            $response['qtde_con_perf'],
            $response['qtde_con_evol'],
            $response['qtde_con_pot'],
            $response['qtde_con_potv'],
            $response['origem'],
            $response['qtde_con_his'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR013($string)
    {
        $response = [];

        $fieldSequence = array(4, 14, 4, 3, 89);

        list(
            $response['tipo_de_registro'],
            $response['descricao'],
            $response['qtde'],
            $response['origem'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR201($string)
    {
        $response = [];

        $fieldSequence = array(4, 3, 2, 2, 103);

        list(
            $response['tipo_de_registro'],
            $response['descricao_mes'],
            $response['ano'],
            $response['mes'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR205($string)
    {
        $response = [];

        $fieldSequence = array(4, 14, 2, 2, 3, 13, 4, 3, 69);

        list(
            $response['tipo_de_registro'],
            $response['descricao_periodo'],
            $response['ano'],
            $response['mes'],
            $response['descricao_mes'],
            $response['valor'],
            $response['perc_pagamento'],
            $response['origem'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR206($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 2, 3, 13, 13, 3, 74);

        list(
            $response['tipo_de_registro'],
            $response['ano'],
            $response['mes'],
            $response['descricao_mes'],
            $response['valor_vencido'],
            $response['valor_avencer'],
            $response['origem'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR202($string)
    {
        $response = [];

        $fieldSequence = array(4, 14, 8, 13, 13, 3, 59);

        list(
            $response['tipo_de_registro'],
            $response['descricao'],
            $response['data'],
            $response['valor'],
            $response['valor_media'],
            $response['origem'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR203($string)
    {
        $response = [];

        $fieldSequence = array(4, 14, 6, 4, 87);

        list(
            $response['descricao'],
            $response['qtde'],
            $response['perc_periodo'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR301($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 2, 3, 3, 3, 98);

        list(
            $response['tipo_de_registro'],
            $response['ano'],
            $response['mes'],
            $response['descricao_mes'],
            $response['qtde_empresas'],
            $response['qtde_bancos'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR302($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 35, 4, 9, 1, 54);

        list(
            $response['tipo_de_registro'],
            $response['data'],
            $response['nome'],
            $response['qtde_dia'],
            $response['cnpj'],
            $response['tipo_consultante'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR410($string)
    {
        $response = [];

        $fieldSequence = array(4, 9, 9, 92);

        list(
            $response['tipo_de_registro'],
            $response['qtde_ocorrencias'],
            $response['qtde_ultima'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR412($string)
    {
        $response = [];

        $fieldSequence = array(4, 9, 27, 3, 2, 2, 3, 2, 2, 3, 13, 20, 4, 13, 3, 4);

        list(
            $response['tipo_de_registro'],
            $response['qtde_ocorrencias'],
            $response['grupo_ocorrencia'],
            $response['descricao_mes_ini'],
            $response['mes_ini'],
            $response['ano_ini'],
            $response['descricao_mes_fim'],
            $response['mes_fim'],
            $response['ano_fim'],
            $response['moeda'],
            $response['valor'],
            $response['origem'],
            $response['agencia'],
            $response['valor_total'],
            $response['cod_natureza'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR303($string)
    {
        $response = [];

        $fieldSequence = array(4, 111);

        list(
            $response['tipo_de_registro'],
            $response['frase_alerta']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR402($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 8, 80, 15);

        list(
            $response['tipo_de_registro'],
            $response['data'],
            $response['hora'],
            $response['descricao'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR403($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 8, 94, 1);

        list(
            $response['tipo_de_registro'],
            $response['data'],
            $response['hora'],
            $response['descricao'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseR404($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 8, 4, 5, 85);

        list(
            $response['tipo_de_registro'],
            $response['data'],
            $response['hora'],
            $response['fator_risk'],
            $response['fator_prin'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB358($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 12, 1, 10, 2, 8, 2, 1, 3, 9, 17, 20, 4, 5, 4, 1, 2, 8);

        list(
            $response['tipo_de_registro'],
            $response['tipo_pefin'],
            $response['modalidade'],
            $response['tipo_ocor'],
            $response['chave_cadus'],
            $response['filler'],
            $response['data_ocorr'],
            $response['sigla_mod'],
            $response['principal'],
            $response['tipo_moeda'],
            $response['valor'],
            $response['contrato'],
            $response['origem'],
            $response['filial'],
            $response['qtde_ocorr'],
            $response['cod_banco'],
            $response['subjud_x'],
            $response['uf'],
            $response['filler2']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB389($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 12, 1, 10, 2, 8, 2, 1, 3, 9, 17, 20, 4, 5, 4, 1, 2, 8);

        list(
            $response['tipo_de_registro'],
            $response['tipo_refin'],
            $response['modalidade'],
            $response['tipo_ocor'],
            $response['chave_cadus'],
            $response['filler'],
            $response['data_ocorr'],
            $response['sigla_mod'],
            $response['principal'],
            $response['tipo_moeda'],
            $response['valor'],
            $response['contrato'],
            $response['origem'],
            $response['filial'],
            $response['qtde_ocorr'],
            $response['cod_banco'],
            $response['subjud_x'],
            $response['uf'],
            $response['filler2']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB362($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 3, 9, 4, 25, 2, 5, 1, 8, 1, 10, 35);

        list(
            $response['tipo_de_registro'],
            $response['data_ocorr'],
            $response['tipo_moeda'],
            $response['valor'],
            $response['cartorio'],
            $response['cidade'],
            $response['uf'],
            $response['qtde_ocorr'],
            $response['subjudice'],
            $response['data'],
            $response['tipo_ocorr'],
            $response['chave_cadus'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB364($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 20, 1, 3, 9, 4, 4, 25, 2, 5, 1, 1, 10, 18);

        list(
            $response['tipo_de_registro'],
            $response['data_ocorr'],
            $response['natureza'],
            $response['principal'],
            $response['tipo_moeda'],
            $response['valor'],
            $response['distribuidor'],
            $response['vara'],
            $response['cidade'],
            $response['uf'],
            $response['qtde_ocorr'],
            $response['subjudice'],
            $response['tipo_ocorr'],
            $response['chave_cadus'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB366($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 10, 14, 45, 5, 3, 4, 1, 10, 11);

        list(
            $response['tipo_de_registro'],
            $response['data_ocorr'],
            $response['tipo_ocorr'],
            $response['cnpj_pie'],
            $response['empresa'],
            $response['total_ocorr'],
            $response['qualif'],
            $response['vara_civil'],
            $response['filler'],
            $response['chave_cadus'],
            $response['filler2']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB360($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 6, 2, 4, 3, 9, 14, 16, 4, 25, 2, 5, 1, 9, 3);

        list(
            $response['tipo_de_registro'],
            $response['data_ocorr'],
            $response['num_cheque'],
            $response['alinea'],
            $response['qtde'],
            $response['tipo_moeda'],
            $response['valor'],
            $response['banco'],
            $response['filler'],
            $response['agencia'],
            $response['cidade'],
            $response['uf'],
            $response['qtde_ocorr'],
            $response['tipo_conta'],
            $response['conta'],
            $response['filler2']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB368($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 2, 3, 9, 17, 20, 4, 5, 1, 10, 32);

        list(
            $response['tipo_de_registro'],
            $response['data_ocorr'],
            $response['modalidade'], // tab7
            $response['tipo_moeda'],
            $response['valor'],
            $response['titulo'],
            $response['origem'],
            $response['local'],
            $response['qtde_ocorr'],
            $response['tipo_ocorr'],
            $response['chave_cadus'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        $response['modalidade'] = $this->tab7($response['modalidade']);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB383($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 2, 4, 25, 4, 2, 11, 9, 20, 5, 25);

        list(
            $response['tipo_de_registro'],
            $response['data_ocorr'],
            $response['tipo_ocorr'],
            $response['vara_civil'],
            $response['cidade'],
            $response['cod_localidade'],
            $response['uf'],
            $response['chave_cadus'],
            $response['qtde_ocorr'],
            $response['tipo'],
            $response['origem'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return string
     */
    private function tabSituacao($string)
    {
        switch ($string) {
            case '02':
                return 'Ativa';
            case '03':
                return 'Inativa';
            case '00':
                return 'Inapta';
            case '04':
                return 'Não Localizada';
            case '05':
                return 'Em Liquidação';
            case '06':
                return 'Suspenso';
            case '07':
                return 'Não Cadastrada';
            case '09':
                return 'Cancelado';
            case '2':
                return 'Ativa';
            case '3':
                return 'Inativa';
            case '0':
                return 'Inapta';
            case '4':
                return 'Não Localizada';
            case '5':
                return 'Em Liquidação';
            case '6':
                return 'Suspenso';
            case '7':
                return 'Não Cadastrada';
            case '9':
                return 'Cancelado';
        }
    }


    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return string
     */
    private function tab7($string)
    {
        switch ($string) {
            case 'AD':
                return 'Adiant Conta';
            case 'AG':
                return 'Emprestimos';
            case 'AR':
                return 'Leasing';
            case 'AV':
                return 'Ch Vista';
            case 'C1':
                return 'Cons Imóveis';
            case 'C2':
                return 'Cons Vei Pes';
            case 'C3':
                return 'Cons Veiculo';
            case 'C4':
                return 'Cons Motos';
            case 'C5':
                return 'Cons Bens';
            case 'C6':
                return 'Cons Aereo';
            case 'CA':
                return 'Oper Cambio';
            case 'CB':
                return 'Cartao Debit';
            case 'CD':
                return 'Crediario';
            case 'CH':
                return 'Cheque';
            case 'CO':
                return 'Consorcio';
            case 'CP':
                return 'Cred Pessoal';
            case 'CQ':
                return 'Ch Predatado';
            case 'CR':
                return 'Impedido Bc';
            case 'CS':
                return 'Cred Seguro';
            case 'CT':
                return 'Cred Cartao';
            case 'CV':
                return 'Cred Veiculo';
            case 'DB':
                return 'Debito em Co';
            case 'DC':
                return 'Dividas Cheq';
            case 'DE':
                return 'Cheque Elet';
            case 'DP':
                return 'Duplicata';
            case 'EC':
                return 'Empres Conta';
            case 'EF':
                return 'Empres Folha';
            case 'FI':
                return 'Financiamento';
            case 'IM':
                return 'Oper Imobili';
            case 'OJ':
                return 'Oper Ajuizad';
            case 'OO':
                return 'Outras Oper';
            case 'OU':
                return 'Outros Cred';
            case 'RC':
                return 'Recuper Cred';
            case 'RE':
                return 'Repasse';
            case 'SA':
                return 'Seguro Auto';
            case 'SE':
                return 'Seg Elementa';
            case 'SF':
                return 'Seguro Fianc';
            case 'SO':
                return 'Seguro Outro';
            case 'SR':
                return 'Seguro Risco';
            case 'SS':
                return 'Seguro Saude';
            case 'ST':
                return 'ServTelecom';
            case 'SV':
                return 'Seguro Vida';
            case 'TD':
                return 'Tit Desconta';
            case 'TF':
                return 'Telefonia Fixa';
        }
    }

    /**
     * Gerar uma resposta falsa da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param void
     *
     * @return string
     */
    private function getResponseSerasaFake()
    {
        return file_get_contents(__DIR__ . '/test/SerasaRelatoApiFakeResponse.txt');
    }
}
