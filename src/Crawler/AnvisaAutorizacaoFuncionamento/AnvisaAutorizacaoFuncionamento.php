<?php

namespace App\Crawler\AnvisaAutorizacaoFuncionamento;

use App\Crawler\Spider;
use App\Helper\Document;
use Exception;

class AnvisaAutorizacaoFuncionamento extends Spider
{
    private const URL_CONSULTA = 'https://consultas.anvisa.gov.br/api/empresa/funcionamento';
    private const MAX_RESULTS = 30;

    protected function validateAndSetCrawlerAttributes()
    {
        $this->cnpj = Document::removeMask($this->param['cnpj']);
        $this->limite = $this->param['limite'] ??  self::MAX_RESULTS;

        if (!Document::validarCnpj($this->cnpj)) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }
    }

    /**
     * Metodo de inicio
     *
     * <AUTHOR> <<EMAIL>>
     * @return array
     */
    protected function start()
    {
        $this->setProxy();

        $params = [
            'count' => $this->limite,
            'filter[cnpj]' => $this->cnpj,
            'page' => '1'
        ];

        $this->headers = [
            'Connection: keep-alive',
            'Cache-Control: no-cache',
            'Accept: application/json, text/plain, */*',
            'Pragma: no-cache',
            'Authorization: Guest',
            'If-Modified-Since: Mon, 26 Jul 1997 05:00:00 GMT',
            'Sec-Fetch-Site: same-origin',
            'Sec-Fetch-Mode: cors',
            'Sec-Fetch-Dest: empty',
            'Referer: https://consultas.anvisa.gov.br/',
            'Accept-Language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7'
        ];

        $urlConsulta = self::URL_CONSULTA . '?' . http_build_query($params);
        $res = $this->getResponse($urlConsulta, 'GET', null, $this->headers);

        return $this->parseContent($res);
    }

    /**
     * Metodo de captura dos resultados do criterio
     *
     * <AUTHOR> Costa <<EMAIL>>
     * @param  string $response
     * @return array
     */
    public function parseContent($response)
    {
        $response = json_decode($response, true);

        if (empty($response['content'])) {
            throw new Exception("Nenhum Resultado Encontrado", 2);
        }

        $info = array();
        foreach ($response['content'] as $key => $res) {
            $info[$key] = [
                'cnpj' => $res['cnpj'],
                'autorizacao' => $res['autorizacao'],
                'razaoSocial' => $res['razaoSocial'],
                'autorizacao' => $res['autorizacao'],
                'novaAutorizacao' => $res['novaAutorizacao'],
                'dataAutorizacao' => $res['dataAutorizacao'],
                'numeroProcesso' => $res['numeroProcesso'],
                'aProcessos' => $this->parseProcessos($res['numeroProcesso'])
            ];
        }

        return $info;
    }

    /**
     * Metodo de busca de processos do criterio
     *
     * <AUTHOR> Costa <<EMAIL>>
     * @param  string $numProcesso
     * @return array
     */
    public function parseProcessos($numProcesso)
    {
        $urlProcessos = self::URL_CONSULTA . '/' . $numProcesso;
        $res = $this->getResponse($urlProcessos, 'GET', null, $this->headers);
        return json_decode($res, true);
    }
}
