<?php

namespace App\Crawler\DadosCadastraisPj;

use App\Crawler\DadosCadastraisPj\CadastroPj;
use App\Crawler\SpinePjQsa\SpinePjQsa;
use App\Manager\DynamoManager;
use Aws\DynamoDb\Marshaler;
use Exception;

class SpinePj
{
    private $client;
    private $marshaler;

    public function __construct()
    {
        $dynamoManager = new DynamoManager();
        $this->client = $dynamoManager->getClient();
        $this->marshaler = new Marshaler();
    }

    public function search($cnpj, $phoneLimit = 20, $recuperaQsa = true)
    {
        $oCadastroPj = new CadastroPj();

        $spine_pj = $this->getSpinePj($cnpj);

        /**
         * Informacoes Gerais
         */
        $oCadastroPj->setCnpj($cnpj);
        $oCadastroPj->setRazaoSocial($spine_pj['razao_social']);
        $oCadastroPj->setNomeFantasia(isset($spine_pj['nome_fantasia']) ? $spine_pj['nome_fantasia'] : '');
        $oCadastroPj->setMatriz($spine_pj['matriz'] == 'True' ? 'Sim' : 'Não');
        $oCadastroPj->setTipoEstabelecimento($spine_pj['matriz'] == 'True' ? 'MATRIZ' : 'FILIAL');
        $oCadastroPj->setDataAbertura($this->formataData($spine_pj['data_abertura']));
        $oCadastroPj->setSituacaoCadastral($spine_pj['situacao_cadastral']);
        $oCadastroPj->setDataSituacao($this->formataData($spine_pj['data_situacao']));
        $oCadastroPj->setNaturezaJuridicaCodigo($spine_pj['natureza_juridica']);
        $oCadastroPj->setNaturezaJuridicaDescricao($this->cnj($spine_pj['natureza_juridica']));
        $oCadastroPj->setTipoCnae(isset($spine_pj['tipo_cnae']) ? $spine_pj['tipo_cnae'] : '');
        $oCadastroPj->setCnae($spine_pj['cnae']);
        $oCadastroPj->setCnaeSegmento((string) intval($spine_pj['cnae_segmento']));
        $oCadastroPj->setCnaeDescricao($this->cnae($spine_pj['cnae_segmento']));
        $oCadastroPj->setDataConsulta(date('d/m/Y H:i:s'));
        $oCadastroPj->setDominio(isset($spine_pj['dominio']) ? $spine_pj['dominio'] : '');
        $oCadastroPj->setCatchall(isset($spine_pj['catchall']) ? $spine_pj['catchall'] : '');
        $oCadastroPj->setOptanteSimples(isset($spine_pj['simples_nacional']) ? $spine_pj['simples_nacional'] : '');
        $oCadastroPj->setNumeroFiliais(isset($spine_pj['numero_filiais']) ? $spine_pj['numero_filiais'] : '');

        /**
         * Porte Financeiro
         */
        $oCadastroPj->setCapitalSocial(isset($spine_pj['capital_social']) ? $spine_pj['capital_social'] : '');
        $oCadastroPj->setPorte(isset($spine_pj['porte']) ? $spine_pj['porte'] : '');
        $oCadastroPj->setSetor(isset($spine_pj['setor']) ? $spine_pj['setor'] : '');
        $oCadastroPj->setFaixaFuncionarios(isset($spine_pj['faixa_funcionarios'])
            ? $spine_pj['faixa_funcionarios']
            : '');
        $oCadastroPj->setFaturamentoAnualEstimado(isset($spine_pj['faturamento_anual_estimado'])
            ? $spine_pj['faturamento_anual_estimado']
            : '');
        $oCadastroPj->setTipo(isset($spine_pj['tipo']) ? $spine_pj['tipo'] : '');
        $oCadastroPj->setOperacionalidade(isset($spine_pj['operacionalidade']) ? $spine_pj['operacionalidade'] : '');
        $oCadastroPj->setMotivoSituacao(isset($spine_pj['motivo_situacao']) ? $spine_pj['motivo_situacao'] : '');

        /**
         * Classe de Risco
         */
        $oCadastroPj->setClasseRisco(isset($spine_pj['classe_risco'])) ? $spine_pj['classe_risco'] : '';

        /**
         * Simples Nacional
         */
        $spineSimplesNacional = $this->getSpineSimplesNacional($cnpj);
        if ($spineSimplesNacional) {
            $oCadastroPj->setSimplesNacional(array(
                'CNPJ'                  => $spineSimplesNacional['cnpj'],
                'DataConsulta'          => $spineSimplesNacional['data_consulta'],
                'StatusSimplesNacional' => $spineSimplesNacional['status_sn'],
                'StatusSimei'           => $spineSimplesNacional['status_simei'],
                'DataSimplesNacional'   => trim(array_key_exists(
                    'data_opcao_sn',
                    $spineSimplesNacional
                ) ? $spineSimplesNacional['data_opcao_sn'] : ''),
                'DataSimei'             => trim(array_key_exists(
                    'data_opcao_simei',
                    $spineSimplesNacional
                ) ? $spineSimplesNacional['data_opcao_simei'] : ''),
            ));
        } else {
            $oCadastroPj->setSimplesNacional(array(
                'CNPJ'                      => $cnpj,
                'DataConsulta'              => null,
                'StatusSimplesNacional'     => null,
                'StatusSimei'               => null,
                'DataSimplesNacional'       => null,
                'DataSimei'                 => null,
            ));
        }


        /**
         * Endereços
         */
        $enderecos = [];
        $logr_tipo_nome = (isset($spine_pj['logr_tipo'])) ? "{$spine_pj['logr_tipo']} " : '';
        $logr_tipo_nome .= (isset($spine_pj['logr_nome'])) ? $spine_pj['logr_nome'] : '';

        $enderecos[] = array(
            'Bairro'                => (isset($spine_pj['bairro'])) ? $spine_pj['bairro'] : '',
            'Cidade'                => (isset($spine_pj['cidade'])) ? $spine_pj['cidade'] : '',
            'UF'                    => (isset($spine_pj['uf'])) ? $spine_pj['uf'] : '',
            'CEP'                   => (isset($spine_pj['cep'])) ? $spine_pj['cep'] : '',
            'IBGE'                  => (isset($spine_pj['ibge'])) ? $spine_pj['ibge'] : '',
            'LogradouroTipo'        => (isset($spine_pj['logr_tipo'])) ? $spine_pj['logr_tipo'] : '',
            'LogradouroNumero'      => (isset($spine_pj['logr_numero'])) ? $spine_pj['logr_numero'] : '',
            'LogradouroComplemento' => (isset($spine_pj['logr_complemento'])) ? $spine_pj['logr_complemento'] : '',
            'Logradouro'            => $logr_tipo_nome,
            'Latitude'              => (isset($spine_pj['latitude'])) ? $spine_pj['latitude'] : '',
            'Longitude'             => (isset($spine_pj['longitude'])) ? $spine_pj['longitude'] : '',
            'UltimaAtualizacao'     => ''
        );
        $oCadastroPj->setEnderecos($enderecos);

        /**
         * Quadro de Sócios e Administradores caso precise retornar
         */
        try {
            $socios = $this->verificaQsa($cnpj, $recuperaQsa);
        } catch (Exception $e) {
            $socios = [];
        }
        $oCadastroPj->setSocios($socios);

        /**
         * Telefones
         */
        $spine_pj_telefones = $this->getSpinePjTelefones($cnpj);

        if ($phoneLimit > 0 && count($spine_pj_telefones) >= $phoneLimit) {
            $spine_pj_telefones = array_splice($spine_pj_telefones, 0, $phoneLimit);
        }
        $telefones = [];
        foreach ($spine_pj_telefones as $spine_pj_telefone) {
            $telefones[] = array(
                'CNPJ'                      => $spine_pj_telefone['cnpj'],
                'ID'                        => $spine_pj_telefone['id'],
                'DataLog'                   => $spine_pj_telefone['data_log'],
                'DDD'                       => $spine_pj_telefone['ddd'],
                'Descricao'                 => $spine_pj_telefone['descricao'],
                'Telefone'                  => $spine_pj_telefone['telefone'],
                'TelefoneComDDD'            => '(' . $spine_pj_telefone['ddd'] . ')' . $spine_pj_telefone['telefone'],
                'Rank'                      => array_key_exists(
                    'rank',
                    $spine_pj_telefone
                ) ? $spine_pj_telefone['rank'] : '',
            );
        }
        $oCadastroPj->setTelefones($telefones);

        $spineAtividadesSecundarias = $this->getSpinePjAtividadesSecundarias($cnpj);

        $atividadesSecundarias = [];
        foreach ($spineAtividadesSecundarias as $cnae) {
            $atividadesSecundarias[] = array(
                'codigo' => $cnae['cnae'],
                'descricao' => $this->cnae($cnae['cnae'])
            );
        }
        $oCadastroPj->setAtividadesSecundarias($atividadesSecundarias);

        /**
         * E-mails
         */
        $spine_pj_emails = $this->getSpinePjEmails($cnpj);
        $emails = [];
        foreach ($spine_pj_emails as $spine_pj_email) {
            $emails[] = array(
                'EnderecoEmail'     => $spine_pj_email['email'],
                'UltimaAtualizacao' => null
            );
        }
        $oCadastroPj->setEmails($emails);
        $oCadastroPj = $this->removeAsterisk($oCadastroPj);

        return $oCadastroPj;
    }

    private function verificaQsa($cnpj, $recuperaQsa)
    {
        if ($recuperaQsa) {
            $socios = $this->getCommonQsa($cnpj);
            return array_values($socios);
        }

        return $socios = [];
    }

    private function removeAsterisk($data)
    {
        foreach ($data as $key => $value) {
            if (is_array($data->$key)) {
                foreach ($data->$key as $k => $v) {
                    if (is_array($data->$key[$k])) {
                        $data->$key[$k] = $this->removeAsterisk((object)$v);
                    }
                }
            }
            $value == '********' ? $data->$key = '' : $data->$key;
        }

        return $data;
    }

    private function getCommonQsa($cnpj)
    {
        $spinePjQsa = new SpinePjQsa([
            'documento' => $cnpj
        ], '');

        $spinePjQsa->run();
        $result = $spinePjQsa->getLastResponse();
        if ($result['status'] != 'success') {
            throw new \Exception($result['message']);
        }
        return $result['data'];
    }

    private function getSpinePj($cnpj)
    {
        $spine = $this->client->getItem(array(
            'TableName' => 'spine_pj',
            'Key' =>  $this->marshaler->marshalItem(array('cnpj' => $cnpj)),
        ));

        if (empty($spine['Item'])) {
            return [];
        }

        return $this->marshaler->unMarshalItem($spine['Item']);
    }

    private function getSpineSimplesNacional($cnpj)
    {
        $spine = $this->client->getItem(array(
            'TableName' => 'spine_pj_simplesnacional',
            'Key' =>  $this->marshaler->marshalItem(array('cnpj' => $cnpj)),
        ));

        if (empty($spine['Item'])) {
            return [];
        }

        return $this->marshaler->unMarshalItem($spine['Item']);
    }

    private function getSpinePjCnae($id)
    {
        $spine = $this->client->query(array(
            'TableName' => 'spine_pj_cnae',
            'KeyConditionExpression' => 'id = :id',
            'ExpressionAttributeValues' =>  $this->marshaler->marshalItem(array(':id' => (string) intval($id))),
            'Select' => 'ALL_ATTRIBUTES',
            'ScanIndexForward' => true,
        ));

        $data = [];
        foreach ($spine['Items'] as $item) {
            $data[] = $this->marshaler->unMarshalItem($item);
        }

        return $data;
    }

    private function getSpinePjTelefones($cnpj)
    {
        $spine = $this->client->query(array(
            'TableName' => 'spine_pj_telefones',
            'KeyConditionExpression' => 'cnpj = :cnpj',
            'ExpressionAttributeValues' =>  $this->marshaler->marshalItem(array(':cnpj' => $cnpj)),
            'Select' => 'ALL_ATTRIBUTES',
            'ScanIndexForward' => true,
        ));

        $data = [];
        foreach ($spine['Items'] as $item) {
            $data[] = $this->marshaler->unMarshalItem($item);
        }

        return $data;
    }

    private function getSpinePjAtividadesSecundarias($cnpj)
    {
        $spine = $this->client->query(array(
            'TableName' => 'spine_pj_cnae_secundario',
            'KeyConditionExpression' => 'cnpj = :cnpj',
            'ExpressionAttributeValues' =>  $this->marshaler->marshalItem(array(':cnpj' => $cnpj)),
            'Select' => 'ALL_ATTRIBUTES',
            'ScanIndexForward' => true,
        ));

        $data = [];
        foreach ($spine['Items'] as $item) {
            $data[] = $this->marshaler->unMarshalItem($item);
        }

        return $data;
    }

    private function getSpinePjEmails($cnpj)
    {
        $spine = $this->client->query(array(
            'TableName' => 'spine_pj_emails',
            'KeyConditionExpression' => 'cnpj = :cnpj',
            'ExpressionAttributeValues' =>  $this->marshaler->marshalItem(array(':cnpj' => $cnpj)),
            'Select' => 'ALL_ATTRIBUTES',
            'ScanIndexForward' => true,
        ));

        $data = [];
        foreach ($spine['Items'] as $item) {
            $data[] = $this->marshaler->unMarshalItem($item);
        }

        return $data;
    }

    private function formataData($data)
    {
        if (strlen($data) == 8) {
            $data = substr($data, 6, 2) . '/' . substr($data, 4, 2) . '/' . substr($data, 0, 4);
        }
        return $data;
    }

    private function cnae($cnae)
    {
        if (!$cnae) {
            return '';
        }
        $cnae = $this->getSpinePjCnae((string) intval($cnae));
        if (count($cnae) > 0) {
            return $cnae[0]['descricao'];
        }

        return '';
    }

    public function getSpinePjCompanyName($cnpj)
    {
        $spine = $this->client->getItem(array(
            'TableName' => 'spine_pj',
            'Key' =>  $this->marshaler->marshalItem(array('cnpj' => $cnpj)),
            'ProjectionExpression' => 'razao_social',
        ));

        if (empty($spine['Item']['razao_social'])) {
            throw new Exception("Nenhum registro encontrado", 2);
        }

        return $this->marshaler->unMarshalItem($spine['Item']);
    }

    private function cnj($cnj)
    {
        $aCnj = array(
            '1015' => 'ORGAO PUBLICO DO PODER EXECUTIVO FEDERAL',
            '1023' => 'ORGAO PUBLICO DO PODER EXECUTIVO ESTADUAL OU DO DISTRITO FEDERAL',
            '1031' => 'ORGAO PUBLICO DO PODER EXECUTIVO MUNICIPAL',
            '1040' => 'ORGAO PUBLICO DO PODER LEGISLATIVO FEDERAL',
            '1058' => 'ORGAO PUBLICO DO PODER LEGISLATIVO ESTADUAL OU DO DISTRITO FEDERAL',
            '1066' => 'ORGAO PUBLICO DO PODER LEGISLATIVO MUNICIPAL',
            '1074' => 'ORGAO PUBLICO DO PODER JUDICIARIO FEDERAL',
            '1082' => 'ORGAO PUBLICO DO PODER JUDICIARIO ESTADUAL',
            '1090' => 'ORGAO AUTONOMO DE DIREITO PUBLICO',
            '1104' => 'AUTARQUIA FEDERAL',
            '1112' => 'AUTARQUIA ESTADUAL OU DO DISTRITO FEDERAL',
            '1120' => 'AUTARQUIA MUNICIPAL',
            '1139' => 'FUNDACAO FEDERAL',
            '1147' => 'FUNDACAO ESTADUAL OU DO DISTRITO FEDERAL',
            '1155' => 'FUNDACAO MUNICIPAL',
            '1163' => 'ORGAO PUBLICO AUTONOMO FEDERAL',
            '1171' => 'ORGAO PUBLICO AUTONOMO ESTADUAL OU DO DISTRITO FEDERAL',
            '1180' => 'ORGAO PUBLICO AUTONOMO MUNICIPAL',
            '1198' => 'COMISSAO POLINACIONAL',
            '1201' => 'FUNDO PUBLICO',
            '1210' => 'ASSOCIACAO PUBLICA',
            '2011' => 'EMPRESA PUBLICA',
            '2038' => 'SOCIEDADE DE ECONOMIA MISTA',
            '2046' => 'SOCIEDADE ANONIMA ABERTA',
            '2054' => 'SOCIEDADE ANONIMA FECHADA',
            '2062' => 'SOCIEDADE EMPRESARIA LIMITADA',
            '2070' => 'SOCIEDADE EMPRESARIA EM NOME COLETIVO',
            '2089' => 'SOCIEDADE EMPRESARIA EM COMANDITA SIMPLES',
            '2097' => 'SOCIEDADE EMPRESARIA EM COMANDITA POR ACOES',
            '2100' => 'SOCIEDADE MERCANTIL DE CAPITAL E INDUSTRIA',
            '2127' => 'SOCIEDADE EM CONTA DE PARTICIPACAO',
            '2135' => 'EMPRESARIO (INDIVIDUAL)',
            '2143' => 'COOPERATIVA',
            '2151' => 'CONSORCIO DE SOCIEDADES',
            '2160' => 'GRUPO DE SOCIEDADES',
            '2178' => 'ESTABELECIMENTO, NO BRASIL, DE SOCIEDADE ESTRANGEIRA',
            '2194' => 'ESTABELECIMENTO, NO BRASIL, DE EMPRESA BINACIONAL ARGENTINO-BRASILEIRA',
            '2216' => 'EMPRESA DOMICILIADA NO EXTERIOR',
            '2224' => 'CLUBE/FUNDO DE INVESTIMENTO',
            '2232' => 'SOCIEDADE SIMPLES PURA',
            '2240' => 'SOCIEDADE SIMPLES LIMITADA',
            '2259' => 'SOCIEDADE SIMPLES EM NOME COLETIVO',
            '2267' => 'SOCIEDADE SIMPLES EM COMANDITA SIMPLES',
            '2275' => 'EMPRESA BINACIONAL',
            '2283' => 'CONSORCIO DE EMPREGADORES',
            '2291' => 'CONSORCIO SIMPLES',
            '2305' => 'EMPRESA INDIVIDUAL DE RESPONSABILIDADE LIMITADA (DE NATUREZA EMPRESARIA)',
            '2313' => 'EMPRESA INDIVIDUAL DE RESPONSABILIDADE LIMITADA (DE NATUREZA SIMPLES)',
            '3034' => 'SERVICO NOTARIAL E REGISTRAL (CARTORIO)',
            '3069' => 'FUNDACAO PRIVADA',
            '3077' => 'SERVICO SOCIAL AUTONOMO',
            '3085' => 'CONDOMINIO EDILICIO',
            '3107' => 'COMISSAO DE CONCILIACAO PREVIA',
            '3115' => 'ENTIDADE DE MEDIACAO E ARBITRAGEM',
            '3123' => 'PARTIDO POLITICO',
            '3131' => 'ENTIDADE SINDICAL',
            '3204' => 'ESTABELECIMENTO, NO BRASIL, DE FUNDACAO OU ASSOCIACAO ESTRANGEIRAS',
            '3212' => 'FUNDACAO OU ASSOCIACAO DOMICILIADA NO EXTERIOR',
            '3220' => 'ORGANIZACAO RELIGIOSA',
            '3239' => 'COMUNIDADE INDIGENA',
            '3247' => 'FUNDO PRIVADO',
            '3999' => 'ASSOCIACAO PRIVADA',
            '4014' => 'EMPRESA INDIVIDUAL IMOBILIARIA',
            '4022' => 'SEGURADO ESPECIAL',
            '4081' => 'CONTRIBUINTE INDIVIDUAL',
            '4090' => 'CANDIDATO A CARGO POLITICO ELETIVO',
            '4111' => 'LEILOEIRO',
            '5010' => 'ORGANIZACAO INTERNACIONAL',
            '5029' => 'REPRESENTACAO DIPLOMATICA ESTRANGEIRA',
            '5037' => 'OUTRAS INSTITUICOES EXTRATERRITORIAIS',
            '8885' => 'NATUREZA JURIDICA INVALIDA'
        );

        return $aCnj[$cnj];
    }
}
