<?php

namespace App\Crawler\DadosCadastraisPj;

use App\Crawler\ReceitaFederalPjGeneric\Models\ReceitaFederalPjGenericModel;
use App\Crawler\ReceitaFederalPjGeneric\Models\ReceitaFederalPjGenericCnaeSecundarioModel;
use App\Crawler\ReceitaFederalPjGeneric\Models\ReceitaFederalPjGenericQsaModel;
use App\Crawler\GenericInterface;
use App\Crawler\DadosCadastraisPj\SpinePj;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Util;
use Exception;

class DadosCadastraisPj extends Spider implements GenericInterface
{
    protected function start()
    {
        $spider = new SpinePj();
        return json_decode(json_encode($spider->search($this->param['cnpj'], 20, $this->param['recuperaQsa'])), true);
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['cnpj'])) {
            throw new Exception('Parâmetro CNPJ é obrigatório', 1);
        }

        $this->param['cnpj'] = str_pad(preg_replace("@\\D+@i", "", $this->param['cnpj']), 14, 0, STR_PAD_LEFT);

        $this->param['recuperaQsa'] = isset($this->param['recuperaQsa']) ? $this->param['recuperaQsa'] : true;

        if (!Document::validarCnpj($this->param['cnpj'])) {
            throw new Exception('Parâmetro CNPJ inválido', 1);
        }
    }

    public function parseToGeneric(array $data, string $sourceName): ReceitaFederalPjGenericModel
    {
        $model = new ReceitaFederalPjGenericModel();

        $model->source = $sourceName;
        $model->cnpj = Document::formatCnpj($data['cnpj']);
        $model->tipo = $data['tipoEstabelecimento'];
        $model->data_abertura = $data['dataAbertura'];
        $model->nome_empresarial = $data['razaoSocial'];
        $model->nome_fantasia = $data['nomeFantasia'];
        $model->atividade_economica_principal = Util::cnaeFormat($data['cnaeSegmento'])
            . ' - ' . $data['cnaeDescricao'];
        $model->natureza_juridica = Util::naturezaJuridicaFormat($data['naturezaJuridicaCodigo']);
        $model->logradouro = $data['enderecos'][0]['Logradouro'] ?? null;
        $model->numero = $data['enderecos'][0]['LogradouroNumero'] ?? null;
        $model->complemento = $data['enderecos'][0]['LogradouroComplemento'] ?? null;
        $model->cep = isset($data['enderecos'][0]['CEP']) ? Util::cepFormat($data['enderecos'][0]['CEP']) : null;
        $model->bairro = $data['enderecos'][0]['Bairro'] ?? null;
        $model->municipio = $data['enderecos'][0]['Cidade'] ?? null;
        $model->uf = $data['enderecos'][0]['UF'] ?? null;
        $model->endereco_eletronico = $data['emails'][0]['EnderecoEmail'];
        $model->telefone = $data['telefones'][0]['TelefoneComDDD'];
        $model->situacao_cadastral = $data['situacaoCadastral'];
        $model->data_situacao = $data['dataSituacao'];
        $model->motivo_situacao = $data['motivoSituacao'];
        $model->cod_atividade = Util::cnaeFormat($data['cnaeSegmento']);
        $model->nome_atividade = $data['cnaeDescricao'];
        $model->cod_natureza = Util::naturezaJuridicaFormat($data['naturezaJuridicaCodigo']);
        $model->nome_natureza = $data['naturezaJuridicaDescricao'];
        $model->cnpj_uf = $model->cnpj . '|' . $model->uf;
        $model->initial_capital = $data['capitalSocial'];

        foreach ($data['atividadesSecundarias'] as $cnae) {
            $modelCnae = new ReceitaFederalPjGenericCnaeSecundarioModel();

            $modelCnae->codigo = Util::cnaeFormat($cnae['codigo']);
            $modelCnae->descricao = $cnae['descricao'];
            $modelCnae->atividade_economica_secundaria = $modelCnae->codigo . ' - ' . $modelCnae->descricao;

            $model->aAtividadeSecundaria[] = $modelCnae;
        }

        foreach ($data['socios'] as $qsa) {
            $modelQsa = new ReceitaFederalPjGenericQsaModel();

            $modelQsa->name = $qsa['nome'];
            $modelQsa->qualification = $qsa['qualificacao'];
            $modelQsa->qsa = $qsa['nome'] . ' - ' . $qsa['qualificacao'];

            $model->aQsa[] = $modelQsa;
        }

        return $model;
    }
}
