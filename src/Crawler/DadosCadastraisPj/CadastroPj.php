<?php

namespace App\Crawler\DadosCadastraisPj;

/**
 * Created by PhpStorm.
 * User: smakhoul
 * Date: 24/06/16
 * Time: 10:47
 */
class CadastroPj
{
    /**
     * Informações Gerais
     */

    /**
     * @var string
     */
    public $cnpj;

    /**
     * @var string
     */
    public $razaoSocial;

    /**
     * @var string
     */
    public $nomeFantasia;

    /**
     * @var string
     */
    public $matriz;

    /**
     * @var string
     */
    public $dataAbertura;

    /**
     * @var string
     */
    public $situacaoCadastral;

    /**
     * @var string
     */
    public $dataSituacao;

    /**
     * @var string
     */
    public $naturezaJuridicaCodigo;

    /**
     * @var string
     */
    public $naturezaJuridicaDescricao;

    /**
     * @var string
     */
    public $tipoCnae;

    /**
     * @var string
     */
    public $cnae;

    /**
     * @var string
     */
    public $cnaeSegmento;

    /**
     * @var string
     */
    public $cnaeDescricao;

    /**
     * @var string
     */
    public $dominio;

    /**
     * @var string
     */
    public $catchall;

    /**
     * Endereço
     */

    /**
     * @var array
     */
    public $enderecos;

    /**
     * Telefones
     */

    /**
     * @var array
     */
    public $telefones;

    /**
     * Emails
     */

    /**
     * @var array
     */
    public $emails;

    /**
     * Quadro de Sócios e Administradores
     */

    /**
     * @var array
     */
    public $socios;

    /**
     * @var array
     */
    public $atividadesSecundarias;

    /**
     * Simples Nacional
     */

    /**
     * @var array
     */
    public $simplesNacional;

    /**
     * @var string
     */
    public $optanteSimples;

    /**
     * Numero de Filiais
     *
     * @var string
     */
    public $numeroFiliais;

    /**
     *  Porte Financeiro
     */

    /**
     * @var string
     */
    public $capitalSocial;

    /**
     * @var string
     */
    public $porte;

    /**
     * @var string
     */
    public $setor;

    /**
     * @var string
     */
    public $faixaFuncionarios;

    /**
     * @var string
     */
    public $faturamentoAnualEstimado;

    /**
     * @var string
     */
    public $tipo;

    /**
     * @var string
     */
    public $tipoEstabelecimento;

    /**
     * @var string
     */
    public $operacionalidade;

    /**
     * @var string
     */
    public $motivoSituacao;

    /**
     * Classe Risco
     */

    /**
     * @var string
     */
    public $classeRisco;

    /**
     * @var array
     */
    public $ultimaAtualizacao;

    /**
     * @var string
     */
    public $dataConsulta;

    public function setCampos(array $data)
    {
        foreach ($data as $field => $value) {
            call_user_func_array(array($this, 'set' . ucfirst($field)), array($value));
        }
    }

    /**
     * @return string
     */
    public function getCnpj()
    {
        return $this->cnpj;
    }

    /**
     * @param string $cnpj
     */
    public function setCnpj($cnpj)
    {
        $this->cnpj = $cnpj;
    }

    /**
     * @return string
     */
    public function getRazaoSocial()
    {
        return $this->razaoSocial;
    }

    /**
     * @param string $razaoSocial
     */
    public function setRazaoSocial($razaoSocial)
    {
        $this->razaoSocial = $razaoSocial;
    }

    /**
     * @return string
     */
    public function getNomeFantasia()
    {
        return $this->nomeFantasia;
    }

    /**
     * @param string $nomeFantasia
     */
    public function setNomeFantasia($nomeFantasia)
    {
        $this->nomeFantasia = $nomeFantasia;
    }

    /**
     * @return string
     */
    public function getDataAbertura()
    {
        return $this->dataAbertura;
    }

    /**
     * @param string $dataAbertura
     */
    public function setDataAbertura($dataAbertura)
    {
        $this->dataAbertura = $dataAbertura;
    }

    /**
     * @return string
     */
    public function getDominio()
    {
        return $this->dataAbertura;
    }

    /**
     * @param string $dominio
     */
    public function setDominio($dominio)
    {
        $this->dominio = $dominio;
    }

    /**
     * @return string
     */
    public function getCatchall()
    {
        return $this->catchall;
    }

    /**
     * @param string $catchall
     */
    public function setCatchall($catchall)
    {
        //Fiz isso porque quando $catchall vinha vázio, eu recebia false no upmap
        if ($catchall === true) {
            $this->catchall = 'true';
        } elseif ($catchall === false) {
            $this->catchall = 'false';
        } else {
            $this->catchall = '';
        }
    }

    /**
     * @return string
     */
    public function getCnaeSegmento()
    {
        return $this->cnaeSegmento;
    }

    /**
     * @param string $cnaeSegmento
     */
    public function setCnaeSegmento($cnaeSegmento)
    {
        $this->cnaeSegmento = $cnaeSegmento;
    }

    /**
     * @return string
     */
    public function getCnaeDescricao()
    {
        return $this->cnaeDescricao;
    }

    /**
     * @param string $cnaeDescricao
     */
    public function setCnaeDescricao($cnaeDescricao)
    {
        $this->cnaeDescricao = $cnaeDescricao;
    }

    /**
     * @return string
     */
    public function getFaixaFuncionarios()
    {
        return $this->faixaFuncionarios;
    }

    /**
     * @param string $faixaFuncionarios
     */
    public function setFaixaFuncionarios($faixaFuncionarios)
    {
        $this->faixaFuncionarios = $faixaFuncionarios;
    }

    /**
     * @return string
     */
    public function getSituacaoCadastral()
    {
        return $this->situacaoCadastral;
    }

    /**
     * @param string $situacaoCadastral
     */
    public function setSituacaoCadastral($situacaoCadastral)
    {
        $this->situacaoCadastral = $situacaoCadastral;
    }

    /**
     * @return string
     */
    public function getNaturezaJuridicaCodigo()
    {
        return $this->naturezaJuridicaCodigo;
    }

    /**
     * @param string $naturezaJuridicaCodigo
     */
    public function setNaturezaJuridicaCodigo($naturezaJuridicaCodigo)
    {
        $this->naturezaJuridicaCodigo = $naturezaJuridicaCodigo;
    }

    /**
     * @return string
     */
    public function getNaturezaJuridicaDescricao()
    {
        return $this->naturezaJuridicaDescricao;
    }

    /**
     * @param string $naturezaJuridicaDescricao
     */
    public function setNaturezaJuridicaDescricao($naturezaJuridicaDescricao)
    {
        $this->naturezaJuridicaDescricao = $naturezaJuridicaDescricao;
    }

    /**
     * @return string
     */
    public function getPorte()
    {
        return $this->porte;
    }

    /**
     * @param string $porte
     */
    public function setPorte($porte)
    {
        $this->porte = $porte;
    }

    /**
     * @return string
     */
    public function getTipoCnae()
    {
        return $this->tipoCnae;
    }

    /**
     * @param string $tipoCnae
     */
    public function setTipoCnae($tipoCnae)
    {
        $this->tipoCnae = $tipoCnae;
    }

    /**
     * @return array
     */
    public function getTelefones()
    {
        return $this->telefones;
    }

    /**
     * @param array $telefones
     */
    public function setTelefones($telefones)
    {
        $this->telefones = $telefones;
    }

    /**
     * @return array
     */
    public function getEmails()
    {
        return $this->emails;
    }

    /**
     * @param array $emails
     */
    public function setEmails($emails)
    {
        $this->emails = $emails;
    }

    /**
     * @return array
     */
    public function getEnderecos()
    {
        return $this->enderecos;
    }

    /**
     * @param array $enderecos
     */
    public function setEnderecos($enderecos)
    {
        $this->enderecos = $enderecos;
    }

    /**
     * @return array
     */
    public function getSocios()
    {
        return $this->socios;
    }

    /**
     *  @param array $socios
     */
    public function setSocios($socios)
    {
        $this->socios = $socios;
    }

    /**
     *  @return array
     */
    public function getUltimaAtualizacao()
    {
        return $this->ultimaAtualizacao;
    }

    /**
     * @param array $ultimaAtualizacao
     */
    public function setUltimaAtualizacao($ultimaAtualizacao)
    {
        $this->ultimaAtualizacao = $ultimaAtualizacao;
    }

    /**
     * @return string
     */
    public function getDataConsulta()
    {
        return $this->dataConsulta;
    }

    /**
     * @param string $dataConsulta
     */
    public function setDataConsulta($dataConsulta)
    {
        $this->dataConsulta = $dataConsulta;
    }

    /**
     * @return string
     */
    public function getSetor()
    {
        return $this->setor;
    }

    /**
     * @param string $setor
     */
    public function setSetor($setor)
    {
        $this->setor = $setor;
    }

    /**
     * @return string
     */
    public function getCapitalSocial()
    {
        return $this->capitalSocial;
    }

    /**
     * @param string $capitalSocial
     */
    public function setCapitalSocial($capitalSocial)
    {
        $this->capitalSocial = $capitalSocial;
    }

    /**
     * @return string
     */
    public function getDataSituacao()
    {
        return $this->dataSituacao;
    }

    /**
     * @param string $dataSituacao
     */
    public function setDataSituacao($dataSituacao)
    {
        $this->dataSituacao = $dataSituacao;
    }

    /**
     * @return string
     */
    public function getMotivoSituacao()
    {
        return $this->motivoSituacao;
    }

    /**
     * @param string $motivoSituacao
     */
    public function setMotivoSituacao($motivoSituacao)
    {
        $this->motivoSituacao = $motivoSituacao;
    }

    /**
     * @return string
     */
    public function getIbge()
    {
        return $this->ibge;
    }

    /**
     * @param string $ibge
     */
    public function setIbge($ibge)
    {
        $this->ibge = $ibge;
    }

    /**
     * @return string
     */
    public function getFaturamentoAnualEstimado()
    {
        return $this->faturamentoAnualEstimado;
    }

    /**
     * @param string $faturamentoAnualEstimado
     */
    public function setFaturamentoAnualEstimado($faturamentoAnualEstimado)
    {
        $this->faturamentoAnualEstimado = $faturamentoAnualEstimado;
    }

    /**
     * @return string
     */
    public function getOperacionalidade()
    {
        return $this->operacionalidade;
    }

    /**
     * @param string $operacionalidade
     */
    public function setOperacionalidade($operacionalidade)
    {
        $this->operacionalidade = $operacionalidade;
    }

    /**
     * @return string
     */
    public function getClasseRisco()
    {
        return $this->classeRisco;
    }

    /**
     * @param string $classeRisco
     */
    public function setClasseRisco($classeRisco)
    {
        $this->classeRisco = $classeRisco;
    }

    /**
     * @return string
     */
    public function getCnae()
    {
        return $this->cnae;
    }

    /**
     * @param string $cnae
     */
    public function setCnae($cnae)
    {
        $this->cnae = $cnae;
    }

    /**
     * @return string
     */
    public function getMatriz()
    {
        return $this->matriz;
    }

    /**
     * @param string $matriz
     */
    public function setMatriz($matriz)
    {
        $this->matriz = $matriz;
    }

    /**
     * @return string
     */
    public function getTipo()
    {
        return $this->tipo;
    }

    /**
     * @param string $tipo
     */
    public function setTipo($tipo)
    {
        $this->tipo = $tipo;
    }

    /**
     * @return string
     */
    public function getTipoEstabelecimento()
    {
        return $this->tipoEstabelecimento;
    }

    /**
     * @param string $tipoEstabelecimento
     */
    public function setTipoEstabelecimento($tipoEstabelecimento)
    {
        $this->tipoEstabelecimento = $tipoEstabelecimento;
    }

    /**
     * @return string
     */
    public function getSimplesNacional()
    {
        return $this->simplesNacional;
    }

    /**
     * @param string $simplesNacional
     */
    public function setSimplesNacional($simplesNacional)
    {
        $this->simplesNacional = $simplesNacional;
    }

    /**
     * @return string
     */
    public function getOptanteSimples()
    {
        return $this->optanteSimples;
    }

    /**
     * @param string $optanteSimples
     */
    public function setOptanteSimples($optanteSimples)
    {
        $this->optanteSimples = $optanteSimples;
    }

    /**
     * @return string
     */
    public function getNumeroFiliais()
    {
        return $this->numeroFiliais;
    }

    /**
     * @param string $numeroFiliais
     */
    public function setNumeroFiliais($numeroFiliais)
    {
        $this->numeroFiliais = $numeroFiliais;
    }

    /**
     * @return array
     */
    public function getAtividadesSecundarias()
    {
        return $this->atividadesSecundarias;
    }

    /**
     *  @param array $atividadesSecundarias
     */
    public function setAtividadesSecundarias($atividadesSecundarias)
    {
        $this->atividadesSecundarias = $atividadesSecundarias;
    }
}
