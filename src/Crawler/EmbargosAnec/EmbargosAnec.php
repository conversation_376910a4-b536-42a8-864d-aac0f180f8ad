<?php

namespace App\Crawler\EmbargosAnec;

use Exception;
use App\Crawler\Spider;
use App\Factory\MongoDB;
use App\Helper\Document;

/**
 * Classe de consulta dos dados na Transparência PEP
 *
 * <AUTHOR>
 */
class EmbargosAnec extends Spider
{
    private const INDEX_MONGODB = 'document_name';
    private const LIMIT = 50;
    private $documento = '';

    protected function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCpfOuCnpj($this->param['cpf_cnpj'])) {
            throw new Exception('Documento invalido', 1);
        } else {
            $this->documento = Document::removeMask($this->param['cpf_cnpj']);
        }
    }

    protected function start()
    {
        $results = $this->searchByDoc();

        if (empty($results)) {
            throw new Exception('Nenhum registro encontrado', 2);
        }

        return $this->parseResulsts($results);
    }

    private function parseResulsts($results)
    {
        foreach ($results as $value) {
            $data[] = [
                'id' => $value['id'],
                'fazenda_id' => $value['fazenda_id'],
                'fazenda_nome' => $value['fazenda_nome'],
                'proprietario_doc' => $value['proprietario_doc'],
                'proprietario_nome' => $value['proprietario_nome'],
                'ano' => $value['ano'],
                'longitude' => $value['longitude'],
                'latitude' => $value['latitude'],
                'uf' => $value['uf'],
                'municipio' => $value['municipio']
            ];
        }

        return $data;
    }

    private function searchByDoc()
    {
        $doc = $this->documento;

        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('common')
            ->setCollection('embargos_anec');
        $fields = ['proprietario_doc','proprietario_nome'];
        $result = json_decode(
            json_encode(
                $manager
                    ->atlasSearch(
                        $doc,
                        self::INDEX_MONGODB,
                        self::LIMIT,
                        $fields
                    )
                    ->toArray(),
                true
            ),
            true
        );

        return $result;
    }
}
