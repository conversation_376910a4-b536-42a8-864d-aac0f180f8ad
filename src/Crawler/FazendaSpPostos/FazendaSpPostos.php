<?php

namespace App\Crawler\FazendaSpPostos;

use App\Crawler\FazendaSpPostos\Models\FazendaSpPostoModel;
use App\Crawler\Spider;
use App\Helper\Document;
use Exception;

class FazendaSpPostos extends Spider
{
    private $criterio = 'NomePosto';
    private $envVars = [];
    private $postos = [];
    private const URL = 'http://www.fazenda.sp.gov.br/PostoCombustivel/Sistema/Publico/Pesquisa2.aspx';

    public function start()
    {
        $this->requestResults();
        return $this->postos;
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['cnpj_nome'])) {
            throw new Exception('Informe um parâmetro válido.');
        }

        if (Document::validarCnpj($this->param['cnpj_nome'])) {
            $this->param['cnpj_nome'] = Document::removeMask($this->param['cnpj_nome']);
            $this->criterio = 'CNPJ';
        }
    }

    private function requestResults()
    {
        $this->setEnvVars();
        $params = [
            'Nome' => $this->param['cnpj_nome'],
            'Criterio' => $this->criterio,
            'btnPesquisa' => 'Pesquisar',
            'Mes' => '',
            'Ano' => '',
            'DRT' => ''
        ];
        $params = array_merge($params, $this->envVars);
        $response = $this->getResponse(self::URL, 'POST', $params);
        $this->parseResults($response);
    }

    private function setEnvVars()
    {
        $response = $this->getResponse(self::URL, 'POST');
        $idVars = array('__LASTFOCUS', '__EVENTTARGET', '__EVENTARGUMENT', '__VIEWSTATEGENERATOR',
                        '__VIEWSTATE', '__EVENTVALIDATION', '__PREVIOUSPAGE');
        $params = [];

        foreach ($idVars as $var) {
            preg_match('/<input.*?name="' . $var . '".*?value="(.*)"/', $response, $value);
            $params[$var] = $value[1];
        }

        return $this->envVars = $params;
    }

    private function parseResults($results)
    {
        $trs = $this->getTrs($results);
        $this->getTds($trs);
    }

    private function getTrs($results)
    {
        if (preg_match_all('@<tr class="tabulacao6">(.*?)<\/tr>@is', $results, $trs)) {
            return $trs[1];
        }

        throw new Exception('Não foi encontrado nenhum posto que atendesse ao critério informado', 2);
    }

    private function getTds($trs)
    {
        foreach ($trs as $tr) {
            preg_match_all('@<td>(.*?)<\/td>@is', $tr, $tds);
            $this->parseObject($tds[1]);
        }
    }

    private function parseObject($tds)
    {
        $posto = new FazendaSpPostoModel();
        $posto->dataDoe = $tds[0];
        $posto->nome = $tds[1];
        $posto->endereco = $tds[2];
        $posto->bairro = $tds[3];
        $posto->cidade = $tds[4];
        $posto->liminar = $tds[5];
        $posto->cnpj = $tds[6];
        $posto->ie = $tds[7];
        $posto->drt = $tds[8];

        $this->postos[] = $posto;
    }
}
