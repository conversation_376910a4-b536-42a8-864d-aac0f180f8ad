<?php

namespace App\Crawler\CertidaoNegativaRN;

use Exception;
use App\Helper\Pdf;
use App\Helper\Document;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Manager\S3\Bucket\StaticUplexisBucket;

class CertidaoNegativaRN extends Spider
{
    private const URL = 'https://pje.tjrn.jus.br/certidaoPjeWeb/index.jsf';
    private const MAIN_URL = 'https://pje.tjrn.jus.br/certidaoPjeWeb/';
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const CERTIDAOCONJUNTA_S3_PATH = 'captura/Certidao_negativa_RN/';

    private $tipo = '';
    private $cnpj = '';
    private $cpf = '';
    private $pdf = '';

    protected function validateAndSetCrawlerAttributes()
    {
        if (Document::validarCnpj($this->param['documento'])) {
            $this->cnpj = Document::formatCnpj($this->param['documento']);
            $this->tipo = 'cnpj';
        } elseif (Document::validarCpf($this->param['documento'])) {
            $this->cpf = Document::formatCpf($this->param['documento']);
            $this->tipo = 'cpf';
        } else {
            throw new Exception("Documento inválido", 6);
        }
    }

    protected function start()
    {
        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::CERTIDAOCONJUNTA_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;


        $this->setProxy();
        $this->setCurlOpt([
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        try {
            $html = $this->getResponse(self::MAIN_URL);
            $javaxpattern = '/javax\.faces\.ViewState.*ue=\"(.*?)\"/m';
            preg_match($javaxpattern, $html, $javax);

            if ($this->tipo == 'cnpj') {
                $pdf = $this->getPdfCnpj($javax[1]);
            } else {
                $pdf = $this->getPdfCpf($javax[1]);
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), 3);
        }

        $this->savePdf($pdf);

        $data = [
            'pdf' => $this->pdf
        ];

        return $data;
    }

    private function getPdfCnpj($javax)
    {
        $params = [
            'form' => 'form',
            'form:j_idt43' => '',
            'form:cbModelo' => 'CERTIDAO_FALENCIA_RECUPERACAO_JUDICIAL',
            'form:radio' => 'CPJ',
            'form:txtnome' => $this->param['nome'],
            'form:txtcnpj' => $this->cnpj,
            'javax.faces.ViewState' => $javax
        ];
        $pdf = $this->getResponse(self::URL, 'POST', $params);
        return $pdf;
    }

    private function getPdfCpf($javax)
    {
        $params = [
            'form' => 'form',
            'form:j_idt43' => '',
            'form:cbModelo' => 'CERTIDAO_FALENCIA_RECUPERACAO_JUDICIAL',
            'form:radio' => 'CPF',
            'form:txtnome' => $this->param['nome'],
            'form:txtcpf' => $this->cpf,
            'javax.faces.ViewState' => $javax
        ];
        $pdf = $this->getResponse(self::URL, 'POST', $params);
        return $pdf;
    }

    private function savePdf($pdf)
    {
        file_put_contents($this->certificateLocalPath, $pdf);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        $this->pdf = $this->certificateUrl;
    }
}
