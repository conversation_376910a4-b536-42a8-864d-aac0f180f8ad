<?php

namespace App\Crawler\GoogleMapsEndereco;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Crawler\ColunaVertebralConsultaPj\Models\ColunaVertebralPjModel;
use App\Crawler\ColunaVertebralConsultaPj\Models\ColunaVertebralPjCnjModel;
use App\Crawler\ColunaVertebralConsultaPj\Models\ColunaVertebralPjCnaeModel;
use App\Manager\ColunaVertebralManager;
use Exception;

/**
 * Classe da fonte Google Maps Endereco
 *
 * @version 1.0.0
 *
 * <AUTHOR> 03/01/2023
 */
class GoogleMapsEndereco extends Spider
{
    private $colunaVertebralManager;

    /**
     * Busca os dados de uma empresa no spine PJ
     *
     * @version 1.0.0
     *
     * <AUTHOR> 03/01/2023
     *
     * @return array
     */
    public function start()
    {
        $this->colunaVertebralManager = new ColunaVertebralManager();

        return $this->searchByCnpj();
    }

    /**
     * Valida os parametros da fonte
     *
     * @version 1.0.0
     *
     * <AUTHOR> 03/01/2023
     *
     * @return void
     */
    public function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCnpj($this->param['cnpj'])) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }

        $this->param['cnpj'] = Document::removeMask($this->param['cnpj']);
    }

    /**
     * Retorna o endereço do PJ informado
     *
     * @version 1.0.0
     *
     * <AUTHOR> Kuniyoshi 03/01/2023
     *
     * @return array
     */
    private function searchByCnpj(): array
    {
        $spinePj = $this->colunaVertebralManager->getSpinePj($this->param['cnpj']);
        $result = [];
        $result['logradouro'] = "{$spinePj['logr_tipo']} {$spinePj['logr_nome']}";
        $result['numero'] = $spinePj['logr_numero'];
        $result['complemento'] = $spinePj['logr_complemento'];
        $result['bairro'] = $spinePj['bairro'];
        $result['municipio'] = $spinePj['cidade'];
        $result['uf'] = $spinePj['uf'];
        $result['cep'] = $spinePj['cep'];
        return $result;
    }
}
