<?php

namespace App\Crawler\EmpresasBeneficiariasPAT;

use App\Crawler\Spider;
use App\Factory\MongoDB;
use App\Helper\Document;
use App\Helper\Str;
use Exception;

class EmpresasBeneficiariasPAT extends Spider
{
    private $limit;

    public function start()
    {
        $nome_cnpj = $this->param['nome_cnpj'];

        if (preg_match("/^[a-zA-Z]/", $nome_cnpj)) {
            $nome_cnpj = Str::removerAcentos($nome_cnpj);
        }

        if (preg_match("/^\d/", $nome_cnpj)) {
            $nome_cnpj = Document::removeMask($nome_cnpj);
        }

        $result = $this->searchData($nome_cnpj);

        if (empty($result)) {
            throw new Exception("A pesquisa não encontrou nenhum dado correspondente.", 2);
        }

        return $result;
    }


    /** Função de busca na base de dados pelo nome Razão Social ou CNPJ
     * @param $nome_cnpj
     * @return mixed[]
     * @throws Exception
     */
    public function searchData($nome_cnpj)
    {
        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('common')
            ->setCollection('empresas_beneficiarias_pat');

        $result = $manager->textSearch($nome_cnpj, $this->limit);

        foreach ($result as $value) {
            $array[] = $this->parseResult($value);
        }

        if (!$result) {
            throw new Exception('Nenhum resultado encontrado', 2);
        }

        return $array;
    }


    public function parseResult($array)
    {
        return [
            "uf" => $array['uf'],
            "cod_municipio" => $array['cod_municipio'],
            "tipo_inscricao" => $array['tipo_inscricao'],
            "cnpj_cei" => $array['cnpj_cei'],
            "razao_social" => $array['razao_social'],
            "numero_inscricao" => $array['numero_inscricao'],
            "cnae" => $array['cnae'],
            "qnt_estabelecimentos" => $array['qnt_estabelecimentos'],
            "qnt_trabalhadores" => $array['qnt_trabalhadores'],
            "municipio" => $array['municipio']
        ];
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->limit = $this->param['limit'] ?? 100;

        $nome_cnpj = trim($this->param['nome_cnpj']);

        if (empty($nome_cnpj)) {
            throw new Exception('Parâmetro Inválido', 1);
        }
    }
}
