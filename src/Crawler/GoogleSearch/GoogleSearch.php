<?php

namespace App\Crawler\GoogleSearch;

use App\Crawler\Spider;
use Exception;

class GoogleSearch extends Spider
{
    private const URL_GOOGLE_SEARCH = 'https://www.googleapis.com/customsearch/v1?key=';
    private const CHAVE_API_GOOGLE_1 = 'AIzaSyD3cZ2QPOg1WzKF-eenIO37d77JmTnXJ0A';
    private const CHAVE_API_GOOGLE_2 = 'AIzaSyB6o0Al0Dt-awGN395y_BgNvi4oiJc5-yo';

    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['company'])) {
            throw new Exception('Parâmetro inválido');
        }
    }

    protected function start()
    {
        $response = $this->getGoogleSearch($this->param['company']);

        if (empty($response['items'])) {
            throw new Exception('Nada encontrado', 2);
        }

        return $response['items'];
    }

    /**
     * Realiza a consulta no Google Search
     *
     * @version 1.0.0
     * <AUTHOR> - 11/09/2018
     *
     * @param string $company
     *
     * @return []
     *
     */
    private function getGoogleSearch($company)
    {
        $parameters = '&cx=011037585287182197482:vjyua_g4uy0&num=5&q=';
        $response = $this->getResponse(self::URL_GOOGLE_SEARCH . $this->randKeysApi() . $parameters . $company, 'GET');
        return json_decode($response, true);
    }

    private function randKeysApi()
    {
        $api_keys = array(self::CHAVE_API_GOOGLE_1, self::CHAVE_API_GOOGLE_2);
        return $api_keys[array_rand($api_keys, 1)];
    }
}
