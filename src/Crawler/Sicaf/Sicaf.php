<?php

namespace App\Crawler\Sicaf;

use App\Crawler\Spider;
use Exception;
use App\Helper\Document;
use App\Helper\Util;
use App\Crawler\Sicaf\SicafType;

/**
 * Classe da fonte Sicaf (281)
 *
 * @version 1.0.0
 *
 * <AUTHOR> Mesquita 09/09/2019
 *  *
 */
class Sicaf extends Spider
{
    private const MAIN_URL = 'https://www3.comprasnet.gov.br/sicaf-web/' .
        'public/pages/consultas/consultarRestricaoContratarAdministracaoPublica.jsf';
    private const TYPE_PJ = 'PJ';
    private const TYPE_PF = 'PF';
    private const DEFAULT_LIMIT = 20;

    private $sicafType;
    private $type;
    private $searchParam;

    protected function start()
    {
        $this->setProxy();

        $this->sicafType = new SicafType();

        $results = $this->getSourceResult();

        return $results;
    }


    /**
     * Valida os critérios da fonte
     *
     * @version 1.0.0
     *
     * <AUTHOR> Me<PERSON>quita 09/09/2019
     *
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            echo __METHOD__, "\n";
        }

        $this->searchParam =  $this->param['criterio'];

        if (!Document::validarCpfOuCnpj($this->searchParam)) {
            throw new Exception('Parâmetro ou critério inválido.', 6);
        }

        if (Document::validarCnpj($this->searchParam)) {
            $this->type = self::TYPE_PJ;
        }

        if (Document::validarCpf($this->searchParam)) {
            $this->type = self::TYPE_PF;
        }

        $this->searchParam =  \App\Helper\Document::formatCpfOrCnpj($this->searchParam);
    }

    /**
     * Retorna o resultado da fonte
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 09/09/2019
     *
     * <AUTHOR> Vidal 28/01/2021 - Alteração para recaptcha
     *
     * @return array
     */
    private function getSourceResult()
    {
        if ($this->debug) {
            echo __METHOD__, "\n";
        }

        $firstHtml = $this->getResponse(self::MAIN_URL);

        $viewState = $this->getViewState($firstHtml);

        $this->sicafType->getSicafPrefix($firstHtml);

        if ($this->type == self::TYPE_PF) {
            $firstHtml = $this->changeToPf($viewState);

            $this->sicafType->getSicafPrefix($firstHtml);
        }

        $key = $this->getReCaptchaKey($firstHtml);

        $recaptcha = $this->resolveReCaptcha($key);

        $response = utf8_decode(
            $this->getSearchHtml($viewState, $recaptcha)
        );

        $this->checkErrors($response);

        return $this->parse($response);
    }

    /**
     * Pega o ViewState no html
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 09/09/2019
     *
     * @return string
     */
    private function getViewState($html)
    {
        if ($this->debug) {
            echo __METHOD__, "\n";
        }

        $patternViewState = '/<input[^>]*id="javax\.faces\.ViewState"\s*value="([^>]*)"\s[^>]*>/isU';

        $dados = Util::parseDados(
            [
                'viewState' => [
                    $patternViewState,
                    null
                ]
            ],
            $html
        );

        if (empty($dados['viewState'])) {
            throw new Exception('Não foi possível encontrar o viewState no html', 3);
        }

        return $dados['viewState'];
    }

    /**
     * Altera a busca para Pessoa Física
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 09/09/2019
     *
     * @return string
     */
    private function changeToPf($viewState)
    {
        if ($this->debug) {
            echo __METHOD__, "\n";
        }
        return $this->getResponse(self::MAIN_URL, 'POST', $this->sicafType->getChangePfParam($viewState));
    }

    /**
     * Envia busca e pega html com os resultados
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 09/09/2019
     *
     * <AUTHOR> Vidal 28/01/2021 - Alteração para recaptcha
     *
     * @return string
     */
    private function getSearchHtml($viewState, $recaptcha)
    {
        if ($this->debug) {
            echo __METHOD__, "\n";
        }

        $param = $this->sicafType->getParams(
            $this->searchParam,
            $this->type,
            $recaptcha,
            $viewState
        );

        return $this->getResponse(self::MAIN_URL, 'POST', $param);
    }

    /**
     * Verifica se houve algum erro após efetuar a busca
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 09/09/2019
     *
     */
    private function checkErrors($response)
    {
        if ($this->debug) {
            echo __METHOD__, "\n";
        }
        $patternMessages = '/msgs:..summary:"(?<messages>[^>]*)",detail/i';
        $matches = [];

        if (preg_match($patternMessages, $response, $matches)) {
            if (preg_match('/Campo.C.digo.de.seguran.a.inv.lido./i', $matches['messages'])) {
                throw new Exception('Problema para quebrar o captcha', 3);
            }

            if (preg_match('/Fornecedor.n.o.credenciado./i', $matches['messages'])) {
                throw new Exception('Nenhum dado encontrado', 2);
            }

            throw new Exception('Erro no resultado: ' . $matches['messages'], 3);
        }

        if (Document::validarCpfOuCnpj($this->searchParam)) {
            if (!preg_match('#<legend[^>]*>detalhar</legend>#i', $response)) {
                throw new Exception('Problema com o resultado da fonte', 3);
            }
        }
    }

    /**
     * Parse dos dados de resposta
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 09/09/2019
     *
     * @return array
     */
    private function parse($response)
    {
        if ($this->debug) {
            echo __METHOD__, "\n";
        }
        $result = Util::parseDados($this->getParsePatterns(), $response);
        return array_map('self::parseResult', $result);
    }

    /**
     * Patterns para parse dos dados
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 09/09/2019
     *
     * @return array
     */
    private function getParsePatterns()
    {
        if ($this->debug) {
            echo __METHOD__, "\n";
        }
        if ($this->type == self::TYPE_PJ) {
            return [
                'cnpj' => ['/cnpj.*?informacao.*?>(.*?)<\/label/si', null],
                'razao_social' => ['/raz.*?o\ssocial.*?informacao.*?>(.*?)<\/label/si', null],
                'nome_fantasia' => ['/nome\sfantasia.*?informacao.*?>(.*?)<\/label/si', null],
                'situacao' => ['/situa.*?o<.*?informacao.*?>(.*?)<\/label/si', null],
                'situacao_cadastral' => ['/cadastral.*?informacao.*?>(.*?)<\/label/si', null]
            ];
        }

        return [
            'cpf' => ['/cpf.*?informacao.*?>(.*?)<\/label/si', null],
            'nome' => ['/nome.*?informacao.*?>(.*?)<\/label/si', null],
            'situacao' => ['/situa.*?o<.*?informacao.*?>(.*?)<\/label/si', null],
            'situacao_cadastral' => ['/cadastral.*?informacao.*?>(.*?)<\/label/si', null]
        ];
    }

    /**
     * Parse do conteúdo da resposta
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 09/09/2019
     *
     * @return string
     */
    private function parseResult($value)
    {
        if ($this->debug) {
            echo __METHOD__, "\n";
        }
        if (empty($value) || trim($value) == '-') {
            return null;
        }

        return $value;
    }

    /**
     * Resolve o captcha
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 09/09/2019
     *
     * <AUTHOR> Vidal 28/01/2021 - Alteração para recaptcha
     *
     * @return string
     */
    private function resolveReCaptcha($key)
    {
        if ($this->debug) {
            echo __METHOD__, "\n";
        }
        return $this->solveReCaptcha($key, self::MAIN_URL);
    }


    /**
     * Pega a chave do captcha no html
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 09/09/2019
     *
     * <AUTHOR> Vidal 28/01/2021 - Alteração para recaptcha
     *
     * @return string
     */
    private function getReCaptchaKey($html)
    {
        if ($this->debug) {
            echo __METHOD__, "\n";
        }
        $patternKey = '/<div[^>]*data-sitekey=\"(.*)\"\s*data-callback.*<\/div>/isU';

        if (!preg_match($patternKey, $html, $matches)) {
            throw new Exception('Não foi possível encontrar a key do recaptcha no html', 3);
        };

        return $matches[1];
    }
}
