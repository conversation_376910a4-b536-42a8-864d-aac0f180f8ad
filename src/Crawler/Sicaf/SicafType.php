<?php

namespace App\Crawler\Sicaf;

use Exception;
use App\Helper\Document;

/**
 * Classe de parametros fonte Sicaf (281) de acordo com o tipo
 *
 * @version 1.0.0
 *
 * <AUTHOR> Mesquita 09/09/2019
 */
class SicafType
{
    private const TYPE_PJ = 'PJ';
    private const TYPE_PF = 'PF';

    /**
     * A fonte Sicaf injeta nos ids das tags um número de dois digitos, aleatório.
     * O atributo abaixo armazena o prefixo contendo o número utilizando o nome do input de cnpj em um regex.
     * Ex.: f:j_idt49:tipoPessoaPesquisa, sendo "f:j_idt49:" o prefixo
     *
     * @var string
     */
    private $prefix;

    /**
     * Retor os parametros para busca no site
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 09/09/2019
     *
     * @return array
     */
    public function getParams($param, $type, $recaptcha, $viewState)
    {
        if (empty($this->prefix)) {
            throw new Exception('Não será possível prefixar os dados para consulta!', 3);
        }

        if ($type == self::TYPE_PF) {
            return $this->parseParamsCpf($param, $recaptcha, $viewState);
        }

        if ($type == self::TYPE_PJ) {
            return $this->parseParamsCnpj($param, $recaptcha, $viewState);
        }
    }

    /**
     * Retorna os parametros para PF
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 09/09/2019
     *
     * @return array
     */
    private function parseParamsCpf($param, $recaptcha, $viewState)
    {
        $result = [
            'f' => 'f',
            "{$this->prefix}tipoPessoaPesquisa" => 'PESSOA_FISICA',
            "{$this->prefix}cpfPesquisa" => $param,
            "{$this->prefix}nomePesquisa" => '',
            'g-recaptcha-response' => $recaptcha,
            "{$this->prefix}btnPesquisar" => '',
            'javax.faces.ViewState' => $viewState,
        ];

        if (!Document::validarCpf($param)) {
            $result["{$this->prefix}cpfPesquisa"] = '';
            $result["{$this->prefix}nomePesquisa"] = $param;
        }

        return $result;
    }

    /**
     * Retorna os parametros para PJ
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 09/09/2019
     *
     * <AUTHOR> Vidal 28/01/2021 - Alteração para recaptcha
     *
     * @return array
     */
    private function parseParamsCnpj($param, $recaptcha, $viewState)
    {
        $result = [
            'f' => 'f',
            "{$this->prefix}tipoPessoaPesquisa" => 'PESSOA_JURIDICA',
            "{$this->prefix}cnpjPesquisa" => $param,
            "{$this->prefix}nomePesquisa" => '',
            'g-recaptcha-response' => $recaptcha,
            "{$this->prefix}btnPesquisar" => '',
            'javax.faces.ViewState' => $viewState,
        ];

        if (!Document::validarCnpj($param)) {
            $result["{$this->prefix}cnpjPesquisa"] = '';
            $result["{$this->prefix}nomePesquisa"] = $param;
        }

        return $result;
    }

    /**
     * Retorna os parametros para alterar para PF
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 09/09/2019
     *
     * <AUTHOR> Vidal 28/01/2021 - Alteração para recaptcha
     *
     * @return array
     */
    public function getChangePfParam($viewState)
    {
        return [
            'javax.faces.partial.ajax' => true,
            'javax.faces.source' => "{$this->prefix}tipoPessoaPesquisa",
            'javax.faces.partial.execute' => "{$this->prefix}tipoPessoaPesquisa",
            'javax.faces.partial.render' => 'f',
            'javax.faces.behavior.event' => 'change',
            'javax.faces.partial.event' =>  'change',
            'f' =>  'f',
            "{$this->prefix}tipoPessoaPesquisa" => 'PESSOA_FISICA',
            "{$this->prefix}cnpjPesquisa" => '',
            "{$this->prefix}nomePesquisa" => '',
            'g-recaptcha-response' => '',
            'javax.faces.ViewState' => $viewState
        ];
    }

    /**
     * Retorna os parametros para acessar a página de detalhe
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 09/09/2019
     *
     * <AUTHOR> Vidal 28/01/2021 - Alteração para recaptcha
     *
     * @return array
     */
    public function getDetailParam($viewState, $id, $param)
    {
        return [
            'javax.faces.partial.ajax' =>   true,
            'javax.faces.source' => "f:resultado:{$id}:btnDetalhar",
            'javax.faces.partial.execute' => '@all',
            'javax.faces.partial.render' => 'f',
            "f:resultado:{$id}:btnDetalhar" =>  "f:resultado:{$id}:btnDetalhar",
            'f' =>  'f',
            "{$this->prefix}tipoPessoaPesquisa" => 'PESSOA_FISICA',
            "{$this->prefix}cpfPesquisa" => '',
            "{$this->prefix}nomePesquisa" => $param,
            'g-recaptcha-response' => '',
            'javax.faces.ViewState' => $viewState
        ];
    }

    public function getSicafPrefix($searchPageHtml)
    {
        $knownInputs = [
            "tipoPessoaPesquisa",
            "cpfPesquisa",
            "nomePesquisa",
            "btnPesquisar"
        ];

        foreach ($knownInputs as $input) {
            if (preg_match("/(f:j_idt\\d+:)${input}/i", $searchPageHtml, $result)) {
                $this->prefix = $result[1];
                break;
            }
        }
    }
}
