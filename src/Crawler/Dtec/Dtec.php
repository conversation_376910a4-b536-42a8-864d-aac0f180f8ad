<?php

namespace App\Crawler\Dtec;

use Exception;
use App\Helper\Util;
use SimpleXMLElement;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\BilhetadorFornecedoresDynamo;
use App\Manager\DtecManager;

class Dtec extends Spider
{
    private $query;
    private $base;
    private $limit;

    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['nome_documento'])) {
            throw new Exception('Parâmetro de criterio inválido', 1);
        }

        $nomeDocumento = trim($this->param['nome_documento']);

        if (Document::validarCpfOuCnpj($nomeDocumento)) {
            $cpfCnpj = preg_replace("/[^0-9]/", "", $nomeDocumento);
            $this->query = 'cpf:' . $cpfCnpj . '';
        } else {
            $nomeRazaoSocial = strtoupper(preg_replace("/\\s+/", "-", $nomeDocumento));
            $this->query = '(nome:' . $nomeRazaoSocial . ') OR (nome_cpf:' . $nomeRazaoSocial . ')';
        }

        if (empty($this->param['base'])) {
            throw new Exception('Base não informada', 1);
        }

        if (!in_array($this->param['base'], ['flex', 'gov', 'amb', 'x'])) {
            throw new Exception('Base desconhecida', 1);
        }

        $this->base = $this->param['base'];

        if (!empty($this->param['limite']) && is_integer((int) $this->param['limite'])) {
            $this->limit = $this->param['limite'];
        } else {
            $this->limit = 10;
        }
    }

    /**
    * Bases = [ x, flex, gov, amb]
    */
    protected function start()
    {
        $this->setProxy();
        $manager = new DtecManager();
        $xml = $this->createXmlWithQueryAndLimite();
        $url = preg_replace('/<produto>/isu', $this->base, DTEC_URL);
        $xmlResponse = $manager->getData($xml, $this->base);
        $result = $this->parseXmlResponseToArray($xmlResponse);

        if ($this->base != 'gov') {
            $result['resultados'] = $this->cleaningGovResults($result['resultados']);
            $result['numeroResultadosRetornados'] = count($result['resultados']);
        }
        $result['resultados'] = $this->identifyIncompleteInformation($result['resultados']);

        if (count($result['resultados']) < 1) {
            throw new Exception("Nenhum resultado encontrado", 2);
        }

        if (
            empty($this->auth['usuario'])
            || ($this->auth['usuario'] === DTEC_USER
            && $this->auth['cliente'] === DTEC_CLIENT)
        ) {
            $manager->insertBilhetador($result, $this->base, $this->query);
        }

        return $result;
    }

    /**
     * Função para identificar informações que ainda passam por
     * modificações do fornecedor.
     */
    private function identifyIncompleteInformation(array $results): array
    {
        $msg = "(Foi identificado que as informações a seguir passam " .
                "por atualizações e podem apresentar pequena imprecisão)";

        foreach ($results as $i => $result) {
            if (preg_match('/\(comp\)/is', $result['titulo'])) {
                $result['titulo'] = str_replace('(comp)', $msg, $result['titulo']);
                $results[$i] = $result;
            }
        }

        return $results;
    }

    /*
     * Função para limpar resultados da base GOV
     * */
    private function cleaningGovResults(array $results): array
    {
        $newResults = [];
        foreach ($results as $result) {
            if (strtoupper($result['tipo_suspeita']) !== "GOVERNO") {
                array_push($newResults, $result);
            }
        }
        return $newResults;
    }


    private function parseXmlResponseToArray($xmlResponse)
    {
        $unparsedArray = Util::xmlToArray($xmlResponse);
        $parsedArray = [];
        $baseParams = array(
            'numResults' => 'numeroResultadosTotal',
            'queryTime' => 'tempoConsulta',
            'simpleDebugInfo' => 'queries',

        );

        foreach ($baseParams as $originalName => $parsedName) {
            if (isset($unparsedArray[$originalName])) {
                $parsedArray[$parsedName] = $unparsedArray[$originalName];
            }
        }

        if (empty($parsedArray)) {
            throw new Exception('Resposta em formato inválido', 3);
        }

        $parsedArray['numeroResultadosRetornados'] = 0;
        $parsedArray['resultados'] = [];

        if (isset($unparsedArray['resultList']['doc']['attribute'])) {
            $transitionArray = $unparsedArray['resultList']['doc']['attribute'];
            $unparsedArray['resultList']['doc'] = [];
            $unparsedArray['resultList']['doc'][] = array('attribute' => $transitionArray);
        }

        if (empty($unparsedArray['resultList'])) {
            return $parsedArray;
        }

        for ($i = 0; $i < count($unparsedArray['resultList']['doc']); $i++) {
            $attributeArray = $unparsedArray['resultList']['doc'][$i]['attribute'];
            for ($j = 0; $j < count($attributeArray); $j++) {
                $name = $attributeArray[$j]['name'] ?? '';
                $value = $attributeArray[$j]['value'] ?? '';

                $nameAdd = '';

                $iteration = 0;
                while (isset($parsedArray['resultados'][$i][$name . $nameAdd])) {
                    $iteration++;
                    $nameAdd = '_' . $iteration;
                }
                $parsedArray['resultados'][$i][$name . $nameAdd] = $value;

                if ($attributeArray[$j]['name'] == 'links_tribunais') {
                    $linksTribunaisArray = explode(';', $attributeArray[$j]['value']['string']);
                    $parsedArray['resultados'][$i]['links_tribunais'] = $linksTribunaisArray;
                }
            }
        }
        $parsedArray['numeroResultadosRetornados'] = count($parsedArray['resultados']);
        return $parsedArray;
    }

    private function createXmlWithQueryAndLimite()
    {
        if (isset($this->auth) && !empty($this->auth)) {
            $cliente    = $this->auth['cliente'];
            $usuario    = $this->auth['usuario'];
            $senha    = $this->auth['senha'];
        } else {
            $cliente    = DTEC_CLIENT;
            $usuario    = DTEC_USER;
            $senha      = DTEC_PASSWORD;
        }

        $xml = new SimpleXMLElement('<consulta/>');
        $xml->addChild('cliente', htmlspecialchars($cliente));
        $xml->addChild('usuario', htmlspecialchars($usuario));
        $xml->addChild('senha', htmlspecialchars($senha));
        $xml->addChild('qry', htmlspecialchars($this->query));
        $xml->addChild('options', 'maxRows:' . htmlspecialchars($this->limit));

        $stringXml = $xml->asXML();
        $stringXml = str_replace('<?xml version="1.0"?>', '', $stringXml);
        return $stringXml;
    }
}
