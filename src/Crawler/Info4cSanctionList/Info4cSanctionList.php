<?php

namespace App\Crawler\Info4cSanctionList;

use App\Crawler\Spider;
use App\Factory\MongoDB;
use Exception;

class Info4cSanctionList extends Spider
{
    private const INDEX_MONGODB = 'search';
    private const LIMIT = 100;

    private $name;
    private $limit;

    public function start()
    {
        $result = $this->getDatabyName();
        if (empty($result)) {
            throw new Exception("Nenhum Resultado Encontrado", 2);
        }
        return $result;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->limit = $this->param['limit'] ??  self::LIMIT;
        $this->name = trim($this->param['name']);

        if (empty($this->name)) {
            throw new Exception("Critério de busca inválido.", 6);
        }
    }

    /** Busca registros na base
     * @return array
     */
    private function getDatabyName()
    {
        $names = $this->breakName($this->name);

        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('info4c')
            ->setCollection('info4c_sanctionlist');
        $fields = ['search_field'];
        $result = json_decode(
            json_encode(
                $manager
                    ->atlasSearch(
                        $names,
                        self::INDEX_MONGODB,
                        $this->limit,
                        $fields,
                        'phrase'
                    )
                    ->toArray(),
                true
            ),
            true
        );

        foreach ($result as $key => $value) {
            $data[] = [
                'id' => $value['id'],
                'title' => $value['title'],
                'first_name' => $value['first_name'],
                'last_name' => $value['last_name'],
                'full_name' => $value['full_name'],
                'other_names' => $value['other_names'],
                'original_name' => $value['original_name'],
                'birth_date' => $value['birth_date'],
                'birth_place' => $value['birth_place'],
                'additional_information' => $value['additional_information'],
                'type_sdn_or_entity' => $value['type_sdn_or_entity'],
                'address' => $value['address'],
                'passsport_nr' => $value['passsport_nr'],
                'list_name' => $value['list_name'],
                'list_type' => $value['list_type'],
                'list_date' => $value['list_date'],
                'authority' => $value['authority'],
                'whitelist' => $value['whitelist']
            ];
        }
        return $data;
    }

    private function breakName($name)
    {
        $nameArr = explode(" ", $name);
        $firstName = $nameArr[0];
        $newName = $nameArr[1] . ' ' . $nameArr[2] . ' ' . $firstName;

        return [$newName, $name];
    }
}
