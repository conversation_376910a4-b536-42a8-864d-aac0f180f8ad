<?php

namespace App\Crawler\AnvisaConsultaProcesso;

use App\Crawler\AnvisaConsultaProcesso\Models\AnvisaConsultaProcessoDadoModel;
use App\Crawler\AnvisaConsultaProcesso\Models\AnvisaConsultaProcessoPeticaoModel;
use App\Crawler\AnvisaConsultaProcesso\Models\AnvisaConsultaProcessoDetalheModel;
use App\Crawler\AnvisaConsultaProcesso\Models\AnvisaConsultaProcessoModel;
use App\Crawler\Spider;
use App\Helper\Document;
use Exception;

class AnvisaConsultaProcesso extends Spider
{
    private $cnpj;
    private const API = 'https://consultas.anvisa.gov.br/api/documento/tecnico/';

    public function start()
    {
        $this->setCurlOpt([
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        $results = $this->getResults();

        return $results;
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCpfOuCnpj($this->param['cnpj'])) {
            throw new Exception('Parâmetro de criterio inválido', 1);
        }

        $this->cnpj = Document::removeMask($this->param['cnpj']);
    }

    private function getResults()
    {
        $request = $this->requestResults($this->cnpj);
        $process = $this->filterProcess($request);
        $results = $this->parseResult($process, $this->cnpj);

        return $results;
    }

    private function requestResults($cnpj)
    {
        $params = [
            'count' => '10',
            'filter[cnpj]' => $cnpj,
            'page' => '1'
        ];

        $response = $this->requestResponse('GET', self::API . '?', $params);
        $response = json_decode($response);

        return $response;
    }

    private function requestResponse($method, $url, $params = null)
    {
        $headers = [
            'Authorization: Guest'
        ];

        $query = $params != null ? http_build_query($params) : $params;

        $response = $this->getResponse($url . $query, $method, [], $headers);

        return $response;
    }

    private function filterProcess($process)
    {
        $arrProcess = array();
        if (count($process->content) > 0) {
            foreach ($process->content as $key => $proc) {
                if ($proc->processo) {
                    $arrProcess[$key] = $proc->processo->numero;
                }
            }
        }

        if (empty($arrProcess)) {
            throw new Exception('Não existe processos para o CNPJ informado', 2);
        }

        return $arrProcess;
    }

    private function parseResult($process, $cnpj)
    {
        $responseBody = $this->getProcessDetails($process);
        $details = $this->filterRawData($responseBody);
        $object = $this->setObject($cnpj, json_encode($responseBody), $details);

        return $object;
    }

    private function setObject($cnpj, $responseBody, $details = [])
    {
        foreach ($details as $detail) {
            $arrayPeticoes = array();
            if (count($detail['peticoes']) > 0) {
                if (!isset($detail['peticoes'][0])) {
                    $detail['peticoes'] = array($detail['peticoes']);
                }

                foreach ($detail['peticoes'] as $peticoes) {
                    $voPeticao  = new AnvisaConsultaProcessoPeticaoModel();
                    $voPeticao->processo = $peticoes['processo'];
                    $voPeticao->protocolo = $peticoes['protocolo'];
                    $voPeticao->assunto = $peticoes['assunto'];
                    $voPeticao->situacao = $peticoes['situacao'];
                    $voPeticao->localizacao = $peticoes['localizacao'];
                    $voPeticao->data_publicacao = $peticoes['data_publicacao'];
                    $voPeticao->expediente = $peticoes['expediente'];

                    $arrayPeticoes[] = $voPeticao;
                }
            }

            $voDetalhe = new AnvisaConsultaProcessoDetalheModel();
            $voDetalhe->processo = $detail['proc_numero'];
            $voDetalhe->protocolo = $detail['protocol_numero'];
            $voDetalhe->assunto = $detail['assunto'];
            $voDetalhe->data_entrada = $detail['data_entrada'];
            $voDetalhe->situacao = $detail['situacao'];
            $voDetalhe->localizacao = $detail['area'];
            $voDetalhe->data_publicacao = $detail['publicacao'];
            $voDetalhe->expediente = $detail['expediente'];
            $voDetalhe->aPeticao = $arrayPeticoes;
            $voDetalhe->html = '';

            $vo = new AnvisaConsultaProcessoDadoModel();
            $vo->razao_social = $detail['razao_social'];
            $vo->processo = $detail['proc_numero'];
            $vo->situacao = $detail['situacao'];
            $vo->detalhe = $voDetalhe;

            $aVo[] = $vo;
        }

        $voPrincipal = new AnvisaConsultaProcessoModel();
        $voPrincipal->data_consulta = date('Y-m-d H:i:s');
        $voPrincipal->cnpj = $cnpj;
        $voPrincipal->html = $responseBody;
        $voPrincipal->aDado = $aVo;

        if (!($voPrincipal instanceof AnvisaConsultaProcessoModel)) {
            throw new Exception('O retorno do método ' . __METHOD__
                        . ' deve ser um objeto do tipo AnvisaConsultaProcesso');
        }

        return $voPrincipal;
    }

    private function getProcessDetails($process)
    {

        $details = array();
        foreach ($process as $proc) {
            $response = $this->requestResponse('GET', self::API . $proc);
            $response = json_decode($response);
            $details[] = $response;
        }

        if (empty($details)) {
            throw new Exception('Não foi possível capturar os detalhes do processo.', 4);
        }

        return $details;
    }

    private function filterRawData($details)
    {
        $arrResult = array();
        foreach ($details as $key => $detail) {
            if ($detail->empresa) {
                $arrResult[$key]['cnpj'] = $detail->empresa->cnpj;
                $arrResult[$key]['razao_social'] = $detail->empresa->razaoSocial;
            }

            if ($detail->processo) {
                $arrResult[$key]['proc_numero'] = $detail->processo->numero;
                $arrResult[$key]['protocol_numero'] = $detail->processo->peticao->protocolo;
                $arrResult[$key]['expediente'] = isset($detail->processo->peticao->expediente) &&
                                                !empty($detail->processo->peticao->expediente)
                                                ? $this
                                                    ->formataDado($detail->processo->peticao->expediente, '######/##-#')
                                                : '';
                $arrResult[$key]['data_entrada'] = date('d/m/Y', strtotime($detail->processo->peticao->dataEntrada));
                $arrResult[$key]['assunto'] = $detail->processo->peticao->assunto->codigo . ' - ' .
                                                $detail->processo->peticao->assunto->descricao;
                $arrResult[$key]['area'] = $detail->processo->peticao->area->sigla . ' - ' .
                                            $detail->processo->peticao->area->nome . ' desde ' .
                                            date('d/m/Y', strtotime($detail->processo->peticao->area->recebimento));
                $arrResult[$key]['situacao'] = $detail->processo->peticao->situacao->descricao . ' em ' .
                                                date('d/m/Y', strtotime($detail->processo->peticao->situacao->data));
                $arrResult[$key]['publicacao'] = date('d/m/Y', strtotime($detail->processo->peticao->dataPublicacao));
                $arrResult[$key]['publicacao'] .= !empty($detail->processo->peticao->resolucao)
                                                    && !empty($detail->processo->peticao->dataPublicacao)
                                                    ?  ' (' . $detail->processo->peticao->resolucao . ')'
                                                    : '';
                $arrResult[$key]['recebimento'] = $this->formataData($detail->processo->peticao->area->recebimento);
            }

            $arrResult[$key]['peticoes'] = array();
            if (count($detail->peticoes) > 0) {
                foreach ($detail->peticoes as $peticao) {
                    $arrResult[$key]['peticoes']['processo'] = isset($peticao->processo)
                                                                ? $peticao->processo
                                                                : '';
                    $arrResult[$key]['peticoes']['protocolo'] = isset($peticao->protocolo)
                                                                ? $peticao->protocolo
                                                                : '';
                    $arrResult[$key]['peticoes']['assunto'] = isset($peticao->assunto)
                                                                ? $peticao->assunto->codigo
                                                                    . ' - ' . $peticao->assunto->descricao
                                                                : '';
                    $arrResult[$key]['peticoes']['situacao'] = isset($peticao->situacao)
                                                                ? $peticao->situacao->descricao .
                                                                    $this->formataData($peticao->situacao->data)
                                                                : '';
                    $arrResult[$key]['peticoes']['localizacao'] = isset($peticao->area)
                                                                    ? $peticao->area->sigla
                                                                    . ' - ' . $peticao->area->nome . ' desde ' .
                                                                    $this->formataData($peticao->area->remessa)
                                                                    : '';
                    $arrResult[$key]['peticoes']['data_publicacao'] = $this->formataData($peticao->dataPublicacao);
                    $arrResult[$key]['peticoes']['data_publicacao'] .= !empty($peticao->resolucao)
                                                                        && !empty($peticao->dataPublicacao)
                                                                        ? ' (' . $peticao->resolucao . ')'
                                                                        : '';
                    $arrResult[$key]['peticoes']['expediente'] = isset($peticao->expediente) &&
                                                                !empty($peticao->expediente)
                                                            ? $this->formataDado($peticao->expediente, '######/##-#')
                                                            : '';
                }
            }
        }

        return $arrResult;
    }

    private function formataData($data)
    {
        return date('d/m/Y', strtotime($data));
    }

    private function formataDado($dado, $mask)
    {
        $j = 0;
        $novoDado = null;
        for ($i = 0; $i <= (strlen($mask) - 1); $i++) {
            if ($mask[$i] != '#') {
                $novoDado .= $mask[$i];
                continue;
            }
            $novoDado .= $dado[$j];
            $j++;
        }
        return $novoDado;
    }
}
