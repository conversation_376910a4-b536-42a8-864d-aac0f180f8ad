<?php

namespace App\Crawler\AnvisaConsultaProcesso\Models;

use Exception;

class AnvisaConsultaProcessoModel
{
    public $cnpj;
    public $aDado;
    public $data_consulta;
    public $html;

    public function __set($name, $value)
    {
        if (property_exists($this, $name)) {
            return $this->$name = $value;
        }

        throw new Exception("Propriedade set '{$name}' não existe", 3);
    }

    public function __get($name)
    {
        if (property_exists($this, $name)) {
            return $this->$name;
        }

        throw new Exception("Propriedade get '{$name}'  não existe", 3);
    }
}
