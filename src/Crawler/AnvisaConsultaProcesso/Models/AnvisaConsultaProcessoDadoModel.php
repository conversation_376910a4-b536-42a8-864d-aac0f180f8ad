<?php

namespace App\Crawler\AnvisaConsultaProcesso\Models;

use Exception;

class AnvisaConsultaProcessoDadoModel
{
    public $razao_social;
    public $processo;
    public $situacao;
    public $detalhe;

    public function __set($name, $value)
    {
        if (property_exists($this, $name)) {
            return $this->$name = $value;
        }

        throw new Exception("Propriedade set '{$name}' não existe", 3);
    }

    public function __get($name)
    {
        if (property_exists($this, $name)) {
            return $this->$name;
        }

        throw new Exception("Propriedade get '{$name}'  não existe", 3);
    }
}
