<?php

namespace App\Crawler\AnvisaConsultaProcesso\Models;

use Exception;

class AnvisaConsultaProcessoPeticaoModel
{
    public $processo;
    public $protocolo;
    public $assunto;
    public $situacao;
    public $localizacao;
    public $data_publicacao;
    public $expediente;

    public function __set($name, $value)
    {
        if (property_exists($this, $name)) {
            return $this->$name = $value;
        }

        throw new Exception("Propriedade set '{$name}' não existe", 3);
    }

    public function __get($name)
    {
        if (property_exists($this, $name)) {
            return $this->$name;
        }

        throw new Exception("Propriedade get '{$name}'  não existe", 3);
    }
}
