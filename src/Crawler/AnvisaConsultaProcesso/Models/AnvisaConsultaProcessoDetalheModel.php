<?php

namespace App\Crawler\AnvisaConsultaProcesso\Models;

use Exception;

class AnvisaConsultaProcessoDetalheModel
{
    public $html;
    public $processo;
    public $protocolo;
    public $assunto;
    public $data_entrada;
    public $situacao;
    public $localizacao;
    public $data_publicacao;
    public $expediente;
    public $aPeticao = [];

    public function __set($name, $value)
    {
        if (property_exists($this, $name)) {
            return $this->$name = $value;
        }

        throw new Exception("Propriedade set '{$name}' não existe", 3);
    }

    public function __get($name)
    {
        if (property_exists($this, $name)) {
            return $this->$name;
        }

        throw new Exception("Propriedade get '{$name}'  não existe", 3);
    }
}
