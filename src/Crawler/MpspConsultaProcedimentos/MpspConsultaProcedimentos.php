<?php

namespace App\Crawler\MpspConsultaProcedimentos;

use App\Crawler\Spider;
use App\Helper\Str;
use App\Helper\Util;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

class MpspConsultaProcedimentos extends Spider
{
    private const URL_BASE = "https://sismpconsultapublica.mpsp.mp.br";
    private const URL_REQUEST = "/ConsultarProcedimentos/ObterProcedimentos";
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const FILE_S3_PATH = 'captura/mpsp_consulta_procedimentos/';
    private $document;
    private $limit;
    private $array_links = [];
    private $number_of_results = 0;


    public function start()
    {
        $this->getDataLinks();

        if (empty($this->array_links)) {
            throw new Exception('Nenhum resultado encontrado.', 2);
        }

        return $this->parseData($this->array_links);
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->document = trim($this->param['document']);
        $this->limit = $this->param['limit'] ?? 100;

        if (empty($this->document)) {
            throw new Exception('Parâmetro não informado.', 6);
        }
    }

    /** Pega os links de todos os procedimentos
     * <AUTHOR> Santos - 09 fev. 22
     */
    private function getDataLinks()
    {
        $page = 1;
        /* Esse laço verifica o numero de resultados e percorre as páginas do site */
        do {
            $html = $this->makeRequest($page);
            preg_match_all('/href="(\/Detalhe.*?)"/i', $html, $links);
            $this->returnLinks($links[1]);
            $page++;
        } while ($this->limit < $this->number_of_results);
    }

    /** Realiza busca no site e retorna o html da página.
     * @param $page
     * @return mixed
     * @throws Exception
     * <AUTHOR> Santos - 09 fev 2022
     */
    public function makeRequest($page)
    {
        $params = [
            'PaginaAtual' => $page,
            'NumeroMPTipo' => '',
            'NumeroMPUA' => '',
            'NumeroMPSequencial' => '',
            'NumeroMPAno' => '',
            'NumeroTJ' => '',
            'NomeParte' => $this->document,
            'DocParte' => '',
            'AssuntoTabUni' => '',
            'TipoProcedimento' => '',
            'UA' => ''
        ];
        return $this->getResponse(self::URL_BASE . self::URL_REQUEST, 'POST', $params);
    }


    /** Metodo que captura os links dos procedimentos
     * @param $array
     * <AUTHOR> Santos - 09 fev 22
     */
    private function returnLinks($array)
    {
        foreach ($array as $value) {
            /* Condição que verifica se o numero de resultados é igual ao parametro limit      */
            if ($this->number_of_results < $this->limit) {
                $this->array_links[] = $value;
                $this->number_of_results++;
            }
        }
    }

    /** Realiza a busca dos procedimentos a partir do link
     * @param $links
     * @return array
     * @throws Exception
     * <AUTHOR> Santos - 09 fev 22
     */
    private function parseData($links)
    {
        foreach ($links as $link) {
            $html = $this->getResponse(self::URL_BASE . $link);

            preg_match('/Ocorreu\sum\serro\sao\sprocessar\ssua\srequisi&#231;&#227;o/iu', $html, $matches);
            if (!empty($matches)) {
                $result['alert'] = 'Ocorreu um erro ao processar a requisição e alguns resultados não foram incluídos.';
            } else {
                $basic_data = $this->parseBasicData($html);
                $details = $this->getDataDetails($html);

                $result['dados'][] = array_merge($basic_data, $details);
            }
        }

        return $result;
    }

    private function parseBasicData($html)
    {
        $header = Util::queryXPath($html, '//*[@class="sectionRow"]/label');
        $content = Util::queryXPath($html, '//*[@class="sectionRow"]/span');
        $array = array_combine($header, $content);
        $basicData = [];

        foreach ($array as $key => $value) {
            $key = $this->convertHeader($key);
            /* Quebra a string das partes em um array */
            if ($key === 'partes' || $key === "assunto") {
                preg_match_all('/(.*?)<br>\s/isu', $value, $matches);
                $basicData[$key] = $matches[1];
            } else {
                $basicData[$key] = $value;
            }
        }

        return $basicData;
    }

    /** Convert o header do array para realizar parse dos dados
     * @param $string
     * @return array|string|string[]
     */
    private function convertHeader($string)
    {
        $string = preg_replace('/:(.*)/', '', $string);

        $header = array(
            'Número MP',
            'Número TJ',
            'Tipo de Procedimento',
            'Unidade',
            'Situação',
            'Assunto',
            'Partes',
            'Instauração',
            'N° do Inquérito na Delegacia',
            'Nome da Delegacia',
            'Vara de Origem',
            'Cargo',
            'Remetido para'
        );

        $replace = array(
            'numero_mp',
            'numero_tj',
            'tipo_procedimento',
            'unidade',
            'situacao',
            'assunto',
            'partes',
            'instauracao',
            'num_inquerito_delegacia',
            'nome_delegacia',
            'vara_origem',
            'cargo',
            'remetido_para'
        );

        return str_replace($header, $replace, $string);
    }

    private function getDataDetails($html)
    {
        $table = Util::queryXPath($html, '//table/tbody');
        $vinculos = Util::queryXPath($table[0], '//tr/td');
        $anexos = Util::queryXPath($table[1], '//tr');
        $movimentacoes = Util::queryXPath($table[2], '//tr');
        $data = [];
        foreach (array_filter($vinculos) as $value) {
            $data['vinculos'][] = Str::stripTagsRecursive($value);
        }

        foreach ($anexos as $value) {
            $anexo = Util::queryXPath($value, '//td');
            preg_match('/href="(.*?)">(.*)<\/a>/isu', $value, $matches);
            $data['anexos'][] = [
                'anexo' => ($anexo[0] === "Não há anexos!") ? $anexo[0] : $matches[2],
                'link' => !empty($matches[1]) ? $this->saveFile(
                    self::URL_BASE . $matches[1],
                    pathinfo($matches[2], PATHINFO_EXTENSION)
                ) : null,
                'tipo' => $anexo[1]
            ];
        }

        foreach ($movimentacoes as $value) {
            $dado_movimentacao = Util::queryXPath($value, '//td');
            $movimentacao = str_replace('?', '-', $dado_movimentacao[1]);
            $detalhe = $this->parseDetalheMovimentacao($dado_movimentacao[2]);

            $data['movimentacoes'][] = [
                'data' => $dado_movimentacao[0],
                'movimentacao' => Str::stripTagsRecursive($movimentacao),
                'detalhes' => Str::stripTagsRecursive($detalhe)

            ];
        }

        return $data;
    }

    private function parseDetalheMovimentacao($data)
    {
        if (empty($data)) {
            return null;
        }

        $detalhes = explode("<br>", $data);
        $result = [];
        foreach ($detalhes as $value) {
            preg_match('/<span>(.*?)<a\slength="0"\shref="(.*)">/iu', $value, $matches);
            empty($matches) ? $result['detalhe'][] = $value : $result['arquivos'][] = [
                'arquivo' => $matches[1],
                'link' => !empty($matches[1]) ? $this->saveFile(
                    self::URL_BASE . $matches[2],
                    pathinfo($matches[2], null)
                ) : null
            ];
        }
        return $result;
    }

    /** Salva os arquivos no S3
     * @param $url
     * @param $extension
     * @return string
     * @throws Exception
     * <AUTHOR> Santos - 02 mar. 22
     */
    private function saveFile($url, $extension)
    {
        $extension = preg_replace('/\s/', '', $extension);
        $uniqid = md5(uniqid(rand(), true));

        //alguns links não vêm com o tipo de arquivo definido
        empty($extension) ? $fileName = "{$uniqid}.pdf" : $fileName = "{$uniqid}.{$extension}";

        $fileLocalPath = "/tmp/{$fileName}";
        $fileS3Path = self::FILE_S3_PATH . $fileName;
        $fileUrl = self::S3_STATIC_PATH . $fileS3Path;

        file_put_contents($fileLocalPath, $this->getResponse($url));
        (new S3(new StaticUplexisBucket()))->save($fileS3Path, $fileLocalPath);

        return $fileUrl;
    }
}
