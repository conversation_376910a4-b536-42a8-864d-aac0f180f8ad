<?php

namespace App\Crawler\BoaVistaScpcNet;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\BoaVista\BoaVistaManager;
use Exception;

/**
 * Fonte BoaVistaSpcNet 172
 *
 * @since 2019-08-13
 * @version 1.0.0 - Criação
 * <AUTHOR> <<EMAIL>>
 *
 */
class BoaVistaScpcNet extends Spider
{
    private const URL_LAMBDA = LAMBDA_LUMEN . LAMBDA;
    private $document;

    public function __construct($param, $auth)
    {
        parent::__construct($param, $auth);
        $this->param['cpf_cnpj'] = preg_replace('/\D/', '', $this->param['cpf_cnpj']);
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro de documento inválido', 1);
        }

        if (strlen(trim($this->param['cpf_cnpj'])) == 11 && !Document::validarCpf($this->param['cpf_cnpj'])) {
            throw new Exception('CPF Invalido', 1);
        }

        if (strlen(trim($this->param['cpf_cnpj'])) == 14 && !Document::validarCnpj($this->param['cpf_cnpj'])) {
            throw new Exception('CNPJ Invalido', 1);
        }

        $this->document = $this->param['cpf_cnpj'];
    }

    protected function start()
    {
        $result = $this->getData($this->document);

        if (empty($result)) {
            throw new Exception("Nenhum resultado encontrado", 2);
        }

        if (preg_match('/\bERRO\b/i', json_encode($result))) {
            throw new Exception($result, 6);
        }

        //Transformar objeto em array
        $result = $this->objectToArray($result);
        $check = $result;

        if (!isset($check['informacoes_cadastrais_pf']) && !isset($check['informacoes_cadastrais_pj'])) {
            throw new Exception("Erro ao buscar os dados", 6);
        }
        $documento = $this->document;
        (new BoaVistaManager())->saveScpcNet($result, 'SCPCNET', $documento);

        return $result;
    }

    /**
     * Retorna os dados da fonte Boa Vista Scpc Net
     * @return array
     * <AUTHOR> Guilherme Sório - 05/02/2021
     * Revisão
     * @version 1.0.0
     */
    public function getData($document)
    {
        $filterJson = $this->filtersToJson($document);
        $response = json_decode($this->makeRequest($filterJson));
        return $response->data;
    }

    /**
     * Cria o filtro de pesquisa na Boa Vista Scpc Net
     *
     * @return string
     * <AUTHOR> Guilherme Sório - 04/02/2021
     * Revisão
     * @version 1.0.0
     */
    private function filtersToJson($document)
    {
        $arrObj = array(
            'retry' => '1',
            'source' => 'BoaVistaConsumer',
            'param' => array(
                'documento' => $document,
                'consulta' => 'localiza_scpc_net'
            ),
        );

        return json_encode($arrObj);
    }

    /**
     * Faz a requisição para o lambda na fonte Boa Vista Scpc Net
     *
     * <AUTHOR> Guilherme Sório - 04/02/2021
     */
    private function makeRequest($filterJson)
    {
        $ch = curl_init(self::URL_LAMBDA);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $filterJson);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'Content-Length: ' . strlen($filterJson),
        ));
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 0);
        curl_setopt($ch, CURLOPT_TIMEOUT, 800);
        return curl_exec($ch);
    }

    /**
     * Método responsável por transformar um objeto em array
     * <AUTHOR> Guilherme Sório - 05/02/2021
     *
     * @return array
     */
    private function objectToArray($data)
    {
        if (is_array($data) || is_object($data)) {
            return json_decode(
                str_replace(
                    "\\/",
                    "",
                    json_encode($data)
                ),
                true
            );
        }

        return $data;
    }
}
