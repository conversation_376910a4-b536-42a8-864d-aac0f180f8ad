<?php

namespace App\Crawler\CertidaoCeatTrtSP;

use App\Crawler\Spider;
use App\Crawler\SpinePj\SpinePj;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\ColunaVertebralManager;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

class CertidaoCeatTrtSP extends Spider
{
    private const BASE_URL = 'https://aplicacoes10.trt2.jus.br/certidao_trabalhista_eletronica/public';
    private const URL_REQUEST = '/index.php/index/solicitacao';
    private const URL_CAPTCHA = '/captcha/image.php';
    private const URL_PDF = '/index.php/index/recuperarcertidao';
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const CERTIDAO_S3_PATH = 'captura/certidao_ceat_trt2_sp/';
    private $cpfCnpj = '';
    private $tipoPesquisado = 1;
    private $nome;


    public function start()
    {
        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::CERTIDAO_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;
        $retry = 3;

        if (empty($this->nome)) {
            throw new Exception('Nome não encontrado', 2);
        }

        while ($retry >= 0) {
            try {
                $this->setProxy();
                $captcha = $this->validateCaptcha();
                $pdf = $this->makeRequest($captcha);
                $text = $this->savePdfAndReturnText($pdf);
                break;
            } catch (Exception $e) {
                if ($retry == 0) {
                    throw new Exception('Erro na requisição', 3);
                }
                $retry--;
            }
        }

        $data = $this->parseData($text);
        $data = [
            'info' => $data,
            'pdf' => $this->pdf
        ];

        return $data;
    }

    /**
     * Valida o captcha da fonte
     * <AUTHOR> Pereira
     * @return array
     */
    private function validateCaptcha()
    {
        $html = $this->getResponse(self::BASE_URL . self::URL_REQUEST, 'GET');

        preg_match('/image.php?([\s\S]*?)"/', $html, $captcha);
        preg_match('/captcha\[id\]"\svalue="([\s\S]*?)"/', $html, $idCaptcha);
        $captchaUrl = self::BASE_URL . self::URL_CAPTCHA . $captcha[1];

        $this->getImageAndBreakCaptcha($captchaUrl);

        return [$this->captcha, $idCaptcha[1]];
    }

    /**
     * Captura a certidão da requisição em pdf.
     * <AUTHOR> Pereira
     * @return $pdf
     */
    private function makeRequest($captcha)
    {
        $params = [
            'tipoDocumentoPesquisado' => $this->tipoPesquisado,
            'numeroDocumentoPesquisado' => $this->cpfCnpj,
            'nomePesquisado' => $this->nome,
            'jurisdicao' => 0,
            'periodo' => 1,
            'data_inicial' => '',
            'data_final' => '',
            'captcha[id]' => $captcha[1],
            'captcha[input]' => $captcha[0],
            'submit' => '',
        ];

        curl_setopt($this->ch, CURLOPT_FOLLOWLOCATION, true);

        $this->getResponse(self::BASE_URL . self::URL_REQUEST, 'POST', $params);
        $pdf = $this->getResponse(self::BASE_URL . self::URL_PDF);

        return $pdf;
    }

    /**
     * Salva o pdf capturado na amazom temporariamente.
     * <AUTHOR> Pereira
     * @param string $data
     * @return $text
     */
    private function savePdfAndReturnText($pdf)
    {
        file_put_contents($this->certificateLocalPath, $pdf);

        $text = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'layout',
            'nopgbrk'
        ]);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        $this->pdf = $this->certificateUrl;

        return $text;
    }


    private function parseData($text)
    {
        $patterns = [
            'name' => ['@Nome:\s([\s\S]*?)\n@'],
            'numCertidao' => ['@Certidão\snº\s([\s\S]*?)\\n@'],
            'descricao' => ['@(CERTIFICA-SE[\S\s]*?\.)@'],
            'aviso' => ['@(IMPORTANTE[\S\s]*?)página@'],
        ];

        preg_match('/(NÃO CONSTA|CONSTA)/', $text, $statusCertidao);
        $statusCertidao = $statusCertidao[0] == 'NÃO CONSTA' ? 'NEGATIVA' : 'POSITIVA';

        preg_match("/(CPF|CNPJ):\s([\s\S]*?)\n/", $text, $cpfCnpj);

        $data = Util::parseDados($patterns, $text);
        $data['cpfCnpj'] = $cpfCnpj[2];
        $data['statusCertidao'] = $statusCertidao;
        $data = array_map("utf8_decode", $data);

        return $data;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->cpfCnpj = trim($this->param['documento']);
        $unmaskedDocument = Document::removeMask(trim($this->param['documento']));

        if (!Document::validarCpfOuCnpj($this->cpfCnpj)) {
            throw new Exception('Documento Inválido', 6);
        }

        if (Document::validarCnpj($this->cpfCnpj)) {
            $this->tipoPesquisado = 2;
            $this->cpfCnpj = Document::formatCnpj($this->cpfCnpj);
            $spinePj = (object) (new SpinePj(['documento' => $unmaskedDocument], []))->run();
            $this->nome = $spinePj->data["razao_social"];
        } else {
            $this->cpfCnpj = Document::formatCpf($this->cpfCnpj);
            $spinePf = (object) (new ColunaVertebralManager())->getSpinePf($unmaskedDocument);
            $this->nome = $spinePf->nome;
        }
    }
}
