<?php

namespace App\Crawler\LexisNexisBridgerInsight;

use Exception;

/**
 * Classe para pesquisa na Api LexisNexis das fontes (277 e 278) Bridger Insight (LexisNexis) - (PF|PJ)
 *
 * @version 1.0.0
 *
 * <AUTHOR> Mesquita 15/04/2019
 */
class LexisNexisBridgerInsightApi
{
    private const API_URL = "https://bridger.lexisnexis.com/LN.WebServices/11.1/XGServices.svc?wsdl";

    private $user;
    private $pass;
    private $client;

    /**
     * Construtor da classe que seta os dados de autenticação da API
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return void
     */
    public function __construct($user, $pass, $client)
    {
        $this->user = $user;
        $this->pass = $pass;
        $this->client = $client;
    }

    /**
     * Retorna os dados encontrados na API
     *
     * @param LexisNexisBridgerInsight $lexisNexisBridgerInsight
     *
     * <AUTHOR> - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    public function getApiData($lexisNexisBridgerInsight)
    {
        $result = [];
        try {
            $result = $this->getResponse($lexisNexisBridgerInsight);
        } catch (Exception $ex) {
            $this->doError($ex);
        }

        $this->verifyResult($result);
        return $result;
    }

    /**
     * Verifica a resposta da API
     *
     * @param array $result
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return void
     */
    private function verifyResult($result)
    {
        if (!isset($result->SearchResult->Records)) {
            throw new Exception('Nada encontrado!', 2);
        }
    }

    /**
     * Trata mensagens de erro da API
     *
     * @param Exception $exception
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return void
     */
    private function doError($exception)
    {
        if (preg_match('#Invalid.Credentials#', $exception)) {
            throw new Exception('Credenciais inválidas', 3);
        }

        throw new Exception($exception->getMessage(), 3);
    }

    /**
     * Monta critérios, manda requisição para API e retorna a resposta
     *
     * @param LexisNexisBridgerInsight $lexisNexisBridgerInsight
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return array|string
     */
    private function getResponse($lexisNexisBridgerInsight)
    {
        $params = [
            'Search' => [
                'context' => [
                    'ClientID' => $this->client,
                    'UserID' => $this->user,
                    'Password' => $this->pass
                ],
                'config' => [
                    'Watchlist' => [
                        'AutomaticFalsePositiveRules' => $this->getAutomaticFalsePositiveRules(),
                        'DataFiles' => $this->getDataFiles(),
                        'GeneralOptions' => $this->getGeneralOptions(),
                        'MatchDispositionRules' => $this->getMatchDispositionRules(),
                        'MatchOptions' => $this->getMatchOptions()
                    ]
                ],
                'input' => [
                    'Records' => [
                        'InputRecord' => [
                            'Entity' => [
                                'EntityType' => $lexisNexisBridgerInsight->type,
                                'Name' => [
                                    'First' => '',
                                    'Full' => $lexisNexisBridgerInsight->name,
                                    'Generation' => '',
                                    'Last' => '',
                                    'Middle' => '',
                                    'Title' => '',
                                    'MaidenName' => '',
                                    'SecondSurname' => ''
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        return $lexisNexisBridgerInsight->getSoapResponse(self::API_URL, 'Search', $params);
    }

    /**
     * Retorna array com os itens do nó AutomaticFalsePositiveRules
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    private function getAutomaticFalsePositiveRules()
    {
        return [
            'Addresses' => false,
            'Citizenship' => false,
            'Countries' => false,
            'DisplayAFPMatches' => false,
            'DOB' => false,
            'DOBTolerance' => 0,
            'EntityType' => false, //SE NÃO SETAR COMO TRUE ELE MISTURA EMPRESAS COM PESSOAS
            'Gender' => false,
            'GenerateResultRecord' => false,
            'IDs' => false,
            'Phones' => false,
            'ShowResultAsAlert' => false,
            'WhiteList' => false,
        ];
    }

    /**
     * Retorna array com os itens do nó DataFiles
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    private function getDataFiles()
    {
        return [
            'WatchlistDataFile' => [
                'AlwaysGenerateMatchesForCitiesAndPorts' => false,
                'Custom' => false,
                'IgnoreWeakAKAs' => false,
                'MinScore' => 85,
                'Name' => 'WorldCompliance - Full.BDF',
                'SearchCriteria' => [
                    'Groups' => []
                ],
                'TopLevelOperatorIsOr' => false
            ]
        ];
    }

    /**
     * Retorna array com os itens do nó GeneralOptions
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    private function getGeneralOptions()
    {
        return [
            'GenerateAlertsForAllRecords' => false,
            'GenerateAlertsForErrors' => false,
            'IgnoreMatchesAgainstVessels' => false,
            'ScanAddressForBlockedCountries' => false,
            'ScanNameForBlockedCountries' => false,
            'ScanRunTogetherWordsForBlockedCountries' => false
        ];
    }

    /**
     * Retorna array com os itens do nó MatchDispositionRules
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    private function getMatchDispositionRules()
    {
        return [
            'GenerateTrueMatchUpdates' => false,
            'IndividualMatchDisposition' => false,
            'TrueMatchSetsOthersToFalsePositive' => false,
            'CheckForFalseMatchUpdates' => false
        ];
    }

    /**
     * Retorna array com os itens do nó MatchOptions
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    private function getMatchOptions()
    {
        return [
            'PaymentScreeningOptions' => [
                'EFTOptions' => [
                    'ACHGatewayOperatorOFACScreeningIndicator' => false,
                    'ACHSecondaryOFACScreeningIndicator' => false
                ]
            ],
            'ScanAllFieldsForBICs' => false,
            'ScanAllFieldsForCountryCodes' => false,
            'ScanAllFieldsForCountryNames' => false,
            'ScanAllFieldsForNames' => false,
            'Phones' => true
        ];
    }
}
