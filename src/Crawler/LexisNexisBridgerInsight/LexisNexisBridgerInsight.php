<?php

namespace App\Crawler\LexisNexisBridgerInsight;

use App\Crawler\LexisNexisBridgerInsight\LexisNexisBridgerInsightApi;
use App\Crawler\Spider;
use App\Helper\Str;
use App\Helper\Util;
use Exception;

/**
 * Classe das fontes (277 e 278) Bridger Insight (LexisNexis) - (PF|PJ)
 *
 * @version 1.0.0
 *
 * <AUTHOR> Mesquita 15/04/2019
 */
class LexisNexisBridgerInsight extends Spider
{
    public $name;
    public $type;

    /**
     * Valida os parametros e define as variáveis
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return void
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['name'])) {
            throw new Exception("Critério name não informado!", 1);
        }

        if (empty($this->param['type'])) {
            throw new Exception("Critério type não informado!", 1);
        }

        if (empty($this->auth['user']) || empty($this->auth['pass']) || empty($this->auth['client'])) {
            throw new Exception("Critérios para acesso a API não foram infomados corretamente!", 1);
        }

        $this->name = $this->removeSiglas($this->param['name']);
        $this->type = $this->param['type'];
    }

    /**
     * Pega os dados via API e retorna o parse dos dados
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    protected function start()
    {
        $data = new LexisNexisBridgerInsightApi($this->auth['user'], $this->auth['pass'], $this->auth['client']);

        $response = $this->parse(
            $data->getApiData($this)
        );

        return collect($response)->sortByDesc('name_score')->values()->toArray();
    }

    /**
     * Parse dos dados com a resposta da api
     *
     * @param array|object $response
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    private function parse($response)
    {
        if (!isset($response->SearchResult->Records->ResultRecord->Watchlist->Matches->WLMatch)) {
            throw new Exception('Houve um erro com a resposta da api', 3);
        }

        $matches = $response->SearchResult->Records->ResultRecord->Watchlist->Matches->WLMatch;

        //Quando só tem 1 dado a resposta é um objeto, quando tem mais é array
        if (!is_array($matches)) {
            return $this->parseMatch(0, $matches);
        }

        $result = [];
        foreach ($matches as $index => $match) {
            $result = array_merge($this->parseMatch($index, $match), $result);
        }

        return $result;
    }

    /**
     * Parse de cada entidade|indivíduo encontrado
     *
     * @param int $index
     * @param object $match
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    private function parseMatch($index, $match)
    {
        $result[$index] = [
            'name' => $match->EntityDetails->Name->Full,
            'entity_type' => $match->EntityDetails->EntityType,
            'name_score' => $match->BestNameScore,
            'numero' => $match->EntityDetails->ListReferenceNumber,
            'gender' => $match->EntityDetails->Gender,
            'data_listed' => $this->formatDate($match->EntityDetails->DateListed),
            'reason_listed' => $match->EntityDetails->ReasonListed,
            'list_reference' => $match->EntityDetails->ListReferenceNumber,
        ];

        $resultComments = $this->parseEntityDetailsComments($match->EntityDetails->Comments);
        $resultAddress = $this->parseEntityAddress($match->EntityDetails->Addresses->EntityAddress);
        $resultDocuments = $this->parseDocuments($match->EntityDetails->IDs->EntityID);
        $resultInfos = $this->parseEntityAdditionalInfo($match->EntityDetails->AdditionalInfo->EntityAdditionalInfo);
        $resultAkas = $this->parseAkas($match->EntityDetails->AKAs->EntityAKA);

        $result[$index] = array_merge($result[$index], $resultComments);
        $result[$index] = array_merge($result[$index], $resultAddress);
        $result[$index] = array_merge($result[$index], $resultDocuments);
        $result[$index] = array_merge($result[$index], $resultInfos);
        $result[$index] = array_merge($result[$index], $resultAkas);

        return $result;
    }

    /**
     * Parse das informações adicionais de entidade|indivíduo
     *
     * @param array $additionalInfos
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    private function parseEntityAdditionalInfo($additionalInfos)
    {
        $result = [];
        foreach ($additionalInfos as $additionalInfo) {
            //Este link precisa de login e senha para acessar então não vamos enviar ele para fonte
            if (preg_match('#Link.to.WorldCompliance.Online.Database#i', $additionalInfo->Value)) {
                continue;
            }
            $comments = (!isset($additionalInfo->Comments))
                ? null
                : $this->parseEntityAdditionalInfoComments($additionalInfo);

            $result['additional_info'][] = [
                'comments' => $comments,
                'value' => ($additionalInfo->Type == 'DOB' && strlen($additionalInfo->Value) == 10) ?
                    $this->formatDate($additionalInfo->Value) : $additionalInfo->Value,
                'type' => ($additionalInfo->Type == 'DOB') ? 'birthday' : $additionalInfo->Type
            ];
        }

        return $result;
    }

    /**
     * Parse dos itens das informações adicionais de entidade|indivíduo
     *
     * @param object $additionalInfo
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return array|string
     */
    private function parseEntityAdditionalInfoComments($additionalInfo)
    {
        if ($additionalInfo->Value == 'Sources of Record Information') {
            return array_map('trim', explode('|', $additionalInfo->Comments));
        }

        return $additionalInfo->Comments;
    }

    /**
     * Parse dos detalhes da entidade|indivíduo
     *
     * @param array $matches
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    private function parseEntityDetailsComments($matches)
    {
        $result = [];
        $patterns = [
            'source' => ['#Source:([^>]*?)\|#i', null],
            'level' => ['#level:([^>]*?)\|#i', null],
            'category' => ['#Category:([^>]*?)\|#i', null],
            'subcategory' => ['#Subcategory:([^>]*?)\|#i', null],
            'last_update' => ['#Last updated:([^>]*?)\|#i', null],
            'profile_notes' => ['#Profile.Notes:([^>]*)#i', null],
            'associations' => ['#\|\|.associations:.\|(.*?)\|\|#i', null]
        ];

        $result = Str::encoding(
            Util::parseDados($patterns, $matches)
        );
        $result['last_update'] = $this->formatDate($result['last_update']);
        $result['associations'] = $this->parseAssociations($result);
        return $result;
    }

    /**
     * Parse dos apelidos ou pseudônimo da entidade|indivíduo
     *
     * @param array|object $akas
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    private function parseAkas($akas)
    {
        $result = [];

        if (empty($akas)) {
            return ['akas' => $result];
        }

        if (!is_array($akas)) {
            return ['akas' => [$akas->Name->Full]];
        }

        foreach ($akas as $aka) {
            $result['akas'][] = $aka->Name->Full;
        }

        return $result;
    }

    /**
     * Parse dos documentos da entidade|indivíduo
     *
     * @param array|object $documentList
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    private function parseDocuments($documentList)
    {
        $result = [];

        if (empty($documentList)) {
            return ['documents' => $result];
        }

        if (!is_array($documentList)) {
            return ['documents' => [$this->parseDocument($documentList)]];
        }

        foreach ($documentList as $document) {
            $result['documents'][] = $this->parseDocument($document);
        }

        return $result;
    }

    /**
     * Parse do documento da entidade|indivíduo
     *
     * @param object $document
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    private function parseDocument($document)
    {
        return [
            'document' => $document->Number,
            'type' => $this->parseDocumentType($document->Type)
        ];
    }

    /**
     * Parse do tipo do documento da entidade|indivíduo
     *
     * @param string $type
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return string
     */
    private function parseDocumentType($type)
    {
        if ($type == 'National') {
            return 'Documento Nacional';
        }

        if ($type == 'Other') {
            return 'Outros';
        }

        if ($type == 'ProprietaryUID') {
            return 'Identificador Universal';
        }
    }

    /**
     * Parse de endereços da entidade|indivíduo
     *
     * @param array|object $addressList
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    private function parseEntityAddress($addressList)
    {
        $result = [];

        if (empty($addressList)) {
            return ['address' => $result];
        }

        if (!is_array($addressList)) {
            return ['address' => [$this->parseAddress($addressList)]];
        }

        foreach ($addressList as $address) {
            $result['address'][] = $this->parseAddress($address);
        }
        return $result;
    }

    /**
     * Parse de endereço da entidade|indivíduo
     *
     * @param object $address
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    private function parseAddress($address)
    {
        $stateProvinceDistrict = $address->StateProvinceDistrict ?? null;
        return [
            'city' => (!isset($address->City)) ? null : $address->City,
            'country' => (!isset($address->Country)) ? null : $address->Country,
            'id' => (!isset($address->ID)) ? null : $address->ID,
            'postal_code' => (!isset($address->PostalCode)) ? null : $address->PostalCode,
            'state_province_district' => $stateProvinceDistrict,
            'street1' => (!isset($address->Street1)) ? null : $address->Street1,
            'type' => (!isset($address->Type)) ? null : $address->Type
        ];
    }

    /**
     * Parse associados da entidade|indivíduo
     *
     * @param string $associations
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    private function parseAssociations($associations)
    {
        if (empty(trim($associations['associations']))) {
            return [];
        }
        return array_map('trim', explode('|', $associations['associations']));
    }

    /**
     * Remove apenas as siglas, não usei da util, pois ela remove pontos,
     *  fazendo alguns critérios não retornarem resultados
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return void
     */
    public function removeSiglas($string)
    {
        $siglas = array(
            'last'      => array(
                "SA",
                "IND\\s*LINDUSTRIAL",
                "COM\\s*LCOMERCIAL",
                "IND\\s*INDUSTRIA",
                "ME",
                "EPP",
                "CIA"
            ),
            'any_place' => array(
                "LTDA",
                "S\\/A",
                "\\s*S\\/A\\s*",
                "\\s*S\\s+A\\s+",
                "\\s+S\\s+A\\s*",
                "LTDA\\s*-\\s*ME",
                "COM\\s*COMERCIO",
                "IPT",
                "INSS",
                "LTDA\\s*ME",
                "VIA\\s*VAREJO",
                "(\\s+)D(?:O|E|A)\\s+"
            )
        );
        $tag = '<var>';

        $patterns = array(
            'last' => "@\\s+{$tag}$@is",
            'any_place' => "@\\b{$tag}@is"
        );

        foreach ($siglas as $key => $tipo) {
            foreach ($tipo as $sigla) {
                $pattern = str_ireplace($tag, $sigla, $patterns[$key]);
                $string = preg_replace($pattern, '$1', $string);
            }
        }

        return $string;
    }

    /**
     * Formata data
     *
     * @param string $date
     *
     * <AUTHOR> Mesquita - 27/08/2019
     *
     * @version 1.0.0
     *
     * @return string
     */
    private function formatDate($date)
    {
        list($year, $month, $day) = explode('-', $date);
        return "{$day}/{$month}/{$year}";
    }
}
