<?php

namespace App\Crawler\CertidaoCeatTrtRS;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

use function GuzzleHttp\json_decode;

class CertidaoCeatTrtRS extends Spider
{
    private $name = '';
    private $cpfCnpj = '';
    private $tipoDocumento = '';
    private const BASE_URL = 'https://pje.trt4.jus.br/';
    private const URL_REQUEST = 'certidoes/trabalhista/emissao';
    private const URL_API = 'pje-certidoes-api/api/propriedades';
    private const URL_API_COD = 'pje-certidoes-api/api/certidoes/trabalhistas/emissao';
    private const URL_CERTIFICATE = 'pje-certidoes-api/api/certidoes/trabalhistas/';
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const CERTIDAO_S3_PATH = 'captura/certidao_ceat_trt4_rs/';
    private $headers = ['User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0'];

    public function start()
    {
        $this->setProxy();
        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::CERTIDAO_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;

        $siteKey = $this->getSiteKeyCaptcha();
        $token = $this->resolveRecaptcha($siteKey);
        $html = $this->makeRequestCertificate($token);
        $text = $this->createPdf($html);
        $data = $this->parseData($text);
        $data['pdf'] = $this->pdf;

        return $data;
    }

    /**
     * Captura o siteKey do captcha.
     * <AUTHOR> Pereira
     * @return string
     */
    private function getSiteKeyCaptcha()
    {
        $json = $this->getResponse(self::BASE_URL . self::URL_API, 'GET', [], $this->headers);
        $json = json_decode($json, true);
        return $json['chaveDeSiteDoCaptcha'];
    }

    /**
     * Faz a requisição da pagina da certidão.
     * <AUTHOR> Pereira
     * @return string
     */
    private function makeRequestCertificate($token)
    {
        $params = [
            'criterioDeEmissao' => $this->tipoDocumento,
            'nome' => $this->name,
            'numeroDoDocumento' => $this->cpfCnpj,
            'respostaDoCaptcha' => $token
        ];

        $params = json_encode($params);
        $response = $this->getResponse(self::BASE_URL . self::URL_API_COD, 'POST', $params, $this->headers);
        $response = json_decode($response);

        $url = self::BASE_URL . self::URL_CERTIFICATE . "{$response->codigo}";
        $certificate = $this->getResponse($url, 'GET', [], $this->headers);
        $certificate = json_decode($certificate, true);

        if (empty($certificate['conteudoHTML'])) {
            throw new Exception("Erro ao capturar as informações, tente novamente.", 3);
        }

        return $certificate['conteudoHTML'];
    }

    /**
     * Cria e sobe para o S3 o arquivo pdf criado.
     * <AUTHOR> Pereira
     * @return string $text
     */
    private function createPdf($html)
    {
        (new Pdf())->saveHtmlToPdf(
            utf8_decode($html),
            $this->certificateLocalPath
        );

        $text = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'layout',
            'nopgbrk'
        ]);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        $this->pdf = $this->certificateUrl;
        return $text;
    }

    /**
     * Resolve o captcha e seta a resposta
     * <AUTHOR> Pereira
     * @return string
     */
    private function resolveRecaptcha($siteKey): string
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $retry = 3;

        do {
            if (!empty($siteKey)) {
                return $this->solveReCaptcha($siteKey, self::BASE_URL . self::URL_REQUEST);
            }

            $retry--;
        } while ($retry > 0);

        throw new Exception("Erro ao localizar dados do captcha na página.", 3);
    }

    /**
     * Faz a captura dos dados
     * <AUTHOR> Pereira
     * @return string
     */
    private function parseData($text)
    {
        $patterns = [
            'codVerificacao' => ['@digo\sde\sverificação:\s([\s\S]*?)\n@'],
            'situacao' => ['@(Certifica-se[\s\S]*?)\n+Observ@'],
            'observacao' => ['@(1.\sEsta[\s\S]*?\/certidoes\/)@'],
        ];

        $data = Util::parseDados($patterns, $text);
        $data = array_map("utf8_decode", $data);

        return $data;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->cpfCnpj = trim($this->param['name_document']);

        if (Document::validarCnpj($this->cpfCnpj)) {
            $this->cpfCnpj = explode('/', Document::formatCnpj($this->cpfCnpj));
            $this->cpfCnpj = $this->cpfCnpj[0];
            $this->tipoDocumento = 'RAIZ_DE_CNPJ';
        } elseif (Document::validarCpf($this->cpfCnpj)) {
            $this->cpfCnpj = Document::formatCpf($this->cpfCnpj);
            $this->tipoDocumento = 'CPF';
        } else {
            $this->name = trim($this->param['name_document']);
            $this->tipoDocumento = 'NOME';
        }
    }
}
