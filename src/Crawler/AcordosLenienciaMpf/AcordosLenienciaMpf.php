<?php

namespace App\Crawler\AcordosLenienciaMpf;

use App\Factory\MongoDB;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Str;
use Exception;

class AcordosLenienciaMpf extends Spider
{
    private const INDEX_MONGODB = 'razao_social';
    private const COLLECTION = 'acordos_leniencia_mpf';
    private const DATA_BASE = 'common';
    private const LIMIT = 50;
    private $manager;

    public function __construct($param, $auth)
    {
        parent::__construct($param, $auth);
        $this->manager = (new MongoDB())
            ->connectSources()
            ->setDb(self::DATA_BASE);
    }

    protected function start()
    {
        $fields = ['interessado'];
        $result = json_decode(
            json_encode(
                $this->manager
                    ->setCollection(self::COLLECTION)
                    ->atlasSearch(
                        $this->param['razao_social'],
                        self::INDEX_MONGODB,
                        self::LIMIT,
                        $fields,
                        'phrase'
                    )
                    ->toArray(),
                true
            ),
            true
        );

        if (empty($result)) {
            throw new Exception('Nenhum resultado encontrado!', 2);
        }

        return $result;
    }

    protected function validateAndSetCrawlerAttributes()
    {
        $chars = array('/', '.', ',', '-');
        $this->param['razao_social'] = str_replace($chars, '', $this->param['razao_social']);

        if (empty($this->param['razao_social'])) {
            throw new Exception("Parâmetro de busca inválido!!", 6);
        }

        $this->param['razao_social'] = Str::removerAcentos($this->param['razao_social']);
        $this->param['razao_social'] = Str::removeSiglas($this->param['razao_social']);
    }
}
