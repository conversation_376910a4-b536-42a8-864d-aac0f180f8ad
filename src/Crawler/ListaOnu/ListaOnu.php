<?php

namespace App\Crawler\ListaOnu;

use App\Crawler\Spider;
use Exception;
use App\Factory\MongoDB;

class ListaOnu extends Spider
{
    private const INDEX_MONGODB = 'name';
    private const LIMIT = 50;

    private $criterion = '';

    protected function validateAndSetCrawlerAttributes()
    {
        if (empty(trim($this->param['nome']))) {
            throw new Exception('Parâmetro de criterio inválido', 1);
        }

        $this->criterion = preg_replace("/\\s+/isu", " ", $this->param['nome']);
        $this->criterion = strtoupper(trim($this->criterion));
    }

    protected function start()
    {
        $this->validateAndSetCrawlerAttributes();

        $results = $this->getResultsByNome($this->param['nome']);

        if (empty($results)) {
            foreach ($this->setSeachNames() as $name) {
                $result = $this->getResultsByNome($name);
                if ($result) {
                    $results[] = $result;
                }
            }
        }

        if (empty($results[0])) {
            throw new Exception('Nenhum resultado encontrado!', 2);
        }

        $results = $this->parseArrayKeyToLower($results);

        $data = [];


        foreach ($results as $result) {
            if ($result['score'] < (int)$results[0]['score']) {
                continue;
            }

            $data[] = $result;
        }

        return $data;
    }

    private function parseArrayKeyToLower($value)
    {
        if (is_array($value)) {
            $array = [];
            foreach ($value as $keyValue => $valueValue) {
                $array[strtolower($keyValue)] = $this->parseArrayKeyToLower($valueValue);
            }

            return $array;
        }

        return $value;
    }

    private function getResultsByNome($name)
    {
        $results = [];

        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('common')
            ->setCollection('lista_onu');
        $fields = ['FIRST_NAME', 'FOURTH_NAME', 'SECOND_NAME', 'THIRD_NAME'];

        $results = json_decode(
            json_encode(
                $manager
                    ->atlasSearch(
                        $this->setSeachNames($this->param['nome']),
                        self::INDEX_MONGODB,
                        self::LIMIT,
                        $fields
                    )
                    ->toArray(),
                true
            ),
            true
        );

        $results = $this->getRelevantMatches($results);

        if (empty($results)) {
            return [];
        }

        return $results;
    }

    public function setSeachNames()
    {
        $explodedNames = explode(" ", $this->param['nome']);
        return $explodedNames;
    }

    public function getRelevantMatches($mongoResults)
    {
        $filteredResults = [];
        foreach ($mongoResults as $result) {
            if ($result['score'] >= 1) {
                $filteredResults[] = $result;
            }
        }
        return $filteredResults;
    }
}
