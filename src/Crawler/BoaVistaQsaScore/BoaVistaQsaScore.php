<?php

namespace App\Crawler\BoaVistaQsaScore;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\BoaVista\BoaVistaManager;
use Exception;

class BoaVistaQsaScore extends Spider
{
    private $cnpj;

    public function start()
    {

        $sources = [
            'administradores',
            'score',
            'socios'
        ];

        $response = (new BoaVistaManager())->searchDefineRisco($this->cnpj, $sources, false, 'qsaScore');

        if (isset($response['socios'])) {
            // Validar cada documento dos sócios, por que há uma condição no spine_update que
            // interrompe a atualização caso um destes não possua um documento válido.
            $socios = array_filter($response['socios'], function ($socio) {
                if (Document::validarCpf($socio['cpf_cnpj']) || Document::validarCnpj($socio['cpf_cnpj'])) {
                    return $socio;
                }
            });

            sort($socios);

            $response['socios'] = $socios;
        } else {
            $response['socios'] = [];
        }

        $this->updateSpine('BoaVistaQsaScore', array_merge($response, ['cnpj' => $this->cnpj]));

        return $response;
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['cnpj'])) {
            throw new Exception("Parâmetro CNPJ é obrigatório", 1);
        }

        if (!Document::validarCnpj($this->param['cnpj'])) {
            throw new Exception('Parâmetro Inválido', 1);
        }

        $this->cnpj = Document::removeMask($this->param['cnpj']);
    }
}
