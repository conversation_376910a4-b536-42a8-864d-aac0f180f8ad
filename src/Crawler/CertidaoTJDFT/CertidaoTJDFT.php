<?php

namespace App\Crawler\CertidaoTJDFT;

use Exception;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\Bucket\StaticUplexisBucket;

/**
 * Classe da fonte Certidao TJDFT
 *
 * <AUTHOR>
 */
class CertidaoTJDFT extends Spider
{
    private const MAIN_URL = 'https://cnc.tjdft.jus.br/solicitacao-externa';
    private const FIRST_REQUEST = 'https://cnc-api.tjdft.jus.br/pessoas/<documento>/externo?captcha=';
    private const SECOND_REQUEST = 'https://cnc-api.tjdft.jus.br/solicitacoes/externo?captcha=';
    private const CAPTCHA_TOKEN = '6LdF880UAAAAAH3wiwYD1ZjpMsR2gkXmvs_wy38W';

    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const CERTIDAO_TJDFT_S3_PATH = 'captura/certidao_tjdft/';

    protected $captcha;
    protected $token;
    protected $nome;
    protected $doc;

    /**
     * Executa a fonte Certidao TJDFT
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 28/01/2019
     *
     * @return array
     */
    protected function start()
    {
        $uniqd = md5(uniqid(rand(), true));

        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::CERTIDAO_TJDFT_S3_PATH .
        $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;

        $documento = preg_replace('/\D/', '', $this->param['cpf_cnpj']);

        $this->setProxy();
        $this->setCurlOpt([
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HEADER => 1
        ]);

        $retry = 5;
        do {
            try {
                $postParams = $this->getPostParams($documento);
                $certificateData = $this->generateAndReturnCertificateData($postParams);
                break;
            } catch (Exception) {
                $retry--;
                if ($retry == 0) {
                    throw new Exception('Não foi possível capturar as informações, tente reprocessar!', 3);
                }
            }
        } while ($retry > 0);

        return $this->parseData($certificateData['certidao']);
    }

    /**
     * Parse e retorno dos dados
     *
     * @param array $data
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 28/01/2019
     *
     * @return array
     */
    private function parseData($data)
    {
        $pdfContent = $this->saveAndReturnContentPdf($data['url_certidao']);

        return [
            'documento' => Document::formatCpfOrCnpj($this->doc),
            'nome' => trim($this->nome),
            'conteudo' => $this->parseItens($pdfContent, 'conteudo'),
            'dataEmissao' => date('d/m/Y', strtotime($data['data_emissao'])),
            'dataAtualizacao' => '', //Não veio a informação no novo site
            'dataValidade' =>  date('d/m/Y', strtotime($data['data_validade'])),
            'seloDigital' => $this->parseItens($pdfContent, 'selo_digital'),
            'pdf' => $this->certificateUrl
        ];
    }

    /**
     * Parse dos itens
     *
     * @param string $pdfContent
     * @param string $item
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 28/01/2019
     *
     * @return string
     */
    private function parseItens($pdfContent, $item)
    {
        $patterns = [
            'conteudo' => '/(certificamos.*?).?observa....es:/i',
            'selo_digital' => '/selo.digital.de.seguran..a:\s(.*?)\s/i'
        ];

        $matches = [];

        if (preg_match($patterns[$item], $pdfContent, $matches)) {
            if (isset($matches[1])) {
                return trim($matches[1]);
            }
        }
        return null;
    }

    /**
     * Salva o PDF e retorna o conteúdo em texto
     *
     * @param string $link
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 28/01/2019
     *
     * @return string
     */
    private function saveAndReturnContentPdf($link)
    {
        $headers = [
            'accept: application/json, text/plain, */*',
            'accept-language: pt-BR,pt;q=0.9',
            'origin: https://cnc.tjdft.jus.br',
            'referer: https://cnc.tjdft.jus.br/',
            'user-agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
        ];

        $content = $this->getResponse($link, 'GET', [], $headers);

        $this->validatePdfContent($content);

        file_put_contents($this->certificateLocalPath, $content);
        $text = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'nopgbrk',
            'layout',
            'fixed 4'
        ]);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);

        return Str::encoding($text);
    }

    /**
     * Valida erros no pdf
     *
     * @param string $content
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 28/01/2019
     *
     * @return string
     */
    private function validatePdfContent($content)
    {
        //Valida se não expirou o pdf
        if (preg_match('/has.expired/i', $content)) {
            throw new Exception("O Link do pdf expirou", 3);
        }
    }

    /**
     * Pega os parametros para enviar no post da pesquisa
     *
     * @param string $documento
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 28/01/2019
     *
     * @return string
     */
    private function getPostParams($documento)
    {
        $this->captcha = $this->resolveRecaptcha();
        $url = str_replace('<documento>', $documento, self::FIRST_REQUEST);
        $url = $url . $this->captcha;

        $headers = [
            'accept: application/json, text/plain, */*',
            'accept-language: pt-BR,pt;q=0.9',
            'origin: https://cnc.tjdft.jus.br',
            'referer: https://cnc.tjdft.jus.br/',
            'user-agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
        ];

        $result = $this->getResponse($url, 'GET', [], $headers);

        preg_match('/X-CNC-TKSE:\s(.*?)\n.*?({.*?})/isu', $result, $matches);

        if (empty($matches[1])) {
            throw new Exception("Erro ao pegar o token", 3);
        }

        $this->token = $matches[1];
        $this->token = substr($this->token, 0, -1);

        if (!empty($matches[2])) {
            $result = $matches[2];
            $result = json_decode($result, true);
            return $result;
        }

        throw new Exception("Erro ao pegar os parametros do post", 3);
    }

    /**
     * Executa a pesquisa e retorna os dados
     *
     * @param array $postParams
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 28/01/2019
     *
     * @return array
     */
    private function generateAndReturnCertificateData($postParams)
    {
        $this->nome = $postParams['nome_receita'];
        $this->doc = $postParams['cpf_cnpj'];

        $params = [
            "nome" => $postParams['nome_receita'],
            "cpf_cnpj" => $postParams['cpf_cnpj'],
            "tipo_certidao" => "FALENCIA_CONCORDATA"
        ];

        $headers = [
            'Origin: https://cnc.tjdft.jus.br',
            'Referer: https://cnc.tjdft.jus.br/',
            'User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
            'X-Cnc-Tkse: ' . $this->token,
        ];

        $this->setCurlOpt([
            CURLOPT_HEADER => 0
        ]);

        $result = $this->getResponse(self::SECOND_REQUEST . $this->captcha, 'POST', json_encode($params), $headers);
        $result = json_decode($result, true);

        if (!empty($result['certidao'])) {
            return $result;
        }

        throw new Exception("Erro ao gerar a certidão", 3);
    }

    /**
     * Resolve o captcha
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 28/01/2019
     *
     * @return array
     */
    private function resolveRecaptcha()
    {
        $captcha = $this->solveReCaptcha(self::CAPTCHA_TOKEN, self::MAIN_URL, 2);

        if (!empty($captcha)) {
            return $captcha;
        }

        throw new Exception("Erro ao resolver o captcha", 3);
    }

    /**
     * Valida e retorna os parametros da fonte
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 28/01/2019
     *
     * @return string
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCpf($this->param['cpf_cnpj']) && !Document::validarCnpj($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro de critério invalido', 1);
        }
    }
}
