<?php

namespace App\Crawler\ChequeSemFundoFederalWeb;

use Exception;
use SoapClient;
use App\Helper\Document;
use App\Crawler\Spider;
use App\Manager\ChequeSemFundoFederalWebManager;

class ChequeSemFundoFederalWeb extends Spider
{
    // docs ?!
    // http://www.federalweb.com.br/integracao/

    private $criterion;

    private $consultarResult;

    private $testing = false;

    private $arrayDados = [];

    private $manager;

    protected function start()
    {
        $this->manager = new ChequeSemFundoFederalWebManager();
        $this->searchByDocumentoFederalWeb();

        $this->analyseResult();

        $this->setArrayDados();

        $this->manager->insertBilhetador($this->arrayDados, $this->criterion);

        return $this->arrayDados;
    }

    private function searchByDocumentoFederalWeb()
    {
        $params = [
            'cod_cliente'      => $this->testing ? '1' : '329', // utilizar para testes o valor '1'
            'cod_usuario'      => $this->testing ? 1 : 1416, // utilizar para testes o valor '1'
            'senha_usuario'    => $this->testing ? '1' : '302120', // utilizar para testes o valor '1'
            'documento'        => $this->criterion,
            'cod_consulta'     => 1,
            //'cmc7'             => '',
            'formato_resposta' => 3,
            'dispositivo'      => 1,
            'cod_integrador'   => 1,
            'dataCheque'       => date('Y-m-d'),
            'valor'            => 1,
        ];

        try {
            $result = $this->manager->getSoapData('Consultar', ['parameters' => $params]);
            $this->consultarResult = $result->ConsultarResult;
        } catch (Exception $e) {
            throw $e;
        }
    }

    private function setArrayDados()
    {
        $divisor = explode("<TR>", $this->consultarResult);

        for ($i = 5; $i < count($divisor); $i++) {
            $regex = '/<\/TR><\/TABLE>/';
            if (preg_match_all($regex, $divisor[$i], $match)) {
                break;
            }

            $regex = '/<TD(.*?)>(.*?)<\/TD>/';

            preg_match_all($regex, $divisor[$i], $response);
            $tagsDate = strip_tags($response[0][0]);
            preg_match('/(.*?)\/\/(.*?)\/.?/', $tagsDate, $date);
            $dataResponse = $date[1] . '/' . $date[2];

            $banco = explode("-", strip_tags($response[0][1]));
            $dados = array(
            'DESCRICAO' => $banco[1],
            'ULTIMA_DATA' => $dataResponse,
            'BANCO' => $banco[0],
            'AGENCIA' => strip_tags($response[0][2]),
            // 'MOTIVO' => $response[0][3],
            'QUANTIDADE' => strip_tags($response[0][4]),
            'ERRO' => '',
            );


            $this->arrayDados[] = $dados;
        }
    }

    private function analyseResult()
    {
        $pattern = '/NADA CONSTA CCF\/BACEN/';

        $str = $this->consultarResult;
        if (preg_match($pattern, $str, $matches, PREG_OFFSET_CAPTURE, 3)) {
            $this->manager->insertBilhetador($str, $this->criterion);
            throw new Exception("Nada encontrado", 2);
        }
    }

    private function checkErrorsFederalWeb($result)
    {
        $pattern = '/DOCUMENTO INVALIDO/';
        if (preg_match($pattern, $result, $matches, PREG_OFFSET_CAPTURE, 3)) {
            throw new Exception("Documento Inválido", 6);
        }
    }

    protected function validateAndSetCrawlerAttributes()
    {

        if (empty($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro de documento inválido');
        }

        $this->param['cpf_cnpj'] = preg_replace('/[^0-9]/', '', (string) $this->param['cpf_cnpj']);

        if (Document::validarCpf($this->param['cpf_cnpj'])) {
            $this->criterion = str_pad($this->param['cpf_cnpj'], 11, '0', STR_PAD_LEFT);
        } elseif (Document::validarCnpj($this->param['cpf_cnpj'])) {
            $this->criterion = str_pad($this->param['cpf_cnpj'], 14, '0', STR_PAD_LEFT);
        } else {
            throw new Exception('Parâmetro de documento inválido');
        }
    }
}
