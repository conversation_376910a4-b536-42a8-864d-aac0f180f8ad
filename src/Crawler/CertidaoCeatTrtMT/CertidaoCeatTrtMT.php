<?php

namespace App\Crawler\CertidaoCeatTrtMT;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

class CertidaoCeatTrtMT extends Spider
{
    private const MAIN_URL = "https://pje.trt23.jus.br";
    private const BASE_URL = "/certidoes/trabalhista/emissao";
    private const API_URL = "/pje-certidoes-api/api/certidoes/trabalhistas";
    private const CAPTCHA_URL = "/pje-certidoes-api/api/propriedades";
    private const PATH_STATIC_S3 = S3_STATIC_URL;
    private const PATH_CERTIDAO_S3 = 'captura/certidao_ceat_trt23_mt/';

    private string $documento = '';
    private string $nomeRazaoSocial = '';
    private string $criterio = '';

    public function start()
    {
        $this->setProxy();
        $data = $this->makeRequest();
        $data = $this->makePdfAndGetText($data);
        $result = $this->parseText($data['textPdf']);

        return [
            'info' => $result,
            'pdf' => $data['urlPdf']
        ];
    }

    /** Função que realização requisição na API
     * <AUTHOR> Santos - 03 maio 23
     * @return string
     * @throws Exception
     */
    public function makeRequest(): mixed
    {
        $params = [
            "criterioDeEmissao" => $this->criterio,
            "nome" => $this->nomeRazaoSocial,
            "numeroDoDocumento" => $this->documento,
            "respostaDoCaptcha" => $this->resolveRecaptcha()
        ];

        $response = $this->getResponse(
            self::MAIN_URL . self::API_URL . '/emissao',
            'POST',
            json_encode($params, JSON_THROW_ON_ERROR)
        );

        $result = json_decode($response, true, 512, JSON_THROW_ON_ERROR);
        if (isset($result['codigo'])) {
            return $this->getResponse(self::MAIN_URL . self::API_URL . "/{$result['codigo']}");
        }

        throw new Exception('Não foi possível capturar as informações da página.', 3);
    }


    /**
     *
     * @return array
     *
     * @param  string  $data
     *
     * @throws \Exception
     */
    private function makePdfAndGetText(string $data): array
    {
        $uniqid = md5(uniqid(rand(), true));
        $certificateName = "{$uniqid}.pdf";
        $certificateLocalPath = "/tmp/{$certificateName}";
        $certificateS3Path = self::PATH_CERTIDAO_S3 . $certificateName;
        $certificateUrl = self::PATH_STATIC_S3 . $certificateS3Path;

        $response = json_decode($data, true, 512, JSON_THROW_ON_ERROR);

        (new Pdf())->saveHtmlToPdf(utf8_decode($response['conteudoHTML']), $certificateLocalPath);

        (new S3(new StaticUplexisBucket()))->save($certificateS3Path, $certificateLocalPath);

        $text = (new Pdf())->getTextFromPdf($certificateLocalPath, ['layout', 'nopgbrk']);

        return ["urlPdf" => $certificateUrl, "textPdf" => $text];
    }


    /** Função que faz o parse do texto do PDF
     *
     * @return array
     *
     * @param  string  $text
     *
     * @throws \Exception
     */
    private function parseText(string $text): array
    {
        $patterns = [
            'codVerificacao' => ['@Código\sde\sverificação:(.*)@'],
            'descricao' => ['@(Certifica-se[\s\S]*?)\s+Observações@'],
            'observacoes' => ['@Observações:([\s\S]*?)\s+Certidão@']
        ];
        $data = Util::parseDados($patterns, $text);
        return array_map("utf8_decode", $data);
    }


    public function validateAndSetCrawlerAttributes()
    {
        $this->documento = trim($this->param['documento']);

        if (empty($this->documento)) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        if (Document::validarCpf($this->documento)) {
            $this->documento = Document::formatCpf($this->documento);
            $this->criterio = "CPF";
        } elseif (Document::validarCnpj($this->documento)) {
            $this->documento = Document::formatCnpj($this->documento);
            $this->documento = substr($this->documento, 0, 10);
            $this->criterio = "RAIZ_DE_CNPJ";
        } else {
            $this->nomeRazaoSocial = trim($this->documento);
            $this->criterio = "NOME";
        }
    }


    /**
     * @throws \Exception
     */
    private function resolveRecaptcha()
    {
        $token = $this->getResponse(self::MAIN_URL . self::CAPTCHA_URL);
        preg_match('/"chaveDeSiteDoCaptcha":"([\s\S]*?)"/', $token, $keyCapctha);
        $retry = 3;
        do {
            if (!empty($keyCapctha[1])) {
                return $this->solveReCaptcha($keyCapctha[1], self::MAIN_URL . self::BASE_URL);
            }
            $retry--;
        } while ($retry > 0);
        throw new Exception("Erro ao localizar dados do captcha na página.", 3);
    }
}
