<?php

namespace App\Crawler\CertidaoRegional;

use App\Helper\Util;
use Exception;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\Bucket\StaticUplexisBucket;

class CertidaoRegional extends Spider
{
    private $certificateName;
    private $certificatePath;
    private $certificatePathS3;

    private $mainURL = 'https://www2.trf4.jus.br/trf4/processos/certidao/index.php';
    private $formURL = "https://www2.trf4.jus.br/trf4/processos/certidao/proc_processa_certidao.php?";
    private $formDoc = "https://www2.trf4.jus.br/trf4/processos/certidao/certidaoreg/formulario_solicitacao.php";
    private $pdfURL = "https://www2.trf4.jus.br/trf4/processos/certidao_balcao/certidao_emite_cjf.php?";

    /**
     * Valida os parametros
     *
     * @return void
     *
     * <AUTHOR> <<EMAIL>>
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['nome'])) {
            throw new Exception('É obrigatório o Nome!', 1);
        }

        if (empty($this->param['cpf_cnpj'])) {
            throw new Exception('É obrigatório o CPF/CNPJ!', 1);
        }
    }

    protected function start()
    {
        $uniqid = md5(uniqid(rand(), true));
        $this->certificateName = 'certificate_' . $uniqid . '.pdf';
        $this->certificatePath = "/tmp/{$this->certificateName}";
        $this->certificatePathS3 = S3_STATIC_URL .
            "captura/certidao_regional/{$this->certificateName}";

        $this->param['nome'] = Str::removerAcentos($this->param['nome']);
        $this->param['cpf_cnpj'] = Document::formatCpfOrCnpj($this->param['cpf_cnpj']);
        $this->setAlternativeProxy();
        // Gerando Cookies
        $html = $this->getResponse($this->mainURL);

        $key = $this->getSitekey($html);
        $captcha = $this->solveReCaptcha($key, $this->mainURL);

        $params = [
            'string_nome' => $this->param['nome'],
            'string_cpf' => $this->param['cpf_cnpj'],
            'string_tipo_cert' => 'N',
            'g-recaptcha-response' => $captcha
        ];

        $query = http_build_query($params);
        curl_setopt($this->ch, CURLOPT_HEADER, true);
        curl_setopt($this->ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($this->ch, CURLOPT_MAXREDIRS, 5);
        $response = $this->getResponse("{$this->formURL}{$query}");

        if (!preg_match('/&num_contro_certid=([\d]+)/is', $response, $output)) {
            throw new Exception('Não foi possível recuperar o id da certidão', 3);
        }

        $pdf = $this->getPdf($output[1], $response);

        $textFromPdf = $this->getTextOfPdf($pdf);

        return $this->getData($textFromPdf);
    }

    /**
     * Recupera texto do PDF gerado
     *
     * @param string $pdf
     * @return string
     *
     * <AUTHOR> Medeiros <<EMAIL>>
     */
    private function getTextOfPdf($pdf)
    {
        file_put_contents($this->certificatePath, $pdf);

        $text = (new Pdf())->getTextFromPdf($this->certificatePath, [
            'layout',
            'nopgbrk'
        ]);

        return $text;
    }

    /**
     * Gera o PDF da certidão
     *
     * @param string $idCertidao
     * @return string
     *
     * <AUTHOR> Medeiros <<EMAIL>>
     */
    private function getPdf($idCertidao, $html)
    {
        $certParams = [
            'num_contro_certid' => $idCertidao,
            'num_cpf_cgc_judici' => Document::removeMask($this->param['cpf_cnpj']),
            'nom_parte_judici' => $this->param['nome']
        ];

        $certQuery = http_build_query($certParams);

        if (Document::validarCnpj(Document::removeMask($this->param['cpf_cnpj']))) {
            $certQuery = $this->getParamsBySite($html);
        }

        curl_setopt($this->ch, CURLOPT_HEADER, false);

        $response = $this->getResponse("{$this->pdfURL}{$certQuery}");

        return $response;
    }

    private function getParamsBySite($html)
    {
        $pattern = '/emite_cjf\.php\?([\w\d+_&=\.]+)/is';

        if (preg_match($pattern, Str::cleanString($html), $output)) {
            return $output[1];
        } else {
            throw new Exception('Não foi possível recuperar link da certidão', 3);
        }
    }

    /**
     * Parseia as informações do resultado da fonte
     *
     * @param string $html
     * @return array
     *
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function parseData(string $html): array
    {
        $html = utf8_encode($html);
        $notWorkPattern = '/não foi possível emitir/iu';
        $anyHavePattern = '/nada consta/iu';
        $needFullName = '/No nome da parte\, deve ser informado o nome completo da parte./iu';

        if (preg_match($needFullName, $html)) {
            throw new Exception('No nome da parte, deve ser informado o nome completo da parte.', 3);
        }

        if (preg_match($notWorkPattern, $html)) {
            return $this->notHaveSufficientPermission($html);
        }

        if (preg_match($anyHavePattern, $html)) {
            return $this->nothingAbout($html);
        }

        throw new Exception('Houve um resultado não esperado!', 3);
    }

    /**
     * Recupera as informações contidas no PDF
     *
     * @param string $text
     * @return array
     *
     * <AUTHOR> Medeiros <<EMAIL>>
     */
    private function getData($text)
    {
        $text = Str::cleanString($text);
        $patterns = [
            'data_consulta' => ['/certid\W+o\s+emitida\s+em:\s+([\/\d]+)/is', null],
            'hora_consulta' => ['/certid\W+o\s+emitida\s+em:\s+[\/\d\sà]+s\s([\d\:]+)/is', null],
            'numero'        => ['/valida\W+o:\s+([\d]+)/is', null],
            'text'          => ['/(Certificamos[\W\w\d]+.)\s+N/is', null]
        ];
        $this->savePdf();
        $data = Util::parseDados($patterns, $text);
        $data['text'] = utf8_decode($data['text']);
        $data['nome'] = $this->param['nome'];
        $data['documento'] = $this->param['cpf_cnpj'];
        $data['pdf'] = $this->certificatePathS3;

        return $data;
    }

    /**
     * Parseia não tem permissão suficiente
     *
     * @param string $html
     * @return array
     *
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function notHaveSufficientPermission(string $html): array
    {
        $textoPattern = '/(<b>não são suficientes[\s\S]*justiça federal da 4ª região\.)/iu';

        $emissaoPattern = '/(?:Emissão:\s<\/b>\s([\S]*)\s([\S]*)\.)/iu';

        if (!preg_match($textoPattern, $html, $textoMatch)) {
            throw new Exception('Não foi possível parsear conteúdo!', 3);
        }

        if (!preg_match($emissaoPattern, $html, $emissaoMatch)) {
            throw new Exception('Não foi possível parsear conteúdo!', 3);
        }

        $replaceLink = "/trf4/processos/certidao/certidaoreg/formulario_solicitacao.php";

        $texto = preg_replace('/[\n\r]/', '', $textoMatch[1]);
        $texto = str_replace($replaceLink, $this->formDoc, $texto);
        $texto = str_replace(['<p>', '</p>'], '', $texto);
        $texto = str_replace('<span style="color:#FF0000;">', '><span style="color:#FF0000;">', $texto);
        $texto = preg_replace('/(\s)+/', ' ', $texto);
        $texto = strip_tags($texto, '<a><b>');

        $this->adjustAndSavePdf($html);

        return [
            'nome' => $this->param['nome'],
            'documento' => $this->param['cpf_cnpj'],
            'texto' => $texto,
            'numero' => '',
            'hora_consulta' => $emissaoMatch[1],
            'data_consulta' => $emissaoMatch[2],
            'pdf' => $this->certificatePathS3,
        ];
    }

    /**
     * Parseia o Nada Consta
     *
     * @param string $html
     * @return array
     *
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function nothingAbout(string $html): array
    {

        //$textoPattern = '/(<b>(?:&nbsp;[\s\S]*)nada consta[\s\S]*Judiciárias Federais)/miu';
        $textoPattern = '/<b>nada consta(.|\n)*?Judiciárias Federais/iu';
        $numeroPattern = '/número de controle\s<b>\s*(.*?)<br\/>/iu';
        $emissaoPattern = '/certidão emitida em:\s([\S]*)\sàs\s([\S]*)/iu';

        if (!preg_match($textoPattern, $html, $textoMatch)) {
            throw new Exception('Não foi possível parsear conteúdo!', 3);
        }

        if (!preg_match($emissaoPattern, $html, $emissaoMatch)) {
            throw new Exception('Não foi possível parsear conteúdo!', 3);
        }

        if (!preg_match($numeroPattern, $html, $numeroMatch)) {
            throw new Exception('Não foi possível parsear conteúdo!', 3);
        }



        $texto = preg_replace('/[\n\r]/', '', $textoMatch[0]);
        $texto = str_replace('&nbsp;', '', $texto);
        $texto = preg_replace('/(\s)+/', ' ', $texto);
        $texto = strip_tags($texto/*, '<b>'*/);

        $this->adjustAndSavePdf($html, true);

        return [
            'nome' => $this->param['nome'],
            'documento' => $this->param['cpf_cnpj'],
            'texto' => $texto,
            'numero' => $numeroMatch[1],
            'hora_consulta' => $emissaoMatch[2],
            'data_consulta' => $emissaoMatch[1],
            'pdf' => $this->certificatePathS3,
        ];
    }

    /**
     * Salva o pdf no S3
     *
     * <AUTHOR> Medeiros <<EMAIL>>
     */
    private function savePdf()
    {
        (new S3(new StaticUplexisBucket()))->save(
            "captura/certidao_regional/{$this->certificateName}",
            $this->certificatePath
        );
    }

    /**
     * Ajusta as caracteristicas do HTML e salva no Pdf
     *
     * @param string $html
     * @param boolean $needBarcode
     * @return void
     *
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function adjustAndSavePdf(string $html, bool $needBarcode = false)
    {

        //$barcodePatterns = '/src="img\/(.*?)"/iu';
        $barcodePatterns = '/src="\.\.\/img\/(.*?)"/iu';

        $search = [
            '/infra_css/',
            'href="css/',
            '/infra_js/',
            'certidaoreg/images/bracons.gif',
            'bot_imprimir.gif',
            'charset=iso-8859-1',
        ];

        $replaces = [
            'https://www2.trf4.jus.br/infra_css/',
            'href="https://www2.trf4.jus.br/trf4/processos/certidao/css/',
            'https://www2.trf4.jus.br/infra_js/',
            'http://' . S3_STATIC_BUCKET . '/captura/certidao_regional/assets/bracons.gif',
            'http://' . S3_STATIC_BUCKET . '/captura/certidao_regional/assets/bot_imprimir.gif',
            'charset=utf-8',
        ];

        if (!preg_match_all($barcodePatterns, $html, $match) && $needBarcode) {
            throw new Exception('Não foi possível gerar o PDF! - (Imagens)', 3);
        }

        if ($needBarcode) {
            $baseImage = 'https://www2.trf4.jus.br/trf4/processos/certidao/img/';

            $barcode = $match[1][0];
            $qrcode = $match[1][1];

            $barcodeImage = file_get_contents("{$baseImage}$barcode");
            $type = pathinfo($barcode, PATHINFO_EXTENSION);

            $search[] = "img/${barcode}";
            $base64 = base64_encode($barcodeImage);
            $replaces[] = "data:image/{$type};base64,{$base64}";

            $qrcodeImage = file_get_contents("{$baseImage}$qrcode");
            $type = pathinfo($qrcode, PATHINFO_EXTENSION);

            $search[] = "img/${qrcode}";
            $base64 = base64_encode($qrcodeImage);
            $replaces[] = "data:image/{$type};base64,{$base64}";
        }

        $html = str_replace($search, $replaces, $html);

        (new Pdf())->saveHtmlToPdf($html, $this->certificatePath);

        (new S3(new StaticUplexisBucket()))->save(
            "captura/certidao_regional/{$this->certificateName}",
            $this->certificatePath
        );
    }

    /**
     * Retorna sitekey do reCaptcha V2
     *
     * @param string $html
     * @return array
     *
     * <AUTHOR> Vidal <<EMAIL>>
     */
    private function getSiteKey($html)
    {
        if (preg_match('/data-sitekey=\"(.*?)\"/is', $html, $matches)) {
            return $matches[1];
        }

        throw new Exception('Não foi possível recuperar o sitekey', 3);
    }
}
