<?php

namespace App\Crawler\ChatGptNewsResume;

use App\Crawler\Spider;
use App\Manager\HtmlToText\HtmlToTextManager;
use App\Manager\ChatGPT\ChatGPTManager;
use Exception;

class ChatGptNewsResume extends Spider
{
    public function start()
    {
        $text = (new HtmlToTextManager())->transform($this->link);
        $resumo = (new ChatGPTManager())->resumeNews($text);
        return [
            'resumo' => $resumo
        ];
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty(trim($this->param['link']))) {
            throw new Exception('Parâmetros inválidos', 3);
        }

        $this->link = $this->param['link'];
    }
}
