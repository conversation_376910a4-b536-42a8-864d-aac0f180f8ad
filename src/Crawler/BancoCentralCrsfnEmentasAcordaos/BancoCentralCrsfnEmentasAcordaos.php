<?php

namespace App\Crawler\BancoCentralCrsfnEmentasAcordaos;

use App\Crawler\Spider;
use App\Helper\Str;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;
use ReflectionClass;

class BancoCentralCrsfnEmentasAcordaos extends Spider
{
    private $criterion = '';

    private $result;

    private $query;

    private $numOcorrencias = 15;

    private $awsFile = S3_STATIC_URL . "captura/bcb_crsfn/<replace>";

    private $s3Path = "captura/bcb_crsfn";

    private $localPath = '/tmp/BancoCentralCrsfnEmentasAcordaos';

    /**
     *  Just to debug
     */
    private $refClass = null;
    protected $removeNameSpaceFromDebug = true;


    protected function start(): array
    {
        $this->debug();

        $this->result = $this->searchByName();

        return $this->parseResults();
    }

    private function searchByName(): string
    {
        $this->debug();

        try {
            $result = $this->getResponse($this->getUrl());
        } catch (Exception $e) {
            throw $e;
        }

        return $result;
    }

    private function parseResults(): array
    {
        $json = json_decode($this->result);

        $resultados = $json
            ->d
            ->query
            ->PrimaryQueryResult
            ->RelevantResults
            ->Table
            ->Rows;

        $arrResult = array();
        $index = -1;

        foreach ($resultados as $resultado) {
            foreach ($resultado as $r) {
                $index++;
                foreach ($r->Cells->results as $r2) {
                    if ($r2->Key == 'HitHighlightedSummary') {
                        $snippet = $this->encoding($r2->Value);
                        //$snippet = preg_replace('/[^[:print:]]/', ' ', $snippet);
                        $arrResult[$index]['snippet'] = $snippet;
                    }

                    if ($r2->Key == 'fileName') {
                        $title = $r2->Value;
                        $fileName = $this->setFileName(pathinfo($title));
                        $linkUrl = "http://www.bcb.gov.br/crsfn/download.asp?arquivo=";
                        $link = $linkUrl . urlencode(utf8_decode($r2->Value));
                        $arrResult[$index]['link'] = $link;
                        $arrResult[$index]['title'] = $title;
                        $arrResult[$index]['awsPath'] = str_replace('<replace>', "$fileName", $this->awsFile);
                        $arrResult[$index]['fileName'] = $fileName;
                    }
                }

                if (isset($snippet)) {
                    if (!preg_match("#" . urldecode($this->query) . "#i", strip_tags($snippet))) {
                        continue;
                    }
                }
            }
        }

        $result = [];
        foreach ($arrResult as $arr) {
            $awsLink = '';

            if (!empty($arr['link'])) {
                $doc = $this->getResponse($arr['link']);

                if (!$this->saveDoc($doc, $arr['fileName'])) {
                    throw new Exception("Erro ao salvar documento", 6);
                }

                $localPath = $this->localPath . $arr['fileName'];
                $s3Path = $this->s3Path . '/' . $arr['fileName'];

                if (!$this->saveDocToS3($s3Path, $localPath)) {
                    throw new Exception('Erro ao salvar o pdf', 6);
                }

                $awsLink = S3_STATIC_URL . $s3Path;

                @unlink($localPath);
            }

            $result[] = [
                'title' => $arr['title'],
                'snippet' => $arr['snippet'],
                'html' => '',
                'link' => $awsLink
            ];
        }

        if (count($result) == 0) {
            throw new Exception('Nada foi encontrado', 2);
        }

        return (array) $result;
    }

    private function saveDoc(string $doc, $fileName): bool
    {
        $this->debug();

        return @file_put_contents($this->localPath . $fileName, $doc);
    }

    private function saveDocToS3(string $s3Path, string $docPath): bool
    {
        $this->debug();

        return (bool)(new S3(new StaticUplexisBucket()))->save($s3Path, $docPath);
    }

    private function setFileName(array $fileInfo): string
    {
        $key = md5(uniqid(rand(), true));

        return $key . "." . $fileInfo['extension'];
    }

    private function encoding($str): string
    {
        if (!is_array($str) and !is_object($str)) {
            $str = str_replace(['<c0>', '</c0>', '<ddd/>'], '', $str);
        }

        return Str::encoding($str);
    }

    private function getUrl(): string
    {
        $this->debug();

        /* Condição que pesquisa pelo nome exato */
        $nome = $this->param['exato'] ? '$2plic' . $this->criterion . '$2plic' : $this->criterion;

        $url = [
            'http://www.bcb.gov.br/crsfn/proxyBuscaSP.asp?',
            'queryString=querytext=$plicsite:https://edicao-crsfn.bcb.gov.br/Documentos/%20',
            $nome,
            '%20$plic%26sourceid=$plic3aea6f46-8745-4ae8-98a7-75e485d13bdb$plic',
            '%26selectproperties=$plicfileName,Path,HitHighlightedSummary$plic',
            '%26refiners=$plicRefinableInt04%28discretize=manual/1995/2000/2005/2010/2015',
            '%29,RefinableInt07%28discretize=manual/100/150/200/250/300/350/400%29$plic%26rowlimit=',
            $this->numOcorrencias,
            '%26refinementfilters=$plicAND%28RefinableInt04:range%28min,max%29,RefinableInt07:',
            'range%28min,max%29%29$plic%26startrow=0&_=1446731303151'
        ];

        return implode('', $url);
    }

    protected function validateAndSetCrawlerAttributes(): void
    {
        $this->debug();

        // validation
        if (empty($this->param['nome'])) {
            throw new Exception('Parâmetro inválido', 1);
        }

        // setting
        $nome = $this->param['nome'];
        $this->criterion = $this->prepareCriterion($nome);
    }

    private function prepareCriterion(string $str): string
    {
        $this->debug();

        $str = Str::cleanString($str);
        $str = Str::removerAcentos($str);
        $str = trim($str);

        return urlencode($str);
    }

    public function debug(): void
    {

        if (property_exists($this, 'debug') && $this->debug) {
            if (!$refClass) {
                $refClass = new ReflectionClass($this);
            }

            $trace = debug_backtrace()[1];
            $class = $trace['class'];

            $removePropName = 'removeNameSpaceFromDebug';

            if (property_exists($this, $removePropName) && $this->{$removePropName}) {
                $class = $refClass->getShortName();
            }

            $fn = $trace['function'];
            print PHP_EOL . $class . '::' . $fn . PHP_EOL;
        }
    }
}
