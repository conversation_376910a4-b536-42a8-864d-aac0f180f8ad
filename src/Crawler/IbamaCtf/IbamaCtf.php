<?php

namespace App\Crawler\IbamaCtf;

use App\Crawler\Spider;
use App\Helper\Str;
use App\Helper\Document;
use Exception;

class IbamaCtf extends Spider
{
    private const RULES = [
        'numero_de_registro' => '#<td\s*id\=\"td_num_registro\"[^>]*?>\s*<span\s*>' .
            '<input\s*type\=\"text"\s*align\=\"left\"\s[^>]*?tabIndex=\"[^v]*value\=\"(.*?)\"\s*[^>]*><\/span>#is',
        'data_consulta' => '#<span\s*><input\s*type\=\"text"\s*align\=\"left\"\s[^>]' .
            '*?tabIndex=\"[^id]*id\=\"dat_consulta\"\s*[^v]*value\=\"(.*?)\"[^>]*><\/span>#is',
        'dat_emissao' => '#<span\s*><input\s*type\=\"text"\s*align\=\"left\"\s[^>]*?' .
            'tabIndex=\"[^id]*id\=\"dat_emissao\"\s*[^v]*value\=\"(.*?)\"[^>]*><\/span>#is',
        'dat_validade' => '#<span\s*><input\s*type\=\"text"\s*align\=\"left\"\s[^>]*?' .
            'tabIndex=\"[^id]*id\=\"dat_validade\"\s*[^v]*validade\"\s*value\=\"(.*?)\"[^>]*><\/span>#is',
        'cnpj' => '#<span\s*><input\s*type="text"\s*align="left"[^i]*Index\=\"[^i]*id\=\"' .
            'nom_cnpj\"[^l]*lue=\"(.*?)\"\s*[^C]*Class[^<]*<\/span>#is',
        'razao_social' => '#<span\s*><input\s*type="text"\s*align="left"[^i]*Index\=\"[^i]*' .
            'id\=\"nom_pessoa\"[^l]*lue=\"(.*?)\"\s*[^C]*Class[^<]*<\/span>#is',
        'nome_fantasia' => '#<span\s*><input\s*type="text"\s*align="left"[^i]*Index\=\"[^i]' .
            '*id\=\"nom_fantasia\"[^l]*lue=\"(.*?)\"\s*[^C]*Class[^<]*<\/span>#is',
        'data_de_abertura' => '#<span\s*><input\s*type="text"\s*align="left"[^i]*Index\=\"[^i]' .
            '*id\=\"dat_constituicao\"[^l]*lue=\"(.*?)\"\s*[^C]*Class[^<]*<\/span>#is',
        'logradouro' => '#<span\s*><input\s*type="text"\s*align="left"[^i]*Index\=\"[^i]*id\=' .
            '\"endereco\"[^l]*lue=\"(.*?)\"\s*[^C]*Class[^<]*<\/span>#is',
        'complemento' => '#<span[^>]*><input\s*type="text"\s*align="left"[^i]*Index\=\"[^i]*' .
            'id\=\"complemento"\s*[^>]*lue=\"(.*?)\"\s*[^C]*Class[^<]*<\/span>#is',
        'numero' => '#<span[^>]*><input\s*type="text"\s*align="left"[^i]*Index\=\"[^i]*id\=' .
            '\"numero"\s*[^>]*lue=\"(.*?)\"\s*[^C]*Class[^<]*<\/span>#is',
        'municipio' => '#<span[^>]*><input\s*type="text"\s*align="left"[^i]*Index\=\"[^i]' .
            '*id\=\"municipio"\s*[^>]*lue=\"(.*?)\"\s*[^C]*Class[^<]*<\/span>#is',
        'bairro' => '#<span[^>]*><input\s*type="text"\s*align="left"[^i]*Index\=\"[^i]*id\=' .
            '\"bairro"\s*[^>]*lue=\"(.*?)\"\s*[^C]*Class[^<]*<\/span>#is',
        'uf' => '#<span[^>]*><input\s*type="text"\s*align="left"[^i]*Index\=\"[^i]*id\=' .
            '\"uf"\s*[^>]*lue=\"(.*?)\"\s*[^C]*Class[^<]*<\/span>#is',
        'cep' => '#<span[^>]*><input\s*type="text"\s*align="left"[^i]*Index\=\"[^i]*id\=' .
            '\"cep"\s*[^>]*lue=\"(.*?)\"\s*[^C]*Class[^<]*<\/span>#s',
        'categoria_detalhes' => '#<tr\s*id\=\"categoria_detalhe_ln_[^0]\">\s*<td\s*class\=\"' .
            'formDinGrideTD\"\s*style\="[^>]*>(.*?)<\/td>\s*<td\s*class\=\"formDinGrideTD\"\s*' .
            'style\=\"[^>]*>(.*?)<\/td>#is'
    ];
    private $urlPost = "https://servicos.ibama.gov.br/ctf/publico/certificado_regularidade_consulta.php";

    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (empty($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro de documento inválido');
        }

        if (!Document::validarCpfOuCnpj($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro de documento inválido');
        }

        $this->param['cpf_cnpj'] = preg_replace('/[^0-9]/', '', $this->param['cpf_cnpj']);
    }

    protected function start()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $this->setProxy();

        $headers = [
            'Origin' => 'https://servicos.ibama.gov.br',
            'User-Agent' => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko)' .
                            'Chrome/93.0.4577.63 Safari/537.36',
            'Host' => 'servicos.ibama.gov.br',
            'SSL_VERIFYHOST' => false
        ];

        $html = $this->getResponse($this->urlPost, "POST", $this->getDados(), $headers);

        $cnpjFound = preg_match(
            "#class=\"formDinMsgRodapeErros\"><blink>\\s*CPF\\s*\\/\\s*CNPJ\\s*n.{1,8}o\\s*" .
                "encontrado<br>\\s*<\\/blink>#is",
            $html
        );
        if ($cnpjFound) {
            throw new Exception("CNPJ não encontrado.", 2);
        }

        return $this->parseResponse($html);
    }

    public function parseResponse($response)
    {
        if ($this->debug) {
            print __METHOD__ . "\n";
        }

        if (empty($response)) {
            throw new Exception("Erro de Entrada - parametro invalido", PARAM_ERROR);
        }

        $rulesResult = [];
        $retorno = [];
        foreach (self::RULES as $ruleName => $regex) {
            preg_match_all($regex, $response, $rulesResult[$ruleName]);

            //retira o primeiro elemento dos matches, por ser a string inteira
            array_shift($rulesResult[$ruleName]);

            if ($ruleName !== 'categoria_detalhes') {
                $retorno[$ruleName] = (count($rulesResult[$ruleName]) == 1)
                ? $rulesResult[$ruleName][0]
                : array_merge($rulesResult[$ruleName][0], $rulesResult[$ruleName][1]);
            } else {
                foreach ($rulesResult[$ruleName][0] as $key => $value) {
                    $retorno[$ruleName][$key] = [
                        $value,
                        $rulesResult[$ruleName][1][$key]
                    ];
                }
            }
        }

        $retorno['certificado'] = array('');
        if (
            preg_match_all(
                '/<span id="campo_aviso" class="formDinCampoHtml">\s*<p' .
                '\s+class=\'formDinRotulo\'.*?>(.*?)<\/p>\s*<\/span>/s',
                $response,
                $output_array
            )
        ) {
            $certificado = str_replace('</p>', "\n", $output_array[1][0]);
            $certificado = strip_tags($certificado);
            $retorno['certificado'] = array($certificado);
        }

        return Str::encoding($retorno);
    }

    private function getDados()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $param = array(
            "formDinAcao" => "Consultar",
            "formDinPosVScroll" => "30",
            "formDinPosHScroll" => "0",
            "num_cpf_cnpj" => $this->param['cpf_cnpj'],
            "num_pessoa" => ""
        );

        return $param;
    }
}
