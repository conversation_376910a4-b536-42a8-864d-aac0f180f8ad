<?php

namespace App\Crawler\ReclameAqui;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Str;
use Exception;
use Goutte\Client;

class ReclameAqui extends Spider
{
    private const URL =
    "https://iosearch.reclameaqui.com.br/raichu-io-site-search-v1/companies/search/<empresa>";
    private const URL_BUSCA = "https://www.reclameaqui.com.br/busca/?q=<empresa>";
    private const URL_EMPRESA_CNPJ = "https://iosite.reclameaqui.com.br/raichu-io-site-v1/company/<id>/public/";
    private const URL_EMPRESA = "https://iosite.reclameaqui.com.br/raichu-io-site-v1/companyindex/company/<id>";
    private const URL_RECLAMACAO = "https://iosearch.reclameaqui.com.br/raichu-io-site-search-v1/complains/";
    private const USER_AGENT = [
        'User-Agent' => "Mozilla/5.0 (X11; Linux x86_64)" .
            "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36"
    ];
    private const DEFAULT_RETRY = 10;

    private $name;
    private $descLimit = 2;

    protected function validateAndSetCrawlerAttributes()
    {
        if (empty(trim($this->param['nome']))) {
            throw new Exception('Parâmetro de criterio inválido', 1);
        }

        $this->name = strtolower(trim($this->param['nome']));

        if (Document::validarCnpj($this->name)) {
            $this->name = Document::removeMask($this->name);
        } else {
            $this->name = rawurlencode(Str::removeSiglas($this->name));
        }
    }

    protected function start()
    {
        $this->setProxy();

        $data = $this->getEmpresaGeral();
        $data = $this->getAllComplaints($data);
        $empresaGeral = $data['empresa'];
        $descricao = $data['descricao'];

        $empresa = $this->getEmpresaByEmpresaGeral($empresaGeral);
        $complaint = $this->getComplaintByEmpresa($empresa, $empresaGeral['id']);
        $result = $this->parseDataToResult($empresaGeral, [], $complaint, $descricao);
        return $result;
    }

    private function getEmpresaGeral()
    {
        $retry = 0;
        $url = str_replace('<empresa>', $this->name, self::URL);
        $urlBusca = str_replace('<empresa>', $this->name, self::URL_BUSCA);
        $responseArray = null;

        while ($retry <= self::DEFAULT_RETRY) {
            if ($retry > 0) {
                if ($retry % 2 == 0) {
                    echo "unblocker \n";
                    $this->setProxyDataUnblocker();
                } else {
                    echo "normal \n";
                    $this->setProxy();
                }
            }

            $headers = [
                "User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0",
                "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "Accept-Language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7",
                "DNT: 1",
                "Connection: keep-alive",
                "Upgrade-Insecure-Requests: 1"
            ];

            $response = $this->getResponse($url, "GET", [], $headers);
            $responseArray = json_decode($response, JSON_INVALID_UTF8_IGNORE);

            if (!empty($responseArray)) {
                break;
            }

            if ($retry == self::DEFAULT_RETRY) {
                throw new Exception('Tentativas de buscas excedidas. Tente processar novamente!', 3);
            }

            $retry++;
        }

        $companyName = Str::removerAcentos(trim(strtolower(urldecode($this->name))));
        $companyNameSite = Str::removerAcentos(trim(strtolower(Str::removeSiglas(
            $responseArray['companies'][0]['name']
        ))));
        $matchName = (strpos($companyNameSite, $companyName));

        $isEmpty = (empty($responseArray['companies'][0]['name']));

        if ($isEmpty || $matchName === false) {
            throw new Exception('Nada encontrado', 2);
        }

        $data['empresa'] = $responseArray['companies'][0];

        return $data;
    }

    public function getAllComplaints($empresa)
    {
        $queryParams = [
            "company" => $empresa['empresa']['id'],
            "status" => 'PENDING',
            "index" => '0',
            "offset" => '10',
            "order" => 'created',
            "orderType" => 'desc',
        ];

        $url = self::URL_RECLAMACAO . '?' . http_build_query($queryParams) .
            "&deleted=bool:false&evaluated=bool:false&fields=title,description";

        $headers = [
            "User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0",
            "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language: en-US,en;q=0.5",
            "DNT: 1",
            "Connection: keep-alive",
            "Upgrade-Insecure-Requests: 1"
        ];

        $response = $this->getResponse($url, "GET", [], $headers);

        $response = json_decode($response, true);

        if (is_array($response) && count($response) > 0) {
            $empresa['descricao'] = $this->parseComplaints($response['data']);
        }

        return $empresa;
    }

    public function parseComplaints($complaints)
    {
        foreach ($complaints as $complaint) {
            $parsedComplaints[] = strip_tags($complaint['description'], 'br');
        }
        return $parsedComplaints;
    }

    private function requestWithUserAgent($url, $userAgent)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_USERAGENT, $userAgent);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        return curl_exec($curl);
    }

    private function getCriterioCitado($result)
    {
        foreach ($result as $key => $d) {
            $descreption[$key] = strip_tags($d['description']);

            if ($this->descLimit == count($descreption)) {
                break;
            }
        }

        return $descreption;
    }

    private function getEmpresaCnpjByEmpresaGeral($empresa)
    {
        $url = str_replace('<id>', $empresa['id'], self::URL_EMPRESA_CNPJ);
        $response = $this->getResponse($url, "GET", [], self::USER_AGENT);
        $responseArray = json_decode($response, true);
        if ((strcmp($responseArray['status'], 'ERROR') == 0)) {
            throw new Exception('CNPJ inválido', 2);
        }

        return $responseArray;
    }

    private function getEmpresaByEmpresaGeral($empresa)
    {
        $url = str_replace('<id>', $empresa['id'], self::URL_EMPRESA);
        $response = $this->getResponse($url, "GET", [], self::USER_AGENT);
        $responseArray = json_decode($response, true);
        return $responseArray;
    }

    private function getComplaintByEmpresa($empresa, $empresaId)
    {
        $complaint = [];
        foreach ($empresa['data'] as $empresaReputacao) {
            if ($empresaReputacao['company']['id'] == $empresaId) {
                $complaint = array(
                    'consumerScore' => $empresaReputacao['consumerScore'],
                    'dealAgainPercentual' => $empresaReputacao['dealAgainPercentual'],
                    'solvedPercentual' => $empresaReputacao['solvedPercentual'],
                    'answeredPercentual' => $empresaReputacao['answeredPercentual'],
                    'start' => $empresaReputacao['start'],
                    'totalComplains' => $empresaReputacao['totalComplains'],
                    'averageAnswerTime' => $empresaReputacao['averageAnswerTime'],
                    'totalEvaluated' => $empresaReputacao['totalEvaluated'],
                    'totalNotAnswered' => $empresaReputacao['totalNotAnswered'],
                    'totalAnswered' => $empresaReputacao['totalAnswered'],
                );
                break;
            }
        }
        return $complaint;
    }

    private function parseDataToResult($empresaGeral, $empresaCnpj, $complaint, $descricao)
    {
        $result = [];
        $result['identificacao'] = array(
            'empresa'       => $empresaGeral['name'] ?? '',
            'data_cadastro' => $complaint['start'] ? date('d/m/Y', strtotime($complaint['start'])) : '',
            'site'          => $empresaGeral['url'] ?? '',
            'telefone'      => $empresaCnpj['phones'][0]['number'] ?? ''
        );

        $status = [
            'GREAT' => 'ÓTIMO',
            'GOOD' => 'BOM',
            'REGULAR' => 'REGULAR',
            'BAD' => 'RUIM',
            'NOT_RECOMMENDED' => 'NÃO RECOMENDADO',
            'NO_INDEX' => 'SEM ÍNDICE',
            'UNDER_REVIEW' => 'SOB REVISÃO'
        ];
        if (isset($status[$empresaGeral['status']])) {
            $classificacao = $status[$empresaGeral['status']];
            if (!empty($empresaGeral['finalScore']) && $empresaGeral['finalScore'] != '--') {
                $classificacao .= ' - ' . $empresaGeral['finalScore'];
            }
        } else {
            $classificacao = '';
        }
        $result['reputacao'] = array(
            'classificacao' => $classificacao,
            'atendidas'     => $complaint['answeredPercentual'] ? $complaint['answeredPercentual'] . '%' : '',
            'solucao'       => $complaint['solvedPercentual'] ? $complaint['solvedPercentual'] . '%' : '',
            'voltaria'      => $complaint['dealAgainPercentual'] ? $complaint['dealAgainPercentual'] . '%' : ''
        );

        if (!empty($complaint['averageAnswerTime'])) {
            $tmr = $this->converterSegundos((int)$complaint['averageAnswerTime'], '%a dias e %h horas');
        } else {
            $tmr = '';
        }
        $result['avaliacao'] = array(
            'nota_consumidor'      => $complaint['consumerScore'] ?? '',
            'tempo_medio_resposta' => $tmr,
            'avaliacao'            => $complaint['totalEvaluated'] ?? '',
            'nao_atendidas'        => $complaint['totalNotAnswered'] ?? '',
            'atendidas'            => $complaint['totalAnswered'] ?? '',
            'total'                => $complaint['totalComplains'] ?? '',
        );
        $result['reclamacao'] = $descricao;

        return $result;
    }

    private function converterSegundos($segundos, $formato)
    {
        $dtF = new \DateTime('@0');
        $dtT = new \DateTime("@$segundos");
        return $dtF->diff($dtT)->format($formato);
    }
}
