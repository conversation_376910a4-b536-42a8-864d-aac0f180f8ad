<?php

namespace App\Crawler\CertidaoNegativaRR;

use Exception;
use App\Helper\Pdf;
use App\Helper\Document;
use App\Helper\Str;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Manager\S3\Bucket\StaticUplexisBucket;

/**
 * Crawler para certidao negativa RR
 *
 * @version 1.0.0
 *
 * <AUTHOR> da silva 10/2020
 *
 */

class CertidaoNegativaRR extends Spider
{
    private const POST_URL = 'certidao/pages/certidao/certidao-negativa';
    private const RR_URL = 'http://certidao.tjrr.jus.br/';
    private const BASE_URL = 'http://www.tjrr.jus.br/';
    private const MAIN_URL = 'certidao/pages/certidao/certidao-negativa.xhtml';
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const CERTIDAO_S3_PATH = 'captura/certidao_negativa_rn/';
    private $tipo = '';

    protected function start()
    {
        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::CERTIDAO_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;
        $html = $this->getResponse(self::RR_URL . self::POST_URL);
        $this->captcha = $this->resolveCaptcha($html);
        $pdf = $this->getResult($html);
        $pdfLink = $this->savePdf($pdf);
        $data = [
            'pdf' => $pdfLink
        ];
        return $data;
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if (Document::validarCpf($this->param['documento'])) {
            $this->param['documento'] = Document::formatCpf($this->param['documento']);
            $this->tipo = 0;
        } elseif (Document::validarCnpj($this->param['documento'])) {
            $this->param['documento'] = Document::formatCnpj($this->param['documento']);
            $this->tipo = 1;
        } else {
            throw new Exception('Documento inválido', 3);
        }
    }

    private function resolveCaptcha($html)
    {
        preg_match('/sitekey:\"(.*?)\",/m', $html, $token);

        if (!empty($token[1])) {
            return $this->solveReCaptcha($token[1], self::RR_URL . self::POST_URL, 3, 1);
        }
        throw new Exception("Erro ao localizar dados do captcha na página.", 3);
    }

    private function getResult($html)
    {
        if ($this->tipo == 0) {
            $documento = 'cpf';
        } else {
            $documento = 'cnpj';
        }
        $j_idtdPattern = '/\"Captcha\",\"widget_(.*?)\"/m';
        $viewStatePattern = '/ name="javax.faces.ViewState" .*ViewState:1\" value="(.*?)\"/m';
        preg_match($viewStatePattern, $html, $viewState);
        preg_match('/\"Captcha\",\"widget_(.*?)\"/m', $html, $id);
        preg_match($j_idtdPattern, $html, $j_dtid);

        if ($documento == 'cnpj') {
            $this->postCnpj($viewState[1]);
        }

        $params = [
            'form' => 'form',
            'tipo-certidao' => 3,
            'tipo-pessoa' => $this->tipo,
            'nome' => $this->param['nome'],
            $documento => $this->param['documento'],
            'g-recaptcha-response' => $this->captcha,
            'javax.faces.ViewState' => $viewState[1],
            'j_idt59' => 'j_idt59'
        ];

        $pdf = $this->getResponse(self::RR_URL . self::POST_URL, 'POST', $params);

        return $pdf;
    }

    private function savePdf($pdf)
    {
        file_put_contents($this->certificateLocalPath, $pdf);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        return $this->certificateUrl;
    }

    private function postCnpj($viewState)
    {
        $params = [
            'form' => 'form',
            'tipo-certidao' => 3,
            'tipo-pessoa' => 1,
            'nome' => '',
            'cpf' => '',
            'g-recaptcha-response' => '',
            'javax.faces.ViewState' => $viewState,
            'javax.faces.source' => 'tipo-pessoa',
            'javax.faces.partial.event' => 'change',
            'javax.faces.partial.execute' => 'tipo-pessoa',
            'javax.faces.partial.render' => 'pessoa-juridica-escolhida pessoa-fisica-escolhida',
            'CLIENT_BEHAVIOR_RENDERING_MODE' => 'OBSTRUSIVE',
            'javax.faces.behavior.event' => 'valueChange',
            'javax.faces.partial.ajax' => 'true'
        ];

        $response = $this->getResponse(self::RR_URL . self::POST_URL, 'POST', $params);
    }
}
