<?php

namespace App\Crawler\JucespFichaSimplificada;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Helper\Util;
use Exception;

class JucespFichaSimplificada extends Spider
{
    private const URL = 'https://www.jucesponline.sp.gov.br/';
    private const URL_LAMBDA = LAMBDA_LUMEN . LAMBDA . '-512';

    private $uniqid;
    private $criterio;
    private $eventVars = [];
    private $data = [];
    private $nire;
    private $login;
    private $senha;
    private $pdfPath = '/tmp/pdf_{uniqid}.pdf';
    private $done = false;

    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->auth['usuario']) || empty($this->auth['senha'])) {
            throw new Exception('Usuario ou senha inválidos', 6);
        }

        if (empty($this->param['criterio'])) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }

        $cnpj = preg_replace("/\D+/", '', $this->param['criterio']);

        if (!empty($cnpj)) {
            if (!Document::validarCnpj($cnpj)) {
                throw new Exception('Critério inválido', 1);
            }

            $filter = $this->getReceitaFilter($cnpj);
            $response = (object) $this->makeRequest($filter);

            if ($response->data->uf === 'SP') {
                $this->criterio = $response->data->nome_empresarial;
            }
        } else {
            $this->criterio = $this->param['criterio'];
        }
        $this->login = $this->auth['usuario'];
        $this->senha = $this->auth['senha'];
        $this->debug = false;
    }

    protected function start()
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $this->setProxy();

        $this->searchByRazaoSocial();

        if (!is_array($this->data)) {
            throw new Exception("Não foi possível recuperar os dados", 3);
        }

        if (empty($this->data)) {
            throw new Exception("Sua pesquisa não encontrou nenhuma empresa correspondente.", 2);
        }

        return $this->data;
    }

    /**
     * Faz a consulta em outra fonte no lambda
     */
    private function makeRequest($filter)
    {
        $ch = curl_init(self::URL_LAMBDA);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $filter);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'Content-Length: ' . strlen($filter),
        ));
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 0);
        curl_setopt($ch, CURLOPT_TIMEOUT, 540);

        return json_decode(curl_exec($ch));
    }

    /**
     * Monta o request do lambda
     */
    private function getReceitaFilter(string $cnpj)
    {
        return json_encode([
            'retry' => '2',
            'source' => 'ReceitaFederalPjSite',
            'param' => [
                'documento' => $cnpj
            ]
        ]);
    }

    /**
     *  Inicia o crawler
     *
     *  <AUTHOR>
     *  @version 1.0.0
     *  @version 1.0.1 - Ricardo Vidal 17/08/2020
     *                  Abre a página inicial
     *
     */
    protected function searchByRazaoSocial()
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $this->form();

        $params = [
            'ctl00$ajaxMaster' => 'ctl00$cphContent$ajaxForm|ctl00$cphContent$frmBuscaSimples$btPesquisar',
            '__LASTFOCUS' => $this->eventVars['__LASTFOCUS'],
            '__EVENTTARGET' => $this->eventVars['__EVENTTARGET'],
            '__EVENTARGUMENT' => $this->eventVars['__EVENTARGUMENT'],
            '__VIEWSTATE' => $this->eventVars['__VIEWSTATE'],
            '__EVENTVALIDATION' => $this->eventVars['__EVENTVALIDATION'],
            '__VIEWSTATEGENERATOR' => $this->eventVars['__VIEWSTATEGENERATOR'],
            'ctl00$cphContent$frmBuscaSimples$txtPalavraChave' => $this->criterio,
            'ctl00$cphContent$frmBuscaSimples$twePalavraChave_ClientState' => '',
            'ctl00$cphContent$frmBuscaSimples$btPesquisar' => 'Buscar',
            '__ASYNCPOST' => true
        ];

        $response = $this->getResponse(self::URL . 'Default.aspx', 'POST', $params, [
            'Referer' => self::URL . 'Default.aspx'
        ]);

        $this->ondeEstou($response);
    }

    /**
     *  Verifica em qual página está
     *
     *  <AUTHOR>
     *  @version 1.0.0
     *  @version 1.0.1 - Ricardo Vidal 17/08/2020
     *                 Adicionado verificação se a busca foi concluída
     *
     *  @param string $content Conteúdo do site
     *
     */
    private function ondeEstou($content)
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        if ($this->done) {
            return;
        }

        $this->setEventVars($content);
        if (!$this->isLogged($content)) {
            $this->stepLogin();
        }
        if (preg_match('#src="(DocumentoTicket\.aspx\?ticket=.*?)">#is', $content)) {
            $this->stepGetFicha($content);
            return;
        }
        if (preg_match('#src="CaptchaImage\.aspx\?guid=(.*?)"#is', $content)) {
            $this->stepCaptcha($content);
            return;
        }
        if (preg_match('#<a[^>]*href="javascript:__doPostBack\((.*?),(.*?)\)">\s*\d+\s*</a>#is', $content)) {
            $this->stepLista($content);
            return;
        }
        if (
            preg_match(
                '#</strong>\s*-\s*n.*?o\s*encontrou\s*nenhuma\s*empresa\s*correspondente\.\s*</p>#is',
                $content
            )
        ) {
            $this->done = true;
            return;
        } elseif (
            preg_match(
                '#id="ctl00_cphContent_gdvResultadoBusca_ifbGridView_lblDocCount">(.*?)<#is',
                $content,
                $total
            )
        ) {
            if ($total[1] == 0) {
                $this->done = true;
                return;
            }
        } elseif (preg_match('#ctl00_cphContent_frmPreVisualiza_btnEmitir#is', $content)) {
            $this->stepSelecionaFicha($content);
            return;
        } elseif (
            preg_match('#<span\s*id="ctl00_cphContent_pnlErro_lblMensagem">\s*([^<]+)\s*</span>#is', $content, $matches)
        ) {
            throw new Exception($matches[1], 3);
        } elseif (preg_match('#PDF-1.4#is', $content, $matches)) {
            $this->stepGetFicha($content, true);
            return;
        }

        throw new Exception("Nao foi possivel determinar a pagina atual.", 4);
    }

    /**
     *  Abre página com o formulário para login
     *
     *  <AUTHOR>
     *  @version 1.0.0
     *
     */
    private function form()
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }
        $response = $this->getResponse(self::URL . '/Default.aspx', 'GET');

        $this->setEventVars($response);
        $this->stepLogin();
    }

    /**
     *  Set de variáveis para requisições
     *
     *  <AUTHOR>
     *  @version 1.0.0
     *
     *  @param string $content Conteúdo do site
     *
     */
    private function setEventVars($content)
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $idVars = [
            '__LASTFOCUS',
            '__EVENTTARGET',
            '__EVENTARGUMENT',
            '__VIEWSTATE',
            '__EVENTVALIDATION',
            '__PREVIOUSPAGE',
            '__VIEWSTATEGENERATOR'
        ];
        foreach ($idVars as $idVar) {
            $val = '';
            if (preg_match('#id="' . $idVar . '"\s*value="(.*?)"#is', $content, $matches)) {
                $val = $matches[1];
            }

            $this->eventVars[$idVar] = $val;
        }
    }

    /**
     *  Limpa texto dos sócios
     *
     *  <AUTHOR>
     *  @version 1.0.0
     *
     *  @param string $texto Informações dos sócios
     *
     *  @return array
     */
    private function limpaTextoSocios($texto)
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $aTemp = explode("LTIMOS ARQUIVAMENTOS", $texto);
        $aTemp = explode("DIRETORIA", $aTemp[0]);
        preg_match_all("#(.*?)((\d+){2,}\/(\d+){2,}\/(\d+){4,}\.|\. )#is", $aTemp[1], $matches);
        return $matches[0];
    }

    /**
     *  Faz login no site
     *
     *  <AUTHOR>
     *  @version 1.0.0
     *
     */
    private function stepLogin()
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $params = [
            'ctl00$ajaxMaster' => 'ctl00$ajaxLogin|ctl00$frmLogin$btLogin',
            '__LASTFOCUS' => $this->eventVars['__LASTFOCUS'],
            '__EVENTTARGET' => $this->eventVars['__EVENTTARGET'],
            '__EVENTARGUMENT' => $this->eventVars['__EVENTARGUMENT'],
            '__VIEWSTATE' => $this->eventVars['__VIEWSTATE'],
            '__EVENTVALIDATION' => $this->eventVars['__EVENTVALIDATION'],
            '__VIEWSTATEGENERATOR' => $this->eventVars['__VIEWSTATEGENERATOR'],
            'ctl00$frmLogin$txtLogin' => $this->login,
            'ctl00$frmLogin$tweLogin_ClientState' => '',
            'ctl00$frmLogin$txtSenha' => $this->senha,
            'ctl00$frmLogin$tweSenha_ClientState' => '',
            'ctl00$cphContent$frmBuscaSimples$txtPalavraChave' => '',
            'ctl00$cphContent$frmBuscaSimples$twePalavraChave_ClientState' => '',
            'ctl00$frmLogin$btLogin' => 'OK',
            '__ASYNCPOST' => true
        ];

        $response = $this->getResponse(self::URL . '/Default.aspx', 'POST', $params, [
            'Referer' => self::URL . '/Default.aspx'
        ]);

        $this->setEventVars($response);
        if (preg_match_all("/Usu.*?rio n.*?o cadastrado na Nota Fiscal Paulista/ui", $response, $result)) {
            throw new Exception(
                "Erro de autenticação, o CPF informado não está cadastrado na nota fiscal paulista!",
                6
            );
        }
        if (
            preg_match_all(
                "/N.*?o foi poss.*?vel autenticar o usu.*?rio: CPF ou Senha inv.*?lidos/ui",
                $response,
                $result
            )
        ) {
            throw new Exception(
                "Erro de autenticação, verifique se seu login ou senha para esta fonte são válidos!",
                6
            );
        }
    }

    /**
     *  Verifica se está logado
     *
     *  <AUTHOR>
     *  @version 1.0.0
     *  @version 1.0.1 - Ricardo Vidal - 17/08/2020
     *                  Adicionado retorno em boolean
     *
     *  @param string $content Conteúdo do site
     *
     *  @return boolean
     */
    private function isLogged($content)
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        if (preg_match('#<span\s*id="ctl00_frmLogin_lblUsuario"\s*>\s*([^<]+)\s*</span>#', $content)) {
            return true;
        }
        return false;
    }

    /**
     *  Abre a ficha da empresa e chama parse de Pdf para Texto
     *
     *  <AUTHOR>
     *  @version 1.0.0
     *
     *  @param string $content Conteúdo do site
     *  @param boolean $pdf
     *
     */
    private function stepGetFicha($content, $pdf = false)
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }
        if (!$pdf) {
            if (!preg_match('#src="(DocumentoTicket\.aspx\?ticket=.*?)">#is', $content, $matches)) {
                throw new Exception("Não foi possível capturar a ficha ", 3);
            }
            $contentPdf = $this->getResponse(self::URL . $matches[1], 'GET');
        } else {
            $contentPdf = $content;
        }

        $content = $this->parsePdfToText($contentPdf);

        $this->parseData($content);
    }

    /**
     *  Verifica se a pesquisa retornou uma lista. Se não, abre direto
     *
     *  <AUTHOR>
     *  @version 1.0.0
     *
     *  @param string $content Conteúdo do site
     *
     */
    private function stepLista($content)
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }
        $matches = [];
        if (
            !preg_match_all(
                '#<a.*?ctl00_cphContent_gdvResultadoBusca_gdvContent.*?' .
                    'href="javascript:__doPostBack\((.*?),(.*?)\)">\s*(\d+)\s*</a>.*?RazaoSocial">(.*?)</span>#is',
                $content,
                $matches
            )
        ) {
            throw new Exception("Não foi possível capturar a lista", 3);
        }

        for ($i = 0; $i < count($matches[0]); $i++) {
            $this->nire = trim($matches[3][$i]);

            if ($this->criterio != $matches[4][$i]) {
                continue;
            }

            $params = [
                'ctl00$ajaxMaster' => 'ctl00$cphContent$ajaxGrid|' . str_replace('&#39;', '', $matches[1][$i]),
                '__LASTFOCUS' => $this->eventVars['__LASTFOCUS'],
                '__EVENTTARGET' => str_replace('&#39;', '', $matches[1][$i]),
                '__EVENTARGUMENT' => str_replace('&#39;', '', $matches[2][$i]),
                '__VIEWSTATE' => $this->eventVars['__VIEWSTATE'],
                '__EVENTVALIDATION' => $this->eventVars['__EVENTVALIDATION'],
                '__VIEWSTATEGENERATOR' => $this->eventVars['__VIEWSTATEGENERATOR'],
                'ctl00$cphContent$frmBuscaSimples$txtPalavraChave' => $this->criterio,
                'ctl00$cphContent$frmBuscaSimples$twePalavraChave_ClientState' => '',
                '__PREVIOUSPAGE' => $this->eventVars['__PREVIOUSPAGE'],
                '__ASYNCPOST' => true
            ];
            $response = $this->getResponse(self::URL . 'ResultadoBusca.aspx', 'POST', $params, [
                'Referer' => self::URL . 'ResultadoBusca.aspx'
            ]);
            // if ($i == 0) {
            //     $params = [
            //         'ctl00$ajaxMaster' => '',
            //         '__LASTFOCUS' => $this->eventVars['__LASTFOCUS'],
            //         '__EVENTTARGET' => '',
            //         '__EVENTARGUMENT' => '',
            //         '__VIEWSTATE' => $this->eventVars['__VIEWSTATE'],
            //         '__EVENTVALIDATION' => $this->eventVars['__EVENTVALIDATION'],
            //         'ctl00$cphContent$frmBuscaSimples$txtPalavraChave' => $this->criterio,
            //         'ctl00$cphContent$frmBuscaSimples$twePalavraChave_ClientState' => '',
            //         '__PREVIOUSPAGE' => $this->eventVars['__PREVIOUSPAGE'],
            //         '' => '',
            //         'ctl00$ajaxMaster' => 'ctl00$cphContent$ajaxGrid|' . str_replace('&#39;', '', $matches[1][$i]),
            //         '__EVENTTARGET' => str_replace('&#39;', '', $matches[1][$i]),
            //         '__EVENTARGUMENT' => str_replace('&#39;', '', $matches[2][$i])
            //     ];
            //     $response = $this->getResponse(self::URL . 'ResultadoBusca.aspx?IDProduto=1', 'POST', $params, [
            //         'Referer' => self::URL . 'ResultadoBusca.aspx?IDProduto=1'
            //     ]);
            // } else {
            //     $response = $this->getResponse(self::URL . 'Pre_Visualiza.aspx?nire=', 'POST');
            // }

            $this->ondeEstou($response);
        }
        $this->done = true;
    }

    /**
     *  Seleciona a ficha
     *
     *  <AUTHOR>
     *  @version 1.0.0
     *
     *  @param string $content Conteúdo do site
     *
     */
    private function stepSelecionaFicha($content)
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $params = [
            '__EVENTTARGET' => '',
            '__EVENTARGUMENT' => '',
            '__LASTFOCUS' => '',
            '__VIEWSTATE' => $this->eventVars['__VIEWSTATE'],
            '__PREVIOUSPAGE' => $this->eventVars['__PREVIOUSPAGE'],
            '__EVENTVALIDATION' => $this->eventVars['__EVENTVALIDATION'],
            'ctl00$cphContent$frmPreVisualiza$rblTipoDocumento' => '1',
            'ctl00$cphContent$frmPreVisualiza$btnEmitir' => 'OK'
        ];

        $response = $this->getResponse(self::URL . 'SelecionaProduto.aspx', 'POST', $params, [
            'Referer' => self::URL . 'Pre_Visualiza.aspx?nire=' . $this->nire . '&idproduto='
        ]);

        $this->ondeEstou($response);
    }

    /**
     *  Método responsável por quebrar o captcha e prosseguir com a consulta
     *
     *  <AUTHOR>
     *  @version 1.0.0
     *
     *  @param string $content Conteúdo do site
     *
     */
    public function stepCaptcha($content)
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        if (!preg_match('#src="CaptchaImage\.aspx\?guid=(.*?)"#is', $content, $matches)) {
            throw new Exception("Erro ao capturar captcha", 100);
        }

        $captchaUrl = self::URL . '/CaptchaImage.aspx?guid=' . $matches[1];
        $this->getImageAndBreakCaptcha($captchaUrl);

        if (preg_match("#ResultadoBusca#", $content, $matches)) {
            $params = [
                    'ctl00$ajaxMaster' => 'ctl00$cphContent$ajaxGrid|ctl00$cphContent$gdvResultadoBusca$btEntrar',
                    '__LASTFOCUS' => '',
                    '__EVENTTARGET' => '',
                    '__EVENTARGUMENT' => '',
                    '__VIEWSTATE' => $this->eventVars['__VIEWSTATE'],
                    '__EVENTVALIDATION' => $this->eventVars['__EVENTVALIDATION'],
                    '__VIEWSTATEGENERATOR' => $this->eventVars['__VIEWSTATEGENERATOR'],
                    'ctl00$cphContent$frmBuscaSimples$txtPalavraChave' => $this->criterio,
                    'ctl00$cphContent$frmBuscaSimples$twePalavraChave_ClientState' => '',
                    'ctl00$cphContent$gdvResultadoBusca$CaptchaControl1' => $this->captcha,
                    'ctl00$cphContent$gdvResultadoBusca$btEntrar' => 'Continuar',
                    '__ASYNCPOST' => true
            ];
            $url = self::URL . 'ResultadoBusca.aspx';
        } elseif (preg_match("#Pre_Visualiza#", $content, $matches)) {
            $params = [
                    'ctl00$ajaxMaster' => 'ctl00$cphContent$ajaxForm|ctl00$cphContent$frmPreVisualiza$btEntrar',
                    '__EVENTTARGET' => '',
                    '__EVENTARGUMENT' => '',
                    '__VIEWSTATE' => $this->eventVars['__VIEWSTATE'],
                    '__EVENTVALIDATION' => $this->eventVars['__EVENTVALIDATION'],
                    '__VIEWSTATEGENERATOR' => $this->eventVars['__VIEWSTATEGENERATOR'],
                    'ctl00$cphContent$frmPreVisualiza$CaptchaControl1' => $this->captcha,
                    'ctl00$cphContent$frmPreVisualiza$btEntrar' => 'Continuar',
                    '__ASYNCPOST' => true
            ];
            $url = self::URL . 'Pre_Visualiza.aspx?nire=' . $this->nire . '&idproduto=';
        } else {
            throw new Exception("Está solicitando captcha em local inesperado", 100);
        }

        if ($this->debug) {
            print("url action: $url\n");
        };

        $response = $this->getResponse($url, 'POST', $params, [
                'Referer' => $url
            ]);

        $this->ondeEstou($response);
    }

    /**
     *  Parse do resultado
     *
     *  <AUTHOR>
     *  @version 1.0.0
     *
     *  @param string $content Conteúdo do site
     *
     */
    public function parseData($content)
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }
        $content = preg_replace("#\s*Documento\s*Gratuito.*?#is", '', $content);

        $patterns = [
            'rasaoSocial' => ['#DENOMIN.*ATUAL:\n(.*?)\s*\n#is', null],
            'tipo' => ['#TIPO:\s*(.*?)\n#is', null],
            'nireMatriz' => ['#NIRE\s*MATRIZ.*?\n\s*\n\s*(\d+)\s*.*?\n#is', null],
            'dataConstituicao' => ['#NIRE\s*MATRIZ.*?\n\s*\n\s*\d+\s*(\d{2}/\d{2}/\d{4}).*?\n#is', null],
            'dataEmissao' => ['#NIRE\s*MATRIZ.*?\n\s*\n\s*\d+\s*\d{2}/\d{2}/\d{4}\s*(.*?)\s*\n#is', null],
            'dataInicio' => ['#IN.*?CIO\s*DE\s*ATIVIDADE.*?\n\s*\n\s*(\d{2}/\d{2}/\d{4})#is', null],
            'cnpj' => ['#IN.*?CIO\s*DE\s*ATIVIDADE.*?\n\s*\n\s*.*?\s*(\d{2}.\d{3}.\d{3}/\d{4}-\d{2})#is', null],
            'ie' => [
                '#IN.*?CIO\s*DE\s*ATIVIDADE.*?\n\s*\n\s*.*?\s*\d{2}.\d{3}.\d{3}/\d{4}-\d{2}\s*(.*?)\s*CAPITAL#is',
                 null
                ],
            'captalSocial' => ['#CAPITAL\s*\n\s*\n\s*(.*?)\s*\n#is', null],
            'logradouro' => ['#LOGRADOURO:\s(.*?)\s{2,}#is', null],
            'numero' => ['#N.*?MERO:\s(.*?)\s{2,}#is', null],
            'bairro' => ['#BAIRRO:\s(.*?)\s{2,}#is', null],
            'complemento' => ['#COMPLEMENTO:\s(.*?)\s{2,}#is', null],
            'municipio' => ['#MUNIC.*?PIO:\s(.*?)\s{2,}#is', null],
            'cep' => ['#CEP:\s(.*?)\s{2,}#is', null],
            'uf' => ['#UF:\s(.*?)\s{2,}#is', null],
            'objetoSocial' => ['#OBJETO\sSOCIAL\s(.*?)\s*TITULAR#is', null],
        ];

        $data = Str::encoding(Util::parseDados($patterns, $content));

        if (
            preg_match(
                "#TITULAR\s/\sS.*?CIOS\s/\sDIRETORIA\s*\n(.*?)(?:5\s.*?LTIMOS\sARQUIVAMENTOS
                |FIM\s*DAS\s*INFORMA.{1,16}ES\s*PARA\s*NIRE)#is",
                $content,
                $matches
            )
        ) {
            $array_socios = $this->limpaTextoSocios($matches[0]);
            $aSocio = [];

            foreach ($array_socios as $ch => $socio) {
                if (trim($socio) == '') {
                    continue;
                }
                $aSocio[] = preg_replace("#\s+#", ' ', trim($socio));
            }
            $data['socios'] = [$aSocio];
        }

        $data['situacao'] = 'ATIVA';

        // if (preg_match_all('#\n\n\s*EMPRESA\n\n(\s*' . $data['situacao'] . '\n\n)?(.*?)\n#is', $content, $matches)) {
        //     $value = preg_replace("#\s+#", ' ', $matches[2]);
        //     $data['rasaoSocial'] = $value[0];
        // }
        if (empty($data['rasaoSocial'])) {
            $data['rasaoSocial'] = $this->criterio;
        }

        if (
            preg_match_all(
                '#\n\n\s*EMPRESA\n\n\s*(DISSOLVIDA|INCORPORADA|TRANSFORMADA|CINDIDA)\n\n#is',
                $content,
                $matches
            )
        ) {
            $value = preg_replace("#\s+#", ' ', $matches[1]);
            $data['situacao'] = trim($value[0]);
        }

        if (preg_match("#5\s*.*?LTIMOS\s*ARQUIVAMENTOS\s*\n(.*?)FIM\s*DAS\s*INFORMA#is", $content, $matches)) {
            $aDoc = explode('NUM.DOC:', $matches[1]);

            foreach ($aDoc as $doc) {
                $pattern = "#^\s*(.*?)\s*SESS.*?O:\s*(.*?)\s*\n(.*?)$#is";
                if (!preg_match($pattern, $doc, $matches)) {
                    continue;
                }

                $numeroDocumento = $matches[1];
                $sessao = $matches[2];
                $aTemp = explode("\n\n", $matches[3]);
                $aAlteracoes = [];

                foreach ($aTemp as $alt) {
                    if (trim($alt) == '') {
                        continue;
                    }
                    $aAlteracoes[] = preg_replace("#\s+#", ' ', trim($alt));
                }

                $data['arquivamentos'][] = [
                    'numeroDocumento' => $numeroDocumento,
                    'sessao' => $sessao,
                    'atracoes' => $aAlteracoes
                ];
            }
        }

        $this->data[] = $data;
        $this->done = true;
    }

    /**
     *  Define caminho do Pdf
     *
     *  <AUTHOR> Vidal
     *  @version 1.0.0
     *
     */
    private function setPdfPath()
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $this->uniqid = md5(uniqid(rand(), true));
        $this->pdfPath = str_replace('{uniqid}', $this->uniqid, $this->pdfPath);
    }

    /**
     *  Parse do pdf para texto
     *
     *  <AUTHOR> Vidal
     *  @version 1.0.0
     *
     *  @param string $content - Conteúdo do Pdf
     *
     *  @return string - Conteúdo da ficha em texto
     *
     */
    private function parsePdfToText($content)
    {
        if ($this->debug) {
            echo __METHOD__ . PHP_EOL;
        }

        $this->setPdfPath();
        file_put_contents($this->pdfPath, $content);

        return (new Pdf())->getTextFromPdf($this->pdfPath, [
            'layout'
        ]);
    }
}
