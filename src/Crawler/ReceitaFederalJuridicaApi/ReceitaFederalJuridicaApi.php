<?php

namespace App\Crawler\ReceitaFederalJuridicaApi;

use App\Crawler\Spider;
use Exception;

class ReceitaFederalJuridicaApi extends Spider
{
    protected function validateAndSetCrawlerAttributes()
    {
        return false;
    }

    protected function start()
    {
        $this->setProxy();
        $this->setCertificate(dirname(__FILE__) . '/cert.crt', 0);
        $retries = 3;

        do {
            try {
                $token = $this->getApiToken();
                $url = "https://movel02.receita.fazenda.gov.br/servicos-rfb/v2/ConsultaCNPJ/auth";

                $headers = [
                    'Host: movel02.receita.fazenda.gov.br:443',
                    'aplicativo: Pessoa Jur\u00eddica',
                    'Accept: application/json',
                    'versao_app: 2.0',
                    'plataforma: iOS',
                    'Accept-Language: pt-BR;q=1',
                    'Accept-encoding: br, gzip, deflate',
                    'token: ' . $token['token'],
                    'charset: utf-8',
                    'versao: 12.1',
                    'dispositivo: iPhone',
                    'User-Agent: CNPJ/16 (iPhone; iOS 12.1; Scale/3.00) ',
                    'Connection: keep-alive',
                    'Content-Length: 249',
                ];

                $param = [
                    'cnpj' => $this->param['cnpj'],
                    'tokenAuth' => $token['tokenAuth'],
                ];

                $response = $this->getResponse($url, 'POST', $param, $headers);

                $response = json_decode($response, true);

                if ($response['codigoRetorno'] === '00') {
                    break;
                }

                $retries--;

                if ($response['codigoRetorno'] != '00' && $retries === 0) {
                    throw new Exception($response['mensagemRetorno'], 1);
                }
            } catch (Exception $e) {
                $retries--;

                if ($retries === 0) {
                    throw $e;
                }
            }
        } while ($retries > 0);

        if ($response['codigoRetorno'] != '00') {
            throw new Exception($response['mensagemRetorno'], 1);
        }

        return $response;
    }

    /**
     * Gera o token da Receita API
     *
     * @return string
     */
    private function getApiToken()
    {
        // Consulta
        $url = "https://movel02.receita.fazenda.gov.br/servicos-rfb/v2/Util/obterToken";

        $token = $this->generateHash(md5(mt_rand()), '', str_replace('.', 'F', uniqid(mt_rand(), true)));

        $params = [
            'aplicativo' => 'cnpj',
            'idNuvem' => $token,
            'sandbox' => false,
            'so' => 'ios',
        ];

        $this->getResponse($url, 'POST', json_encode($params));

        $code = (int) $this->getLastStatusResponse();

        if ($code !== 200 && $code !== 204) {
            throw new Exception("Error Processing Request", 1);
        }

        $tokenAuth = $this->recoveryMessages($token);

        return [
            'token' => $token,
            'tokenAuth' => $tokenAuth,
        ];
    }

    private function recoveryMessages(string $idDispositivo)
    {
        $url = 'https://movel02.receita.fazenda.gov.br/servicos-rfb/v2/EnviarMensagemNuvem/recuperarMensagens';

        $headers = [
            'host' => 'movel02.receita.fazenda.gov.br:443',
            'aplicativo' => 'Pessoa Jur\u00eddica',
            'Accept' => '*/*',
            'versao_app' => '2.0',
            'plataforma' => 'iOS',
            'Accept-Language' => 'pt-br',
            'Accept-Encoding' => 'br, gzip, deflate',
            'token' => $this->generateToken(),
            'charset' => 'utf-8',
            'versao' => '12.1',
            'dispositivo' => 'iPhone',
            'Content-Length' => '78',
            'User-Agent' => 'CNPJ/16 CFNetwork/975.0.3 Darwin/18.2.0',
            'Connection' => 'keep-alive',
        ];

        $params = [
            'idDispositivo' => $idDispositivo,
        ];

        $response = $this->getResponse($url, 'POST', $params, $headers);

        $response = json_decode($response, true);

        if (empty($response[0]['mensagemEnviada'])) {
            throw new Exception("Error Processing Request", 1);
        }

        $messageSender = json_decode(data_get($response, '0.mensagemEnviada'), true);

        return data_get($messageSender, 'aps.tokenJWT');
    }

    private function generateToken()
    {
        $decodedKey = "Sup3RbP4ssCr1t0grPhABr4sil";
        $data = md5(mt_rand()) . '' . str_replace('.', 'F', uniqid(mt_rand(), true));
        $hmac = hash_hmac("sha1", $data, $decodedKey, true);
        $hash = bin2hex($hmac);
        return $hash;
    }

    /**
     * Gera o hash do app da Receita
     *
     * @param string $cnpj
     * @param string $captchaToken
     * @param string $captchaAnswer
     *
     * @return string
     *
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function generateHash()
    {
        $decodedKey = "Sup3RbP4ssCr1t0grPhABr4sil";
        $data = $this->param['cnpj'] . time();
        $hmac = hash_hmac("sha256", $data, $decodedKey, true);
        $hash = bin2hex($hmac);
        return $hash;
    }
}
