<?php

namespace App\Crawler\CertidaoCriminalJudicialNegativa;

use Exception;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\ColunaVertebralManager;

class CertidaoCriminalJudicialNegativa extends Spider
{
    private $formURI = 'https://www.tjrs.jus.br/proc/alvara/gera_alvara.php';

    private $certificadoURI = 'https://www.tjrs.jus.br/proc/alvara/alvara.php?identificador={numeroConfirmacao}&t=2';

    private $mandatoryFields = [
        'nome',
        'sexo',
        'cpf',
        'nomeMae',
        'dataNascimento',
        'rg',
        'orgaoExpedidor',
        'ufRg',
        'endereco'
    ];

    /**
     * Faz o processamento da fonte
     *
     * @return array
     *
     * <AUTHOR> <<EMAIL>>
     */
    protected function start(): array
    {
        date_default_timezone_set('America/Sao_Paulo');
        $uniqid = md5(uniqid(rand(), true));
        $this->certificateName = 'certificate_' . $uniqid . '.pdf';
        $this->certificatePath = "/tmp/{$this->certificateName}";
        $this->certificatePathS3 = S3_STATIC_URL .
            "captura/certidao_criminal_judicial_negativa/{$this->certificateName}";
        $elasticSearch = new ColunaVertebralManager();

        $criteria = (object) $elasticSearch->getSpinePf($this->param['cpf']);

        $this->setProxy();
        $params = [
            'tipoDocumento' => 2,
            'Municipio' => '',
            'tipoPessoa' => 'F',
            'nome' => $criteria->nome, // Obrigatorio
            'sexo' => $criteria->sexo, // Obrigatorio, M ou F
            'cpf' => $this->param['cpf'], // Obrigatorio
            'cnpj' => '',
            'nomeMae' => $criteria->mae, // Obrigatorio
            'nomePai' => $this->param['nomePai'],
            'dataNascimento' => date('d/m/Y', strtotime($criteria->data_nascimento)), // Obrigatorio
            'nacionalidade' => 1,
            'estadoCivil' => 1,
            'rg' => $this->param['rg'], // Obrigatorio
            'orgaoExpedidor' => $this->param['orgaoExpedidor'], // Obrigatorio
            'ufRg' => $this->param['ufRg'], // Obrigatorio
            'endereco' => $this->param['endereco'], // Obrigatorio
        ];

        $retry = 0;
        do {
            $response = $this->getResponse($this->formURI, 'POST', $params);
            $response = utf8_encode($response);

            if ($response != 'Proxy Error') {
                break;
            }

            $retry++;

            if ($retry >= 3) {
                throw new Exception('Não foi possível gerar a Certidão! - Proxy', 3);
            }
        } while ($retry < 3);

        $match = $this->checkErrorsAndGetResult($response);

        $pdf = $this->getResponse(str_replace("{numeroConfirmacao}", "{$match[1]}", $this->certificadoURI));

        file_put_contents($this->certificatePath, $pdf);

        $parse = $this->parseTextFromPdf();

        (new S3(new StaticUplexisBucket()))->save(
            'captura/certidao_criminal_judicial_negativa/' . $this->certificateName,
            $this->certificatePath
        );

        return [
            'cpf' => $this->param['cpf'],
            'nome' => $criteria->nome,
            'texto' => $parse['texto'],
            'numero' => $match[1],
            'data_consulta' => date('d/m/Y'),
            'hora_consulta' => $parse['hora'],
            'pdf' => $this->certificatePathS3,
        ];
    }

    /**
     * Valida se existem erros
     *
     * @param string $response
     *
     * @return array
     */
    private function checkErrorsAndGetResult(string $response)
    {
        $checkIdade = '/Este\sserviço\snão\semite\sAlvará\sou\sCertidão\sJudicial\spara\smenores\sde\sidade./';

        if (preg_match($checkIdade, $response)) {
            throw new Exception('Este serviço não emite Alvará ou Certidão Judicial para menores de idade.', 3);
        }

        $numeroConfirmacao = '/Número de confirmação:\s\<strong\>(.*?)\<\/strong\>\s\<br\>/';

        if (!preg_match($numeroConfirmacao, $response, $match)) {
            throw new Exception('Não foi possível gerar a Certidão!', 3);
        }

        return $match;
    }

    /**
     * Parseia o texto do Pdf do certificado
     *
     * @return array
     *
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function parseTextFromPdf(): array
    {
        $result = (new Pdf())->getTextFromPdf($this->certificatePath, [
            'layout'
        ]);

        $pattern = '/(À vista dos registros[\s\S]*interessada):(?:[\s\S]*às\s(.*))/i';

        if (!preg_match($pattern, $result, $match)) {
            throw new Exception('Não foi possível parsear a Certidão!', 3);
        }

        return [
            'texto' => $match[1],
            'hora' => $match[2],
        ];
    }

    /**
     * Validação dos paramêtros
     *
     * @return void
     * <AUTHOR> Hugo <<EMAIL>>
     */
    protected function validateAndSetCrawlerAttributes()
    {
        foreach ($this->mandatoryFields as $field) {
            if (empty($this->param[$field])) {
                throw new Exception("${field} é paramêtro obrigatório!", 1);
            }
        }

        $this->param['nomePai'] = !empty($this->param['nomePai']) ? $this->param['nomePai'] : '';
        $this->param['nome'] = Str::removerAcentos($this->param['nome']);
        $this->param['endereco'] = Str::removerAcentos($this->param['endereco']);
    }
}
