<?php

namespace App\Crawler\RendaEstimadaCrediShop;

use App\Crawler\Spider;
use App\Helper\Document;
use Exception;
use App\Helper\BilhetadorFornecedoresHelper;
use App\Crawler\CadastroPf\CadastroPfSpider;
use App\Crawler\SpinePfEmails\SpinePfEmailsSpider;
use App\Crawler\SpinePfEnderecos\SpinePfEnderecosSpider;
use App\Crawler\SpinePfTelefones\SpinePfTelefonesSpider;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Tightenco\Collect\Support\Arr;

class RendaEstimadaCrediShop extends Spider
{
    protected $bigDataUri = 'https://bigboost.bigdatacorp.com.br/peoplev2/';

    protected $bigDataDatasets = ['occupation_data', 'financial_data'];

    protected $bigDataApiKey = BIGDATACORP_TOKEN;

    public function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCpf($this->param['cpf']) || empty($this->param['cpf'])) {
            throw new Exception('Documento inválido');
        }

        $this->param['cpf'] = preg_replace('/\D/isu', '', $this->param['cpf']);
    }

    protected function start()
    {
        $document = $this->param['cpf'];
        $birthDate = $this->param['data_nascimento'] ?? false;

        $localData = (new CadastroPfSpider())->getSpinePf($document);
        $receitaFederal = $this->getReceitaFederalSite($document, $birthDate);

        // Chamada do BigData
        $externalData = $this->getExternalData($document);

        $this->insertBilhetador(implode(',', $this->bigDataDatasets), $externalData);

        return [
            'name' => $localData['nome'],
            'motherName' => $localData['mae'],
            'birthDate' => $this->humanizeBirthDate($localData['data_nascimento'] ?? $birthDate ?? null),
            'age' => $this->getAgeFromBirthDate($localData['data_nascimento'] ?? $birthDate ?? null),
            'gender' => $localData['sexo'],
            'hasObitInformation' => $receitaFederal['obito'] ? true : false,
            'obitInformationYear' => $receitaFederal['obito'] ?? null,
            'registrationStatus' => $receitaFederal['situacao'],
            'registrationStatusDate' => "{$receitaFederal['data']} {$receitaFederal['hora']}",
            'incomeEstimate' => $this->mapIncomeEstimate($externalData),
            'professions' => $this->mapProfessions($externalData),
            'address' => $this->getAddress($document),
            'contacts' => $this->getContacts($document),
            'emails' => $this->getEmails($document)
        ];
    }

    private function getContacts($document)
    {
        $data = (new SpinePfTelefonesSpider())->getSpinePfTelefones($document);

        return collect($data)->map(function ($item) {
            return [
                'ddd' => $item['ddd'],
                'phone' => $item['telefone'],
                'tipo' => $item['tipo']
            ];
        })->toArray();
    }

    private function getEmails($document)
    {
        $data = (new SpinePfEmailsSpider())->getSpinePfEmails($document);

        return collect($data)->map(function ($item) {
            return [
                'email' => $item['email'],
                'validated' => !empty($item['valido']) ? $item['valido'] : '0',
                'updatedAt' => $item['data_log']
            ];
        })->toArray();
    }

    private function getAddress($document)
    {
        $address = (new SpinePfEnderecosSpider())->getSpinePfEnderecos($document);

        return collect($address)->map(function ($location) {
            return [
                'district' => $location['bairro'],
                'city' => $location['cidade'],
                'state' => $location['uf'],
                'zipcode' => $location['cep'],
                'addressType' => $location['tipo_logradouro'],
                'addressNumber' => $location['numero'],
                'addressComplement' => $location['complemento'],
                'address' => $location['tipo_logradouro'] . ' ' . $location['logradouro'],
            ];
        })->toArray();
    }

    /**
     * @param array $externalData
     *
     * @return array
     */
    private function mapProfessions(array $externalData)
    {
        $professions = Arr::get($externalData, 'Result.0.ProfessionData.Professions');

        return collect($professions)->map(function ($profession) {
            return [
                'occupation' => $profession['Level'],
                'incomeEstimate' => $profession['Income'],
                'incomeEstimateRange' => $profession['IncomeRange'],
                'companyName' => $profession['CompanyName'],
                'status' => $profession['Status']
            ];
        })->toArray();
    }

    /**
     * @param array $externalData
     *
     * @return array
     */
    private function mapIncomeEstimate(array $externalData)
    {
        return [
            'COMPANY_OWNERSHIP' => Arr::get($externalData, 'Result.0.FinantialData.IncomeEstimates.COMPANY OWNERSHIP'),
            'MTE' => Arr::get($externalData, 'Result.0.FinantialData.IncomeEstimates.MTE'),
            'IBGE' => Arr::get($externalData, 'Result.0.FinantialData.IncomeEstimates.IBGE'),
            'PROVIDER' => Arr::get($externalData, 'Result.0.FinantialData.IncomeEstimates.BIGDATA'),
            'PROVIDER_V2' => Arr::get($externalData, 'Result.0.FinantialData.IncomeEstimates.BIGDATA_V2')
        ];
    }

    /**
     * @param string $document
     *
     * @return array
     */
    private function getExternalData($document)
    {
        $client = new Client();
        $response = $client->post($this->bigDataUri, [
            'json' => [
                'AccessToken' => $this->bigDataApiKey,
                'q' => "doc{{$document}}",
                'Datasets' => implode(',', $this->bigDataDatasets)
            ]
        ]);

        return json_decode($response->getBody(), true);
    }

    /**
     * @param string|null $birthDate
     * @param string $format
     *
     * @return string|null
     */
    private function humanizeBirthDate($birthDate, $format = 'd/m/Y')
    {
        if ($birthDate && Carbon::canBeCreatedFromFormat($birthDate, 'Ymd')) {
            return Carbon::createFromFormat('Ymd', $birthDate)->format($format);
        }

        if ($birthDate && Carbon::canBeCreatedFromFormat($birthDate, 'd/m/Y')) {
            return Carbon::createFromFormat('d/m/Y', $birthDate)->format($format);
        }

        return null;
    }

    /**
     * A partir da data de nascimento carregada é validado se o formato e verificada diferença em anos do dia
     *
     * @param string|null|bool $birthDate
     *
     * @return int|null
     */
    private function getAgeFromBirthDate($birthDate)
    {
        if ($birthDate && Carbon::canBeCreatedFromFormat($birthDate, 'd/m/Y')) {
            return Carbon::createFromFormat('d/m/Y', $birthDate)->diffInYears(Carbon::today());
        }

        if ($birthDate && Carbon::canBeCreatedFromFormat($birthDate, 'Ymd')) {
            return Carbon::createFromFormat('Ymd', $birthDate)->diffInYears(Carbon::today());
        }

        return null;
    }

    /**
     * @param string $document
     * @param bool|string $birthDate
     *
     * @return array|null
     */
    private function getReceitaFederalSite($document, $birthDate = false)
    {
        $param = [
            'documento' => $document
        ];

        if ($birthDate && Carbon::canBeCreatedFromFormat($birthDate, 'd/m/Y')) {
            $param['data_nascimento'] = $birthDate;
        }

        $params = [
            'retry' => 1,
            'source' => 'ReceitaFederalPfSite',
            'param' => $param
        ];

        return Arr::get(index($params), 'data', []);
    }

    private function insertBilhetador($dataset, $resultado)
    {
        $datasets = explode(',', $dataset);
        $result = $resultado;
        foreach ($datasets as $key => $value) {
            $data = $this->getDataset($value);
            $formatResult = json_encode($result['Result'][0][$data]);
            $queryId = $result['QueryId'];
            $params = [
                'source_method' => 'RendaEstimadaCrediShop',
                'supplier' => 'bigdatacorp',
                'search' => $value,
                'criteria' => $this->param['cpf'],
                'result' => $queryId . $formatResult
            ];
            $bilhetador = new BilhetadorFornecedoresHelper($params);
            $bilhetador->run();
        }
    }

    private function getDataset($dataset)
    {
        switch ($dataset) {
            case 'occupation_data':
                $data = 'ProfessionData';
                break;
            case 'financial_data':
                $data = 'FinantialData';
                break;
        }
        return $data;
    }

    private function verifyError($dados)
    {
        if (isset($dados->Status->api[0]->Code) && $dados->Status->api[0]->Code < 0) {
            throw new Exception($dados->Status->api[0]->Message, 3);
        }
    }
}
