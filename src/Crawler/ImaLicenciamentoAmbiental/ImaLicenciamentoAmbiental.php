<?php

namespace App\Crawler\ImaLicenciamentoAmbiental;

use App\Crawler\Spider;
use App\Helper\Document;
use Exception;

class ImaLicenciamentoAmbiental extends Spider
{
    private $cpfCnpj;
    private $name;
    private $limit;
    private $session;
    private const BASE_URL = "https://consultas.ima.sc.gov.br/consulta/consultar";


    public function start()
    {
        return $this->makeRequest();
    }


    private function makeRequest()
    {
        $params = [
            'consultar' => 1,
            'cnpj' => $this->cpfCnpj,
            'razaoSocial' => $this->name,
        ];

        $result = $this->getResponse(self::BASE_URL, 'POST', $params);
        $parsedResult = json_decode($result);

        if (empty($parsedResult)) {
            throw new Exception('Nenhum resultado encontrado!', 2);
        }

        $result = array_slice($parsedResult, 0, $this->limit);

        return $result;
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty(trim($this->param['cpf_cnpj_name']))) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        $this->cpfCnpj = Document::removeMask(trim($this->param['cpf_cnpj_name']));

        if (!is_numeric($this->cpfCnpj)) {
            $this->name = $this->param['cpf_cnpj_name'];
            $this->cpfCnpj = "";
        }

        $this->limit = $this->param['limit'];
        if (empty($this->limit)) {
            $this->limit = 10;
        }
    }
}
