<?php

namespace App\Crawler\Kurrier;

use App\Manager\Kurrier\KurrierManager;
use App\Crawler\Spider;
use App\Helper\Document;
use DateTime;
use Exception;

class Kurrier extends Spider
{
    /**
     * Descrição do nome da pessoa física ou jurídica
     *
     * @var string
     */
    private $nome;

    /**
     * Tipo da parte ("Reu"|"Autor"|"" - ambos)
     *
     * @var string
     */
    private $tipoParte = '';

    /**
     * Para pesquisa do nome nas ações da Justiça Estadual
     *
     * @var boolean
     */
    private $justicaestadual = 'false';

    /**
     * Para pesquisa do nome nas ações da Justiça Federal
     *
     * @var boolean
     */
    private $justicafederal = 'false';

    /**
     * Para pesquisa do nome nas ações da Justiça Trabalhista
     *
     * @var boolean
     */
    private $justicatrabalhista = 'false';

    /**
     * Caso venha entre 1 e 25 estados, acontece o laço de repetição usando essa variável
     *
     * @var string
     */
    private $estados = '';

    /**
     * Limite de processos
     *
     * @var integer
     */
    private $limite = 20;

    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['nome'])) {
            throw new Exception("Parâmetro nome é obrigatório", 1);
        }

        if (Document::validarCpfOuCnpj($this->param['nome'])) {
            throw new Exception("Parâmetro inválido", 6);
        }

        $this->nome = $this->param['nome'];
        $this->tipoParte = '';
        if (!empty($this->param['partes']) && count($this->param['partes']) == 1) {
            $this->tipoParte = array_shift($this->param['partes']);
        }

        $this->limite = $this->param['limite'];

        foreach ($this->param['justica'] as $justica) {
            if (property_exists($this, $justica)) {
                $this->{$justica} = 'true';
            }
        }

        $this->estados = '';
        if (!empty($this->param['uf'])) {
            $this->estados = count($this->param['uf']) == 27 ? '' : implode(";", $this->param['uf']);
        }


        $this->nascimento = '';
        if (!empty($this->param['nascimento'])) {
            $this->nascimento = (DateTime::createFromFormat('d/m/Y', $this->param['nascimento']));
        }
    }

    /**
     * Padrão dos projetos lambda
     *
     * @return array
     * <AUTHOR> Machado - 2018-11-26
     * Revisão:
     * @version 1.0.0
     *
     * @version 2.0.0
     * <AUTHOR> pro manager
     */
    protected function start()
    {
        $manager = new KurrierManager();

        return $manager->getProcess(
            $this->nome,
            $this->tipoParte,
            $this->justicatrabalhista,
            $this->justicaestadual,
            $this->justicafederal,
            $this->estados,
            $this->nascimento,
            $this->limite
        );
    }
}
