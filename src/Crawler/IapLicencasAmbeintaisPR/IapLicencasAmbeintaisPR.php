<?php

namespace App\Crawler\IapLicencasAmbeintaisPR;

use App\Crawler\Spider;
use App\Helper\Document;
use Exception;

class IapLicencasAmbeintaisPR extends Spider
{
    private const BASE_URL = "http://celepar7.pr.gov.br/sia/licenciamento/consulta/con_licenca_ajax.asp?";
    private const BASE_URL_DETAILS = "http://celepar7.pr.gov.br/sia/licenciamento/consulta/";
    private const BASE_URL_EXCEL =
    "https://celepar7.pr.gov.br/sia/licenciamento/consulta/con_licenca_planilha_ajax.asp?";
    private $cpfCnpj;
    private $limit;
    private $data = [];
    private $count = 0;

    public function start()
    {
        $this->makeLinkExcel();
        $numPages = $this->getNumberPages();
        for ($i = 1; $i <= $numPages; $i++) {
            $this->makeRequest();
        }
        return $this->data;
    }

    /**
     * Verifica se existe páginas e retorna o número de páginas baseado no parametro limit
     * <AUTHOR>
     * @return int
     */
    private function getNumberPages()
    {

        $params = [
            'cont' => 0,
            'topValor' => 10,
            'cod_regional' => "",
            'cod_bacia' => "",
            'cod_municipio' => "",
            'cb_modalidade' => "",
            'cb_atividade' => "",
            'palavraChave' => "",
            'cb_statusLicenca' => 1,
            'protocolo' => "",
            'licenca' => "",
            'cpf_cnpj' => $this->cpfCnpj,
            'txtdataini' => "",
            'txtdatafin' => "",
            'complx012616261780930937' => "1617712604362",
        ];

        $params = http_build_query($params);
        $result = $this->getResponse(self::BASE_URL . $params);

        if (preg_match('/Nenhum registro encontrado/', $result)) {
            throw new Exception('Sem resultados encontrados.', 2);
        }

        preg_match('/registros:\s(\d+)/', $result, $numRegistros);
        $numRegistros = intval($numRegistros[1]);

        $this->data['existeMaisResultados'] = 0;

        if ($numRegistros > $this->limit) {
            $this->data['existeMaisResultados'] = 1;
            $paginas = $this->limit / 10;
        } else {
            preg_match('/de\s(\d+)/', $result, $paginas);
            $paginas = intval($paginas[1]);
        }

        return $paginas;
    }

    /**
     * Cria a url do excel que tras todos os resultados.
     * <AUTHOR> Pereira
     * @return void
     */
    private function makeLinkExcel()
    {
        $params = [
            'cont' => 0,
            'topValor' => 10,
            'cod_regional' => "",
            'cod_bacia' => "",
            'cod_municipio' => "",
            'cb_modalidade' => "",
            'cb_atividade' => "",
            'palavraChave' => "",
            'cb_statusLicenca' => 1,
            'protocolo' => "",
            'licenca' => "",
            'cpf_cnpj' => $this->cpfCnpj,
            'txtdataini' => "",
            'txtdatafin' => "",
            'complx09378982152630928' => "1617892544796",
        ];

        $params = http_build_query($params);

        $this->data['linkExcel'] = self::BASE_URL_EXCEL . $params;
    }

    /**
     * Faz a requisição dos itens
     * <AUTHOR> Pereira
     * @return void
     */
    private function makeRequest()
    {
        $params = [
            'cont' => $this->count,
            'topValor' => 10,
            'cod_regional' => "",
            'cod_bacia' => "",
            'cod_municipio' => "",
            'cb_modalidade' => "",
            'cb_atividade' => "",
            'palavraChave' => "",
            'cb_statusLicenca' => 1,
            'protocolo' => "",
            'licenca' => "",
            'cpf_cnpj' => $this->cpfCnpj,
            'txtdataini' => "",
            'txtdatafin' => "",
            'complx012616261780930937' => "1617712604362",
        ];

        $params = http_build_query($params);
        $result = $this->getResponse(self::BASE_URL . $params);

        if (preg_match('/Nenhum registro encontrado/', $result)) {
            throw new Exception('Sem resultados encontrados.', 2);
        }

        $this->count = $this->count + 10;

        preg_match_all('/(view_licenca.asp\?id=\d+)/', $result, $urls);

        foreach ($urls[0] as $url) {
            $this->makeRequestDetails(self::BASE_URL_DETAILS . $url);
        }
    }

    /**
     * Faz a requisição dos detalhes dos itens
     * <AUTHOR> Pereira
     * @param string $url
     * @return void
     */
    private function makeRequestDetails($url)
    {

        if (count($this->data) == $this->limit) {
            return true;
        }

        $result = $this->getResponse($url);
        $result = html_entity_decode($result, ENT_NOQUOTES, 'ISO-8859-1');
        $result = utf8_encode($result);

        $this->parseDetails($result);
    }


    /**
     * Captura, realiza a estruturação, e faz os tratamento dos dados
     * <AUTHOR> Pereira
     * @param string $result
     * @return void
     */
    private function parseDetails($result)
    {
        preg_match_all(
            '/<td class="form_lab\w+"[\s\S]*?>([\s\S]*?)<\/td>/',
            $result,
            $licencaInfo
        );

        $licencaInfo = $licencaInfo[1];

        $data = [
            'licenca' => $licencaInfo[1],
            'protocolo' => $licencaInfo[2],
            'numLicenca' => $licencaInfo[3],
            'dataEmissao' => $licencaInfo[4],
            'dataValidade' => $licencaInfo[5],
            'razaoSocial' => $licencaInfo[7],
            'endereco' => $licencaInfo[8],
            'bairro' => $licencaInfo[9],
            'municipio' => $licencaInfo[10],
            'cep' => $licencaInfo[11],
            'empreendimento' => $licencaInfo[13],
            'atividade' => $licencaInfo[14],
            'ativEspecifica' => $licencaInfo[15],
            'enderecoEmpreendimento' => $licencaInfo[16],
            'bairroEmpreendimento' => $licencaInfo[17],
            'munEmpreendimento' => $licencaInfo[18],
            'cepEmpreendimento' => $licencaInfo[19],
            'coordenadas' => $licencaInfo[20],
            'corpoHidrico' => $licencaInfo[21],
            'baciaHidrografica' => $licencaInfo[22],
            'origemAguaUtilizada' => "",
            'destinoDoEsgotoSanitario' => "",
            'destinoDoEflutenteFinal' => "",
            'condicionantes' => $licencaInfo[23],
            'paramAtividade' => $licencaInfo[24],
        ];

        if (count($licencaInfo) == 16) {
            $data = [
                'licenca' => $licencaInfo[1],
                'protocolo' => $licencaInfo[2],
                'numLicenca' => $licencaInfo[3],
                'dataEmissao' => $licencaInfo[4],
                'dataValidade' => $licencaInfo[5],
                'razaoSocial' => $licencaInfo[7],
                'endereco' => $licencaInfo[8],
                'bairro' => $licencaInfo[9],
                'municipio' => $licencaInfo[10],
                'cep' => $licencaInfo[11],
                'empreendimento' => "",
                'atividade' => $licencaInfo[12],
                'ativEspecifica' => $licencaInfo[13],
                'enderecoEmpreendimento' => "",
                'bairroEmpreendimento' => "",
                'munEmpreendimento' => "",
                'cepEmpreendimento' => "",
                'coordenadas' => "",
                'corpoHidrico' => "",
                'baciaHidrografica' => "",
                'origemAguaUtilizada' => "",
                'destinoDoEsgotoSanitario' => "",
                'destinoDoEflutenteFinal' => "",
                'condicionantes' => $licencaInfo[14],
                'paramAtividade' => $licencaInfo[15],
            ];
        } elseif (count($licencaInfo) == 28) {
            $data = [
                'licenca' => $licencaInfo[1],
                'protocolo' => $licencaInfo[2],
                'numLicenca' => $licencaInfo[3],
                'dataEmissao' => $licencaInfo[4],
                'dataValidade' => $licencaInfo[5],
                'razaoSocial' => $licencaInfo[7],
                'endereco' => $licencaInfo[8],
                'bairro' => $licencaInfo[9],
                'municipio' => $licencaInfo[10],
                'cep' => $licencaInfo[11],
                'empreendimento' => $licencaInfo[13],
                'atividade' => $licencaInfo[14],
                'ativEspecifica' => $licencaInfo[15],
                'enderecoEmpreendimento' => $licencaInfo[16],
                'bairroEmpreendimento' => $licencaInfo[17],
                'munEmpreendimento' => $licencaInfo[18],
                'cepEmpreendimento' => $licencaInfo[19],
                'coordenadas' => $licencaInfo[20],
                'corpoHidrico' => $licencaInfo[21],
                'baciaHidrografica' => $licencaInfo[22],
                'origemAguaUtilizada' => $licencaInfo[23],
                'destinoDoEsgotoSanitario' => $licencaInfo[24],
                'destinoDoEflutenteFinal' => $licencaInfo[25],
                'condicionantes' => $licencaInfo[26],
                'paramAtividade' => $licencaInfo[27],
            ];
        }

        $this->data['data'][] = $data;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->limit = empty($this->param['limit']) ? 10 : $this->param['limit'];

        $this->cpfCnpj = Document::removeMask($this->param['cpf_cnpj']);

        if (empty($this->cpfCnpj) || !is_numeric($this->cpfCnpj)) {
            throw new Exception('Parâmetro Inválido', 6);
        }
    }
}
