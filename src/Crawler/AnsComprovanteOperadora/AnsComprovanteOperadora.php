<?php

namespace App\Crawler\AnsComprovanteOperadora;

use Exception;
use App\Helper\Pdf;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\Bucket\StaticUplexisBucket;

class AnsComprovanteOperadora extends Spider
{
    private const FIRST_URL = 'https://www.ans.gov.br/cro-web/pages/' .
        'emitirComprovanteDeSituacaoCadastralDeOperadora.xhtml';
    private const SECOND_URL = 'https://www.ans.gov.br/cro-web/pages/validaCodigo.xhtml';

    private $viewState;
    private $content = '';
    private $title = '';
    private $agency = '';
    private $name = '';
    private $code = '';
    private $issued_date = '';

    protected function start()
    {
        $this->setProxy();
        $this->setCurlOpt([
            CURLOPT_SSL_VERIFYHOST  => 0,
            CURLOPT_SSL_VERIFYPEER  => 0,
        ]);

        $responseFirstHtml = $this->firstPageRequest();
        $this->secondPageRequest($responseFirstHtml);
        return [
            'pdf' =>  $this->pdf,
            'content' =>  $this->content,
            'title' =>  $this->title,
            'agency' =>  $this->agency,
            'name' =>  $this->name,
            'number' =>  $this->code,
            'issued_date' =>  $this->issued_date
        ];
    }

    protected function validateAndSetCrawlerAttributes()
    {
        $this->document = preg_replace("/[^0-9]/", "", $this->param['cnpj']);

        if (!Document::validarCpfOuCnpj($this->document)) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }
    }

    /**
     * Método responsável por retornar o código do formulário
     *
     * @version 1.0.0
     * <AUTHOR> de Góes e Thiago Kamida - 11/06/2019
     *
     * @param string $result Retorno do site
     *
     * @return string returna o view state do formulário
     */
    private function getViewState($html)
    {
        if (
            !preg_match(
                '/<input[^>]*id="j_id\d*:javax\.faces\.ViewState:\d"\s*value="-?[0-9]*:-?[0-9]*"\s[^>]*>/is',
                $html,
                $matches
            )
        ) {
            throw new Exception('Spider Broken!', 3);
        }

        if (!preg_match('/"-?\d*:-?\d*/is', $matches[0], $matches2)) {
            throw new Exception('Spider Broken!', 3);
        }

        return str_replace('"', '', $matches2[0]);
    }

    /**
     * Método responsável por resolver o reCaptcha
     *
     * @version 1.0.0
     * <AUTHOR> de Góes e Thiago Kamida - 11/06/2019
     *
     * @param string $result Retorno do site
     *
     */
    private function resolveRecaptcha($html)
    {
        preg_match('/[^sitekey:"][\wÁ-ú]*-"/is', $html, $matches);

        $token = str_replace('"', '', $matches[0]);

        if (!empty($token)) {
            return $this->solveReCaptcha($token, self::SECOND_URL, 2);
        }

        throw new Exception("Erro ao localizar dados do captcha na página.", 3);
    }


    private function setVariables($matches)
    {
        $title = '';
        preg_match('/<h3(.*)[\t\n\s\wÁ-Ú]*/', $matches[0], $title);
        $this->title = $this->clearReturnContent($title[0]);

        $agency = '';
        preg_match('/<br \/>(.*)[\t\n\s\wÁ-Ú]*<\/h3>/', $matches[0], $agency);
        $this->agency = $this->clearReturnContent($agency[0]);

        $name = '';
        preg_match('/[^razão\ssocial][\t\s\A-ZÁ-Ú\-?]*\,\sCNPJ/', $matches[0], $name);
        $this->name = str_replace(", CNPJ", "", $this->clearReturnContent($name[0]));

        $content = '';
        preg_match('/<p>(.*)[\t\n\s\wÁ-Ú]*\.<\/p>/', $matches[0], $content);
        $this->content = $this->clearReturnContent($content[0]);

        $code = '';
        preg_match('/[0-9]+<\/p>/', $matches[0], $code);
        $this->code = $this->clearReturnContent($code[0]);

        $issued_date = '';
        preg_match('/(\d{1,2}\/\d{2}\/\d{4})\s(\d{1,2}\:)+\d{2}.*<\/p>/', $matches[0], $issued_date);
        $this->issued_date = $this->clearReturnContent($issued_date[0]);
    }

    /**
     * Método responsável por salvar o PDF no S3
     *
     * @version 1.0.0
     * <AUTHOR> de Góes e Thiago Kamida - 11/06/2019
     *
     * @param string $result Retorno do site
     *
     * @return string Url do pdf salvo no s3
     */
    private function savePDF($result)
    {
        $fileId = uniqid() . '.pdf';
        $filePath = "/tmp/AnsComprovanteOperadora" . $fileId;
        $s3Path = 'AnsComprovanteOperadora/' . $fileId;
        if (!preg_match('/<div class="comprovante"\>(.*)?[\s|!-@|\w|Á-ú|º]*<br\s\/>[\s]*<\/div>/', $result, $matches)) {
            throw new Exception("Erro ao gerar PDF", 3);
        }
        $this->setVariables($matches);
        (new Pdf())->saveHtmlToPdf(utf8_decode($matches[0]), $filePath);

        if ((new S3(new StaticUplexisBucket()))->save($s3Path, $filePath)) {
            return S3_STATIC_URL . $s3Path;
        }

        throw new Exception('Erro ao salvar o pdf', 3);
    }

    /**
     * Método responsável por fazer as requisições na primeira página
     *
     * @version 1.0.0
     * <AUTHOR> de Góes e Thiago Kamida - 11/06/2019
     *
     * @return string
     */
    private function firstPageRequest()
    {
        $html = $this->getResponse(self::FIRST_URL, 'GET');

        $this->viewState = $this->getViewState($html);

        $firstPostParams = array(
            'pesquisaOpoeradora' => 'pesquisaOpoeradora',
            'pesquisaOpoeradora:razaoSocial' => '',
            'pesquisaOpoeradora:cpfOuCnpj' => $this->document,
            'pesquisaOpoeradora:registroAns' => '',
            'pesquisaOpoeradora:j_idt34' => '',
            'javax.faces.ViewState' => $this->viewState
        );

        $this->getResponse(self::FIRST_URL, 'POST', $firstPostParams);

        $secondPostParams = array(
            'avax.faces.partial.ajax' => true,
            'javax.faces.source' => 'pesquisaOpoeradora:tableData:0:j_idt53',
            'javax.faces.partial.execute' => '@all',
            'pesquisaOpoeradora:tableData:0:j_idt53' => 'pesquisaOpoeradora:tableData:0:j_idt53',
            'pesquisaOpoeradora' => 'pesquisaOpoeradora',
            'pesquisaOpoeradora:razaoSocial' => '',
            'pesquisaOpoeradora:cpfOuCnpj' => $this->document,
            'pesquisaOpoeradora:registroAns' => '',
            'javax.faces.ViewState' => $this->getViewState($html)
        );

        $this->getResponse(self::FIRST_URL, 'POST', $secondPostParams);

        $response = $this->getResponse(self::SECOND_URL, 'GET');

        return $response;
    }

    private function clearReturnContent($content)
    {
        $content = str_replace(array("\r", "\t"), "", $content);
        $content = str_replace(array("\n"), " ", $content);
        $content = strip_tags($content);
        $content = utf8_encode($content);
        $content = utf8_decode($content);
        return $content;
    }

    /**
     * Método responsável por fazer as requisições na segunda página
     *
     * @version 1.0.0
     * <AUTHOR> de Góes e Thiago Kamida - 11/06/2019
     *
     * @return string
     */
    private function secondPageRequest($response)
    {
        if (!preg_match('/Nome\sda\soperadora:\s<strong>[^<].*?<\/strong>/isu', $response)) {
            throw new Exception('Não foram encontrados resultados para essa consulta', 2);
        }

        $recaptchaResponse = $this->resolveRecaptcha($response);

        $params = array(
            'j_idt23' => 'j_idt23',
            'g-recaptcha-response' => $recaptchaResponse,
            'j_idt23:j_idt32' => '',
            'javax.faces.ViewState' => $this->viewState
        );

        $response = $this->getResponse(self::SECOND_URL, 'POST', $params);

        if (preg_match('/O\svalor\sdigitado\sdo\scaptcha\s\(palavra\schave\)\sn.*o\sconfere./isu', $response)) {
            throw new Exception('Erro no Captcha', 3);
        }

        if (preg_match('/Erro\sde\scomunica.*o\scom\so\sservidor\s\(\d{3}\)/isu', $response)) {
            throw new Exception('Erro de comunicação com a ANS', 3);
        }

        $this->pdf = $this->savePDF($response);
    }
}
