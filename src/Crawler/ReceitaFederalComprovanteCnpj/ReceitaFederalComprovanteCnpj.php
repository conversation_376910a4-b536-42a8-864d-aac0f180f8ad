<?php

namespace App\Crawler\ReceitaFederalComprovanteCnpj;

use App\Crawler\Spider;
use Exception;
use App\Helper\Client;
use Symfony\Component\DomCrawler\Crawler;

class ReceitaFederalComprovanteCnpj extends Spider
{
    protected $receitaSiteUrl = "http://servicos.receita.fazenda.gov.br/Servicos/cnpjreva/Cnpjreva_Solicitacao.asp";
    protected $receitaSiteRequest = "http://servicos.receita.fazenda.gov.br/Servicos/cnpjreva/valida_recaptcha.asp";
    private $client;

    protected function validateAndSetCrawlerAttributes()
    {
        return false;
    }

    protected function start()
    {
        $this->param['cnpj'] = preg_replace('/[^0-9]/', '', $this->param['cnpj']);

        switch ($this->param['fornecedor']) {
            case 'receita_federal_site':
                $this->configAlternativeClient();
                $response = $this->receitaFederalSite();
                break;
            case 'receita_federal_serpro':
            case 'receita_federal_api':
            default:
                $response = $this->receitaFederalApi();
                break;
        }

        $this->updateSpine('receitaFederalPj', $response);

        return $response;
    }

    /**
     * Pega as informações do Site da Receita
     *
     * @return array
     *
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function receitaFederalSite(): array
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $form = [
            'origem' => 'comprovante',
            'cnpj' => $this->param['cnpj'],
            'h-captcha-response' => '',
            'submit1' => 'Consultar',
            'search_type' => 'cnpj',
        ];

        $checkErroCaptcha = '/<b>Erro na Consulta<\/b> \-/iu';
        $checkInvalido = '/O\s*n.*?mero\s*do\s*CNPJ\s*n.*?o\s*\S*\s*válido/is';
        $checkErroDesconhecido = '/<font[^>]*?>\s*N.*?MERO\s*DE\s*INSCRI.*?.*?O\s*<\/font>\s*<br>\s*' .
            '<font[^>]*?>\s*<b>(.*?)\s*<\/b>/i';
        $retry = 0;

        $check = false;
        while ($check == false) {
            if ($retry >= 4) {
                throw new Exception('Erro na validação do Captcha!', 3);
            }

            $token = $this->resolveHcaptcha();

            $form['h-captcha-response'] = $token;

            $result = $this->client->request('POST', $this->receitaSiteRequest, $form);

            if (preg_match($checkInvalido, $result->html())) {
                throw new Exception('CNPJ Inválido', 1);
            }

            if (preg_match($checkErroCaptcha, $result->html())) {
                $retry++;
                continue;
            }

            if (!preg_match($checkErroDesconhecido, $result->html())) {
                $retry++;
                continue;
            }

            $check = true;
        }

        $data = $this->parseSiteResult($result->html());
        $data['html'] = '';
        $qsa = $this->parseSiteQsa($result);
        $data = array_merge($data, $qsa);

        return $data;
    }

    /**
     * Pega o QSA do resultado do site da Receita
     *
     * @param Crawler $result
     *
     * @return array
     *
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function parseSiteQsa(Crawler $result): array
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $url = "http://servicos.receita.fazenda.gov.br/Servicos/cnpjreva/Cnpjreva_qsa.asp";

        $qsa = $this->client->request('GET', $url);

        return $this->parseResultQsa($qsa->html());
    }

    /**
     * Parsear os resultados do QSA
     *
     * @param string $html
     *
     * @return array
     */
    private function parseResultQsa($html): array
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $rules = array(
            'initial_capital' => "#<div[^>]*?><b>CAPITAL\s*SOCIAL:<\/b><\/div>\s*<div[^>]*?>(.*?)<\/div>#i",
        );

        foreach ($rules as $k => $p) {
            if (!preg_match($p, $html, $rs)) {
                $result[$k] = '';
                continue;
            }
            $result[$k] = $rs[1];
        }

        if (empty($result)) {
            return false;
        }

        // Caso não tenha qsa
        if (preg_match('#A\s*NATUREZA\s*JUR.*?DICA\s*N.*?O\s*PERMITE\s*O\s*PREENCHIMENTO\s*DO\s*QSA#', $html, $a)) {
            $result['qsa'] = array();
            return $result;
        }

        // Recorta os socios
        if (
            preg_match_all(
                "#(<\!--\s*quadro\s*de\s*s.*?cios\s*-->[\s\S]*?<\!--\s*Fim\s*Quadro\s*de\s*S.*?cios\s*-->)#i",
                $html,
                $rs
            )
        ) {
            foreach ((array) $rs[1] as $v) {
                $socio = array();

                $rules = array(
                    'name' => "#<div[^>]*?><b>Nome\/Nome\s*Empresarial:<\/b><\/div>\s*<div[^>]*?>(.*?)<\/div>#i",
                    'qualification' => "#<div[^>]*?><b>Qualifica.*?.*?o:<\/b><\/div>\s*<div[^>]*?>(.*?)<\/div>#i",
                    'representante_qualificacao' => "#<div[^>]*?><b>Qualif\.\s*Rep\.\s*Legal:<\/b>" .
                        "<\/div>\s*<div[^>]*?>(.*?)<\/div>#i",
                    'representante_legal' => "#<div[^>]*?><b>Nome\s*do\s*Repres\.\s*Legal:<\/b><\/div>" .
                        "\s*<div[^>]*?>(.*?)<\/div>#i",
                    'pais_origem' => "#<div[^>]*?><b>Pa.*?s\s*de\s*Origem:<\/b><\/div>\s*<div[^>]*?>(.*?)<\/div>#i",
                );

                // Retira as informações por socio
                foreach ($rules as $k => $p) {
                    if (!preg_match($p, $v, $rs2)) {
                        $socio[$k] = '';
                        continue;
                    }
                    $socio[$k] = trim($rs2[1]);
                }

                $result['qsa']['corporate_structure'][] = $socio;
            }
        }

        return $result;
    }

    /**
     * Pega os dados do resultado do site da Receita
     *
     * @param string $html
     *
     * @return array
     *
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function parseSiteResult($html): array
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $rules = array(
            'cnpj' => "#<font[^>]*?>\s*N.*?MERO\s*DE\s*INSCRI.*?.*?O\s*<\/font>\s*<br>" .
                "\s*<font[^>]*?>\s*<b>(.*?)\s*<\/b>#i",
            'tipo' => "#<font[^>]*?>\s*N.*?MERO\s*DE\s*INSCRI.*?.*?O\s*<\/font>\s*<br>\s*" .
                "<font[^>]*?>\s*<b>.*?\s*<\/b><br>\s*<b>(.*?)<\/b>#i",
            'data_abertura' => "#<font[^>]*?>\s*DATA\s*DE\s*ABERTURA\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>#i",
            'nome_empresarial' => "#<font[^>]*?>\s*NOME\s*EMPRESARIAL\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>#i",
            'nome_fantasia' => "#<font[^>]*?>\s*T.*?TULO\s*DO\s*ESTABELECIMENTO\s*\(NOME\s*DE\s*FANTASIA\)\s*" .
                "</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>#i",
            'atividade_economica_principal' => "#<font[^>]*?>\s*C.*?DIGO\s*E\s*DESCRI.*?.*?O\s*DA\s*ATIVIDADE\s*" .
                "ECON.*?MICA\s*PRINCIPAL\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>#i",
            'aAtividadeSecundaria' => "#(<font[^>]*?>\s*C.*?DIGO\s*E\s*DESCRI.*?.*?O\s*DAS\s*ATIVIDADES\s*" .
                "ECON.*?MICAS\s*SECUND.*RIAS\s*<\/font>\s*<br>\s*(?:<font[^>]*?>\s*<b>.*?<\/b>\s*<\/font>" .
                "\s*<br>\s*)*)#i",
            'natureza_juridica' => "#<font[^>]*?>\s*C.*?DIGO\s*E\s*DESCRI.*?.*?O\s*DA\s*NATUREZA\s*JUR.*DICA\s*" .
                "</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*</font>\s*<br>#i",
            'logradouro' => "#<font[^>]*?>\s*LOGRADOURO\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*</font>" .
                "\s*<br>#i",
            'numero' => "#<font[^>]*?>\s*N.*?MERO\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*</font>\s*<br>#i",
            'complemento' => "#<font[^>]*?>\s*COMPLEMENTO\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*" .
                "</font>\s*<br>#i",
            'cep' => "#<font[^>]*?>\s*CEP\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*</font>\s*<br>#i",
            'bairro' => "#<font[^>]*?>\s*BAIRRO/DISTRITO\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*" .
                "</font>\s*<br>#i",
            'municipio' => "#<font[^>]*?>\s*MUNIC.*PIO\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*" .
                "</font>\s*<br>#i",
            'uf' => "#<font[^>]*?>\s*UF\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*</font>\s*<br>#i",
            'ente_federativo_responsavel' => "#<font[^>]*?>\s*ENTE\s*FEDERATIVO\s*RESPONS.*?VEL.*?\s*\(EFR\)" .
                "\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>\s*(.*?)\s*</b>\s*</font>\s*<br>#i",
            'endereco_eletronico' => "#<font[^>]*?>\s*ENDERE.*O\s*ELETR.*NICO\s*</font>\s*<br>\s*<font[^>]*?>" .
                "\s*<b>\s*(.*?)\s*</b>\s*</font>\s*<br>#i",
            'telefone' => "#<font[^>]*?>\s*TELEFONE\s*<\/font>\s*<br>\s*<font[^>]*?>\s*<b>\s*(.*?)\s*<\/b><\/font>#i",
            'situacao_cadastral' => "#<font[^>]*?>\s*SITUA.*?.*?O\s*CADASTRAL\s*</font>\s*<br>\s*<font[^>]*?>\s*" .
                "<b>\s*(.*?)\s*<\/b>\s*</font>\s*<br>#i",
            'data_situacao' => "#<font[^>]*?>\s*DATA\s*DA\s*SITUA.*?.*?O\s*CADASTRAL\s*</font>\s*<br>\s*" .
                "<font[^>]*?>\s*<b>\s*(.*?)\s*<\/b>\s*</font>\s*<br>#i",
            'motivo_situacao' => "#<font[^>]*?>\s*MOTIVO\s*DE\s*SITUA.*?.*?O\s*CADASTRAL\s*</font>\s*<br>\s*" .
                "<font[^>]*?>\s*<b>\s*(.*?)\s*<\/b>\s*</font>\s*<br>#i",
            'situacao_especial' => "#<font[^>]*?>\s*SITUA.*?.*?O\s*ESPECIAL\s*</font>\s*<br>\s*<font[^>]*?>" .
                "\s*<b>\s*(.*?)\s*<\/b>\s*</font>\s*<br>#i",
            'data_especial' => "#<font[^>]*?>\s*DATA\s*DA\s*SITUA.*?.*?O\s*ESPECIAL\s*</font>\s*<br>\s*<font[^>]*?>" .
                "\s*<b>\s*(.*?)\s*<\/b>\s*</font>\s*<br>#i",
            'data_consulta' => "#<font[^>]*?>\s*Emitido\s*no\s*dia\s*<b>(.*?)<\/b>\s*.*?s\s*<b>.*?<\/b>#i",
            'hora_consulta' => "#<font[^>]*?>\s*Emitido\s*no\s*dia\s*<b>.*<\/b>\s*.*?s\s*<b>\s*(.*?)\s*<\/b>#i",
        );

        foreach ($rules as $k => $p) {
            if (!preg_match($p, $html, $rs)) {
                $result[$k] = '';
                continue;
            }
            $result[$k] = trim($rs[1]);
        }

        if (!array_filter($result) || empty($result)) {
            throw new Exception('Erro Desconhecido', 3);
        }

        $tmp = explode(' - ', $result['atividade_economica_principal']);
        $result['cod_atividade'] = $tmp[0];
        if (count($tmp) > 1) {
            $result['nome_atividade'] = $tmp[1];
        } else {
            $result['nome_atividade'] = "";
        }

        $tmp = explode(' - ', $result['natureza_juridica']);
        $result['cod_natureza'] = $tmp[0];
        if (count($tmp) > 1) {
            $result['nome_natureza'] = $tmp[1];
        } else {
            $result['nome_natureza'] = "";
        }

        // Somente para Atividades Secundárias
        if (!empty($result['aAtividadeSecundaria'])) {
            if (
                preg_match_all(
                    '#<font[^>]*?>\s*<b>\s*(.*?)\s*</b>\s*<\/font>#i',
                    $result['aAtividadeSecundaria'],
                    $rs2
                )
            ) {
                foreach ($rs2[1] as $value) {
                    $tmp = explode(' - ', $value);
                    $atividade[] = array(
                        'codigo' => trim($tmp[0]),
                        'descricao' => count($tmp) > 1 ? trim($tmp[1]) : '',
                        'atividade_economica_secundaria' => trim($value),
                    );
                }
            }
            $result['aAtividadeSecundaria'] = $atividade;
        }

        return $result;
    }

    /**
     * Pega as informações da api do app da Receita
     *
     * @return array
     *
     * <AUTHOR> - ****
     *         2.0.0 - Vitor Hugo <<EMAIL>>
     *         2.1.0 - Vitor Hugo <<EMAIL>> - Adição de retries
     */
    private function receitaFederalApi(): array
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $this->setProxy();
        $this->setCertificate(dirname(__FILE__) . '/cert.crt', 0);
        $retries = 3;

        do {
            try {
                $token = $this->getApiToken();
                $url = "https://movel02.receita.fazenda.gov.br/servicos-rfb/v2/ConsultaCNPJ/auth";

                $headers = [
                    'Host: movel02.receita.fazenda.gov.br:443',
                    'aplicativo: Pessoa Jur\u00eddica',
                    'Accept: application/json',
                    'versao_app: 2.0',
                    'plataforma: iOS',
                    'Accept-Language: pt-BR;q=1',
                    'Accept-encoding: br, gzip, deflate',
                    'token: ' . $token['token'],
                    'charset: utf-8',
                    'versao: 12.1',
                    'dispositivo: iPhone',
                    'User-Agent: CNPJ/16 (iPhone; iOS 12.1; Scale/3.00) ',
                    'Connection: keep-alive',
                    'Content-Length: 249',
                ];

                $param = [
                    'cnpj' => $this->param['cnpj'],
                    'tokenAuth' => $token['tokenAuth'],
                ];

                $response = $this->getResponse($url, 'POST', $param, $headers);

                $response = json_decode($response, true);

                if ($response['codigoRetorno'] === '00') {
                    break;
                }

                $retries--;

                if ($response['codigoRetorno'] != '00' && $retries === 0) {
                    throw new Exception($response['mensagemRetorno'], 1);
                }
            } catch (Exception $e) {
                $retries--;

                if ($retries === 0) {
                    throw $e;
                }
            }
        } while ($retries > 0);


        if ($response['codigoRetorno'] != '00') {
            throw new Exception($response['mensagemRetorno'], 1);
        }

        $natureza_juridica = explode(' - ', $response['naturezaJuridica']);
        $atividade_economica_principal = explode(' - ', $response['cnaeprincipal']);

        $data = [
            'cnpj' => $this->param['cnpj'],
            'nome_empresarial' => $response['nomeEmpresarial'] ?? '********',
            'tipo' => $response['matrizFilial'],
            'cod_natureza' => $natureza_juridica[0],
            'nome_natureza' => $natureza_juridica[1],
            'natureza_juridica' => $response['naturezaJuridica'],
            'nome_fantasia' => $response['nomeFantasia'] ?? '********',
            'situacao_cadastral' => $response['situacaoCadastral'],
            'data_situacao' => $response['dataSituacaoCadastral'],
            'logradouro' => $response['logradouro'],
            'cep' => $response['cep'],
            'municipio' => $response['municipio'],
            'numero' => $response['numero'],
            'complemento' => $response['complemento'],
            'bairro' => $response['bairro'],
            'uf' => $response['uf'],
            'retornoMensagem' => $response['mensagemRetorno'],
            'data_abertura' => $response['dataAbertura'],
            'hora' => $response['hora'],
            'data_consulta' => $response['data'],
            'hora_consulta' => $response['horaConsulta'],
            'codigoRetorno' => $response['codigoRetorno'],
            'endereco' => $response['endereco'],
            'telefones' => $response['telefones'],
            'motivo_situacao' => $response['motivoSituacaoCadastral'],
            'ente_federativo_responsavel' => $response['enteFederativo'],
            'endereco_eletronico' => $response['correioEletronico'],
            'initial_capital' => $response['capitalSocial'],
            'situacao_especial' => $response['situacaoEspecial'] ?? '********',
            'data_especial' => $response['dataSituacaoEspecial'] ?? '********',
            'qsa' => $response['quadroSocietario'],
            'mensagemQuadroSocietario' => $response['mensagemQuadroSocietario'],
            'cod_atividade' => $atividade_economica_principal[0],
            'nome_atividade' => $atividade_economica_principal[1],
            'atividade_economica_principal' => $response['cnaeprincipal'],
            'aAtividadeSecundaria' => $response['cnaesecundaria'],
        ];

        return $data;
    }

    /**
     * Gera o token da Receita API
     *
     * @return string
     */
    private function getApiToken()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        // Consulta
        $url = "https://movel02.receita.fazenda.gov.br/servicos-rfb/v2/Util/obterToken";

        $token = $this->generateHash(md5(mt_rand()), '', str_replace('.', 'F', uniqid(mt_rand(), true)));

        $params = [
            'aplicativo' => 'cnpj',
            'idNuvem' => $token,
            'sandbox' => false,
            'so' => 'ios',
        ];

        $this->getResponse($url, 'POST', json_encode($params));

        $code = (int) $this->getLastStatusResponse();

        if ($code !== 200 && $code !== 204) {
            throw new Exception("Error Processing Request", 1);
        }

        $tokenAuth = $this->recoveryMessages($token);

        return [
            'token' => $token,
            'tokenAuth' => $tokenAuth,
        ];
    }

    private function recoveryMessages(string $idDispositivo)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $url = 'https://movel02.receita.fazenda.gov.br/servicos-rfb/v2/EnviarMensagemNuvem/recuperarMensagens';

        $headers = [
            'host' => 'movel02.receita.fazenda.gov.br:443',
            'aplicativo' => 'Pessoa Jur\u00eddica',
            'Accept' => '*/*',
            'versao_app' => '2.0',
            'plataforma' => 'iOS',
            'Accept-Language' => 'pt-br',
            'Accept-Encoding' => 'br, gzip, deflate',
            'token' => $this->generateToken(),
            'charset' => 'utf-8',
            'versao' => '12.1',
            'dispositivo' => 'iPhone',
            'Content-Length' => '78',
            'User-Agent' => 'CNPJ/16 CFNetwork/975.0.3 Darwin/18.2.0',
            'Connection' => 'keep-alive',
        ];

        $params = [
            'idDispositivo' => $idDispositivo,
        ];

        $response = $this->getResponse($url, 'POST', $params, $headers);

        $response = json_decode($response, true);

        if (empty($response[0]['mensagemEnviada'])) {
            throw new Exception("Error Processing Request", 1);
        }

        $messageSender = json_decode(data_get($response, '0.mensagemEnviada'), true);

        return data_get($messageSender, 'aps.tokenJWT');
    }

    private function generateToken()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $decodedKey = "Sup3RbP4ssCr1t0grPhABr4sil";
        $data = md5(mt_rand()) . '' . str_replace('.', 'F', uniqid(mt_rand(), true));
        $hmac = hash_hmac("sha1", $data, $decodedKey, true);
        $hash = bin2hex($hmac);
        return $hash;
    }

    /**
     * Gera o hash do app da Receita
     *
     * @param string $cnpj
     * @param string $captchaToken
     * @param string $captchaAnswer
     *
     * @return string
     *
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function generateHash()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $decodedKey = "Sup3RbP4ssCr1t0grPhABr4sil";
        $data = $this->param['cnpj'] . time();
        $hmac = hash_hmac("sha256", $data, $decodedKey, true);
        $hash = bin2hex($hmac);
        return $hash;
    }

    /**
     * Validação do Recaptcha
     *
     * @return string
     *
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function resolveHcaptcha(): string
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $retry = 3;

        do {
            $html = $this->client->request('GET', $this->receitaSiteUrl);

            $element = $html->filter('div.h-captcha');

            if ($element->count() > 0) {
                return $this->solveHCaptcha($element->attr('data-sitekey'), $this->receitaSiteUrl);
            }

            $retry--;
        } while ($retry > 0);

        throw new Exception("Erro ao localizar dados do captcha na página.", 3);
    }

    /**
     * Define as configurações do Client secundário
     *
     * @return void
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function configAlternativeClient()
    {
        $this->client = (new Client())->createGoutteClient((new Client())->createHttpClient([], 'curl'));
    }
}
