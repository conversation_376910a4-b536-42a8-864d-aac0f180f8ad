{
      "cnpj": "#<font[^>]*?>\s*N.*?MERO\s*DE\s*INSCRI.*?.*?O\s*<\/font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)\s*<\/b>#i",
      "tipo": "#<font[^>]*?>\s*N.*?MERO\s*DE\s*INSCRI.*?.*?O\s*<\/font>\s*<br>\s*<font[^>]*?>\s*<b>.*?\s*<\/b><br>\s*<b>(.*?)<\/b>#i",
      "data_abertura": "#<font[^>]*?>\s*DATA\s*DE\s*ABERTURA\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>#i",
      "nome_empresarial": "#<font[^>]*?>\s*NOME\s*EMPRESARIAL\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>#i",
      "nome_fantasia": "#<font[^>]*?>\s*T.*?TULO\s*DO\s*ESTABELECIMENTO\s*\(NOME\s*DE\s*FANTASIA\)\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>#i",
      "atividade_economica_principal": "#<font[^>]*?>\s*C.*?DIGO\s*E\s*DESCRI.*?.*?O\s*DA\s*ATIVIDADE\s*ECON.*?MICA\s*PRINCIPAL\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>#i",
      "aAtividadeSecundaria": "#(<font[^>]*?>\s*C.*?DIGO\s*E\s*DESCRI.*?.*?O\s*DAS\s*ATIVIDADES\s*ECON.*?MICAS\s*SECUND.*RIAS\s*<\/font>\s*<br>\s*(?:<font[^>]*?>\s*<b>.*?<\/b>\s*<\/font>\s*<br>\s*)*)#i",
      "natureza_juridica": "#<font[^>]*?>\s*C.*?DIGO\s*E\s*DESCRI.*?.*?O\s*DA\s*NATUREZA\s*JUR.*DICA\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*</font>\s*<br>#i",
      "logradouro": "#<font[^>]*?>\s*LOGRADOURO\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*</font>\s*<br>#i",
      "numero": "#<font[^>]*?>\s*N.*?MERO\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*</font>\s*<br>#i",
      "complemento": "#<font[^>]*?>\s*COMPLEMENTO\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*</font>\s*<br>#i",
      "cep": "#<font[^>]*?>\s*CEP\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*</font>\s*<br>#i",
      "bairro": "#<font[^>]*?>\s*BAIRRO/DISTRITO\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*</font>\s*<br>#i",
      "municipio": "#<font[^>]*?>\s*MUNIC.*PIO\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*</font>\s*<br>#i",
      "uf": "#<font[^>]*?>\s*UF\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*</font>\s*<br>#i",
      "ente_federativo_responsavel": "#<font[^>]*?>\s*ENTE\s*FEDERATIVO\s*RESPONS.*?VEL.*?\s*\(EFR\)\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>\s*(.*?)\s*</b>\s*</font>\s*<br>#i",
      "endereco_eletronico": "#<font[^>]*?>\s*ENDERE.*O\s*ELETR.*NICO\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>\s*(.*?)\s*</b>\s*</font>\s*<br>#i",
      "telefone": "#<font[^>]*?>\s*TELEFONE\s*<\/font>\s*<br>\s*<font[^>]*?>\s*<b>\s*(.*?)\s*<\/b><\/font>#i",
      "situacao_cadastral": "#<font[^>]*?>\s*SITUA.*?.*?O\s*CADASTRAL\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>\s*(.*?)\s*<\/b>\s*</font>\s*<br>#i",
      "data_situacao": "#<font[^>]*?>\s*DATA\s*DA\s*SITUA.*?.*?O\s*CADASTRAL\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>\s*(.*?)\s*<\/b>\s*</font>\s*<br>#i",
      "motivo_situacao": "#<font[^>]*?>\s*MOTIVO\s*DE\s*SITUA.*?.*?O\s*CADASTRAL\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>\s*(.*?)\s*<\/b>\s*</font>\s*<br>#i",
      "situacao_especial": "#<font[^>]*?>\s*SITUA.*?.*?O\s*ESPECIAL\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>\s*(.*?)\s*<\/b>\s*</font>\s*<br>#i",
      "data_especial": "#<font[^>]*?>\s*DATA\s*DA\s*SITUA.*?.*?O\s*ESPECIAL\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>\s*(.*?)\s*<\/b>\s*</font>\s*<br>#i",
      "data_consulta": "#<font[^>]*?>\s*Emitido\s*no\s*dia\s*<b>(.*?)<\/b>\s*.*?s\s*<b>.*?<\/b>#i",
      "hora_consulta": "#<font[^>]*?>\s*Emitido\s*no\s*dia\s*<b>.*<\/b>\s*.*?s\s*<b>\s*(.*?)\s*<\/b>#i"
}