<?php

namespace App\Crawler\CensecCesdi;

use App\Crawler\Spider;
use App\Helper\Document;
use GuzzleHttp\Client;
use Exception;

class CensecCesdi extends Spider
{
    private $isName = false;
    private $limit = 100;
    private $criteria = "";
    private const URL = "https://censec.org.br/api/busca-atos/cesdi";

    public function start()
    {
        $tipo = $this->isName ? 'nome' : 'cpfCnpj';
        $params = http_build_query([
            $tipo => $this->criteria,
            'recaptcha' => $this->solveReCaptcha('6LdYCDUmAAAAAFg8dIy9G5rDunkiuNacQoVag0bQ', self::URL),
        ]);

        $data = $this->getResponse(self::URL . '?' . $params);
        $data = json_decode($data, true);

        if (empty($data['atos'])) {
            throw new Exception('Nenhum registro encontrado', 2);
        }

        $atos = [];

        $client = new Client();

        $countItems = 1;
        foreach ($data['atos'] as $value) {
            $value['data'] = date('d/m/Y', strtotime($value['data']));

            $response = $client->request('GET', self::URL . '/' . $value['id'], [
                'headers' => [
                    'X-Busca-Ticket' => $data['buscaTicket']
                ]
            ]);

            $ato = json_decode($response->getBody()->getContents(), true);

            $value['detalhes'] = $ato;

            $count = 0;
            $partes = [];

            while (true) {
                $partesResult = $this->getResponse(self::URL . '/' . $value['id'] . "/partes?offset={$count}");
                $partesResult = json_decode($partesResult, true);

                if (empty($partesResult['items'])) {
                    break;
                }

                foreach ($partesResult['items'] as $parte) {
                    $parteResultDetail = $this->getResponse(self::URL . '/' . $value['id'] . "/partes/{$parte['id']}");
                    $parteResultDetail = json_decode($parteResultDetail, true);
                    $parte['detalhes'] = $parteResultDetail;

                    foreach ($parte['detalhes']['documentos'] as &$documento) {
                        if (Document::validarCpfOuCnpj($documento['documento'])) {
                            $documento['documento'] = Document::formatCpfOrCnpj($documento['documento']);
                        }
                    }

                    $partes[] = $parte;
                }

                $count += 10;
            }

            $value['partes'] = $partes;

            $atos[] = $value;

            if ($countItems == $this->limit) {
                break;
            }

            $countItems++;
        }

        return $atos;
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCpfOuCnpj($this->param['criterio'])) {
            $this->isName = true;
        }

        if (empty($this->param['criterio'])) {
            throw new Exception("Parâmetro criterio é obrigatório", 1);
        }

        $this->criteria = $this->param['criterio'];

        if (!empty($this->param['limit'])) {
            $this->limit = $this->param['limit'];
        }
    }
}
