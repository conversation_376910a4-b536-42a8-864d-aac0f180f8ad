<?php

namespace App\Crawler\LicenciamentoAmbientalMg;

use App\Crawler\LicenciamentoAmbientalMg\Models\LicenciamentoAmbientalMgModel;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Util;
use Exception;

class LicenciamentoAmbientalMg extends Spider
{
    private const BASE_URL = 'http://sistemas.meioambiente.mg.gov.br/licenciamento/site/';
    private const PDF_URL = 'http://sistemas.meioambiente.mg.gov.br/licenciamento/uploads/';
    private $document;

    public function start()
    {
        $search = $this->search();
        $linkSearch = $this->parseSearch($search);
        $getResult = $this->getResults($linkSearch);
        $results = $this->parseResults($getResult);
        return $results;
    }

    /**
     * Efetuar a busca no site com base no critério utilizado
     *
     * <AUTHOR> - 22/03/2021
     * @return string
     */
    private function search()
    {
        $params = [
            'LicencaSearch[cnpj]' => $this->document,
        ];
        $response = $this->getResponse(self::BASE_URL . 'consulta-licenca?' . http_build_query($params));
        return $response;
    }

    /**
     * Retorna o link para efetuar a consulta do critério informado
     *
     * <AUTHOR> Guilherme Sório - 22/03/2021
     * @param string $html
     * @return string
     */
    private function parseSearch($html)
    {
        preg_match('/<td.*?><a.*?href="\/\D{1,13}\/\D{1,4}\/(.*)"/', $html, $match);

        if (empty($match)) {
            throw new Exception('Nenhum resultado encontrado', 2);
        }

        return $match[1];
    }

    /**
     * Retorna os dados para o critério pesquisado
     *
     * <AUTHOR> Guilherme Sório - 22/03/2021
     * @param string $link
     * @return array
     */
    private function getResults($link)
    {
        $response = $this->getResponse(self::BASE_URL . $link);

        $patterns = [
            'empreendimento' => ['@<th>Empreendimento<\/th><td>(.*?)<\/td>@is', null],
            'cpf_cnpj' => ['@<th>CNPJ\/CPF<\/th><td>(.*?)<\/td>@is', null],
            'classe' => ['@<th>Classe<\/th><td>(.*?)<\/td>@is', null],
            'regional' => ['@<th>Regional<\/th><td>(.*?)<\/td>@is', null],
            'ano' => ['@<th>Ano<\/th><td>(.*?)<\/td>@is', null],
            'mes' => ['@<th>M.*?s<\/th><td>(.*?)<\/td>@is', null],
            'numero_protocolo' => ['@<th>N.*?Protocolo<\/th><td>(.*?)<\/td>@is', null],
            'municipio' => ['@<th>Munic.*?pio<\/th><td>(.*?)<\/td>@is', null],
            'data_publicacao' => ['@<th>Data\s?de\s?Publica.*?o<\/th><td>(.*?)<\/td>@is', null],
            'decisao' => ['@<th>Decis.*?o<\/th><td>.*?>(.*?)<\/span>@is', null],
            'modalidade' => ['@<th>Modalidade<\/th><td>(.*?)<\/td>@is', null],
            'certificado' => ['@<td><a\s?href="\/\D.*?\/\D.*?\/(.*?)"@is', null]
        ];

        $data = Util::parseDados($patterns, $response);

        //Definir a url completa do certificado
        $data['certificado'] = self::PDF_URL . $data['certificado'];

        return $data;
    }

    /**
     * Retorna todos os dados da fonte no padrão correto do objeto
     *
     * <AUTHOR> Guilherme Sório - 23/03/2021
     * @param array $results
     * @return object
     */
    private function parseResults($results)
    {
        $licenciamentoModel = new LicenciamentoAmbientalMgModel();

        foreach ($results as $key => $value) {
            $licenciamentoModel->$key = utf8_decode($value);
        }

        return $licenciamentoModel;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->document = $this->param['cpf_cnpj'];

        if (!Document::validarCpfOuCnpj($this->document)) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        $this->document = Document::formatCpfOrCnpj($this->document);
    }
}
