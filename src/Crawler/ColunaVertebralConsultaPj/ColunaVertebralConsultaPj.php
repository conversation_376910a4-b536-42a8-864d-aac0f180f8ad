<?php

namespace App\Crawler\ColunaVertebralConsultaPj;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Crawler\ColunaVertebralConsultaPj\Models\ColunaVertebralPjModel;
use App\Crawler\ColunaVertebralConsultaPj\Models\ColunaVertebralPjCnjModel;
use App\Crawler\ColunaVertebralConsultaPj\Models\ColunaVertebralPjCnaeModel;
use App\Manager\ColunaVertebralManager;
use Exception;

/**
 * Classe da fonte Coluna Vertebral PJ
 *
 * @version 1.0.0
 *
 * <AUTHOR> Mesquita 01/10/2020
 */
class ColunaVertebralConsultaPj extends Spider
{
    private $colunaVertebralManager;

    /**
     * Busca os dados de uma empresa no spine PJ
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 01/10/2020
     *
     * @return array
     */
    public function start()
    {
        $this->colunaVertebralManager = new ColunaVertebralManager();

        return $this->searchByCnpj();
    }

    /**
     * Valida os parametros da fonte
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 01/10/2020
     *
     * @return void
     */
    public function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCnpj($this->param['cnpj'])) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }

        $this->param['cnpj'] = Document::removeMask($this->param['cnpj']);
    }

    /**
     * Retorna os dados PJ do critério informado
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 01/10/2020
     *
     * @return array
     */
    private function searchByCnpj(): ColunaVertebralPjModel
    {
        $spinePj = $this->colunaVertebralManager->getSpinePj($this->param['cnpj']);

        $result = new ColunaVertebralPjModel();

        $result->cnpj = $spinePj['cnpj'];
        $result->matriz = ($spinePj['matriz'] == 'True') ? 'sim' : 'nao';
        $result->razao_social = $spinePj['razao_social'];
        $result->nome_fantasia = $spinePj['nome_fantasia'];
        $result->logradouro = "{$spinePj['logr_tipo']} {$spinePj['logr_nome']}";
        $result->numero = $spinePj['logr_numero'];
        $result->complemento = $spinePj['logr_complemento'];
        $result->bairro = $spinePj['bairro'];
        $result->municipio = $spinePj['cidade'];
        $result->uf = $spinePj['uf'];
        $result->cep = $spinePj['cep'];
        $result->data_abertura = $this->colunaVertebralManager->formatDate($spinePj['data_abertura']);
        $result->situacao_cadastral = ($spinePj['situacao_cadastral']);
        $result->data_situacao = $this->colunaVertebralManager->formatDate($spinePj['data_situacao']);
        $result->motivo_situacao = $spinePj['motivo_situacao'];
        $result->situacao_especial = $spinePj['situacao_especial'];
        $result->data_especial = $this->colunaVertebralManager->formatDate($spinePj['data_especial']);
        $result->motivo_especial = $spinePj['motivo_especial'];

        $spinePjCnae = $this->colunaVertebralManager->getSpinePjCnae(
            $spinePj['cnae_segmento'] ?? ''
        );

        $resultCnae = new ColunaVertebralPjCnaeModel();

        if (!empty($spinePjCnae)) {
            if ($spinePjCnae[0]) {
                $resultCnae->codigo = (string)intval($spinePjCnae[0]['id']);
                $resultCnae->descricao = $spinePjCnae[0]['descricao'];
            }
        }

        $result->setCnae($resultCnae);

        $resultCnj = new ColunaVertebralPjCnjModel();

        $resultCnj->codigo = $spinePj['natureza_juridica'];
        $resultCnj->descricao = $this->colunaVertebralManager->getCnpj($spinePj['natureza_juridica']);

        $result->setCnj($resultCnj);

        return $result;
    }
}
