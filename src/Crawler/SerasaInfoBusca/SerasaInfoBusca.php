<?php

namespace App\Crawler\SerasaInfobusca;

use App\Crawler\Spider;
use App\Helper\Util;
use Exception;
use App\Helper\Document;

/**
 * Classe de pesquisa da Serasa InfoBusca
 * @version 1.0.0
 * <AUTHOR>
 */

class SerasaInfobusca extends Spider
{
    private const LOGIN_URL = 'https://sitenet.serasa.com.br/Logon/autentica';
    private const INFOBUSCA_URL = 'https://infobusca.serasa.com.br/?param=';
    private const INFOBUSCA_POST = 'https://infobusca.serasa.com.br/default.aspx';
    private const VIZINHOS_URL = 'https://infobusca.serasa.com.br/vizinhos.aspx?CE=';


    private $login = '';
    private $senha = '';
    private $documento = '';
    private $tipo = '';


    protected function validateAndSetCrawlerAttributes()
    {
        $this->login = $this->auth['login'];
        $this->senha = $this->auth['senha'];
        if (Document::validarCpf($this->param['documento'])) {
            $this->documento = Document::removeMask($this->param['documento']);
            $this->tipo = 1;
            return false;
        } elseif (Document::validarCnpj($this->param['documento'])) {
            $this->documento = Document::removeMask($this->param['documento']);
            $this->tipo = 2;
            return false;
        }

        $this->name = $this->param['criterio'];
    }


    protected function start()
    {
        $loginResponse = $this->login();
        $result = $this->getResult($loginResponse);

        if ($this->tipo == 1) {
            $parseResult = $this->parseResultPF($result);
        } else {
            $parseResult = $this->parseResultPJ($result);
        }
        return $parseResult;
    }

    /**
     * Faz a autenticação no site da serasa e captura o HTML
     *
     * @version 1.0.0
     *
     * <AUTHOR> Silva
     *
     */
    private function login()
    {
        $frkey = file_get_contents('../src/Crawler/SerasaInfoBusca/frkey.txt');
        $frkey2 = file_get_contents('../src/Crawler/SerasaInfoBusca/frkey2.txt');
        $params = [
           'CHECKBOX_TROCA_SENHA' => '',
           'FLAG_ALTERADA' => '',
           'FPRINT' => '8636e8a26b73ec4efc6df048e8913688',
           'FRKEY' => $frkey,
           'FRKEY2' => $frkey2,
           'LOGON' => $this->login,
           'SENHA' => $this->senha,
        ];

        $html = $this->getResponseSSLv6(self::LOGIN_URL, 'POST', $params);

        $this->checkResponse($html);

        return $html;
    }

    /**
     * Faz a captura da resposta, como visto abaixo é necessário 2 posts
     * 1 para capturar o viewstate e inserir como parametro e outro para capturar a resposta
     *
     * @version 1.0.0
     *
     * <AUTHOR> Silva
     *
     */
    private function getResult($response)
    {
        preg_match('/value="(.*?)"/m', $response, $responseValue);

        $html = $this->getResponse(self::INFOBUSCA_URL . $responseValue[1]);
        preg_match('/name="__VIEWSTATE" id="__VIEWSTATE" value="(.*?)"/m', $html, $viewstate);
        preg_match('/id="__VIEWSTATEGENERATOR" value="(.*?)"/m', $html, $viewGenerate);

        $params = [
            '__EVENTARGUMENT' => '',
            '__EVENTTARGET' => '',
            '__LASTFOCUS' => '',
            '__VIEWSTATE' => $viewstate[1],
            '__VIEWSTATEGENERATOR' => $viewGenerate[1],
            'ctl00$ContentPlaceHolder1$DropIdadeFinal' => 0,
            'ctl00$ContentPlaceHolder1$DropIdadeInicial' => 0,
            'ctl00$ContentPlaceHolder1$btnProcurar' => 0,
            'ctl00$ContentPlaceHolder1$pagina' => 1,
            'ctl00$ContentPlaceHolder1$txtCEP' => '',
            'ctl00$ContentPlaceHolder1$txtCPF' => $this->documento,
            'ctl00$ContentPlaceHolder1$txtCidade' => '',
            'ctl00$ContentPlaceHolder1$txtComplemento' => '',
            'ctl00$ContentPlaceHolder1$txtDDD' => '',
            'ctl00$ContentPlaceHolder1$txtDataFinal' => '',
            'ctl00$ContentPlaceHolder1$txtDataInicial' => '',
            'ctl00$ContentPlaceHolder1$txtEndereco' => '',
            'ctl00$ContentPlaceHolder1$txtNome' => '',
            'ctl00$ContentPlaceHolder1$txtNumero' => '',
            'ctl00$ContentPlaceHolder1$txtTelefone' => '',
            'ctl00$ContentPlaceHolder1$txtUF' => '',

        ];

        $html2 = $this->getResponse(self::INFOBUSCA_POST, 'POST', $params);

        preg_match('/name="__VIEWSTATE" id="__VIEWSTATE" value="(.*?)"/m', $html2, $viewstate1);
        preg_match('/id="__VIEWSTATEGENERATOR" value="(.*?)"/m', $html2, $viewGenerate1);

        $paramsFinal = [
            '__LASTFOCUS' => '',
            '__EVENTTARGET' => 'ctl00$ContentPlaceHolder1$grdFind$ctl02$lnkAbrir',
            '__EVENTARGUMENT' => '',
            '__VIEWSTATE' => $viewstate1[1],
            '__VIEWSTATEGENERATOR' => $viewGenerate1[1],
            'ctl00$ContentPlaceHolder1$pagina' => 1,
            'ctl00$ContentPlaceHolder1$txtCPF' => $this->documento,
            'ctl00$ContentPlaceHolder1$txtNome' => '',
            'ctl00$ContentPlaceHolder1$txtDataInicial' => '',
            'ctl00$ContentPlaceHolder1$txtDataFinal' => '',
            'ctl00$ContentPlaceHolder1$DropIdadeInicial' => 0,
            'ctl00$ContentPlaceHolder1$DropIdadeFinal' => 0,
            'ctl00$ContentPlaceHolder1$txtDDD' => '',
            'ctl00$ContentPlaceHolder1$txtTelefone' => '',
            'ctl00$ContentPlaceHolder1$txtEndereco' => '',
            'ctl00$ContentPlaceHolder1$txtNumero' => '',
            'ctl00$ContentPlaceHolder1$txtComplemento' => '',
            'ctl00$ContentPlaceHolder1$txtCidade' => '',
            'ctl00$ContentPlaceHolder1$txtUF' => '',
            'ctl00$ContentPlaceHolder1$txtCEP' => ''

        ];

        $resultFinal = $this->getResponse(self::INFOBUSCA_POST, 'POST', $paramsFinal);

        return $resultFinal;
    }


     /**
     * Captura as informações do vizinhos de acordo com o link informado no HTML
     *
     * @version 1.0.0
     *
     * <AUTHOR> Silva
     *
     */
    private function getVizinhos($html, $type)
    {
        preg_match('/name="__VIEWSTATE" id="__VIEWSTATE" value="(.*?)"/m', $html, $viewstate);
        preg_match('/id="__VIEWSTATEGENERATOR" value="(.*?)"/m', $html, $viewGenerate);
        preg_match('/id="__EVENTVALIDATION" value="(.*?)"\s/m', $html, $eventValidation);
        preg_match_all('/vizinhos\.aspx\?CE=(.*?)\'\)/m', $html, $vizinhosLink);

        if ($type == 1) {
            $vizinhosHtml = $this->getResponse(self::VIZINHOS_URL . $vizinhosLink[1][0]);
        } else {
            $vizinhosHtml = $this->getResponse(self::VIZINHOS_URL . $vizinhosLink[1][1]);
        }
        preg_match_all('/Page\$.&#39;\)">(.*?)<\//m', $html, $qtdPages);

        preg_match_all('/title=""><\/a>\s*<\/td><td>(.*?)<\/td>/m', $vizinhosHtml, $nome);
        preg_match_all('/title=""><\/a>\s*.*?<td>.*?<td>(.*?)<\/td>/m', $vizinhosHtml, $endereco);
        $vizinhos = [];
        foreach ($nome[1] as $key => $value) {
            $vizinhos[] = [
                'nome' => $value,
                'endereco' => $endereco[1][$key]
            ];
        }

        return $vizinhos;
    }

    /**
     * Faz o parse da resposta de acordo com o resultado para Pessoa Física
     *
     * @version 1.0.0
     *
     * <AUTHOR> Silva
     *
     */
    private function parseResultPF($html)
    {
        $vizinhosPrioritario = $this->getVizinhos($html, 1);
        $vizinhosCadastrado = $this->getVizinhos($html, 2);

        $cpfPattern = '/CPF<\/td><td class="conteudo">(.*?)<\/td>/m';
        $nomePattern = '/Nome<\/td><td class="conteudo">(.*?)<\/td>/m';
        $idadePattern = '/Idade<\/td><td class="conteudo">(.*?)</m';
        $nascimentoPattern = '/Data de Nascimento<\/td><td class="conteudo">(.*?)<im/m';
        $notaEnderecoPattern = '/Endere&#231;o Priorit&#225;rio.*col2">(.*?)<\//m';
        $enderecoPrioritarioPattern = '/Endere&#231;o Priorit&#225;rio.*?col3">(.*?)<\/td>/m';
        $notaEnderecoCadPattern = '/Endere&#231;o Cadastrado .*col2">(.*?)</m';
        $enderecoCadatradoPattern = '/Endere&#231;o Cadastrado .*?col3">(.*?)<\/td>/m';
        $telefonePriNotaPattern = '/elefone Priorit&#225;rio<\/td><td class="col2">(.*?)</m';
        $telefonePriPattern = '/elefone Priorit&#225;rio.*col2".*<td>(.*?)<\/td>/m';
        $telefoneCadNotaPattern = '/Telefone Cadastrado.*col2">(.*?)<\/td>/m';
        $telefoneCadPattern = '/Telefone Cadastrado.*col2.*<td>(.*?)<\/td>/m';

        preg_match($cpfPattern, $html, $cpf);
        preg_match($nomePattern, $html, $nome);
        preg_match($idadePattern, $html, $idade);
        preg_match($nascimentoPattern, $html, $nascimento);
        preg_match($notaEnderecoPattern, $html, $notaEndereco);
        preg_match($enderecoPrioritarioPattern, $html, $enderecoPrioritario);
        preg_match_all($notaEnderecoCadPattern, $html, $notaEnderecoCad);
        preg_match_all($enderecoCadatradoPattern, $html, $enderecoCadastrado);
        preg_match($telefonePriNotaPattern, $html, $telefonePriNota);
        preg_match($telefonePriPattern, $html, $telefonePrioritario);
        preg_match_all($telefoneCadNotaPattern, $html, $telefoneCadNota);
        preg_match_all($telefoneCadPattern, $html, $telefoneCad);

        $replaceEndereco1 = '/ <br \/> <span style=\'color:#f00\'>/m';
        $replaceEndereco2 = '/<\/span>/m';
        preg_replace(array($replaceEndereco1, $replaceEndereco2), '', $enderecoCadastrado[1]);


        $data = [
            'cpf' => $cpf[1],
            'nome' => $nome[1],
            'idade' => $idade[1],
            'nascimento' => $nascimento[1],
            'nota_endereco' => $notaEndereco[1],
            'endereco_prioritario' => $enderecoPrioritario[1],
            'nota_endereco_cadastrado' => $notaEnderecoCad[1],
            'endereco_cadastrado' => $enderecoCadastrado[1],
            'telefone_prioritario_nota' => $telefonePriNota[1],
            'telefone_prioritario' => $telefonePrioritario[1],
            'telefone_cadastrado_nota' => $telefoneCadNota[1],
            'telefone_cadastrado' => $telefoneCad[1],
            'vizinhos_prioritario' => $vizinhosPrioritario,
            'vizinhos_cadastrado' => $vizinhosCadastrado
        ];
        return $data;
    }

    /**
     * Faz o parse da resposta de acordo com o resultado para Pessoa jurídica
     *
     * @version 1.0.0
     *
     * <AUTHOR> Silva
     *
     */
    private function parseResultPJ($html)
    {
        $vizinhosPrioritario = $this->getVizinhos($html, 1);
        $vizinhosCadastrado = $this->getVizinhos($html, 2);

        $cnpjPattern = '/CNPJ<\/td><td class="conteudo">(.*?)</m';
        $situacaoPattern = '/Situação cadastral<\/td><td class="conteudo">(.*?)</m';
        $razaoSocialPattern = '/<td class="label">Razão social<\/td><td class="conteudo">(.*?)</m';
        $dataSituacaoPattern = '/situação cadastral<\/td><td class="conteudo">(.*?)<\/td>/m';
        $nomefantasiPattern = '/Nome fantasia<\/td><td class="conteudo">(.*?)</m';
        $dataAberturaPattern = '/Data de abertura<\/td><td class="conteudo">(.*?)</m';
        $atividadeprimariaPattern = '/Atividade econômica primária.*?">(.*?)<\/td>/m';
        $atividadeSecundariaPattern = '/econômica secundária.*?">(.*?)<\/td>/m';
        $naturezaJuridicaPattern = '/Natureza jurídica.*?">(.*?)<\/td>/m';
        $notaEnderecoPattern = '/Endere&#231;o Priorit&#225;rio.*col2">(.*?)<\//m';
        $enderecoPrioritarioPattern = '/Endere&#231;o Priorit&#225;rio.*?col3">(.*?)<\/td>/m';
        $enderecoCadatradoPattern = '/Endere&#231;o Cadastrado .*?col3">(.*?)<\/td>/m';
        $telefonePriNotaPattern = '/elefone Priorit&#225;rio<\/td><td class="col2">(.*?)</m';
        $telefonePriPattern = '/elefone Priorit&#225;rio.*col2".*<td>(.*?)<\/td>/m';
        $telefoneCadNotaPattern = '/Telefone Cadastrado.*col2">(.*?)<\/td>/m';
        $telefoneCadPattern = '/Telefone Cadastrado.*col2.*<td>(.*?)<\/td>/m';
        $notaEnderecoCadPattern = '/Endere&#231;o Cadastrado .*col2">(.*?)</m';

        preg_match($cnpjPattern, $html, $cnpj);
        preg_match($situacaoPattern, $html, $situacaoCadastral);
        preg_match($razaoSocialPattern, $html, $razaoSocial);
        preg_match($dataSituacaoPattern, $html, $dataSituacao);
        preg_match($nomefantasiPattern, $html, $nomefantasia);
        preg_match($dataAberturaPattern, $html, $dataAbertura);
        preg_match($atividadeprimariaPattern, $html, $atividadePrimaria);
        preg_match($atividadeSecundariaPattern, $html, $atividadeSecundaria);
        preg_match($naturezaJuridicaPattern, $html, $naturezaJuridica);
        preg_match($enderecoPrioritarioPattern, $html, $enderecoPrioritario);
        preg_match($notaEnderecoPattern, $html, $notaEndereco);
        preg_match_all($enderecoCadatradoPattern, $html, $enderecoCadastrado);
        preg_match($telefonePriNotaPattern, $html, $telefonePriNota);
        preg_match($telefonePriPattern, $html, $telefonePrioritario);
        preg_match_all($telefoneCadNotaPattern, $html, $telefoneCadNota);
        preg_match_all($telefoneCadPattern, $html, $telefoneCad);
        preg_match_all($notaEnderecoCadPattern, $html, $notaEnderecoCad);

        $data = [
            'cnpj' => $cnpj[1],
            'situacao_cadastral' => $situacaoCadastral[1],
            'razao_social' => $razaoSocial[1],
            'data_situacao' => $dataSituacao[1],
            'nome_fantasia' => $nomefantasia[1],
            'data_abertura' => $dataAbertura[1],
            'atividade_primaria' => $atividadePrimaria[1],
            'atividade_secundaria' => $atividadeSecundaria[1],
            'naturezaJuridica' => $naturezaJuridica[1],
            'nota_endereco' => $notaEndereco[1],
            'endereco_prioritario' => $enderecoPrioritario[1],
            'nota_endereco_cadastrado' => $notaEnderecoCad[1],
            'endereco_cadastrado' => $enderecoCadastrado[1],
            'telefone_prioritario_nota' => $telefonePriNota[1],
            'telefone_prioritario' => $telefonePrioritario[1],
            'telefone_cadastrado_nota' => $telefoneCadNota[1],
            'telefone_cadastrado' => $telefoneCad[1],
            'vizinhos_prioritario' => $vizinhosPrioritario,
            'vizinhos_cadastrado' => $vizinhosCadastrado
        ];

        return $data;
    }

    /**
     * Checa a resposta da página
     *
     * @version 1.0.0
     *
     * <AUTHOR> Vidal
     *
     * @param string $response
     *
     */
    private function checkResponse($response)
    {
        if (preg_match("#<div.class=\"notification.error\">\s*(.*)?\s*<\/div>#i", $response, $matches)) {
            $error = html_entity_decode($matches[1]);

            if (preg_match("#acesso.inv.lido#isu", $error)) {
                throw new Exception('Acesso inválido, verifique as credenciais de acesso.', 6);
            }

            throw new Exception($error, 3);
        }
    }
}
