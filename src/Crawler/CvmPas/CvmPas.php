<?php

namespace App\Crawler\CvmPas;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Str;
use App\Helper\Util;
use Exception;

class CvmPas extends Spider
{
    private const URL = 'https://sistemas.cvm.gov.br/asp/cvmwww/inqueritos';
    private const MAX_RESULTS = 20;

    private $name;
    private $type;
    private $limit;

    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['nome'])) {
            throw new Exception('Parâmetro de criterio inválido', 1);
        }

        if (Document::validarCpf($this->param['nome'])) {
            $this->name = Document::formatCpf($this->param['nome']);
            $this->type = "DOCUMENT";
        } elseif (Document::validarCnpj($this->param['nome'])) {
            $this->name = Document::formatCnpj($this->param['nome']);
            $this->type = "DOCUMENT";
        } else {
            $this->name = utf8_decode($this->param['nome']);
            $this->type = "NAME";
        }

        $this->limit = $this->param['limite'];

        if (empty($this->limit) || $this->limit == 0) {
            $this->limit = self::MAX_RESULTS;
        }
    }

    /**
     * Metodo de início
     *
     * <AUTHOR> Vidal <<EMAIL>>
     * @return array
     */
    protected function start()
    {
        $this->setAlternativeProxy();

        $html = $this->search();

        $results = $this->getProcessos($html);

        return $results;
    }

    /**
     * Metodo responsável por fazer a busca no site
     *
     * <AUTHOR> Vidal <<EMAIL>>
     * @return string
     */
    private function search()
    {
        $params = [
            'txtProcesso' => '',
            'txtIndiciado' => $this->name,
            'txtDocIndiciado' => '',
        ];

        if ($this->type == 'DOCUMENT') {
            $params['txtIndiciado'] = '';
            $params['txtDocIndiciado'] = $this->name;
        }

        $response = $this->getResponse(self::URL . '/ResultBuscaPas_Novo.asp', 'POST', $params, [
            'Referer' => self::URL . '/formbuscapas.asp'
        ]);

        $this->checkErrors($response);

        return $response;
    }

    /**
     * Metodo recuperar e entrar nos links de cada processo
     *
     * <AUTHOR> Vidal <<EMAIL>>
     *
     * @param string $html
     * @return array
     */
    private function getProcessos($html)
    {
        $results = [];

        $pattern = '@Selecione\s*o\s*processo\s*desejado\s*clicando\s*no\s*respectivo\s*n.*?mero(.*?)</HTML>@is';

        if (preg_match_all($pattern, $html, $matches)) {
            $pattern = "@<A\s*HREF='(.*?)'\s*CLASS='MenuItemP'>.*?</A></TD><TD.*?>.*?"
                . "</TD><TD.*?>.*?</TD><TD.*?>(.*?)<BR></TD></TR>@is";
            if (preg_match_all($pattern, $matches[1][0], $matches2)) {
                for ($i = 0; $i < count($matches2[0]); $i++) {
                    if ($i >= $this->limit) {
                        break;
                    }
                    $link = $matches2[1][$i];
                    $nome = $matches2[2][$i];

                    $response = $this->getResponse(self::URL . "/$link", 'GET', [], [
                        'Referer' => self::URL . '/ResultBuscaPas_Novo.asp'
                    ]);

                    $results[] = $this->parseData($response, $nome);
                }
            } else {
                throw new Exception("Erro no get do result do processo", 3);
            }
        } else {
            throw new Exception("Erro ao recuperar os links dos processos", 3);
        }

        return $results;
    }

    /**
     * Metodo responsável por fazer o parse de acusados
     *
     * <AUTHOR> Vidal <<EMAIL>>
     *
     * @param string $html
     *
     * @return array
     */
    private function getAcusados($html)
    {
        $acusados = [];

        $pattern = '@ACUSADOS\s*NO\s*PROCESSO@is';
        $pattern1 = '@<B>PAS.*?</B>@is';

        if (preg_match($pattern, $html, $matches)) {
            $pattern = "@<A\s*HREF='JavaScript:newPopup.*?>(.*?)</A></TD><TD.*?>(.*?)</TD><TD.*?>(.*?)</TD></TR>@is";
            preg_match_all($pattern, $html, $matches);
            for ($i = 0; $i < count($matches[0]); $i++) {
                $acusados[] = [
                    'nome'    => $this->removeHtml($matches[1][$i]),
                    'decisao' => '',
                    'decisor' => '',
                    'status'  => $this->removeHtml($matches[2][$i]),
                    'data'    => $this->removeHtml($matches[3][$i])
                ];
            }
        } elseif (preg_match($pattern1, $html, $matches)) {
            $pattern = "@<A\s*HREF='DetIncdo.asp.*?>(.*?)</A></FONT></TD><TD.*?>(.*?)</FONT></TD><TD.*?>"
                . "(.*?)</FONT></TD><TD>(.*?)</TD></TR>@is";
            if (preg_match_all($pattern, $html, $matches)) {
                for ($i = 0; $i < count($matches[0]); $i++) {
                    $acusados[] = [
                        'nome'    => $this->removeHtml($matches[1][$i]),
                        'decisao' => $this->removeHtml($matches[2][$i]),
                        'decisor' => $this->removeHtml($matches[3][$i]),
                        'status'  => $this->removeHtml($matches[4][$i]),
                        'data'    => ''
                    ];
                }
            }
        }

        return $acusados;
    }

    /**
     * Metodo responsável por fazer o parse de fase
     *
     * <AUTHOR> Vidal <<EMAIL>>
     *
     * @param string $html
     *
     * @return array
     */
    private function getFase($html)
    {
        $fase = [];

        $pattern = '@DADOS\s*DAS\s*.LTIMAS\s*MOVIMENTA.*?ES\s*DO\s*PROCESSO@is';
        if (preg_match($pattern, $html, $matches)) {
            $pattern = "@FASE\s*ATUAL</B></TD><TD.*?><B>SUBFASE\s*ATUAL</B></TD><TD.*?><B>DATA\s*DA\s*."
                . "LTIMA\s*MUDAN.A\s*DE\s*FASE/SUBFASE</B></TD></TR><TR.*?><TD.*?>(.*?)</TD><TD.*?>(.*?)</TD>"
                . "<TD.*?>(.*?)</TD></TR></TABLE><TABLE.*?<B>LOCAL\s*ATUAL</B></TD><TD.*?<B>DATA\s*DA\s*ULTIMA\s*"
                . "MOVIMENTA.*?O\s*DE\s*LOCAL</B></TD></TR><TR.*?><TD.*?>(.*?)</TD><TD.*?>(.*?)</TD></TR></table>@is";
            preg_match($pattern, $html, $matches);
            $fase = [
                'fase_atual'    => $this->removeHtml($matches[1]),
                'subfase_atual' => $this->removeHtml($matches[2]),
                'data_fase'     => $this->removeHtml($matches[3]),
                'local'         => $this->removeHtml($matches[4]),
                'data_local'    => $this->removeHtml($matches[5])
            ];
        }

        return $fase;
    }

    /**
     * Metodo responsável por fazer o parse de info
     *
     * <AUTHOR> Vidal <<EMAIL>>
     *
     * @param string $html
     *
     * @return array
     */
    private function getInfo($html)
    {
        $info = [];

        $pattern = '@INFORMA.*?ES\s*DO\s*PROCESSO@is';
        if (preg_match($pattern, $html, $matches)) {
            $patterns = array(
                "numero"        => ['@<B>N.mero</B></TD><TD>(.*?)</TD>@is',null],
                "assunto"       => ['@<B>Assunto/Objeto</B></TD><TD>(.*?)</TD>@is',null],
                "data_abertura" => ['@<B>Data\s*de\s*abertura</B></TD><TD>(.*?)</TD>@is',null],
                "encarregado"   => [
                    '@<B>Encarregado\s*da\s*Instru.*?o\s*do\s*Processo\s*</B></TD><TD>(.*?)</TD>@is',
                    null
                ]
            );

            $info = Str::encoding(Util::parseDados($patterns, $html));
        }

        return $info;
    }

    /**
     * Metodo responsável por fazer o parse de movimentações
     *
     * <AUTHOR> Vidal <<EMAIL>>
     *
     * @param string $html
     *
     * @return array
     */
    private function getMovimentacoes($html)
    {
        $movimentacoes = [];

        $pattern = '@DADOS\s*DAS\s*.LTIMAS\s*MOVIMENTA.*?ES\s*DO\s*PROCESSO@is';
        if (preg_match($pattern, $html, $matches)) {
            $pattern = '@DADOS\s*DAS\s*.LTIMAS\s*MOVIMENTA.*?ES\s*DO\s*PROCESSO.*?LOCAL</B></TD>(.*?)</TABLE>@is';
            if (preg_match($pattern, $html, $matches)) {
                $pattern = "@<TR.*?><TD>(.*?)</TD><TD.*?>(.*?)</TD><TD.*?>(.*?)</TD></TR>@is";
                preg_match_all($pattern, $matches[1], $matches2);
                for ($i = 0; $i < count($matches2[0]); $i++) {
                    $movimentacoes[] = [
                        'data'    => $this->removeHtml($matches2[1][$i]),
                        'subfase' => $this->removeHtml($matches2[2][$i]),
                        'local'   => $this->removeHtml($matches2[3][$i])
                    ];
                }
            }
        }

        return $movimentacoes;
    }

    /**
     * Metodo responsável por fazer o parse dos dados para array
     *
     * <AUTHOR> Vidal <<EMAIL>>
     *
     * @param string $html
     *
     * @param string $nome
     *
     * @return array
     */
    private function parseData($html, $nome)
    {
        $patterns = [
            "processo"        => ['@<B>N.mero</B></TD><TD>(.*?)</TD>@is',null],
            "tipo_rito"       => ['@<B>Tipo\s*de\s*Rito</B></TD><TD>(.*?)</TD>@is',null],
            "data_julgamento" => ['@<B>Data\s*do\s*Julgamento\s*/(CVM/)</B></TD><TD>(.*?)</TD>@is',null],
            "ementa"          => ['@<B>Ementa</B></TD><TD>\s*<I>(.*?)</I>@is',null]
        ];

        $data = Str::encoding(Util::parseDados($patterns, $html));

        $data['nome'] = Str::encoding($nome);
        $data['acusados'] = Str::encoding($this->getAcusados($html));
        $data['movimentacoes'] = Str::encoding($this->getMovimentacoes($html));
        $data['oFase'] = Str::encoding($this->getFase($html));
        $data['oInfo'] = Str::encoding($this->getInfo($html));

        return $data;
    }

    /**
     * Metodo responsável por checar erros na página
     *
     * <AUTHOR> Vidal <<EMAIL>>
     *
     * @param string $html
     *
     */
    private function checkErrors($html)
    {
        $pattern = '@Favor\s*preencher\s*ao\s*menos\s*um\s*campo\s*no\s*formul.rio\s*anterior@is';
        if (preg_match($pattern, $html)) {
            throw new Exception("Favor preencher ao menos um campo no formulário anterior!", 3);
        }

        $pattern = '@N.o\s*foram\s*encontrados\s*processos\s*administrativos\s*sancionadores\s*'
            . 'para\s*esta\s*consulta@is';
        if (preg_match($pattern, $html)) {
            throw new Exception(
                "Não foram encontrados processos administrativos sancionadores para esta consulta!",
                2
            );
        }
    }

    /**
     * Metodo responsável por retirar o html e espaço das strings
     *
     * <AUTHOR> Vidal <<EMAIL>>
     *
     * @param string $html
     *
     * @return string
     */
    private function removeHtml($html)
    {
        return trim(strip_tags($html));
    }
}
