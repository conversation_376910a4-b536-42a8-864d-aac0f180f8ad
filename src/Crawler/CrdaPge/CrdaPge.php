<?php

namespace App\Crawler\CrdaPge;

use Exception;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Helper\Util;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\Bucket\StaticUplexisBucket;

/**
 *  Clase de consulta da fonte CrdaPge - Certidão Negativa de Débitos da Dívida Ativa PGE
 *
 *  <AUTHOR> - 22/04/2020

 *  @version 1.0
 */
class CrdaPge extends Spider
{
    private const BASE_URL = 'https://www.dividaativa.pge.sp.gov.br/sc/pages/crda/emitirCrda.jsf';
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const MFP_S3_PATH = 'captura/crda_pge/';
    private const TYPE_PF = 'PF';
    private const TYPE_PJ = 'PJ';

    private $params;
    private $viewState;
    private $captchaKey;
    private $certificateName;
    private $certificateLocalPath;
    private $certificateS3Path;
    private $certificateUrl;

    /**
     * Inicio do processamento da fonte
     *
     * <AUTHOR> Mesquita 23/04/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    protected function start()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__;
        }

        $this->setProxy();

        $this->setCurlOpt([
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $response = $this->getResponse(self::BASE_URL);
        $this->getViewState($response);
        $this->resolveCaptcha($response);

        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::MFP_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;

        return $this->getResults();
    }

    /**
     * Resolve o captcha e seta a resposta
     *
     * @param string $response
     *
     * <AUTHOR> Mesquita 23/04/2019
     *
     * @version 1.0.0
     *
     * @return string
     */
    private function resolveCaptcha($response)
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__;
        }

        $this->captchaKey = $this->solveReCaptcha($this->getSiteKey($response), self::BASE_URL);

        if (empty($this->captchaKey)) {
            throw new Exception('Não foi possível quebrar o captcha', 3);
        }
    }

    /**
     * Pega o id do captcha no html
     *
     * @param string $response
     *
     * <AUTHOR> Mesquita 23/04/2019
     *
     * @version 1.0.0
     *
     * @return string
     */
    private function getSiteKey($response)
    {
        preg_match('/data-sitekey="(.*?)"/isu', $response, $match);

        if (empty($match[1])) {
            throw new Exception('Erro ao pegar a chave do catpcha', 3);
        }

        return $match[1];
    }

    /**
     * Pega o viewstate no html
     *
     * @param string $response
     *
     * <AUTHOR> Mesquita 23/04/2019
     *
     * @version 1.0.0
     *
     * @return string
     */
    private function getViewState($response)
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__;
        }

        preg_match('/javax.faces.ViewState".*?value=.(.*?)"/isu', $response, $match);

        if (empty($match[1])) {
            throw new Exception('Erro ao pegar o view state', 3);
        }

        $this->viewState = $match[1];
    }

    /**
     * Retorna os resultados parseados
     *
     * <AUTHOR> Mesquita 23/04/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    private function getResults()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__;
        }

        $this->getPdf();

        return $this->getParse(
            $this->getPdfToTextAndSavePdf()
        );
    }

    /**
     * Parse dos dados
     *
     * @param string $text
     *
     * <AUTHOR> Mesquita 23/04/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    private function getParse($text)
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__;
        }

        $arrPatterns = [
            'conteudo' => ['/(Ressalvado o direito de.*?seja.aquela.acima.informada.)/isu', null],
            'certidao' => ['/Certidão.nº.([\d]*)/isu', null],
            'data_emissao' => ['/Data.e.hora.da.emiss.*?\s(.*?)\s\(hora/isu', null],
            'data_validade' => ['/validade\s(.*),\scontados/isu', null]
        ];

        if ($this->params['type'] == self::TYPE_PF) {
            $arrPatterns['conteudo'] = ['/(Ressalvado o direito de.*?do Interessado\(a\).)/isu', null];
        }

        $result = [
            'documento' => $this->param['cpf_cnpj'],
            'pdf' => $this->certificateUrl
        ];

        $result = array_merge(Util::parseDados($arrPatterns, $text), $result);
        $result = array_map('utf8_decode', $result);

        return $result;
    }

    /**
     * Baixa e salva o PDF
     *
     * <AUTHOR> Mesquita 23/04/2019
     *
     * @version 1.0.0
     *
     * @return void
     */
    private function getPdf()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__;
        }

        $response = $this->getResponse(self::BASE_URL, 'POST', $this->getPostParams());


        $delimiter = '/<span\sclass="rich-messages-label messages-error-label">*(.*?)</';
        if (preg_match($delimiter, $response, $match)) {
            $msg = html_entity_decode($match[1]);

            if (preg_match('/Favor dirigir-se a uma unidade da Secretaria da Fazenda/is', $msg)) {
                throw new Exception(html_entity_decode($msg), 6);
            }
            if (preg_match('/As informa/is', $msg)) {
                throw new Exception(html_entity_decode($msg), 6);
            }

            throw new Exception(html_entity_decode($msg), 3);
        }


        file_put_contents($this->certificateLocalPath, $response);
    }

    /**
     * Retorna o texto do PDF
     *
     * <AUTHOR> Mesquita 23/04/2019
     *
     * @version 1.0.0
     *
     * @return string
     */
    private function getPdfToTextAndSavePdf()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__;
        }

        try {
            $text = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
                'nopgbrk',
                'layout',
                'fixed 4'
            ]);

            (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
            file_exists($this->certificateLocalPath) && unlink($this->certificateLocalPath);

            return Str::cleanString($text);
        } catch (Exception $e) {
            throw new Exception('Erro ao pegar o conteúdo do pdf', 3);
        }
    }

    /**
     * Retorna os dados de envio do post
     *
     * <AUTHOR> Mesquita 23/04/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    private function getPostParams()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__;
        }

        $params = [
            'emitirCrda' => 'emitirCrda',
            'emitirCrda:crdaInputCnpjBase' => '',
            'emitirCrda:crdaInputCpf' => '',
            'g-recaptcha-response' => $this->captchaKey,
            'emitirCrda:j_id97' => 'Emitir',
            'javax.faces.ViewState' => $this->viewState
        ];

        if ($this->params['type'] === self::TYPE_PF) {
            $params['emitirCrda:crdaInputCpf'] = $this->params['document'];
        }

        if ($this->params['type'] === self::TYPE_PJ) {
            $params['emitirCrda:crdaInputCnpjBase'] = $this->params['document'];
        }

        return $params;
    }


    /**
     * Valida e seta parametros de entrada
     *
     * <AUTHOR> Mesquita 23/04/2019
     *
     * @version 1.0.0
     *
     * @return void
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__;
        }

        $this->param['cpf_cnpj'] = preg_replace('/\D/', '', $this->param['cpf_cnpj']);

        if (empty($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro de documento inválido');
        }

        if (strlen(trim($this->param['cpf_cnpj'])) == 11 && !Document::validarCpf($this->param['cpf_cnpj'])) {
            throw new Exception('CPF Invalido', 1);
        }

        if (strlen(trim($this->param['cpf_cnpj'])) == 14 && !Document::validarCnpj($this->param['cpf_cnpj'])) {
            throw new Exception('CNPJ Invalido', 1);
        }

        $type = self::TYPE_PF;
        $cpfCnpj = $this->param['cpf_cnpj'];

        if (Document::validarCnpj($this->param['cpf_cnpj'])) {
            $type = self::TYPE_PJ;
            $cpfCnpj = substr($this->param['cpf_cnpj'], 0, 8);
        }

        $this->params = [
            'document' => $cpfCnpj,
            'type' => $type
        ];
    }
}
