<?php

namespace App\Crawler\ReceitaFederalPjSite;

use App\Crawler\ReceitaFederalPjGeneric\Models\ReceitaFederalPjGenericModel;
use App\Crawler\ReceitaFederalPjGeneric\Models\ReceitaFederalPjGenericCnaeSecundarioModel;
use App\Crawler\ReceitaFederalPjGeneric\Models\ReceitaFederalPjGenericQsaModel;
use App\Crawler\GenericInterface;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Util;
use App\Helper\Str;
use App\Manager\BufferBrownarrow\BufferBrownarrowClient;
use App\Manager\BufferBrownarrow\BufferBrownarrowManager;
use App\Manager\BufferBrownarrow\DTO\InsertRequestDTO;
use GuzzleHttp\Client;
use App\Crawler\ReceitaFederalPjGeneric\ReceitaFederalPjTrait;
use Exception;

class ReceitaFederalPjSite extends Spider implements GenericInterface
{
    use ReceitaFederalPjTrait;

    private const URL_POST = 'https://solucoes.receita.fazenda.gov.br/Servicos/cnpjreva/valida_recaptcha.asp';
    private const URL = 'https://solucoes.receita.fazenda.gov.br/Servicos/cnpjreva/Cnpjreva_Solicitacao.asp';
    private const URL_QSA = 'https://solucoes.receita.fazenda.gov.br/Servicos/cnpjreva/Cnpjreva_qsa.asp';
    private const CAPMONSTER_KEY = 'af4fc5a3-1ac5-4e6d-819d-324d412a5e9d';
    private $originalDocument = null;
    private $bufferData = null;

    protected function validateAndSetCrawlerAttributes()
    {
        $this->originalDocument = Util::formatAsCNPJ($this->param['documento']);
        $this->param['documento'] = preg_replace('/[^0-9]/isu', '', $this->param['documento']);

        if (!Document::validarCnpj($this->param['documento'])) {
            throw new Exception("Critério de busca inválido.", 6);
        }
    }

    protected function start()
    {
        $retry = 5;
        $bufferInserted = false;
        while ($retry >= 0) {
            try {
                if ($this->checkBufferDataIsNotEmpty()) {
                    return $this->bufferData;
                }

                $this->getResponse(self::URL);

                $captcha = $this->solveHCaptchaWithCapMonster(self::CAPMONSTER_KEY, self::URL);

                $data = $this->getData($captcha);
                $qsa = $this->getQsa();

                $finalData = Str::encoding(array_merge($data, $qsa));

                if (!$bufferInserted) {
                    if ($this->checkBufferDataIsNotEmpty()) {
                        return $this->bufferData;
                    }
                    $this->insertDataBuffer($finalData);
                    $bufferInserted = true;
                }

                return $finalData;
            } catch (Exception $e) {
                if ($retry == 0) {
                    throw new Exception($e->getMessage(), 2);
                }

                $retry--;

                sleep(6);
            }
        }
    }

    private function checkBufferDataIsNotEmpty()
    {
        $this->verifyBuffer();
        return !empty($this->bufferData) && isset($this->bufferData['cnpj']);
    }

    private function insertDataBuffer(array $data)
    {
        $bufferBrownarrowClient =  new BufferBrownarrowClient(new Client());
        $bufferBrownarrowManager = new BufferBrownarrowManager($bufferBrownarrowClient);

        $cnpj = $this->originalDocument;

        $insertData = new InsertRequestDTO(
            $cnpj,
            "Ficha cadastral consultada da RFPJ dia " . date('d/m/Y'),
            $data
        );

        $bufferBrownarrowManager->insertBufferData($insertData);
    }

    private function getData($captcha)
    {
        $params = [
            'origem' => 'comprovante',
            'cnpj' => Document::formatCnpj($this->param['documento']),
            'h-captcha-response' => $captcha,
            'search_type' => 'cnpj',
        ];

        $response = $this->getResponse(self::URL_POST, 'POST', $params);

        if (
            preg_match(
                '/N.o\s*existe\s*no\s*Cadastro\s*de\s*Pessoas\s*Jur.dicas\s*o\s*n.mero\s*de\s*CNPJ\s*informado\s*/i',
                $response
            )
        ) {
            throw new Exception("Nenhum resultado encontrado.", 2);
        }

        if (str_contains($response, 'não é válido')) {
            throw new Exception('CNPJ não é válido', 3);
        }

        if (!str_contains($response, 'Aprovado')) {
            throw new Exception('Erro no captcha', 3);
        }

        $patterns = [
            'cnpj' => [
                "#<font[^>]*?>\s*N.*?MERO\s*DE\s*INSCRI.*?.*?O\s*<\/font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)\s*<\/b>#i",
                null
            ],
            'tipo' => [
                "#<font[^>]*?>\s*N.*?MERO\s*DE\s*INSCRI.*?.*?O\s*<\/font>\s*<br>\s*"
                    . "<font[^>]*?>\s*<b>.*?\s*<\/b><br>\s*<b>(.*?)<\/b>#i",
                null
            ],
            'data_abertura' => [
                "#<font[^>]*?>\s*DATA\s*DE\s*ABERTURA\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>#i",
                null
            ],
            'nome_empresarial' => [
                "#<font[^>]*?>\s*NOME\s*EMPRESARIAL\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>#i",
                null
            ],
            'nome_fantasia' => [
                "#<font[^>]*?>\s*T.*?TULO\s*DO\s*ESTABELECIMENTO\s*\(NOME\s*DE\s*FANTASIA\)\s*"
                    . "</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>#i",
                null
            ],
            'atividade_economica_principal' => [
                "#<font[^>]*?>\s*C.*?DIGO\s*E\s*DESCRI.*?.*?O\s*DA\s*ATIVIDADE\s*"
                    . "ECON.*?MICA\s*PRINCIPAL\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>#i",
                null
            ],
            'aAtividadeSecundaria' => [
                "#(<font[^>]*?>\s*C.*?DIGO\s*E\s*DESCRI.*?.*?O\s*DAS\s*ATIVIDADES\s*"
                    . "ECON.*?MICAS\s*SECUND.*RIAS\s*<\/font>\s*<br>\s*(?:<font[^>]*?>\s*<b>.*?<\/b>\s*<\/font>"
                    . "\s*<br>\s*)*)#i",
                null
            ],
            'natureza_juridica' => [
                "#<font[^>]*?>\s*C.*?DIGO\s*E\s*DESCRI.*?.*?O\s*DA\s*NATUREZA\s*JUR.*DICA\s*"
                    . "</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*</font>\s*<br>#i",
                null
            ],
            'logradouro' => [
                "#<font[^>]*?>\s*LOGRADOURO\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*</font>\s*<br>#i",
                null
            ],
            'numero' => [
                "#<font[^>]*?>\s*N.*?MERO\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*</font>\s*<br>#i",
                null
            ],
            'complemento' => [
                "#<font[^>]*?>\s*COMPLEMENTO\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*</font>\s*<br>#i",
                null
            ],
            'cep' => [
                "#<font[^>]*?>\s*CEP\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*</font>\s*<br>#i",
                null
            ],
            'bairro' => [
                "#<font[^>]*?>\s*BAIRRO/DISTRITO\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*" .
                    "</font>\s*<br>#i",
                null
            ],
            'municipio' => [
                "#<font[^>]*?>\s*MUNIC.*PIO\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*" .
                    "</font>\s*<br>#i",
                null
            ],
            'uf' => [
                "#<font[^>]*?>\s*UF\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>(.*?)</b>\s*</font>\s*<br>#i",
                null
            ],
            'ente_federativo_responsavel' => [
                "#<font[^>]*?>\s*ENTE\s*FEDERATIVO\s*RESPONS.*?VEL.*?\s*\(EFR\)"
                    . "\s*</font>\s*<br>\s*<font[^>]*?>\s*<b>\s*(.*?)\s*</b>\s*</font>\s*<br>#i",
                null
            ],
            'endereco_eletronico' => [
                "#<font[^>]*?>\s*ENDERE.*O\s*ELETR.*NICO\s*</font>\s*<br>\s*<font[^>]*?>"
                    . "\s*<b>\s*(.*?)\s*</b>\s*</font>\s*<br>#i",
                null
            ],
            'telefone' => [
                "#<font[^>]*?>\s*TELEFONE\s*<\/font>\s*<br>\s*<font[^>]*?>\s*<b>\s*(.*?)\s*<\/b><\/font>#i",
                null
            ],
            'situacao_cadastral' => [
                "#<font[^>]*?>\s*SITUA.*?.*?O\s*CADASTRAL\s*</font>\s*<br>\s*<font[^>]*?>\s*"
                    . "<b>\s*(.*?)\s*<\/b>\s*</font>\s*<br>#i",
                null
            ],
            'data_situacao' => [
                "#<font[^>]*?>\s*DATA\s*DA\s*SITUA.*?.*?O\s*CADASTRAL\s*</font>\s*<br>\s*"
                    . "<font[^>]*?>\s*<b>\s*(.*?)\s*<\/b>\s*</font>\s*<br>#i",
                null
            ],
            'motivo_situacao' => [
                "#<font[^>]*?>\s*MOTIVO\s*DE\s*SITUA.*?.*?O\s*CADASTRAL\s*</font>\s*<br>\s*"
                    . "<font[^>]*?>\s*<b>\s*(.*?)\s*<\/b>\s*</font>\s*<br>#i",
                null
            ],
            'situacao_especial' => [
                "#<font[^>]*?>\s*SITUA.*?.*?O\s*ESPECIAL\s*</font>\s*<br>\s*<font[^>]*?>"
                    . "\s*<b>\s*(.*?)\s*<\/b>\s*</font>\s*<br>#i",
                null
            ],
            'data_especial' => [
                "#<font[^>]*?>\s*DATA\s*DA\s*SITUA.*?.*?O\s*ESPECIAL\s*</font>\s*<br>\s*<font[^>]*?>"
                    . "\s*<b>\s*(.*?)\s*<\/b>\s*</font>\s*<br>#i",
                null
            ],
            'data_consulta' => [
                "#<font[^>]*?>\s*Emitido\s*no\s*dia\s*<b>(.*?)<\/b>\s*.*?s\s*<b>.*?<\/b>#i",
                null
            ],
            'hora_consulta' => [
                "#<font[^>]*?>\s*Emitido\s*no\s*dia\s*<b>.*<\/b>\s*.*?s\s*<b>\s*(.*?)\s*<\/b>#i",
                null
            ],
        ];

        $result = Util::parseDados($patterns, html_entity_decode($response));

        if (empty($result['cnpj'])) {
            throw new Exception("Erro ao buscar as informações na receita.", 3);
        }

        $tmp = explode(' - ', $result['atividade_economica_principal']);
        $result['cod_atividade'] = $tmp[0];
        $result['nome_atividade'] = !empty($tmp[1]) ? $tmp[1] : "";

        $tmp = explode(' - ', $result['natureza_juridica']);
        $result['cod_natureza'] = $tmp[0];
        $result['nome_natureza'] = !empty($tmp[1]) ? $tmp[1] : "";

        // Somente para Atividades Secundárias
        if (!empty($result['aAtividadeSecundaria'])) {
            if (
                preg_match_all(
                    '#<font[^>]*?>\s*<b>\s*(.*?)\s*</b>\s*<\/font>#i',
                    $result['aAtividadeSecundaria'],
                    $rs2
                )
            ) {
                foreach ($rs2[1] as $value) {
                    $tmp = explode(' - ', $value);
                    $atividade[] = array(
                        'codigo' => trim($tmp[0]),
                        'descricao' => count($tmp) > 1 ? trim($tmp[1]) : '',
                        'atividade_economica_secundaria' => trim($value),
                    );
                }
            }

            $result['aAtividadeSecundaria'] = $atividade;
        }

        $result['cnpj_uf'] = $result['cnpj'] . '|' . $result['uf'];

        return $result;
    }

    private function getQsa()
    {
        $response = $this->getResponse(self::URL_QSA);

        $patterns = [
            'initial_capital' => [
                '#<div[^>]*?><b>CAPITAL\s*SOCIAL:<\/b><\/div>\s*<div[^>]*?>\s*R\$(.*?)\([^>]*?\)<\/div>#i',
                null
            ]
        ];

        $result = Util::parseDados($patterns, $response);

        $result['initial_capital'] =  str_replace(['.', ','], ['', '.'], $result['initial_capital']);

        // Caso não tenha qsa
        if (
            preg_match(
                '#A\s*NATUREZA\s*JUR.*?DICA\s*N.*?O\s*PERMITE\s*O\s*PREENCHIMENTO\s*DO\s*QSA#',
                $response
            )
        ) {
            $result['qsa'] = [];

            return $result;
        }

        // Recorta os socios
        if (
            preg_match_all(
                "#(<\!--\s*quadro\s*de\s*s.*?cios\s*-->[\s\S]*?<\!--\s*Fim\s*Quadro\s*de\s*S.*?cios\s*-->)#i",
                $response,
                $qsaResult
            )
        ) {
            $patterns = [
                'name' => [
                    '#<div[^>]*?><b>Nome\/Nome\s*Empresarial:<\/b><\/div>\s*<div[^>]*?>(.*?)<\/div>#i',
                    null
                ],
                'qualification' => [
                    '#<div[^>]*?><b>Qualifica.*?.*?o:<\/b><\/div>\s*<div[^>]*?>(.*?)<\/div>#i',
                    null
                ],
                'representante_qualificacao' => [
                    '#<div[^>]*?><b>Qualif\.\s*Rep\.\s*Legal:<\/b><\/div>\s*<div[^>]*?>(.*?)<\/div>#i',
                    null
                ],
                'representante_legal' => [
                    '#<div[^>]*?><b>Nome\s*do\s*Repres\.\s*Legal:<\/b><\/div>\s*<div[^>]*?>(.*?)<\/div>#i',
                    null
                ],
                'pais_origem' => [
                    '#<div[^>]*?><b>Pa.*?s\s*de\s*Origem:<\/b><\/div>\s*<div[^>]*?>(.*?)<\/div>#i',
                    null
                ]
            ];

            foreach ($qsaResult[1] as $qsa) {
                $resultQsa = Util::parseDados($patterns, $qsa);
                $resultQsa['qsa'] = $resultQsa['name'] . ' - ' . $resultQsa['qualification'];
                $result['aQsa'][] = $resultQsa;
            }
        }

        return $result;
    }

    public function parseToGeneric(array $data, string $sourceName): ReceitaFederalPjGenericModel
    {
        $model = new ReceitaFederalPjGenericModel();

        $model->source = $sourceName;
        $model->cnpj = $data['cnpj'];
        $model->tipo = $data['tipo'];
        $model->data_abertura = $data['data_abertura'];
        $model->nome_empresarial = $data['nome_empresarial'];
        $model->nome_fantasia = $data['nome_fantasia'];
        $model->atividade_economica_principal = $data['atividade_economica_principal'];
        $model->natureza_juridica = $data['natureza_juridica'];
        $model->logradouro = $data['logradouro'];
        $model->numero = $data['numero'];
        $model->complemento = $data['complemento'];
        $model->cep = $data['cep'];
        $model->bairro = $data['bairro'];
        $model->municipio = $data['municipio'];
        $model->uf = $data['uf'];
        $model->ente_federativo_responsavel = $data['ente_federativo_responsavel'];
        $model->endereco_eletronico = $data['endereco_eletronico'];
        $model->telefone = $data['telefone'];
        $model->situacao_cadastral = $data['situacao_cadastral'];
        $model->data_situacao = $data['data_situacao'];
        $model->motivo_situacao = $data['motivo_situacao'];
        $model->situacao_especial = $data['situacao_especial'];
        $model->data_especial = $data['data_especial'];
        $model->data_consulta = $data['data_consulta'];
        $model->hora_consulta = $data['hora_consulta'];
        $model->cod_atividade = $data['cod_atividade'];
        $model->nome_atividade = $data['nome_atividade'];
        $model->cod_natureza = $data['cod_natureza'];
        $model->nome_natureza = $data['nome_natureza'];
        $model->cnpj_uf = $data['cnpj_uf'];
        $model->html = $data['html'] ?? '';
        $model->initial_capital = $data['initial_capital'];

        foreach ($data['aAtividadeSecundaria'] as $cnae) {
            $modelCnae = new ReceitaFederalPjGenericCnaeSecundarioModel();

            $modelCnae->codigo = $cnae['codigo'];
            $modelCnae->descricao = $cnae['descricao'];
            $modelCnae->atividade_economica_secundaria = $cnae['atividade_economica_secundaria'];

            $model->aAtividadeSecundaria[] = $modelCnae;
        }

        foreach ($data['aQsa'] as $qsa) {
            $modelQsa = new ReceitaFederalPjGenericQsaModel();

            $modelQsa->name = $qsa['name'];
            $modelQsa->qualification = $qsa['qualification'];
            $modelQsa->representante_qualificacao = $qsa['representante_qualificacao'];
            $modelQsa->representante_legal = $qsa['representante_legal'];
            $modelQsa->pais_origem = $qsa['pais_origem'];
            $modelQsa->qsa = $qsa['qsa'];

            $model->aQsa[] = $modelQsa;
        }

        return $model;
    }
}
