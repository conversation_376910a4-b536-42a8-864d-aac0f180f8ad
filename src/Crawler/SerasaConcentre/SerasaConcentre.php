<?php

namespace App\Crawler\SerasaConcentre;

use App\Crawler\Spider;
use Exception;
use App\Manager\S3\S3;
use App\Manager\S3\Bucket\StaticUplexisBucket;

class SerasaConcentre extends Spider
{
    private $production_url = 'https://sitenet43-2.serasa.com.br/Prod/consultahttps';
    private $homolog_url = 'https://mqlinuxext-2.serasa.com.br/Homologa/consultahttps';
    private $serasa_service_url = '';
    private $application_env = 'prod';
    private $start_output_position = 515;
    private $strlen_current = 115;

    protected function start()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if ($this->application_env == 'prod') {
            $this->serasa_service_url = $this->production_url;
        } else {
            $this->serasa_service_url = $this->homolog_url;
        }

        list($str_header, $str_body) = $this->splitString($this->getDados());

        do {
            $tipo = $this->checkTypeResponse($str_header);

            $endPositionOfString = $this->lookingForEndPositionOfString($str_body);

            $strlen = strlen($str_body);

            for ($i = 0; $i <= $strlen; $i += $this->strlen_current) {
                if ($i >= $endPositionOfString) {
                    break;
                }

                $str_current = substr($str_body, $i, $this->strlen_current);

                if (method_exists($this, $method_name = $this->getMethod($this->getKeyResponse($str_current)))) {
                    $codKey = $this->getCodKey($str_current);
                    $response[$codKey][] = $this->{$method_name}($str_current);
                }
            }

            if ($tipo == 'CON') {
                list($str_header, $str_body) = $this->splitString($this->getDados($str_header)); //PASSAR O CON
            }
        } while ($tipo != 'FIM');


        return self::encoding($response);
    }

    private function prepareSerasaStringContinue($string_con)
    {
        //AUTH
        $auth = str_pad($this->auth['usuario'], 8, urlencode(' '));
        $auth .= str_pad($this->auth['senha'], 8, urlencode(' '));
        $auth .= urlencode(str_pad('', 8));

        $string = $auth . urlencode($string_con);

        // T999
        $string .= 'T999';
        $string .= str_pad('', 111);

        return $string;
    }

    private function prepareSerasaString()
    {

        //AUTH
        $auth = str_pad($this->auth['usuario'], 8, urlencode(' '));
        $auth .= str_pad($this->auth['senha'], 8, urlencode(' '));
        $auth .= urlencode(str_pad('', 8));

        //B49C
        $string  = 'B49C';                                                                // 4   - FILLER - X
        $string .= str_pad('', 6);                                                        // 6   - FILLER - X
        $string .= sprintf('%015d', preg_replace("@\\D+@i", "", $this->param['cpf_cnpj'])); // 15  - NUM DOC - N
        $string .= (strlen($this->param['cpf_cnpj']) > 11 ? 'J' : 'F');                   // 1   - TIPO PESSOA - X
        $string .= str_pad('C', 6);                                                       // 6   - BASE CONS - X
        $string .= 'FI';                                                                  // 2   - MODALIDADE - X

        $string .= str_pad('0', 7);                                              // 7   - VLR CONSUL - N
        $string .= str_pad('', 12);                                             // 12  - CENTRO CUST - X
        $string .= 'S';                                                         // 1   - CODIFICADO - X
        $string .= '99';                                                          // 2   - QTD REG - N

        $string .= 'S';                                                         // 1   - CONVERSA - X
        $string .= 'INI';                                                       // 3   - FUNÇÃO - X
        $string .= 'A';                                                         // 1   - TP CONSULTA - X
        $string .= 'N';                                                         // 1   - ATUALIZA - X
        $string .= str_pad('', 18);                                             // 18  - IDENT_TERM - X
        $string .= str_pad('', 10);                                             // 10  - RESCLI - X
        $string .= str_pad('', 1);                                              // 1   - DELTS - X
        $string .= str_pad('', 1);                                              // 1   - COBRA - X
        $string .= str_pad('', 1);                                              // 1   - PASSA - X
        $string .= 'N';                                                         // 1   - CONS.COLLEC - X
        $string .= str_pad('', 57);                                             // 57  - FILLER - X
        $string .= str_pad('0', 15);                                             // 15  - CONSULTANTE - N
        $string .= str_pad('', 234);                                            // 234 - FILLER - X
        // P002
        $string .= 'P002';                                                      // 4 - TIPO_REG - X
        $string .= 'RSPU';                                                      // 4 - COD1 - X
        $string .= str_pad('0', 21);                                             // 21 - CHAVE1 - X
        $string .= str_pad('', 4);                                              // 4 - COD2 - X
        $string .= str_pad('0', 21);                                             // 21 - CHAVE2 - X
        $string .= str_pad('', 4);                                              // 4 - COD3 - X
        $string .= str_pad('0', 21);                                             // 21 - CHAVE3 - X
        $string .= str_pad('', 4);                                              // 4 - COD4 - X
        $string .= str_pad('0', 21);                                             // 21 - CHAVE4 - X
        $string .= str_pad('0', 11);                                             // 11 - FILLER - X
        // I001
        $string .= 'I001';                                                      // 4 - TIPO_REG - X
        $string .= '00';                                                        // 2 - SUB TYPE - X
        $string .= str_pad('D', 1);                                             // 1 - DETALHE - X
        $string .= str_pad('', 1);                                              // 1 - CONFIRMEI - X
        $string .= str_pad('', 1);                                              // 1 - FILLER - X
        $string .= str_pad('', 1);                                              // 1 - FILLER - X
        $string .= str_pad('', 4);                                              // 4 - FILLER - X
        $string .= str_pad('', 1);                                              // 1 - FILLER - X
        $string .= str_pad('', 4);                                              // 4 - FILLER - X
        $string .= str_pad('', 10);                                             // 10 - FILLER - X
        $string .= str_pad('', 1);                                              // 1 - FILLER - X
        $string .= str_pad('', 1);                                              // 1 - FILLER - X
        $string .= str_pad('', 2);                                              // 2 - FILLER - X
        $string .= str_pad('', 1);                                              // 1 - FILLER - X
        $string .= str_pad('', 1);                                              // 1 - FILLER - X
        $string .= str_pad('', 1);                                              // 1 - FILLER - X
        $string .= str_pad('', 2);                                              // 2 - FILLER - X
        $string .= str_pad('', 2);                                              // 2 - FILLER - X
        $string .= str_pad('', 2);                                              // 2 - FILLER - X
        $string .= str_pad('', 2);                                              // 2 - FILLER - X
        $string .= str_pad('', 2);                                              // 2 - FILLER - X
        $string .= str_pad('', 1);                                              // 1 - FILLER - X
        $string .= str_pad('', 1);                                              // 1 - FILLER - X
        $string .= str_pad('', 1);                                              // 1 - FILLER - X
        $string .= str_pad('', 1);                                              // 1 - FILLER - X
        $string .= str_pad('', 17);                                             // 17 - FILLER - X
        $string .= str_pad('', 47);                                             // 47 - FILLER - X
        $string .= str_pad('', 1);                                              // 1 - FILLER - X
        $string .= 'T999';                                                      // end of line

        return $auth . urlencode($string);
    }

    private function getDados($con = null)
    {
        $data = [];
        if (!$con) {
            $data['p'] = $this->prepareSerasaString();
        } else {
            $data['p'] = $this->prepareSerasaStringContinue($con);
        }
        $data = str_replace('+', ' ', $data);
        $str = $this->getResponseByFileGet($this->serasa_service_url, 'POST', $data);

        $this->saveStringS3($str, $data);

        return $this->checkResponse($str);

        //        $str = $this->getResponseSerasaFake();
        //        $str =  $this->checkResponse($str);
        //        return $str;
    }

    private function saveStringS3($data, $url)
    {
        $uniqid = $this->auth['usuario'] . '_' . $this->auth['senha'] . '_' . md5(uniqid(rand(), true));

        $file = preg_replace("@\\D+@i", "", $this->param['cpf_cnpj']) . '_' . $uniqid . '.txt';

        $serasa_concentre_api_request = '/tmp/serasa_concentre_api_request_' . $file;
        $serasa_concentre_api_response = '/tmp/serasa_concentre_api_response_' . $file;

        file_put_contents($serasa_concentre_api_request, $url);
        file_put_contents($serasa_concentre_api_response, $data);

        (new S3(new StaticUplexisBucket()))->save(
            "captura/serasa_concentre_api/request_" . $file,
            $serasa_concentre_api_request
        );
        (new S3(new StaticUplexisBucket()))->save(
            "captura/serasa_concentre_api/response_" . $file,
            $serasa_concentre_api_response
        );
    }

    private function splitString($str)
    {
        $str_header = substr($str, 0, $this->start_output_position);

        $str_body = substr($str, $this->start_output_position);

        return array($str_header, $str_body);
    }

    private function checkTypeResponse($str)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        return substr($str, 57, 3); // tipo de resposta FIM ou CON
    }

    private function lookingForEndPositionOfString($str)
    {
        return stripos($str, 'T999'); // procurando posição final da string
    }

    private function getMethod($key)
    {
        $key = str_replace('_', 'Underline', $key);
        return 'parse' . $key;
    }

    private function getKeyResponse($str)
    {
        $str = str_replace('_', '', $str);
        return substr(ltrim($str), 0, 6);
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (!isset($this->param['cpf_cnpj']) or empty($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro de documento inválido', 6);
        }

        if (!isset($this->auth['usuario']) or empty($this->auth['usuario'])) {
            throw new Exception('Parâmetro de login inválido', 6);
        }

        if (!isset($this->auth['senha']) or empty($this->auth['senha'])) {
            throw new Exception('Parâmetro de senha inválida', 6);
        }
    }

    private function checkResponse($response)
    {
        $this->validateLoginErrors($response);

        if (preg_match("#USUARIO\\/SENHA\\s*INVALIDO#is", $response)) {
            throw new Exception("USUARIO SENHA INVALIDO", 6);
        }

        if (preg_match("#AUTORIZACAO\\s*CANCELADA#is", $response)) {
            throw new Exception("AUTORIZACAO CANCELADA", 6);
        }

        if (preg_match("#NOVA\\s*PASSWORD\\s*INVALIDA#is", $response)) {
            throw new Exception("USUÁRIO OU SENHA FORA DO PADRÂO", 6);
        }

        if (preg_match("#INCONSISTENCIAS\\s*NOS\\s*DADOS\\s*ENVIADOS#is", $response)) {
            throw new Exception("INCONSISTENCIAS NOS DADOS ENVIADOS", 6);
        }

        if (preg_match("#USUARIO\\s*NAO\\s*AUTORIZADO#is", $response)) {
            throw new Exception("USUARIO NAO AUTORIZADO", 6);
        }

        if (preg_match("#USUARIO\\s*BLOQUEADO#is", $response)) {
            throw new Exception("USUARIO BLOQUEADO", 6);
        }

        return $response;
    }

    public static function encoding($str)
    {
        $cleanString = function ($string) {
            $string = preg_replace('/\n/', " ", $string);
            $string = preg_replace('/\s{2,}/', " ", $string);
            $string = preg_replace('/\t/', " ", $string);
            $string = trim($string);
            return $string;
        };

        if (!is_array($str) and !is_object($str)) {
            return $cleanString($str);
        }

        return array_map('self::encoding', $str);
    }

    private function parseA90000($string) // - ok
    {
        $response = [];

        $fieldSequence = array(4, 6, 32, 70);

        list(
            $response['tipo_de_registro'],
            $response['codigo'],
            $response['msg_reduzida'],
            $response['msg_completa']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI00100($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 3);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['msg']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI10000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 70, 8, 1, 8, 2);


        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['nome'],
            $response['data_confirm'],
            $response['cod_sit_cad'],
            $response['data_sit'],
            $response['cod_retorno']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI10100($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 60);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['nome_mãe']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI10200($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 2, 50);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['uf'],
            $response['municipio']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI10500($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 70);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['nome']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI11000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 8, 8, 9, 15, 30);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['data_inicial'],
            $response['data_final'],
            $response['qtde_total'],
            $response['valor'],
            $response['origem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI11001($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 8, 4, 3, 15, 4, 2, 30, 1, 8, 4, 2, 8, 6, 10);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['data_ocorr'],
            $response['num_cartor'],
            $response['valor'],
            $response['praca'],
            $response['uf'],
            $response['cidade'],
            $response['sub_judice'],
            $response['data_carta'],
            $response['filial_cnpj'],
            $response['dig_doc'],
            $response['data_inclusao'],
            $response['hora_inclus'],
            $response['chv_cadus']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI11002($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 4, 2, 2, 8, 16, 76);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['praca'],
            $response['distribuidor'],
            $response['vara'],
            $response['data'],
            $response['processo'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI12000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 8, 8, 9, 15, 30);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['data_inicial'],
            $response['data_final'],
            $response['qtde_final'],
            $response['valor'],
            $response['origem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI12001($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 8, 4, 4, 3, 15, 4, 2, 30, 1, 1, 4, 2, 8, 6, 10);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['praca'],
            $response['uf'],
            $response['cidade'],
            $response['principal'],
            $response['sub_judice'],
            $response['filial_cnpj'],
            $response['dig_doc'],
            $response['data_inclusao'],
            $response['hora_inclus'],
            $response['chv_cadus']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI12002($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 30);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['desc_natureza']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI12003($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 4, 2, 2, 8, 16, 76);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['praca'],
            $response['distribuidor'],
            $response['vara'],
            $response['data'],
            $response['processo'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI13000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 8, 8, 9, 15, 30);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['data_inicial'],
            $response['data_final'],
            $response['qtde_total'],
            $response['valor'],
            $response['origem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI13001($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 8, 2, 4, 4, 2, 30, 4, 2, 8, 6, 10, 28);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['data_ocorr'],
            $response['natureza'],
            $response['vara_civel'],
            $response['praca'],
            $response['uf'],
            $response['cidade'],
            $response['filial_cnpj'],
            $response['dig_doc'],
            $response['data_inclusao'],
            $response['hora_inclus'],
            $response['chv_cadus'],
            $response['desc_natureza']
        ) = $this->splitByLengths($string, $fieldSequence);


        return $response;
    }

    private function parseI14000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 8, 8, 9, 15, 16);


        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['data_inicial'],
            $response['data_final'],
            $response['qtde_total'],
            $response['valor'],
            $response['origem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI14001($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 8, 3, 15, 15, 4, 2, 20, 30, 1, 1, 10);


        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['data_ocorr'],
            $response['natureza'],
            $response['cnpj_origem'],
            $response['valor'],
            $response['praca'],
            $response['uf'],
            $response['nome_empr'],
            $response['cidade'],
            $response['principal'],
            $response['filler'],
            $response['chv_cadus']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI14002($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 1, 4, 2, 8, 6, 16, 30);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['sub_judice'],
            $response['filial_cnpj'],
            $response['dig_doc'],
            $response['data_inclusao'],
            $response['hora_inclus'],
            $response['contr'],
            $response['modalidade']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI14003($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 4, 2, 2, 8, 16, 76);


        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['praca'],
            $response['distribuidor'],
            $response['vara'],
            $response['data'],
            $response['processo'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI15000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 4, 2, 2, 8, 16, 76);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['data_inicial'],
            $response['data_final'],
            $response['valor_total'],
            $response['qtde_total'],
            $response['origem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI15001($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 8, 2, 4, 4, 9, 50, 5, 2, 8, 6, 10);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['data_ocorr'],
            $response['natureza'],
            $response['qualificacao'],
            $response['vara_civel'],
            $response['cnpj_empr'],
            $response['nome_empr'],
            $response['filial_cnpj'],
            $response['dig_doc'],
            $response['data_inclusao'],
            $response['hora_inclus'],
            $response['chv_cadus']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI15002($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 30);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['desc_natureza']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI16000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 8, 8, 15, 9, 16);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['data_incial'],
            $response['data_final'],
            $response['valor'],
            $response['qtde_total'],
            $response['origem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI16001($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 8, 3, 4, 9, 2, 15, 4, 2, 20, 10, 30);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['data_ocorr'],
            $response['banco'],
            $response['agencia'],
            $response['conta_ocorr'],
            $response['natureza'],
            $response['valor'],
            $response['praca'],
            $response['uf'],
            $response['nome_banco'],
            $response['num_cheque'],
            $response['cidade']
        ) = $this->splitByLengths($string, $fieldSequence);


        return $response;
    }

    private function parseI16002($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 4, 2, 8, 6, 10);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['filial_cnpj'],
            $response['dig_doc'],
            $response['data_inclusao'],
            $response['hora_inclus'],
            $response['chv_cadus']
        ) = $this->splitByLengths($string, $fieldSequence);


        return $response;
    }

    private function parseI17001($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 4, 2, 8, 6, 10);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['data_ocorr'],
            $response['banco'],
            $response['agencia'],
            $response['qtde_cheq'],
            $response['praca'],
            $response['uf'],
            $response['nome_bco'],
            $response['nome_cidade'],
            $response['natureza'],
            $response['filial_cnpj'],
            $response['dig_doc'],
            $response['data_inclusao'],
            $response['hora_inclus']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI17002($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 10);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['chv_cadus']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI22000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 8, 8, 9, 15, 1, 16);


        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['data_inicial'],
            $response['data_final'],
            $response['qtde_total'],
            $response['valor'],
            $response['tip_ocor'],
            $response['origem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI22001($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 8, 3, 15, 4, 32, 1, 16, 10, 10, 1);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['data_ocorr'],
            $response['natureza'],
            $response['valor'],
            $response['praca'],
            $response['filler'],
            $response['principal'],
            $response['contrato'],
            $response['filler'],
            $response['chv_cadus'],
            $response['tip_ocor']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI22002($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 4, 2, 8, 6, 1, 12);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['filial_cnpj'],
            $response['dig_doc'],
            $response['data_inclusao'],
            $response['hora_inclus'],
            $response['tip_ocor'],
            $response['modalidade']
        ) = $this->splitByLengths($string, $fieldSequence);


        return $response;
    }

    private function parseI22003($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 4, 2, 8, 6, 1, 12);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['docto'],
            $response['nome'],
            $response['filler'],
            $response['tip_ocor'],
            $response['cred_partic']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI22004($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 4, 2, 2, 8, 16, 76, 1);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['praca'],
            $response['distribuidor'],
            $response['vara'],
            $response['data'],
            $response['processo'],
            $response['mensagem'],
            $response['tip_ocor']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI23000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 4, 2, 2, 8, 16, 76, 1);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['data_inicial'],
            $response['data_final'],
            $response['qtde_total'],
            $response['valor'],
            $response['origem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI23001($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 8, 3, 15, 4, 2, 30, 16, 9, 10);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['data_ocorr'],
            $response['natureza'],
            $response['valor'],
            $response['praca'],
            $response['uf'],
            $response['nome_inst'],
            $response['contrato'],
            $response['cnpj_credor'],
            $response['chv_cadus']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI23002($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 30);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['desc_natur']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI23003($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 4, 2, 8, 6, 76);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['filial_cnpj'],
            $response['dig_doc'],
            $response['data_inclusao'],
            $response['hora_inclus'],
            $response['msg_subj']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI31000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 6, 5, 6, 5, 6, 5, 6, 5, 6, 5);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['mes_atual'],
            $response['qtde_atual'],
            $response['mes_atual1'],
            $response['qtde_atual1'],
            $response['mes_atual2'],
            $response['qtde_atual2'],
            $response['mes_atual3'],
            $response['qtde_atual3'],
            $response['mes_atual4'],
            $response['qtde_atual4']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI31001($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 6, 5, 6, 5, 6, 5, 6, 5, 6, 5);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['mes_atual'],
            $response['qtde_atual'],
            $response['mes_atual1'],
            $response['qtde_atual1'],
            $response['mes_atual2'],
            $response['qtde_atual2'],
            $response['mes_atual3'],
            $response['qtde_atual3'],
            $response['mes_atual4'],
            $response['qtde_atual4']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI31002($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 8, 69);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['data_cons'],
            $response['nome_empre']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI31003($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 4, 1, 4, 3, 3, 3, 3, 60);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['cod_cons'],
            $response['tipo_bloco'],
            $response['mes_anoatu'],
            $response['cons_mes_atu'],
            $response['if_mes_atu'],
            $response['cons_mes_ant_1'],
            $response['if_mes_ant_1'],
            $response['outros_meses']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI31004($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 4, 1, 8, 3, 3);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['cod_cons'],
            $response['tipo_bloco'],
            $response['data_consul'],
            $response['cons_dia'],
            $response['if_dia']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI31099($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 2, 78);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['ret_cod'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI41000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 2, 70, 35);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['cod_retorno'],
            $response['mensagem'],
            $response['doc_confere']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI41001($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 2, 70, 35);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['ind_dddfone'],
            $response['nr_fone'],
            $response['doc_confere'],
            $response['nome_ass'],
            $response['tipo_ass'],
            $response['class_ass']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI41002($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 40, 35, 21, 9);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['logradouro'],
            $response['bairro'],
            $response['cidade'],
            $response['cep']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI41099($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 2, 70);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['cod_retorno'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI42000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 2, 4, 6, 70, 8, 5);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['cod_retorno'],
            $response['cod_scoring'],
            $response['pontuacao'],
            $response['scor_mens'],
            $response['data_refer'],
            $response['percentual']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI42099($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 2, 10, 70);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['cod_retorno'],
            $response['filler'],
            $response['scor_mens']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI43000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 2, 2, 6, 20, 4, 8, 4, 10);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['num_msg'],
            $response['tot_msg'],
            $response['tip_msg'],
            $response['num_doc'],
            $response['motivo'],
            $response['dta_ocor'],
            $response['ddd'],
            $response['telefone']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI45000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 2, 2);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['seq_ere'],
            $response['cod_retorno']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI45001($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 2, 60, 6);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['seq_ere'],
            $response['logradouro'],
            $response['numero']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI45002($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 2, 40, 30);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['seq_ere'],
            $response['complem'],
            $response['bairro']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI45003($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 2, 30, 8, 2, 4, 9);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['seq_ere'],
            $response['municipio'],
            $response['cep'],
            $response['uf'],
            $response['ddd'],
            $response['telefone']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI45099($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 2, 2);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['seq_ere'],
            $response['cod_retorno']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI47000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 2, 17, 78);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['cod_retorno'],
            $response['valor_limite'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI47099($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 2, 17, 78);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['cod_retorno'],
            $response['filler'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI48000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 1, 78);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['class'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI48099($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 2, 78);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['ret_code'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI49000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 4, 75);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['pontuacao'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI49001($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 75);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI49002($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 75);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI49003($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 75);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI49099($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 4, 75);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['filler'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI50000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 75, 18);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['mensagem'],
            $response['valor']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI50099($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 75);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI51000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 9, 80);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['ra'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI51099($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 9, 80);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['filler'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI52000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 70, 14, 4, 8, 8, 2);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['nome'],
            $response['cnpj'],
            $response['nivel'],
            $response['data_desde'],
            $response['data_atual'],
            $response['uf']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI52099($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 80);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI53000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 1, 14, 60, 4);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['tipo_pessoa'],
            $response['cnpj_cpf'],
            $response['nome'],
            $response['capital']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI53099($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 80);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI54000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 1, 14, 60, 20);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['tipo_pessoa'],
            $response['cnpj_cpf'],
            $response['nome'],
            $response['cargo']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI54099($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 80);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI55000($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 70, 14, 20, 2);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['nome'],
            $response['cnpj'],
            $response['cidade'],
            $response['uf']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI55001($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 1, 70, 14, 1, 4, 12);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['tipo_pessoa'],
            $response['nome'],
            $response['cnpj'],
            $response['vinculo'],
            $response['per_capital'],
            $response['desc_vinc']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseI55099($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 80);

        list(
            $response['tipo_de_registro'],
            $response['subtipo'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseF900UnderlineSPC2Underline00($string)
    {
        $response = [];

        $fieldSequence = array(4, 4, 3, 70);

        list(
            $response['tipo_de_registro'],
            $response['cod_cons'],
            $response['subtipo'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseF900UnderlineSPC2Underline01($string)
    {
        $response = [];

        $fieldSequence = array(4, 4, 3, 8, 8, 1, 30, 15);

        list(
            $response['tipo_de_registro'],
            $response['cod_feat'],
            $response['subtipo'],
            $response['data_inc'],
            $response['data_ven'],
            $response['tp_oper'],
            $response['num_con'],
            $response['vlr_deb']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseF900UnderlineSPC2Underline02($string)
    {
        $response = [];

        $fieldSequence = array(4, 4, 3, 60, 30, 2);

        list(
            $response['tipo_de_registro'],
            $response['cod_feat'],
            $response['subtipo'],
            $response['nom_ass'],
            $response['cidade'],
            $response['uf']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseF900UnderlineSPC2Underline03($string)
    {
        $response = [];

        $fieldSequence = array(4, 4, 3, 60, 30);

        list(
            $response['tipo_de_registro'],
            $response['cod_feat'],
            $response['subtipo'],
            $response['filler'],
            $response['origem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseF900UnderlineSPC2Underline07($string)
    {
        $response = [];

        $fieldSequence = array(4, 4, 3, 3, 6, 6, 9, 60, 10);

        list(
            $response['tipo_de_registro'],
            $response['cod_feat'],
            $response['subtipo'],
            $response['qtde_total'],
            $response['data_men'],
            $response['data_mai'],
            $response['vlr_ultima'],
            $response['descricao']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseF900UnderlineSPC6Underline00($string)
    {
        $response = [];

        $fieldSequence = array(4, 4, 3, 70);

        list(
            $response['tipo_de_registro'],
            $response['cod_feat'],
            $response['subtipo'],
            $response['mensagem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseF900UnderlineSPC6Underline04($string)
    {
        $response = [];

        $fieldSequence = array(4, 4, 3, 8, 4, 60);

        list(
            $response['tipo_de_registro'],
            $response['cod_feat'],
            $response['subtipo'],
            $response['data_con'],
            $response['hor_con'],
            $response['nom_ass']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseF900UnderlineSPC6Underline05($string)
    {
        $response = [];

        $fieldSequence = array(4, 4, 3, 30, 2, 30);

        list(
            $response['tipo_de_registro'],
            $response['cod_feat'],
            $response['subtipo'],
            $response['cidade'],
            $response['uf'],
            $response['origem']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function parseF900UnderlineSPC6Underline06($string)
    {
        $response = [];

        $fieldSequence = array(4, 4, 3, 3, 6, 3, 3, 3, 3);

        list(
            $response['tipo_de_registro'],
            $response['cod_feat'],
            $response['subtipo'],
            $response['qtda_total'],
            $response['data_total'],
            $response['qtda_atual'],
            $response['qtda_mes1'],
            $response['qtda_mes2'],
            $response['qtda_mes3']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    private function splitByLengths($string, $arrayLengths)
    {
        $output = [];
        foreach ($arrayLengths as $oneLength) {
            $output[] = substr($string, 0, $oneLength);
            $string = substr($string, $oneLength);
        }

        return ($output);
    }

    private function getCodKey($key)
    {
        $key = substr(ltrim($key), 0, 4);

        $cod = array(
            'A900' => 'mensagem',
            'I001' => 'mensagem',
            'I100' => 'identificacao',
            'I101' => 'identificacao',
            'I102' => 'identificacao',
            'I105' => 'identificacao',
            'I110' => 'protestos',
            'I120' => 'acoes_judiciais',
            'I130' => 'falencias_concordatas',
            'I140' => 'restricoes_financeiras',
            'I150' => 'participacao_em_insucesso',
            'I160' => 'achei',
            'I170' => 'cheques_sem_fundo',
            'I220' => 'dividas_nao_pagas',
            'I230' => 'convem_devedores',
            'I310' => 'registros_de_consultas',
            'I410' => 'confirmacao_de_telefone',
            'I420' => 'concentre_scoring',
            'I430' => 'mensagem_alerta',
            'I450' => 'localizador_de_enderecos',
            'I470' => 'limite_de_credito',
            'I480' => 'alert_scoring',
            'I490' => 'alerta_identidade',
            'I500' => 'faturamento_presumido',
            'I510' => 'renda_presumida',
            'I520' => 'participacao_societaria',
            'I530' => 'socios_e_acionistas',
            'I540' => 'administradores',
            'I550' => 'participacoes_em_outras_empresas',
            'F900' => 'anotacoes_spc',
            'T999' => 'mensagem'
        );

        if (!isset($cod[$key])) {
            return '';
        }

        return $cod[$key];
    }

    private function getResponseSerasaFake()
    {
        return file_get_contents(__DIR__ . '/SerasaFakeResponse.txt');
    }
}
