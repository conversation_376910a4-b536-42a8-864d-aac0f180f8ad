<?php

namespace App\Crawler\EscriturasPublicasDF;

use App\Crawler\EscriturasPublicasDF\Models\EscrituraPublicaDFCartorioModel;
use App\Crawler\EscriturasPublicasDF\Models\EscrituraPublicaDFModel;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Util;
use Exception;

class EscriturasPublicasDF extends Spider
{
    private const BASE_URL = 'https://cache-internet.tjdft.jus.br/cgi-bin/tjcgi1';
    private $cpfName;
    private $session;
    private $escrituras = [];


    public function start()
    {
        $this->setProxy();
        $data = $this->makeRequest();
        $this->parseData($data);
        return $this->escrituras;
    }

    /**
     * Método para fazer a requisição da fonte
     *
     * <AUTHOR>
     * <AUTHOR>
     */
    private function makeRequest()
    {
        $params = [
            'NXTPGM' => 'tjhtml101',
            'SELECAO' => $this->session,
            'CHAVE' => $this->cpfName,
            'CIRC' => 'ZZ',
            'command' => 'Consultar',
            'ORIGEM' => 'INTER',
        ];

        $params = http_build_query($params);

        $result = $this->getResponse(self::BASE_URL . '?' . $params);
        $result = preg_replace('/([\n\t\r]+)/', '', $result);

        return $result;
    }

    /**
     * Método para pegar todas as informações de cada cartório
     *
     * <AUTHOR> Pereira
     * <AUTHOR> Guilherme
     */
    private function parseData($data)
    {
        if (preg_match('/>0 escritura\(s\) localizada\(s\)/', $data)) {
            throw new Exception('Sem resultados encontrados.', 2);
        }

        preg_match_all('/<FONT FACE="Arial,Helvetica" COLOR="#6A7FBF">([\S\s]*?)<BR>/im', $data, $cartorios);
        foreach ($cartorios[1] as $cartorio) {
            $this->parseDataCartorio($cartorio);
        }
    }

    /**
     * Método para separar o titulo do cartório e os itens do cartório
     *
     * <AUTHOR> Pereira
     * <AUTHOR> Guilherme
     */
    private function parseDataCartorio($cartorio)
    {
        $patternCartorio = [];
        $patternItemCartorio = [];

        preg_match('/<a name="([\s\S]*?)">/', $cartorio, $titulo);

        $patternCartorio['cartorioNome'] = utf8_encode($titulo[1]);

        preg_match_all('/<UL>([\s\S]*?)<\/UL>/', $cartorio, $itensCartorio);

        foreach ($itensCartorio[1] as $item) {
            $patternItemCartorio = [
                'tipo' => ['@<B>Tipo do ato:\s([\s\S]*?)<\/B>@', null],
                'nome' => ['@<TD ALIGN=LEFT valign=top width=350>([\s\S]*?)<\/TD>@'],
                'dataAto' => ['@Data\sdo\sAto:\s<\/TD><TD ALIGN=LEFT valign=top width=150>([\s\S]*?)<\/TD>@'],
                'numeroLivro' => ['@mero\sdo\sLivro : <\/TD><TD ALIGN=LEFT valign=top width=150>([\s\S]*?)<\/TD>@'],
                'folha' => ['@<TD\s.*?>Folha\s?:\s?<\/TD><TD.*?>(\d+)<\/TD>@']
            ];

            $data[] = Util::parseDados($patternItemCartorio, $item);
        }

        $patternCartorio['cartorioItens'] = $data;
        $this->finalParse($patternCartorio);
    }

    /**
     * Método para tratar os itens de cada cartório utilizando o Model
     *
     * <AUTHOR> Pereira
     * <AUTHOR> Guilherme
     */
    private function parseDataCartorioItens($cartorioItens)
    {
        $result = [];
        foreach ($cartorioItens as $item) {
            $escrituraPublicaDFCartorioModel = new EscrituraPublicaDFCartorioModel();
            foreach ($item as $key => $value) {
                $escrituraPublicaDFCartorioModel->$key = $value;
            }
            $result[] = $escrituraPublicaDFCartorioModel;
        }

        return $result;
    }

    /**
     * Método que retornará os dados finais devidamente tratados
     *
     * <AUTHOR> Pereira
     * <AUTHOR> Guilherme
     */
    private function finalParse($result)
    {
        $escrituraPublicaDFModel = new EscrituraPublicaDFModel();
        $escrituraPublicaDFModel->cartorioNome = $result['cartorioNome'];
        $escrituraPublicaDFModel->cartorioItens = $this->parseDataCartorioItens($result['cartorioItens']);

        $this->escrituras[] = $escrituraPublicaDFModel;
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty(trim($this->param['cpf_name']))) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        $this->session = 15;

        if (is_numeric(Document::removeMask($this->param['cpf_name']))) {
            $this->session = 14;
        }

        $this->cpfName = trim($this->param['cpf_name']);

        if (is_numeric(Document::removeMask($this->cpfName))) {
            $this->cpfName = Document::removeMask($this->cpfName);
        }
    }
}
