<?php

namespace App\Crawler\CertidaoCeatTrtRJ;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

class CertidaoCeatTrtRJ extends Spider
{
    private $cpfCnpj = '';
    private $cpf = '';
    private $cnpj = '';
    private $pdf = '';
    private $tipoPessoa = '';
    private $tokenCaptcha = '';
    private const BASE_URL = 'https://ceat.trt1.jus.br/certidao/feitosTrabalhistas/aba1.emissao.htm';
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const CERTIDAO_S3_PATH = 'captura/certidao_ceat_trt1_rj/';

    public function start()
    {
        $this->setProxy();

        $this->setCurlOpt([
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::CERTIDAO_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;

        $html = $this->getResponse(self::BASE_URL, 'GET');
        preg_match('/State"\svalue="([\s\S]*?)"/', $html, $viewState);
        preg_match('/sitekey:"([\s\S]+?)"/', $html, $this->tokenCaptcha);

        $retry = 3;
        while ($retry >= 0) {
            try {
                $this->makeRequest($viewState[1]);
                if (!empty($this->cpf)) {
                    $htmlName = $this->makeRequestNameCPF($viewState[1]);

                    $this->checkSiteErrors($htmlName);

                    $pdf = $this->makeRequestPdfCpf($viewState[1], $htmlName);
                } else {
                    $htmlName = $this->makeRequestNameCNPJ($viewState[1]);

                    $this->checkSiteErrors($htmlName);

                    $pdf = $this->makeRequestPdfCNPJ($viewState[1], $htmlName);
                }

                $text = $this->savePdfAndReturnText($pdf);

                preg_match('/(CERTIDÃO\s(NEGATIVA|POSITIVA))/', $text, $statusCertidao);
                $text = preg_replace('/PODER JUDICIÁRIO[\s\S]*?CO\)\\n/m', '', $text);
                $text = preg_replace('/Página[\s\S]*?TRABALHISTAS/', '', $text);
                $text = preg_replace('/\s+v\.1\.0\s\(sapweb\)[\s\S]*/', '', $text);
                $text = preg_replace('/\n[\s\S]*?CERTIDÃO[\s\S]*?\\n\n/', '', $text);

                $data = [
                    'statusCertidao' => $statusCertidao[2],
                    'text' => $text,
                    'pdf' => $this->pdf
                ];

                return $data;
            } catch (Exception $e) {
                if ($retry == 0) {
                    if (preg_match('/site da fonte/is', $e->getMessage())) {
                        throw new Exception($e->getMessage(), 6);
                    }

                    throw new Exception("Erro ao capturar as informações da página", 3);
                }

                $retry--;
            }
        }
    }

    /**
     * Resolve o captcha e seta a resposta
     * <AUTHOR> Pereira
     * @return string
     */
    private function resolveRecaptcha(): string
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $retry = 3;

        do {
            if (!empty($this->tokenCaptcha)) {
                return $this->solveReCaptcha($this->tokenCaptcha[1], self::BASE_URL);
            }

            $retry--;
        } while ($retry > 0);

        throw new Exception("Erro ao localizar dados do captcha na página.", 3);
    }

    /**
     * Primeira requisição da página.
     * <AUTHOR> Pereira
     * @param string $viewState
     * @return void
     */
    private function makeRequest($viewState)
    {
        $params = [
            'javax.faces.partial.ajax' => true,
            'javax.faces.source' => 'form:tipoPessoa',
            'javax.faces.partial.execute' => 'form:tipoPessoa',
            'javax.faces.partial.render' => 'form:tableDocumentos form:nomeConsulta 
            form:painelNomeInformado form:textoVariacao',
            'javax.faces.behavior.event' => 'change',
            'javax.faces.partial.event' => 'change',
            'form' => 'form',
            'form:tipoPessoa' => $this->tipoPessoa,
            'form:inputCPF' => '',
            'form:nomeReceitaCPF' => '',
            'form:nomeConsulta' => '',
            'g-recaptcha-response' => '',
            'javax.faces.ViewState' => $viewState,
        ];

        $this->getResponse(self::BASE_URL, 'POST', $params);
    }

    /**
     * Captura o nome da pessoa jurídica quando for CNPJ.
     * <AUTHOR> Pereira
     * @param string $viewState
     * @return string $htmlName
     */
    private function makeRequestNameCNPJ($viewState)
    {
        $params = [
            'javax.faces.partial.ajax' => true,
            'javax.faces.source' => 'form:inputCNPJ',
            'javax.faces.partial.execute' => 'form:inputCNPJ',
            'javax.faces.partial.render' => 'form:nomeConsulta form:nomeReceitaCPF 
            form:nomeReceitaCNPJ form:botaoConsultar messages',
            'javax.faces.behavior.event' => 'change',
            'javax.faces.partial.event' => 'change',
            'form' => 'form',
            'form:tipoPessoa' => $this->tipoPessoa,
            'form:inputCNPJ' => $this->cnpj,
            'form:nomeReceitaCPF' => '',
            'form:nomeConsulta' => '',
            'g-recaptcha-response' => '',
            'javax.faces.ViewState' => $viewState,
        ];

        $htmlName = $this->getResponse(self::BASE_URL, 'POST', $params);

        return $htmlName;
    }

    /**
     * Captura a certidão da requisição em pdf quando for CNPJ.
     * <AUTHOR> Pereira
     * @param string $viewState
     * @param string $html
     * @return $pdf
     */
    private function makeRequestPdfCNPJ($viewState, $html)
    {

        preg_match('/value="([\s\S]*?)"/', $html, $name);

        $params = [
            'form' => 'form',
            'form:tipoPessoa' => $this->tipoPessoa,
            'form:inputCNPJ' => $this->cnpj,
            'form:nomeReceitaCNPJ' => $name[1],
            'form:nomeConsulta' => '',
            'g-recaptcha-response' => $this->resolveRecaptcha(),
            'form:botaoConsultar' =>  '',
            'javax.faces.ViewState' => $viewState,
        ];

        $pdf = $this->getResponse(self::BASE_URL, 'POST', $params);
        return $pdf;
    }

    /**
     * Captura o nome da pessoa física quando for CPF.
     * <AUTHOR> Pereira
     * @param string $viewState
     * @return string $htmlName
     */
    private function makeRequestNameCPF($viewState)
    {
        $params = [
            'javax.faces.partial.ajax' => true,
            'javax.faces.source' => 'form:inputCPF',
            'javax.faces.partial.execute' => 'form:inputCPF',
            'javax.faces.partial.render' => 'form:nomeConsulta form:nomeReceitaCPF 
            form:nomeReceitaCNPJ form:botaoConsultar messages',
            'javax.faces.behavior.event' => 'change',
            'javax.faces.partial.event' => 'change',
            'form' => 'form',
            'form:tipoPessoa' => $this->tipoPessoa,
            'form:inputCPF' => $this->cpf,
            'form:nomeReceitaCPF' => '',
            'form:nomeConsulta' => '',
            'g-recaptcha-response' => '',
            'javax.faces.ViewState' => $viewState,
        ];

        $htmlName = $this->getResponse(self::BASE_URL, 'POST', $params);

        return $htmlName;
    }

    /**
     * Captura a certidão da requisição em pdf quando for CPF.
     * <AUTHOR> Pereira
     * @param string $viewState
     * @param string $html
     * @return $pdf
     */
    private function makeRequestPdfCpf($viewState, $html)
    {

        preg_match('/value="([\s\S]*?)"/', $html, $name);

        $params = [
            'form' => 'form',
            'form:tipoPessoa' => $this->tipoPessoa,
            'form:inputCPF' => $this->cpf,
            'form:nomeReceitaCPF' => $name[1],
            'form:nomeConsulta' => '',
            'g-recaptcha-response' => $this->resolveRecaptcha(),
            'form:botaoConsultar' =>  '',
            'javax.faces.ViewState' => $viewState,
        ];

        $pdf = $this->getResponse(self::BASE_URL, 'POST', $params);
        return $pdf;
    }

    /**
     * Retorna o texto do PDF
     * <AUTHOR> Pereira
     * @return string
     */
    private function savePdfAndReturnText($pdf)
    {
        file_put_contents($this->certificateLocalPath, $pdf);

        $text = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'layout',
            'nopgbrk'
        ]);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        $this->pdf = $this->certificateUrl;

        return $text;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->cpfCnpj = trim($this->param['cpf_cnpj']);

        if (!Document::validarCpfOuCnpj($this->cpfCnpj)) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        if (Document::validarCnpj($this->cpfCnpj)) {
            $this->cnpj = Document::formatCnpj($this->cpfCnpj);
            $this->tipoPessoa = 'J';
        } elseif (Document::validarCpf($this->cpfCnpj)) {
            $this->cpf = Document::formatCpf($this->cpfCnpj);
            $this->tipoPessoa = 'F';
        }
    }

    /**
     * Lança uma exceção com a mensagem de erro do site.
     * <AUTHOR> Medeiros
     * @param string $html
     * @return void
     */
    private function checkSiteErrors(string $html): void
    {
        $errorValidationPattern = '\<span class=\"ui-messages-error-summary\">(.*?)\<\/span\>';

        if (preg_match("/$errorValidationPattern/is", $html, $errorOutput)) {
            if (count($errorOutput) > 1) {
                throw new Exception("O site da fonte retornou um erro interno: " . $errorOutput[1]);
            }
        }
    }
}
