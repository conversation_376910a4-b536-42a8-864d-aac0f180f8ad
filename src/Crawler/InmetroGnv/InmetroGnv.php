<?php

namespace App\Crawler\InmetroGnv;

use App\Crawler\Spider;
use App\Factory\PostgresDB;
use Exception;

class InmetroGnv extends Spider
{
    private $criterio;

    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['criterio'])) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }
        $this->criterio = $this->param['criterio'];
    }

    protected function start()
    {
        $result = $this->searchByName();

        if (empty($result)) {
            throw new Exception('Nada encontrado', 2);
        }

        return $result;
    }

    /**
     * Procura o criterio no banco de dados
     *
     * <AUTHOR> <<EMAIL>>
     * @return array
     */
    private function searchByName()
    {
        try {
            $db = new PostgresDB();
            $data = $db->connectCaptura()
                ->select('*')
                ->from('common.inmetro_gnv')
                ->where('nome_oficina ilike ?')
                ->setParameter(0, "%$this->criterio%")
                ->execute()
                ->fetchAll();
            $db->disconnect();
        } catch (Exception $e) {
            echo $e->getMessage() . PHP_EOL;
            throw new Exception('Não foi possível fazer a busca no banco de dados.', 3);
        }

        return $data;
    }
}
