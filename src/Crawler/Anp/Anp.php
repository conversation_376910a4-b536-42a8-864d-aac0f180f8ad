<?php

namespace App\Crawler\Anp;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Str;
use Exception;

class Anp extends Spider
{
    private const BASE_URL = 'https://cpl.anp.gov.br/anp-cpl-web';
    private const URL_FORM = '/public/simp/consulta-postos/consulta.xhtml';
    private $cpfCnpj;


    protected function validateAndSetCrawlerAttributes()
    {
        $this->cpfCnpj = trim($this->param['cpf_cnpj']);

        if (!Document::validarCpfOuCnpj($this->cpfCnpj)) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        $this->cpfCnpj = Document::formatCpfOrCnpj($this->cpfCnpj);
    }

    protected function start()
    {
        $this->html = $this->getResponse(self::BASE_URL . self::URL_FORM);
        preg_match("/dswid=(.*?)'/", $this->html, $session);

        $this->clientWindow = $session[1];
        $response = $this->makeRequest();

        if (empty($response)) {
            throw new Exception('Não foi possível capturar as informações, tente reprocessar!', 3);
        }

        $data[] = $this->finalParse($response);

        return $data;
    }

    private function makeRequest($isLink = false, $response = '')
    {
        $captcha = $this->solveCaptcha();
        $viewState = $isLink ? $this->getViewState($response, $isLink) : $this->getViewState($this->html);

        $headers = [
            'X-Requested-With' => 'XMLHttpRequest',
            'Referer' => self::BASE_URL . self::URL_FORM,
            'Faces-Request' => 'partial/ajax'
        ];

        $response = $this->getResponse(
            self::BASE_URL . self::URL_FORM,
            'POST',
            $this->getDadosRequest($captcha, $viewState, $isLink),
            $headers
        );

        if (preg_match('/Posto não encontrado/u', $response)) {
            throw new Exception('Sem resultados!', 2);
        }

        if (
            preg_match('/Ocorreu\sum\serro\sinesperado\sna\saplicação/u', $response)
            || preg_match('/Prezado\susuário,\ssua\ssessão\sexpirou/u', $response)
        ) {
            $response = $this->makeRequest();
        }

        if (preg_match('/<!\[CDATA\[<span\s+id=\W{1}frmConsulta/', $response) && !$isLink) {
            $response = $this->makeRequest(true, $response);
        }

        if (
            preg_match('/<!\[CDATA\[<span\s+id=\W{1}growl-info/', $response)
        ) {
            $response = Str::cleanString($response);
        }

        return $response;
    }

    /**
     * Alteração das expressões reguçares para se encaixarem de acrodo com a PSR
     * regex para captura dos equipamentos
     *
     * @return array
     * <AUTHOR>
     *
     * <AUTHOR> Alves 09/08/2019
     *
     * @param  array  $dado
     *
     * @param  string  $html
     *
     * @version 1.0.0
     *
     */
    private function finalParse($html)
    {
        //regex para captura dos dados
        $situacaoPattern = '/Posto<\/label>\s+<\/h2>\s+<h3>([\s\S].*?)<\/h3>/isu';
        $cpfCnpjPattern = '/CNPJ<\/label>([\s\S].*?)<\/span>/isu';
        $razaoSocialPattern = '/Raz.o\s+Social<\/label>([\s\S].*?)<\/span>/isu';
        $nomeFantasiaPattern = '/Nome\s+Fantasia<\/label>([\s\S].*?)<\/span>/isu';
        $enderecoPattern = '/Endere.o<\/label>([\s\S].*?)<\/span>/isu';
        $complementoPattern = '/Complemento<\/label>([\s\S].*?)<\/span>/isu';
        $bairroPattern = '/Bairro<\/label>([\s\S].*?)<\/span>/isu';
        $municipoUfPattern = '/Munic.pio\/UF<\/label>([\s\S].*?)<\/span>/isu';
        $cepPattern = '/cep<\/label>([\s\S].*?)<\/span>/isu';
        $numeroDespachoPattern = '/N.mero\sDespacho<\/label>([\s\S].*?)<\/span>/isu';
        $dataPublicacaoPattern = '/Data\sda\sPublicação<\/label>([\s\S].*?)<\/span>/isu';
        $tipoPostoPattern = '/Tipo\s+de\s+Posto<\/label>([\s\S].*?)<\/span>/isu';
        $allProductsPatter = '/class="centralizar">([a-zA-Zà-úÀ-Ú0-9\s\-.]+)/is';
        $bandeirapattern = '/Bandeira\/In.cio<\/label>([\s\S].*?)<\/span>/isu';
        $sociosPattern = '/Sócios<\/label><\/td><td(.*?)>([\s\S]*?)<\/td>/isu';

        preg_match($situacaoPattern, $html, $situacao);
        preg_match($cpfCnpjPattern, $html, $cnpj);
        preg_match($razaoSocialPattern, $html, $razaoSocial);
        preg_match($nomeFantasiaPattern, $html, $nomeFantasia);
        preg_match($enderecoPattern, $html, $endereco);
        preg_match($complementoPattern, $html, $complemento);
        preg_match($bairroPattern, $html, $bairro);
        preg_match($municipoUfPattern, $html, $municipioUf);
        preg_match($cepPattern, $html, $cep);
        preg_match($numeroDespachoPattern, $html, $numerodespacho);
        preg_match($dataPublicacaoPattern, $html, $dataPublicacao);
        preg_match($bandeirapattern, $html, $bandeira);
        preg_match($tipoPostoPattern, $html, $tipoPosto);
        preg_match_all($allProductsPatter, $html, $allProducts);
        preg_match_all($sociosPattern, $html, $socios);

        $equipamentos = $this->setProducts(array_chunk($allProducts[1], 3, false));
        $listaSocios = explode('</span><br />', $socios[2][0]);
        $listaSocios = Str::stripTagsRecursive($listaSocios);

        $data = [
            'cnpj_cpf' => ($cnpj[1]),
            'razao_social' => ($razaoSocial[1]),
            'nome_fantasia' => ($nomeFantasia[1]),
            'endereco' => ($endereco[1]),
            'complemento' => ($complemento[1]),
            'bairro' => ($bairro[1]),
            'municipio_uf' => ($municipioUf[1]),
            'cep' => ($cep[1]),
            'situacao' => $situacao[1],
            'numero_despacho' => ($numerodespacho[1]),
            'data_publicacao' => ($dataPublicacao[1]),
            'tipo_do_posto' => ($tipoPosto[1]),
            'equipamentos' => $equipamentos ?? [],
            'bandeira_inicio' => ($bandeira[1]),
            'socios' => array_filter($listaSocios) ?? []
        ];

        return Str::stripTagsRecursive($data);
    }

    private function getViewState($html, $isLink = false)
    {
        if (!$isLink) {
            $pattern = '/ViewState:0"\svalue="(.*?)"/is';
        } else {
            $pattern = '/ViewState:0"><!\[CDATA\[(.*?)]]/is';
        }
        if (!preg_match($pattern, $html, $view)) {
            throw new Exception('Erro ao localizar viewState da página', 2);
        }

        return $view[1];
    }

    private function solveCaptcha()
    {
        $captchaUrl = self::BASE_URL . '/simpleCaptcha.png';
        $this->getImageAndBreakCaptcha($captchaUrl);
        return $this->captcha;
    }


    private function getDadosRequest($captcha, $viewState, $isLink = false)
    {
        $params = [
            'javax.faces.partial.ajax' => true,
            'javax.faces.source' => 'frmConsulta:buttonConsulta',
            'javax.faces.partial.execute' => '@all',
            'javax.faces.partial.render' =>
                'frmConsulta:painel_Resultado_Consulta frmConsulta:captcha-panel growl-info',
            'frmConsulta:buttonConsulta' => 'frmConsulta:buttonConsulta',
            'frmConsulta' => 'frmConsulta',
            'frmConsulta:cnpj:inputText' => $this->cpfCnpj,
            'frmConsulta:nomePostoinputText' => '',
            'frmConsulta:estado:selectOneMenu' => 'Selecione...',
            'frmConsulta:bandeira:selectOneMenu' => 'Selecione...',
            'frmConsulta:combustivel:selectOneMenu' => 'Selecione...',
            'frmConsulta:tipoposto:selectOneMenu' => 'Selecione...',
            'frmConsulta:CaptchaID:inputText' => $captcha,
            'javax.faces.ViewState' => $viewState,
            'javax.faces.ClientWindow' => $this->clientWindow,
        ];

        if ($isLink) {
            $params["javax.faces.source"] = "frmConsulta:postos-table:0:botao_command_link_tabela";
            $params["javax.faces.partial.render"] = "modalPosto";
            $params["frmConsulta:postos-table:0:botao_command_link_tabela"] =
                "frmConsulta:postos-table:0:botao_command_link_tabela";
            $params["frmConsulta:CaptchaID:inputText"] = "";
            $params["javax.faces.ViewState"] = $viewState;
        }
        return $params;
    }

    private function setProducts($data)
    {
        $products = [];
        $tacagens = [];
        $bicos = [];
        foreach ($data as $product) {
            $products[] = $product[0];
            $tacagens[] = $product[1];
            $bicos[] = $product[2];
        }
        return [
            'produtos' => $products,
            'tancagem' => $tacagens,
            'bicos' => $bicos
        ];
    }
}
