<?php

namespace App\Crawler\MinisterioPublicoTrabalho;

use App\Crawler\Spider;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Symfony\Component\DomCrawler\Crawler;
use Exception;

class MinisterioPublicoTrabalho extends Spider
{
    private const BASE_URL = "https://mpt.mp.br/pgt/areas-de-atuacao/conaete";
    private const URL_BUSCA = "https://mpt.mp.br/@@search?";

    private $criterio;
    private $limite;
    private $count_results = 0;
    private $offset = 0;
    private $data = [];

    public function start()
    {
        $this->getData();

        if ($this->count_results == 0) {
            throw new Exception("Nenhum resultado encontrado.", 2);
        }
        return $this->data;
    }

    private function getData()
    {
        /* Esse laço verifica o numero de resultados e percorre as páginas do site */
        do {
            $this->searchData();
            $this->offset += 10;
        } while ($this->count_results < $this->limite);
    }


    /** Busca resultados na página
     *
     * @throws \Exception
     */
    private function searchData()
    {
        $criterio = urlencode($this->criterio);

        $urlString =
            "https://mpt.mp.br/@@search?src=777&" .
            "advanced_search=False&" .
            "created.query:date:list:record=1970/01/02%2000%3A00%3A00%20GMT%2B0&" .
            "created.range:record=min&" .
            "pt_toggle=%23&" .
            "sort_on=&" .
            "SearchableText={$criterio}&" .
            "sort_order=&" .
            "b_start:int={$this->offset}&" .
            "portal_type:list=News%20Item&" .
            "portal_type:list=documento_corregedoria&" .
            "portal_type:list=arquivo_campanha&" .
            "portal_type:list=publicacao&" .
            "portal_type:list=campanha_coordenadoria&" .
            "portal_type:list=pagina-3-nivel&" .
            "portal_type:list=imagem_galeria&" .
            "portal_type:list=File&" .
            "portal_type:list=documento_indicador_apge&" .
            "portal_type:list=Folder&" .
            "portal_type:list=Document&" .
            "portal_type:list=documento_ouvidoria&" .
            "portal_type:list=Image&" .
            "portal_type:list=legislacao";

        $url = (self::URL_BUSCA . $urlString);

        $response = $this->getResponse($url);
        $this->parseData($response);
    }

    /** Parseia os dados da página pegando apenas notícias
     *
     * <AUTHOR> Santos - 14 jul. 22
     *
     * @param  string  $html
     */
    private function parseData(string $html)
    {
        $crawler = new Crawler($html);
        $numberResults = $crawler->filter('#search-results > .searchResults > li')->count();

        if ($numberResults > 0) {
            for ($x = 0; $x < $numberResults; $x++) {
                $title = $crawler->filter('#search-results > .searchResults > li')
                    ->eq($x)
                    ->children('.result-title')->text();

                $link = $crawler->filter('#search-results > .searchResults > li')
                    ->eq($x)
                    ->filter('a')->attr('href');

                $subtitle = $crawler->filter('#search-results > .searchResults > li')
                    ->eq($x)
                    ->html();

                preg_match('/discreet\scroppedDescription">(.*?)<\/p>/isu', $subtitle, $matches);

                $this->data[] = [
                    'title' => $title,
                    'link' => $link,
                    'subtitle' => $matches[1] ?? ''
                ];

                $this->count_results++;

                if ($this->limite == $this->count_results) {
                    break;
                }
            }
        } else {
            $this->limite = $this->count_results;
        }
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->criterio = trim($this->param['criterio']);
        $this->limite = $this->param['limite'] ?? 10;

        if (empty($this->criterio)) {
            throw new Exception('Parâmetro de critério inválido', 1);
        }
    }
}
