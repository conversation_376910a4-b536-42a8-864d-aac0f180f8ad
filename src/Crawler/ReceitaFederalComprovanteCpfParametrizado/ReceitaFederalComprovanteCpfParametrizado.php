<?php

namespace App\Crawler\ReceitaFederalComprovanteCpfParametrizado;

use App\Helper\Document;
use App\Crawler\ReceitaFederalComprovanteCpf\ReceitaFederalComprovanteCpf;
use App\Crawler\Spider;
use Exception;

class ReceitaFederalComprovanteCpfParametrizado extends Spider
{
    private const NASCIMENTO_DIVERGENTE_REGEX = '/data\sde\snascimento.*?divergente/isu';
    private $document;
    private $name;
    private $birthDate;
    private $message;

    protected function validateAndSetCrawlerAttributes()
    {
        if (
            isset($this->param['cpf'])
            && !empty($this->param['cpf'])
            && Document::validarCpf($this->param['cpf'])
        ) {
            $this->document = preg_replace("/\\D+/isu", "", $this->param['cpf']);
            $this->document = str_pad($this->document, 11, '0', STR_PAD_LEFT);
        } else {
            throw new Exception('Parâmetro de documento inválido', 1);
        }

        if (empty($this->param['nome'])) {
            throw new Exception('Parâmetro de nome inválido', 1);
        }

        $this->name = trim($this->param['nome']);

        if (isset($this->param['data_nascimento']) && !empty($this->param['data_nascimento'])) {
            if (preg_match("/[0-2][0-9]\\/[0-1][0-9]\\/[1-2][0-9]{3}/", $this->param['data_nascimento'])) {
                $this->birthDate = $this->param['data_nascimento'];
            } else {
                throw new Exception('Parâmetro de data de nascimento inválido', 1);
            }
        } else {
            throw new Exception('Parâmetro de data de nascimento inválido', 1);
        }
    }

    protected function start()
    {
        $results = $this->getResultsByCpfAndNomeAndNascimento();

        return $results;
    }

    protected function addExtraDataToResponse($response)
    {
        if (!empty($this->message)) {
            $response['message'] = $this->message;
        }

        return $response;
    }

    private function getResultsByCpfAndNomeAndNascimento()
    {
        $result =  $this->getReceitaFederalComprovante($this->document, $this->birthDate);

        if ($this->isRegexMatch($result['message'], self::NASCIMENTO_DIVERGENTE_REGEX)) {
            $result =  $this->getReceitaFederalComprovante($this->document, '');

            if ($this->isRegexMatch($result['message'], self::NASCIMENTO_DIVERGENTE_REGEX)) {
                throw new Exception('Data de nascimento informada e registrada divergentes da data real');
            }
        }

        if ($result['status'] != 'success') {
            throw new Exception($result['message']);
        }

        $this->message = '';
        if ($this->onlyDigits($result['data']['nascimento']) != $this->onlyDigits($this->birthDate)) {
            $this->message .= 'Data de nascimento informada divergente da data real';
        }
        if (strtoupper(trim($this->name)) != strtoupper(trim($result['data']['nome']))) {
            if (!empty($this->message)) {
                $this->message .= ' / ';
            }
            $this->message .= 'Nome informado divergente do nome real';
        }
        if (empty($message)) {
            $this->message = 'OK';
        }

        return $result['data'];
    }

    private function onlyDigits($dataNascimento)
    {
        return preg_replace('/\D*/isu', '', $dataNascimento);
    }

    private function getReceitaFederalComprovante()
    {
        $receitaFederalModel = new ReceitaFederalComprovanteCpf(
            array(
                'fornecedor'        =>  'receita_federal_api',
                'cpf'               =>  $this->document,
                'data_nascimento'   =>  $this->birthDate,
            ),
            []
        );
        $result = $receitaFederalModel->run();
        return $result;
    }
    private function isRegexMatch($target, $regex)
    {
        return preg_match_all($regex, $target);
    }
}
