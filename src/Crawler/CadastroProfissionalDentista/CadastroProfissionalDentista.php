<?php

namespace App\Crawler\CadastroProfissionalDentista;

use App\Crawler\Spider;
use App\Helper\Util;
use Exception;

/**
 * Description of CadastroProfissionalDentista
 *
 * <AUTHOR>
 */
class CadastroProfissionalDentista extends Spider
{
    private const URL = 'https://website.cfo.org.br/profissionais-cadastrados/';
    private const URL_CROSP = 'https://cro-sp.implanta.net.br/servicosOnline/Publico/ConsultaInscritos/';
    private const URL_CROSP_BUSCA = 'https://cro-sp.implanta.net.br/servicosOnline//publico/ConsultaInscritos/Buscar';
    private const DEFAULT_LIMIT = 50;

    private $count = 0;
    private $limit = 0;
    private $result = [];
    private $headers = array(
        ':authority' => 'cro-sp.implanta.net.br',
        ':method' => 'POST',
        ':path' => '/servicosOnline//publico/ConsultaInscritos/Buscar',
        'content-length' => '1135',
        'content-type' => 'application/json; charset=UTF-8',
        'referer' => 'https://cro-sp.implanta.net.br/servicosOnline/Publico/ConsultaInscritos/',
        'user-agent' => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko)' .
            ' Chrome/75.0.3770.100 Safari/537.36',
        'x-requested-with' => 'XMLHttpRequest'
    );

    /**
     * Iniciar crawler da fonte
     *
     * @param string $response
     * @return string
     * <AUTHOR>
     *
     * @version 1.0.0
     * @version 1.1.0 Jefferson Mesquita 31/05/2019 - Troca de Post para GET
     */
    protected function start()
    {
        $data1 = $this->getParamsCrosp();
        $params = http_build_query($this->getParams());
        $response = $this->getResponse(self::URL . '?' . $params);
        $this->parseResponse($response);
        $this->resultPaging($response);
        $dados = $this->result;
        $data = $this->getUniDados($dados, $data1);
        return $data;
    }

    /**
     * Paginar resultados
     *
     * @param string $response
     *
     * <AUTHOR> Mesquita 12/06/2019
     *
     * @version 1.0.0
     *
     * @return void
     */
    private function resultPaging($response)
    {
        if ($this->count >= $this->limit) {
            return;
        }

        if (!preg_match('/<a\shref="\?([^>]*)"\s?(role=.link.)?([^>]*)?>Pr.oacute.xima<\/a>/i', $response, $match)) {
            return;
        }

        $params = str_replace(' ', '%20', trim($match[1]));
        $response = $this->getResponse(self::URL . '?' . $params);
        $this->parseResponse($response);
        $this->resultPaging($response);
    }

    /**
     * Pegar Parametros
     *
     * @param string $response
     *
     * <AUTHOR>
     *
     * @version 1.0.0 ??
     * @version 1.1.0 Jefferson Mesquita 12/06/2019 - Adicionar variável limit
     *
     * @return array
     */
    private function getParams()
    {
        $this->limit = (empty($this->param['limit']) ? self::DEFAULT_LIMIT : $this->param['limit']);

        if (empty($this->param['nome'])) {
            throw new Exception('Parametro nome é obrigatório');
        }
        return [
            'nome' => $this->param['nome'],
            'cro' => 'Todos',
            'categoria' => 'todas',
            'especialidade' => 'todas',
            'inscricao' => ''
        ];
    }

    /**
     * Parse dos dados da fonte
     *
     * @param string $response
     *
     * <AUTHOR>
     *
     * @version 1.0.0
     * @version 1.1.0 Jefferson Mesquita 31/05/2019 - Mensagem quando não tiver resultados
     * @version 1.1.1 Jefferson Mesquita 12/06/2019 - Limitador para paginação
     *
     * @return array
     */
    private function parseResponse($response)
    {
        preg_match_all('/<br\s*\/><hr\/>.*cfo:.*\n/isU', $response, $profissionaisHtml);

        foreach ($profissionaisHtml[0] as $profissional) {
            $this->count++;
            $this->result[] = [
                'categoria' => $this->extractDataFromHtml($profissional, 'categoria'),
                'estado' => $this->extractDataFromHtml($profissional, 'estado'),
                'inscricao_cro' => $this->extractDataFromHtml($profissional, 'inscricao_cro'),
                'nome' => $this->extractDataFromHtml($profissional, 'nome'),
                'situacao' => $this->extractDataFromHtml($profissional, 'situacao'),
                'tipo_inscricao' => $this->extractDataFromHtml($profissional, 'tipo_inscricao'),
                'especialidade' => $this->extractDataFromHtml($profissional, 'especialidade'),
                'data_inscricao_cro' => $this->extractDataFromHtml($profissional, 'data_inscricao_cro'),
                'data_registro_cro' => $this->extractDataFromHtml($profissional, 'data_registro_cro')
            ];

            if ($this->count == $this->limit) {
                break;
            }
        }
    }

    /**
     * Extrai dados do html
     *
     * @param string $html
     * @param string $data
     * @return string
     * <AUTHOR> Machado - 2019-05-16
     * Revisão
     * @version 1.0.0
     * @version 1.1.0 Jefferson Mesquita 31/05/2019 - retirado espaços das especialidades
     */
    private function extractDataFromHtml($html, $data)
    {
        $regex = [
            'categoria' => '/<hr\/>(.*)\s\-/isU',
            'estado' => '/inscri....o\:\s(..)\-/isU',
            'inscricao_cro' => '/inscri....o\:\s*.{2}\-.{2,3}-(\d*)/is',
            'nome' => '/<b>(.*)<\/b>/isU',
            'situacao' => '/>situa....o\:(.*)</isU',
            'tipo_inscricao' => '/de\sinscri....o\:\s(.*)<br/isU',
            'especialidade' => '/br\s*\/>especialidade:(.*)(?:habilita....es|data)/isU',
            'data_inscricao_cro' => '/cro:\s(.*\d{4})/isU',
            'data_registro_cro' => '/cfo:\s(.*\d{4})/isU',
        ];

        if (empty($regex[$data])) {
            return '';
        }

        preg_match($regex[$data], $html, $result);

        if ($data == 'especialidade' && !empty($result[1])) {
            $especialidades = str_replace('<br />', '', $result[1]);
            $especialidades = preg_replace('/especialidade\:/is', ',', trim($especialidades));
            return str_replace('<br/>', '', $especialidades);
        }

        return !empty($result[1]) ? $result[1] : '';
    }

    /**
     *<AUTHOR> Alves
     *@param ''
     *@version 1.0.0 - scraping para a crosp
     *@return data
     */
    private function getParamsCrosp()
    {
        $this->setProxy();
        $html = $this->getResponse(self::URL_CROSP);

        $params = array(
            'Configuracao' => [
                'Acao' => 0,
                'CamposConfiguraveis' => 386,
                'CidadeComercial' => false,
                'DataSituacao' => false,
                'EmailsComerciais' => false,
                'EspecialidadesAtivas' => true,
                'FiltroCategoria' => false,
                'FiltroCidade' => false,
                'FiltroCPFCNPJ' => false,
                'FiltroEspecialidade' => false,
                'FiltrosConfiguraveis' => 0,
                'FiltroSituacao' => false,
                'FiltroSituacaoDetalhe' => false,
                'FiltroTipoInscricao' => false,
                'FiltroUF' => false,
                'Id' => 'cebe8825-b82d-45d5-b8f8-6721a046deb4',
                'IdConfiguracoesServicosOnlineCamposVisiveisConsultaPublica' => 'cebe8825-b82d-45d5-b8f8-6721a046deb4',
                'IdsSituacoes' => [],
                'IdsSituacoesDetalhes' => [],
                'Responsabilidades' => true,
                'Site' => false,
                'SituacoesDetalhes' => true,
                'TelefonesComerciais' => 'false',
                'UFComercial' => false,
            ],
            'CPFCNPJ' => null,
            'EnderecoCidadeEmpresaFuncionamentoProfissionalComercial' => null,
            'EnderecoUFEEmpresaFuncionamentoProfissionalComercial' => null,
            'Id' => null,
            'IdCategoria' => null,
            'Ids' => [],
            'IdsEspecialidades' => [],
            'IdSituacao' => null,
            'IdSituacaoDetalhe' => null,
            'IdsSituacoesConfiguracao' => null,
            'IdsSituacoesDetalhesConfiguracao' => null,
            'IgnorarTamanhoMaximoFiltro' => false,
            'NomeRazaoSocial' => $this->param['nome'],
            'NumeroRegistro' => null,
            'Pagina' => 1,
            'Tamanho' => 100,
            'Usuario' => null
        );

        $response = $this->getResponse(self::URL_CROSP_BUSCA, 'POST', $params, $this->headers);
        $dados = json_decode($response, true);
        $dados = Util::arrayFlatten($dados);
        $data = [];
        foreach ($dados as $key) {
            $dataInscricao = preg_replace('/T00:00:00/m', '', $key['DataInscricao']);
            $dataRegistro = preg_replace('/T00:00:00/m', '', $key['DataSituacao']);
            $data[] = [
                'categoria' => $key['Categoria'],
                'estado' => 'SP',
                'inscricao_cro' => $key['NumeroRegistro'],
                'nome' => $key['Nome'],
                'situacao' => $key['Situacao'],
                'tipo_inscricao' =>  $key['TipoInscricao'],
                'especialidade' => $key['Especialidades'],
                'data_inscricao_cro' => $dataInscricao,
                'data_registro_cro' => $dataRegistro,

            ];
        }
        //Ultimo array sempre volta vazio, para ajustar isso removo a ultima posição do array.
        array_pop($data);
        return $data;
    }

    /**
     * faz a união dos dados de ambas as fontes
     *
     * <AUTHOR> Alves
     *
     * @param mixed $cfo
     * @param array|null $crosp
     *
     */
    private function getUniDados($cfo, $crosp)
    {
        if (!empty($cfo) && !empty($crosp)) {
            return array_merge($cfo, $crosp);
        }

        if (empty($cfo) && !empty($crosp)) {
            return $crosp;
        }

        if (!empty($cfo) && empty($crosp)) {
            return $cfo;
        }

        throw new Exception("Não foi encontrado resultado para esta consulta", 2);
    }

    /**
     * Esboço de função de validação de parametros
     *
     * <AUTHOR> Alves
     *
     * @param mixed $cfo
     * @param array|null $crosp
     *
     */
    protected function validateAndSetCrawlerAttributes()
    {
        return false;
    }
}
