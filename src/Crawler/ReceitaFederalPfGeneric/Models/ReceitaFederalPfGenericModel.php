<?php

namespace App\Crawler\ReceitaFederalPfGeneric\Models;

use Exception;

class ReceitaFederalPfGenericModel
{
    public $cpf;
    public $nome;
    public $nascimento;
    public $situacao;
    public $inscricao;
    public $digito_verificador;
    public $obito;
    public $hora;
    public $data;
    public $chave;
    public $source;
    public $cpf_nome;
    public $nome_dataNascimento;

    public function __set($name, $value)
    {
        if (property_exists($this, $name)) {
            return $this->$name = $value;
        }

        throw new Exception("Propriedade set '{$name}' não existe", 3);
    }

    public function __get($name)
    {
        if (property_exists($this, $name)) {
            return $this->$name;
        }

        throw new Exception("Propriedade get '{$name}'  não existe", 3);
    }
}
