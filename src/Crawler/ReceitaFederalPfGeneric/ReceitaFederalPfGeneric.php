<?php

namespace App\Crawler\ReceitaFederalPfGeneric;

use Exception;
use App\Crawler\SpiderGeneric;
use App\Helper\Document;
use App\Helper\Date;
use App\Crawler\ReceitaFederalPfGeneric\Models\ReceitaFederalPfGenericModel;

/**
 * Classe Genérica para busca de vizinhos em todas as fontes relacionadas
 *
 * @version 1.0.0
 *
 * <AUTHOR> Mesquita 05/08/2020
 *
 * @return array
 */
class ReceitaFederalPfGeneric extends SpiderGeneric
{
    /**
     * Inicia a execução da classe
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 05/08/2020
     *
     * @return array
     */
    public function start(): ReceitaFederalPfGenericModel
    {
        $data = $this->runGenericSources($this->param, $this->auth ?? []);

        if ($data->source !=  'ColunaVertebralConsultaPf') {
            $this->updateSpine('ReceitaFederalPf', $data);
        }

        return $data;
    }

    /**
     * Valida os parametros recebidos
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 05/08/2020
     *
     * @return void
     */
    public function validateAndSetCrawlerAttributes()
    {
        $this->param['documento'] = preg_replace('/[^0-9]/isu', '', $this->param['documento']);

        if (!Document::validarCpf($this->param['documento'])) {
            throw new Exception("Critério de busca inválido.", 6);
        }

        if (!empty($this->param['data_nascimento']) && !Date::validateDate($this->param['data_nascimento'])) {
            throw new Exception("Critério data de nascimento no formato errado.", 6);
        }
    }

    protected function getSourcesConfig(): array
    {
        return [
            [
                'source' => 'ReceitaFederalPfSite',
                'params' => ['documento' => 'documento', 'data_nascimento' => 'data_nascimento'],
                'auths' => []
            ],
            [
                'source' => 'ReceitaFederalPfInfoSimples',
                'params' => ['documento' => 'documento', 'data_nascimento' => 'data_nascimento'],
                'auths' => []
            ],
            [
                'source' => 'ColunaVertebralConsultaPf',
                'params' => ['cpf' => 'documento'],
                'auths' => []
            ]
        ];
    }
}
