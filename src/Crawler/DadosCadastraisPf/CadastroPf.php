<?php

namespace App\Crawler\DadosCadastraisPf;

/**
 * Created by PhpStorm.
 * User: smakhoul
 * Date: 24/06/16
 * Time: 10:47
 */
class CadastroPf
{
    /**
     * Informações Complementares
     */

    /**
     * @var string
     */
    public $cpf;

    /**
     * @var string
     */
    public $nome;

    /**
     * @var string
     */
    public $nomeMae;

    /**
     * @var string
     */
    public $dataNascimento;

    /**
     * @var string
     */
    public $idade;

    /**
     * @var string
     */
    public $sexo;

    /**
     * @var string
     */
    public $estadoCivil;

    /**
     * @var string
     */
    public $signo;

    /**
     * @var string
     */
    public $dataObito;

    /**
     * Telefones
     */

    /**
     * @var array
     */
    public $telefones;

    /**
     * E-mails
     */

    /**
     * @var array
     */
    public $emails;

    /**
     * Endereços
     */

    /**
     * @var array
     */
    public $enderecos;

    /**
     * Documentos
     */

    /**
     * @var string
     */
    public $rg;

    /**
     * @var string
     */
    public $secao;

    /**
     * @var string
     */
    public $tituloEleitor;

    /**
     * @var string
     */
    public $zona;

    /**
     * @var string
     */
    public $orgaoEmissor;

    /**
     * @var string
     */
    public $nis;

    /**
     * @var string
     */
    public $ufEmissao;

    /**
     * Empresas que possui participação
     */

    /**
     * @var array
     */
    public $sociedades;

    /**
     * Profissão/Ocupação
     */

    /**
     * @var string
     */
    public $profissao;

    /**
     * Renda e classe social
     */

    /**
     * @var string
     */
    public $classeSocial;

    /**
     * @var string
     */
    public $escolaridade;

    /**
     * @var string
     */
    public $cbo;

    /**
     * @var string
     */
    public $rendaEstimada;


    /**
     * Outros
     */

    /**
     * @var string
     */
    public $dataSituacao;

    /**
     * @var string
     */
    public $horaSituacao;

    /**
     * @var string
     */
    public $ultimaAtualizacaoPf;


    /**
     * @var string
     */
    public $situacaoReceitaBancoDados;

    /**
     * @var string
     */
    public $hashRf;

    /**
     * @var string
     */
    public $relacionados;



    public function setCampos(array $data)
    {
        foreach ($data as $field => $value) {
            call_user_func_array(array($this, 'set' . ucfirst($field)), array($value));
        }
    }

    /**
     * @return string
     */
    public function getCpf()
    {
        return $this->cpf;
    }

    /**
     * @param string $cpf
     */
    public function setCpf($cpf)
    {
        $this->cpf = $cpf;
    }

    /**
     * @return string
     */
    public function getNome()
    {
        return $this->nome;
    }

    /**
     * @param string $nome
     */
    public function setNome($nome)
    {
        $this->nome = $nome;
    }

    /**
     * @return string
     */
    public function getSexo()
    {
        return $this->sexo;
    }

    /**
     * @param string $sexo
     */
    public function setSexo($sexo)
    {
        $this->sexo = $sexo;
    }

    /**
     * @return string
     */
    public function getDataNascimento()
    {
        return $this->dataNascimento;
    }

    /**
     * @param string $dataNascimento
     */
    public function setDataNascimento($dataNascimento)
    {
        $this->dataNascimento = $dataNascimento;
    }

    /**
     * @return string
     */
    public function getNomeMae()
    {
        return $this->nomeMae;
    }

    /**
     * @param string $nomeMae
     */
    public function setNomeMae($nomeMae)
    {
        $this->nomeMae = $nomeMae;
    }

    /**
     * @return string
     */
    public function getIdade()
    {
        return $this->idade;
    }

    /**
     * @param string $idade
     */
    public function setIdade($idade)
    {
        $this->idade = $idade;
    }

    /**
     * @return string
     */
    public function getSigno()
    {
        return $this->signo;
    }

    /**
     * @param string $signo
     */
    public function setSigno($signo)
    {
        $this->signo = $signo;
    }

    /**
     * @return array
     */
    public function getTelefones()
    {
        return $this->telefones;
    }

    /**
     * @param array $telefones
     */
    public function setTelefones($telefones)
    {
        $this->telefones = $telefones;
    }

    /**
     * @return array
     */
    public function getEnderecos()
    {
        return $this->enderecos;
    }

    /**
     * @param array $enderecos
     */
    public function setEnderecos($enderecos)
    {
        $this->enderecos = $enderecos;
    }

    /**
     * @return array
     */
    public function getEmails()
    {
        return $this->emails;
    }

    /**
     * @param array $emails
     */
    public function setEmails($emails)
    {
        $this->emails = $emails;
    }

    /**
     * @return string
     */
    public function getUltimaAtualizacaoPf()
    {
        return $this->ultimaAtualizacaoPf;
    }

    /**
     * @param string $ultimaAtualizacaoPf
     */
    public function setUltimaAtualizacaoPf($ultimaAtualizacaoPf)
    {
        $this->ultimaAtualizacaoPf = $ultimaAtualizacaoPf;
    }

    /**
     * @return string
     */
    public function getSituacaoReceitaBancoDados()
    {
        return $this->situacaoReceitaBancoDados;
    }

    /**
     * @param string $situacaoReceitaBancoDados
     */
    public function setSituacaoReceitaBancoDados($situacaoReceitaBancoDados)
    {
        $this->situacaoReceitaBancoDados = $situacaoReceitaBancoDados;
    }

    /**
     * @return string
     */
    public function getRendaEstimada()
    {
        return $this->rendaEstimada;
    }

    /**
     * @param string $rendaEstimada
     */
    public function setRendaEstimada($rendaEstimada)
    {
        $this->rendaEstimada = $rendaEstimada;
    }

    /**
     * @return string
     */
    public function getProfissao()
    {
        return $this->profissao;
    }

    /**
     * @param string $profissao
     */
    public function setProfissao($profissao)
    {
        $this->profissao = $profissao;
    }

    /**
     * @return string
     */
    public function getDataObito()
    {
        return $this->dataObito;
    }

    /**
     * @param string $dataObito
     */
    public function setDataObito($dataObito)
    {
        $this->dataObito = $dataObito;
    }

    /**
     * @return array
     */
    public function getSociedades()
    {
        return $this->sociedades;
    }

    /**
     * @param array $sociedades
     */
    public function setSociedades($sociedades)
    {
        $this->sociedades = $sociedades;
    }

    /**
     * @return string
     */
    public function getRelacionados()
    {
        return $this->relacionados;
    }

    /**
     * @param string $relacionados
     */
    public function setRelacionados($relacionados)
    {
        $this->relacionados = $relacionados;
    }

    /**
     * @return string
     */
    public function getClasseSocial()
    {
        return $this->classeSocial;
    }

    /**
     * @param string $classeSocial
     */
    public function setClasseSocial($classeSocial)
    {
        $this->classeSocial = $classeSocial;
    }

    /**
     * @return string
     */
    public function getSecao()
    {
        return $this->secao;
    }

    /**
     * @param string $secao
     */
    public function setSecao($secao)
    {
        $this->secao = $secao;
    }

    /**
     * @return string
     */
    public function getRg()
    {
        return $this->rg;
    }

    /**
     * @param string $rg
     */
    public function setRg($rg)
    {
        $this->rg = $rg;
    }

    /**
     * @return string
     */
    public function getEscolaridade()
    {
        return $this->escolaridade;
    }

    /**
     * @param string $escolaridade
     */
    public function setEscolaridade($escolaridade)
    {
        $this->escolaridade = $escolaridade;
    }

    /**
     * @return string
     */
    public function getOrgaoEmissor()
    {
        return $this->orgaoEmissor;
    }

    /**
     * @param string $orgaoEmissor
     */
    public function setOrgaoEmissor($orgaoEmissor)
    {
        $this->orgaoEmissor = $orgaoEmissor;
    }

    /**
     * @return string
     */
    public function getZona()
    {
        return $this->zona;
    }

    /**
     * @param string $zona
     */
    public function setZona($zona)
    {
        $this->zona = $zona;
    }

    /**
     * @return string
     */
    public function getDataSituacao()
    {
        return $this->dataSituacao;
    }

    /**
     * @param string $dataSituacao
     */
    public function setDataSituacao($dataSituacao)
    {
        $this->dataSituacao = $dataSituacao;
    }

    /**
     * @return string
     */
    public function getHoraSituacao()
    {
        return $this->horaSituacao;
    }

    /**
     * @param string $horaSituacao
     */
    public function setHoraSituacao($horaSituacao)
    {
        $this->horaSituacao = $horaSituacao;
    }

    /**
     * @return string
     */
    public function getUfEmissao()
    {
        return $this->ufEmissao;
    }

    /**
     * @param string $ufEmissao
     */
    public function setUfEmissao($ufEmissao)
    {
        $this->ufEmissao = $ufEmissao;
    }

    /**
     * @return string
     */
    public function getTituloEleitor()
    {
        return $this->tituloEleitor;
    }

    /**
     * @param string $tituloEleitor
     */
    public function setTituloEleitor($tituloEleitor)
    {
        $this->tituloEleitor = $tituloEleitor;
    }

    /**
     * @return string
     */
    public function getCbo()
    {
        return $this->cbo;
    }

    /**
     * @param string $cbo
     */
    public function setCbo($cbo)
    {
        $this->cbo = $cbo;
    }

    /**
     * @return string
     */
    public function getEstadoCivil()
    {
        return $this->estadoCivil;
    }

    /**
     * @param string $estadoCivil
     */
    public function setEstadoCivil($estadoCivil)
    {
        $this->estadoCivil = $estadoCivil;
    }

    /**
     * @return string
     */
    public function getNis()
    {
        return $this->nis;
    }

    /**
     * @param string $nis
     */
    public function setNis($nis)
    {
        $this->nis = $nis;
    }


    /**
     * @return string
     */
    public function getHashRf()
    {
        return $this->hashRf;
    }

    /**
     * @param string $hashRf
     */
    public function setHashRf($hashRf)
    {
        $this->hashRf = $hashRf;
    }
}
