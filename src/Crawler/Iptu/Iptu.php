<?php

namespace App\Crawler\Iptu;

use App\Crawler\Spider;
use App\Manager\ElasticsearchManager;
use Exception;

class Iptu extends Spider
{
    protected function start()
    {
        $indexParams = [
            'index' => 'iptu',
            'type' => 'sp',
            'body' => [
                "size" => isset($this->param['limite']) ? $this->param['limite'] : 15,
                'query' => [
                    'bool' => [
                        'should' => [
                            [
                                'query_string' => [
                                    'default_field' => 'nome_do_contribuinte_1',
                                    'query' => $this->addSlashes($this->param['nome']),
                                    'minimum_should_match' => '100%'
                                ]
                            ],
                            [
                                'query_string' => [
                                    'default_field' => 'nome_do_contribuinte_2',
                                    'query' => $this->addSlashes($this->param['nome']),
                                    'minimum_should_match' => '100%'
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $results = (new ElasticsearchManager())->search($indexParams);

        $total = count($results['hits']['hits']);

        for ($i = 0; $i < $total; $i++) {
            similar_text(
                strtolower($results['hits']['hits'][$i]['_source']['nome_do_contribuinte_1']),
                strtolower($this->param['nome']),
                $similarity1
            );
            similar_text(
                strtolower($results['hits']['hits'][$i]['_source']['nome_do_contribuinte_2']),
                strtolower($this->param['nome']),
                $similarity2
            );

            if ($similarity1 < 80 && $similarity2 < 80) {
                unset($results['hits']['hits'][$i]);
            }
        }

        if (empty($results['hits']['hits'])) {
            throw new Exception('Nada Encontrado', 2);
        }

        $response = [];
        foreach ($results['hits']['hits'] as $key => $value) {
            $response[] = $value['_source'];
        }

        return $response;
    }
    private function addSlashes($string)
    {
        $string = addcslashes($string, '+');
        $string = addcslashes($string, '-');
        $string = addcslashes($string, '!');
        $string = addcslashes($string, '(');
        $string = addcslashes($string, ')');
        $string = addcslashes($string, '^');
        $string = addcslashes($string, '"');
        $string = addcslashes($string, '~');
        $string = addcslashes($string, '*');
        $string = addcslashes($string, '?');
        $string = addcslashes($string, ':');
        $string = addcslashes($string, '/');

        return $string;
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['nome'])) {
            throw new Exception('Parâmetro inválido');
        }
    }
}
