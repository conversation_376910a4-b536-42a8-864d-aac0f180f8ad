<?php

namespace App\Crawler\Cfp;

use App\Crawler\Spider;
use Exception;
use Symfony\Component\DomCrawler\Crawler;
use App\Helper\Document;
use App\Helper\Str;

/**
 *  Clase de consulta da fonte CFP - CONSELHO FEDERAL DE PSICOLOGIA
 *
 *  <AUTHOR> - 29/07/2019
 *  <AUTHOR> - 04/11/2021 - Removendo atributos e métodos não utilizados.
 *
 *  @version 1.0
 */
class Cfp extends Spider
{
    private const SOURCE_LIMIT_DEFAULT = 10;

    private const BASE = 'https://cadastro.cfp.org.br';
    private const API = 'https://cn-api.cfp.org.br/psi/busca?';

    private $limit;
    private $result = [];

    private $cpf;
    private $cnpj;
    private $name;
    private $googleKey;

    /**
     * Inicio do processamento da fonte
     *
     * <AUTHOR> 29/07/2019
     *
     * @version 1.0.0
     *
     * @return array
     */
    protected function start()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__;
        }

        $retry = 7;
        while ($retry >= 0) {
            try {
                $this->setRecaptchaKey();

                $response = $this->getProfessionals();

                if (empty($response)) {
                    throw new Exception("Erro na busca", 3);
                }

                if (preg_match('/Por favor.*?envie o formul.*?rio novamente/mi', $response)) {
                    throw new Exception("Não foi possível quebrar o captcha", 2);
                }

                $professionals = json_decode($response, true);

                if (empty($professionals)) {
                    throw new Exception("Nenhum resultado encontrado", 2);
                }

                $this->setResults($professionals);

                return $this->result;
            } catch (Exception $e) {
                if ($retry == 0) {
                    throw new Exception($e->getMessage(), 2);
                }
                $retry--;
            }
        }
    }

    private function setResults($professionals)
    {

        foreach ($professionals as $n => $professional) {
            if (count($this->result) >= $this->limit) {
                break;
            }

            $this->result[] = [
                'registro' => $professional['registro'],
                'nome' => $professional['Nome'],
                'data_situacao' => '-',
                'situacao' => $professional['situacao'],
                'regiao' => $professional['nomeregional']
            ];
        }
    }

    private function setRecaptchaKey()
    {

        if ($this->debug) {
            echo PHP_EOL . __METHOD__;
        }

        try {
            $this->googleKey = $this->solveRecaptchaV3('6LdhVd8UAAAAAL9RbkzRrEAloAp9dWfemA7kJ5oP', '#', self::BASE);
        } catch (Exception $e) {
            throw new Exception("Não foi possível quebrar o captcha", 3);
        }
    }

    private function getProfessionals()
    {

        if ($this->debug) {
            echo PHP_EOL . __METHOD__;
        }

        $this->setProxy();

        $params = [
            'nome' => $this->name,
            'regiao' => '',
            'registro' => '',
            'cpf' => $this->cpf,
            'cnpj' => $this->cnpj,
            'tipo' => $this->cnpj != '' ? 'PJ' : 'PF',
            'recaptchaToken' => $this->googleKey
        ];

        return $this->getResponse(self::API . http_build_query($params));
    }

    /**
     * Valida e retorna os parametros
     *
     * <AUTHOR> Mesquita 29/07/2019
     * <AUTHOR> Vidal - 04/11/2021
     * Removido tratativa de CNPJ não utilizada e a máscara do CPF, necessário para o POST.
     *
     * @version 1.0.0
     *
     * @return array
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print PHP_EOL . __METHOD__;
        }

        $this->limit = $this->param['limit'] ??  self::SOURCE_LIMIT_DEFAULT;
        $serachCpf = '';
        $searchName = '';

        if (empty($this->param['nome_cpf']) || Document::validarCnpj($this->param['nome_cpf'])) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }

        $searchName = Str::removerAcentos($this->param['nome_cpf']);
        $this->name = $searchName;

        if (Document::validarCpf($this->param['nome_cpf'])) {
            $serachCpf = Document::formatCpfOrCnpj($this->param['nome_cpf']);

            $this->cpf = Document::removeMask($serachCpf);
            $this->name = '';
        }
    }
}
