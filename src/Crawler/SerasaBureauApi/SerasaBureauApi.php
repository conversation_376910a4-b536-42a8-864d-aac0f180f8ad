<?php

namespace App\Crawler\SerasaBureauApi;

use App\Crawler\Spider;
use App\Helper\Str;
use Exception;
use App\Manager\S3\S3;
use App\Manager\S3\Bucket\StaticUplexisBucket;

/**
 * Classe de pesquisa da Serasa Bureau Api
 *
 * @version 1.0.1
 * <AUTHOR>
 */
class SerasaBureauApi extends Spider
{
    private $production_url = 'https://sitenet43.serasa.com.br/Prod/consultahttps';
    private $homolog_url = 'https://mqlinuxext.serasa.com.br/Homologa/consultahttps';
    private $serasa_service_url = '';
    private $application_env = 'prod';
    private $start_output_position = 515;
    private $strlen_current = 115;

    private $count = 20;

    /**
     * Função principal de execução
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @return array
     */
    protected function start()
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }

        if ($this->application_env == 'prod') {
            $this->serasa_service_url = $this->production_url;
        } else {
            $this->serasa_service_url = $this->homolog_url;
        }

        list($str_header, $str_body) = $this->splitString($this->getDados());

        do {
            $tipo = $this->checkTypeResponse($str_header);

            $endPositionOfString = $this->lookingForEndPositionOfString($str_body);

            $strlen = strlen($str_body);

            for ($i = 0; $i <= $strlen; $i += $this->strlen_current) {
                if ($i >= $endPositionOfString) {
                    break;
                }

                $str_current = substr($str_body, $i, $this->strlen_current);

                if (method_exists($this, $method_name = $this->getMethod($this->getKeyResponse($str_current)))) {
                    $codKey = $this->getCodKey($this->getKeyResponse($str_current));
                    $response[$codKey][] = $this->{$method_name}($str_current);
                }
            }

            if ($tipo == 'CON') {
                list($str_header, $str_body) = $this->splitString($this->getDados($str_header)); //PASSAR O CON
            }
        } while ($tipo != 'FIM' && $this->count-- > 0);

        if (!$response) {
            throw new Exception('Nenhum resultado encontrado.', 2);
        }

        return Str::encoding($response);
    }

    /**
     * Prepara a string de parametrização
     *
     * @version 1.0.1
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Retirado urlencode da string parametrizada
     *
     * @param string $string_con
     *
     * @return string
     */
    private function prepareSerasaStringContinue($string_con)
    {
        //AUTH
        $auth = str_pad($this->auth['usuario'], 8);
        $auth .= str_pad($this->auth['senha'], 8);
        $auth .= str_pad('', 8);

        $string = $auth . $string_con;

        // T999
        $string .= 'T999';
        $string .= str_pad('', 111);

        return $string;
    }

    /**
     * Prepara a string de parametrização de início
     *
     * @version 1.0.1
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Retirado urlencode da string parametrizada
     *
     * @return string
     */
    private function prepareSerasaString()
    {
        //AUTH;
        $auth = str_pad($this->auth['usuario'], 8);
        $auth .= str_pad($this->auth['senha'], 8);
        $auth .= str_pad('', 8);

        // B49C
        $string = 'B49C';
        //$string .= sprintf('%-6s', '');
        $string .= str_pad('', 6);
        $string .= sprintf('%015d', preg_replace("@\\D+@i", "", $this->param['cpf_cnpj']));  // 15  - NUM DOC - N
        $string .= 'F';                                     // 1   - TIPO PESSOA - F
        //$string .= sprintf('%-6s', 'C');
        $string .= str_pad('C', 6);
        $string .= 'FI';
        $string .= sprintf('%07d', 0);
        //$string .= sprintf('%-12s', '');
        $string .= str_pad('', 12);
        $string .= 'S';
        $string .= '99';                                    // 2   - QTD REG - N
        $string .= 'S';
        $string .= 'INI';
        $string .= 'A';
        $string .= 'N';
        //$string .= sprintf('%-30s', '');
        $string .= str_pad('', 30);
        $string .= 'D';
        $string .= 'N';
        //$string .= sprintf('%-10s', '');
        $string .= str_pad('', 10);
        $string .= '00';
        $string .= 'N';                                     // S OU N
        //$string .= sprintf('%-8s', '');
        $string .= str_pad('', 8);
        $string .= sprintf('%015d', 0);
        $string .= 'S';
        //$string .= sprintf('%-9s', '');
        $string .= str_pad('', 9);
        $string .= '1';                                     // 1 OU 2
        //$string .= sprintf('%-259s', '');
        $string .= str_pad('', 259);

        // P006
        $string .= 'P006';
        $string .= 'S';
        $string .= 'S';
        $string .= 'S';
        $string .= 'S';
        $string .= 'S';
        //$string .= sprintf('%-2s', '');
        $string .= str_pad('', 2);
        $string .= '05';                                    // 05 OU 99
        $string .= 'S';
        $string .= 'S';
        $string .= 'S';
        $string .= 'S';
        $string .= 'S';
        $string .= 'S';
        $string .= 'S';
        $string .= 'S';
        //$string .= sprintf('%-1s', '');
        $string .= str_pad('', 1);
        $string .= 'S';
        $string .= 'S';
        $string .= 'S';
        $string .= 'N';
        $string .= 'S';
        //$string .= sprintf('%-88s', '');
        $string .= str_pad('', 88);

        // T999
        $string .= 'T999';
        //$string .= sprintf('%-111s', '');
        $string .= str_pad('', 111);

        return $auth . $string;
    }

    /**
     * Busca os dados na API
     *
     * @version 1.0.1
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Mudado o pedido de GET para POST
     *
     * @param string|null $con
     *
     * @return string
     */
    private function getDados($con = null)
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }

        $data = [];
        if (!$con) {
            $data['p'] = $this->prepareSerasaString();
        } else {
            $data['p'] = $this->prepareSerasaStringContinue($con);
        }

        $str = $this->getResponseByFileGet($this->serasa_service_url, 'POST', $data);

        $this->saveStringS3($str, $data);

        $str = $this->checkResponse($str);

        return $str;
        //return $this->getResponseSerasaFake();
    }

    /**
     * Salva a string no S3
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $data
     * @param string $url
     *
     * @return void
     */
    private function saveStringS3($data, $url)
    {
        $uniqid = $this->auth['usuario'] . '_' . $this->auth['senha'] . '_' . md5(uniqid(rand(), true));

        $file = preg_replace("@\\D+@i", "", $this->param['cpf_cnpj']) . '_' . $uniqid . '.txt';

        $serasa_bureau_api_request = '/tmp/serasa_bureau_api_request_' . $file;
        $serasa_bureau_api_response = '/tmp/serasa_bureau_api_response_' . $file;

        file_put_contents($serasa_bureau_api_request, $url);
        file_put_contents($serasa_bureau_api_response, $data);

        (new S3(new StaticUplexisBucket()))->save(
            "captura/serasa_bureau_api/request_" . $file,
            $serasa_bureau_api_request
        );
        (new S3(new StaticUplexisBucket()))->save(
            "captura/serasa_bureau_api/response_" . $file,
            $serasa_bureau_api_response
        );
    }

    /**
     * Função de separar string
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $str
     *
     * @return array
     */
    private function splitString($str)
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }

        $str_header = substr($str, 0, $this->start_output_position);

        $str_body = substr($str, $this->start_output_position);

        return array($str_header, $str_body);
    }

    /**
     * Função de checar tipo da resposta
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $str
     *
     * @return string
     */
    private function checkTypeResponse($str)
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }
        return substr($str, 57, 3); // tipo de resposta FIM ou CON
    }

    /**
     * Função de busca de fim da string
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return string
     */
    private function lookingForEndPositionOfString($str)
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }
        return stripos($str, 'T999'); // procurando posição final da string
    }

    /**
     * Função de busca do método
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $key
     *
     * @return string
     */
    private function getMethod($key)
    {
        return 'parse' . $key;
    }

    /**
     * Função de busca da resposta da chave
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $str
     *
     * @return string
     */
    private function getKeyResponse($str)
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }
        return substr(ltrim($str), 0, 4);
    }

    /**
     * Função de validação dos parâmetros
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @return void
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }

        if (!isset($this->param['cpf_cnpj']) or empty($this->param['cpf_cnpj'])) {
            throw new Exception('Parâmetro de documento inválido', 1);
        }

        if (
            !isset($this->auth['usuario']) ||
            empty($this->auth['usuario']) ||
            strlen($this->auth['usuario']) > 8
        ) {
            throw new Exception('Parâmetro de usuário inválido', 1);
        }

        if (
            !isset($this->auth['senha']) ||
            empty($this->auth['senha']) ||
            strlen($this->auth['senha']) > 8
        ) {
            throw new Exception('Parâmetro de senha inválido', 1);
        }
    }

    /**
     * Checa a resposta da API
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR e adicionado catch de erro
     *
     * @param string $response
     *
     * @return string
     */
    private function checkResponse($response)
    {
        $this->validateLoginErrors($response);

        if (preg_match("#USUARIO\\/SENHA\\s*INVALIDO#is", $response)) {
            throw new Exception("USUARIO SENHA INVALIDO", 3);
        }

        if (preg_match("#AUTORIZACAO\\s*CANCELADA#is", $response)) {
            throw new Exception("AUTORIZACAO CANCELADA", 3);
        }

        if (preg_match("#NOVA\\s*PASSWORD\\s*INVALIDA#is", $response)) {
            throw new Exception("USUÁRIO OU SENHA FORA DO PADRÂO", 3);
        }

        if (preg_match("#INCONSISTENCIAS\\s*NOS\\s*DADOS\\s*ENVIADOS#is", $response)) {
            throw new Exception("INCONSISTENCIAS NOS DADOS ENVIADOS", 3);
        }

        if (preg_match("#USUARIO\\s*NAO\\s*AUTORIZADO#is", $response)) {
            throw new Exception("USUARIO NAO AUTORIZADO", 3);
        }

        if (preg_match("#ACESSO\\s*BLOQUEADO#", $response)) {
            throw new Exception("USUÁRIO BLOQUEADO", 3);
        }

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB001($string)
    {
        $response = [];

        $fieldSequence = array(4, 45, 11, 15, 8, 4, 1, 4, 9, 1, 8, 1, 1, 1, 1, 1);

        list(
            $response['tipo_de_registro'],
            $response['grafia'],
            $response['cpf'],
            $response['rg'],
            $response['data_nasc'],
            $response['cod_cidade'],
            $response['titular'],
            $response['filler'],
            $response['link_n'],
            $response['situacao'],
            $response['data_atual'],
            $response['reservado'],
            $response['indic_erro'],
            $response['excluir_graf'],
            $response['nova_situacao'],
            $response['filler2']
        ) = $this->splitByLengths($string, $fieldSequence);

        $response['situacao'] = $this->tabSituacaoCpf($response['situacao']);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB002($string)
    {
        $response = [];

        $fieldSequence = array(4, 3, 8, 8, 45, 1, 15, 15, 5, 8, 2, 1);

        list(
            $response['tipo_de_registro'],
            $response['filler'],
            $response['data_atualiza'],
            $response['data_nasc'],
            $response['nome_mae'],
            $response['sexo'],
            $response['tipo_doc'],
            $response['num_doc'],
            $response['orgao_emissor'],
            $response['data_emissor'],
            $response['uf_emissor'],
            $response['filler2']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB003($string)
    {
        $response = [];

        $fieldSequence = array(4, 12, 2, 12, 25, 2, 11, 4, 8, 4, 8, 4, 8, 2, 9);

        list(
            $response['tipo_de_registro'],
            $response['estado_civil'],
            $response['depend'],
            $response['escolar'],
            $response['mun_nasc'],
            $response['uf_nasc'],
            $response['cpf_conjuge'],
            $response['ddd_res'],
            $response['fone_res'],
            $response['ddd_coml'],
            $response['fone_coml'],
            $response['ramal'],
            $response['celular'],
            $response['cod_cliente'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        $response['estado_civil'] = $this->tab5($response['estado_civil']);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseA900($string)
    {
        $response = [];

        $fieldSequence = array(4, 6, 32, 70);

        list(
            $response['tipo_de_registro'],
            $response['codigo'],
            $response['msg_reduzida'],
            $response['msg_completa']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB004($string)
    {
        $response = [];

        $fieldSequence = array(4, 30, 5, 10, 20, 25, 2, 8, 6, 5);

        list(
            $response['tipo_de_registro'],
            $response['logradouro'],
            $response['numero'],
            $response['complemento'],
            $response['bairro'],
            $response['cidade'],
            $response['uf'],
            $response['cep'],
            $response['desde'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB005($string)
    {
        $response = [];

        $fieldSequence = array(4, 10, 9, 5, 7, 1, 3, 9, 9, 9, 6, 4, 5, 9, 25);


        list(
            $response['tipo_de_registro'],
            $response['ocupacao'],
            $response['ra'],
            $response['ct_serie'],
            $response['num_ct'],
            $response['filler'],
            $response['perc_partic'],
            $response['ra_med'],
            $response['limite_1'],
            $response['limite_98'],
            $response['qtde_obs'],
            $response['tipo_tecnolog'],
            $response['canal_va'],
            $response['vlr_aluguel'],
            $response['filler2']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB006($string)
    {
        $response = [];

        $fieldSequence = array(4, 40, 6, 3, 30, 20, 12);

        list(
            $response['tipo_de_registro'],
            $response['empresa'],
            $response['desde'],
            $response['tipo_moeda'],
            $response['profissao'],
            $response['cargo'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB352($string)
    {
        $response = [];

        $fieldSequence = array(4, 40, 8, 5, 2, 43, 6, 6, 1);

        list(
            $response['tipo_de_registro'],
            $response['empresa'],
            $response['cnpj'],
            $response['perc_partic'],
            $response['estado'],
            $response['situacao'],
            $response['dt_ini_partic'],
            $response['dt_ult_atu'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB353($string)
    {
        $response = [];

        $fieldSequence = array(4, 3, 6, 3, 3, 3, 3, 3, 3, 3, 3, 3, 8, 8, 59);

        list(
            $response['tipo_de_registro'],
            $response['qtde_total_cred'],
            $response['dt_atual'],
            $response['qtde_atual_cred'],
            $response['qtde_mes_1_cred'],
            $response['qtde_mes_2_cred'],
            $response['qtde_mes_3_cred'],
            $response['qtde_total_cheq'],
            $response['qtde_atual_cheq'],
            $response['qtde_mes_1_cheq'],
            $response['qtde_mes_2_cheq'],
            $response['qtde_mes_3_cheq'],
            $response['data_atualiza'],
            $response['data_ficad'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB354($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 40, 12, 3, 9, 39);

        list(
            $response['tipo_de_registro'],
            $response['data_ocorr'],
            $response['origem'],
            $response['modalidade'],
            $response['tipo_moeda'],
            $response['valor'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB356($string)
    {
        $response = [];

        $fieldSequence = array(4, 4, 20, 8, 6, 6, 10, 13, 4, 1, 13, 1, 25);

        list(
            $response['tipo_de_registro'],
            $response['agencia'],
            $response['banco'],
            $response['conta_corr'],
            $response['ch_inicial'],
            $response['ch_final'],
            $response['motivo_susta'],
            $response['informe'],
            $response['fonte'],
            $response['mensagem'],
            $response['documento'],
            $response['tp_docto'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB357($string)
    {
        $response = [];

        $fieldSequence = array(4, 5, 28, 6, 6, 3, 9, 20, 4, 2, 28);


        list(
            $response['tipo_de_registro'],
            $response['qtde_total'],
            $response['descricao'],
            $response['data_menor'],
            $response['data_maior'],
            $response['tip_moeda'],
            $response['vlr_ultima'],
            $response['origem'],
            $response['filial'],
            $response['tipo_pefin'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB358($string)
    {
        $response = [];

        $fieldSequence = array(4, 2, 12, 1, 10, 2, 8, 2, 1, 3, 9, 17, 20, 4, 5, 4, 1, 2, 8);


        list(
            $response['tipo_de_registro'],
            $response['tipo_pefin'],
            $response['modalidade'],
            $response['tipo_ocor'],
            $response['chave_cadus'],
            $response['filler'],
            $response['data_ocorr'],
            $response['sigla_mod'],
            $response['principal'],
            $response['tipo_moeda'],
            $response['valor'],
            $response['contrato'],
            $response['origem'],
            $response['filial'],
            $response['qtde_ocorr'],
            $response['cod_banco'],
            $response['subjud_x'],
            $response['uf'],
            $response['filler2']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB359($string)
    {
        $response = [];

        $fieldSequence = array(4, 5, 28, 6, 6, 3, 9, 20, 4, 30);

        list(
            $response['tipo_de_registro'],
            $response['qtde_total'],
            $response['descricao'],
            $response['data_menor'],
            $response['data_maior'],
            $response['tip_moeda'],
            $response['vlr_ultima'],
            $response['origem'],
            $response['filial'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB360($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 6, 2, 4, 3, 9, 3, 11, 16, 4, 25, 2, 5, 1, 9, 3);

        list(
            $response['tipo_de_registro'],
            $response['data_ocorr'],
            $response['num_cheque'],
            $response['alinea'],
            $response['qtde'],
            $response['tipo_moeda'],
            $response['valor'],
            $response['banco'],
            $response['chave_cadus'],
            $response['filler'],
            $response['agencia'],
            $response['cidade'],
            $response['uf'],
            $response['qtde_ocorr'],
            $response['tipo_conta'],
            $response['conta'],
            $response['filler2']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB361($string)
    {
        $response = [];

        $fieldSequence = array(4, 5, 28, 6, 6, 3, 9, 25, 2, 27);

        list(
            $response['tipo_de_registro'],
            $response['qtde_total'],
            $response['descricao'],
            $response['data_menor'],
            $response['data_maior'],
            $response['tip_moeda'],
            $response['valor'],
            $response['cidade'],
            $response['uf'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB362($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 3, 9, 4, 25, 2, 5, 1, 8, 1, 10, 35);

        list(
            $response['tipo_de_registro'],
            $response['data_ocorr'],
            $response['tipo_moeda'],
            $response['valor'],
            $response['cartorio'],
            $response['cidade'],
            $response['uf'],
            $response['qtde_ocorr'],
            $response['subjudice'],
            $response['data'],
            $response['tipo_ocorr'],
            $response['chave_cadus'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB363($string)
    {
        $response = [];

        $fieldSequence = array(4, 5, 28, 6, 6, 3, 9, 20, 2, 32);

        list(
            $response['tipo_de_registro'],
            $response['qtde_total'],
            $response['descricao'],
            $response['data_menor'],
            $response['data_maior'],
            $response['tip_moeda'],
            $response['valor'],
            $response['natureza'],
            $response['uf'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB364($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 20, 1, 3, 9, 4, 4, 25, 2, 5, 1, 1, 10, 18);

        list(
            $response['tipo_de_registro'],
            $response['data_ocorr'],
            $response['natureza'],
            $response['principal'],
            $response['tipo_moeda'],
            $response['valor'],
            $response['distribuidor'],
            $response['vara'],
            $response['cidade'],
            $response['uf'],
            $response['qtde_ocorr'],
            $response['subjudice'],
            $response['tipo_ocorr'],
            $response['chave_cadus'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB365($string)
    {
        $response = [];

        $fieldSequence = array(4, 5, 28, 6, 6, 40, 26);

        list(
            $response['tipo_de_registro'],
            $response['total_ocorr'],
            $response['descricao'],
            $response['data_menor'],
            $response['data_maior'],
            $response['empresa'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB366($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 10, 14, 45, 5, 3, 4, 1, 10, 11);

        list(
            $response['tipo_de_registro'],
            $response['data_ocorr'],
            $response['tipo_ocorr'],
            $response['cnpj_pie'],
            $response['empresa'],
            $response['total_ocorr'],
            $response['qualif'],
            $response['vara_civil'],
            $response['filler'],
            $response['chave_cadus'],
            $response['filler2']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB367($string)
    {
        $response = [];

        $fieldSequence = array(4, 5, 28, 6, 6, 3, 9, 20, 4, 30);

        list(
            $response['tipo_de_registro'],
            $response['qtde_total'],
            $response['descricao'],
            $response['data_menor'],
            $response['data_maior'],
            $response['tip_moeda'],
            $response['vlr_ultima'],
            $response['origem'],
            $response['local'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function parseB368($string)
    {
        $response = [];

        $fieldSequence = array(4, 8, 2, 3, 9, 17, 20, 4, 5, 1, 10, 32);

        list(
            $response['tipo_de_registro'],
            $response['data_ocorr'],
            $response['modalidade'],
            $response['tipo_moeda'],
            $response['valor'],
            $response['titulo'],
            $response['origem'],
            $response['local'],
            $response['qtde_ocorr'],
            $response['tipo_ocorr'],
            $response['chave_cadus'],
            $response['filler']
        ) = $this->splitByLengths($string, $fieldSequence);

        $response['modalidade'] = $this->tab7($response['modalidade']);

        return $response;
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function splitByLengths($string, $arrayLengths)
    {
        $output = [];
        foreach ($arrayLengths as $oneLength) {
            $output[] = substr($string, 0, $oneLength);
            $string = substr($string, $oneLength);
        }

        return ($output);
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return array
     */
    private function getCodKey($key)
    {
        if ($this->debug) {
            print(PHP_EOL . __METHOD__ . PHP_EOL);
        }
        $cod = array(
            'A900' => 'mensagem',
            'B001' => 'grafia_documento_consultado',
            'B002' => 'dados_auxiliares_identidade',
            'B003' => 'dados_auxiliares_complemento',
            'B004' => 'dados_auxiliares_endereco',
            'B005' => 'dados_auxiliares_ocupacao',
            'B006' => 'dados_auxiliares_empresa',
            'B352' => 'participacao_societaria',
            'B353' => 'registro_de_consultas',
            'B354' => 'detalhe_registro_de_consultas',
            'B356' => 'detalhe_cheques_sustados',
            'B357' => 'resumo_pendencia_pagamento',
            'B358' => 'detalhe_pendencia_pagamento',
            'B359' => 'resumo_cheques_sem_fundos',
            'B360' => 'detalhe_cheques_sem_fundos',
            'B361' => 'resumo_protestos',
            'B362' => 'detalhe_protestos',
            'B363' => 'resumo_acoes_judicias',
            'B364' => 'detalhe_acoes_judiciais',
            'B365' => 'resumo_participacao_falencias',
            'B366' => 'detalhe_participacao_falencias',
            'B367' => 'resumo_convem_devedores',
            'B368' => 'detalhe_convem_devedores',
            'B370' => 'enderecos_telefones'
        );

        if (!isset($cod[$key])) {
            return '';
        }

        return $cod[$key];
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return string
     */
    private function tabSituacaoCpf($string)
    {
        switch ($string) {
            case '0':
                return 'CPF não confirmado no cadastro até esta data';
            case '2':
                return 'Ativo';
            case '6':
                return 'Suspenso';
            case '8':
                return 'Novo (Ativo)';
            case '9':
                return 'Cancelado';
        }
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return string
     */
    private function tab1($string)
    {
        switch ($string) {
            case '1':
                return '1º Grau';
            case '2':
                return '2º Grau';
            case '3':
                return 'Superior Com';
            case '4':
                return 'Superior Inc';
            case '5':
                return 'Pós Grad';
        }
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return string
     */
    private function tab2($string)
    {
        switch ($string) {
            case '1':
                return 'Empregado';
            case '2':
                return 'Sócio Prop';
            case '3':
                return 'Vive Renda';
            case '4':
                return 'Func Públ';
            case '5':
                return 'Autônomo';
            case '6':
                return 'Prof Liber';
            case '7':
                return 'Aposentado';
            case '9':
                return 'Outro';
        }
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return string
     */
    private function tab3($string)
    {
        switch ($string) {
            case '1':
                return 'Quinzenal';
            case '2':
                return 'Mensal';
            case '3':
                return 'Bimestral';
            case '4':
                return 'Trimestral';
            case '5':
                return 'Semestral';
            case '6':
                return 'Anual';
            case '9':
                return 'Outra';
        }
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return string
     */
    private function tab4($string)
    {
        switch ($string) {
            case '1':
                return 'Salário';
            case '2':
                return 'Alugel';
            case '3':
                return 'Dividendos';
            case '4':
                return 'Pensão';
            case '5':
                return 'Aposentadoria';
            case '6':
                return 'Juros';
            case '7':
                return 'Pró-Labore';
            case '9':
                return 'Outra';
        }
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return string
     */
    private function tab5($string)
    {
        switch ($string) {
            case '1':
                return 'Casado';
            case '2':
                return 'Solteiro';
            case '3':
                return 'Viúvo';
            case '4':
                return 'Divorciado';
            case '5':
                return 'Separado';
            case '9':
                return 'Outro';
        }
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return string
     */
    private function tab6($string)
    {
        switch ($string) {
            case '1':
                return 'Própria';
            case '2':
                return 'Financiada';
            case '3':
                return 'Alugada';
            case '4':
                return 'Familiar';
            case '5':
                return 'Funcional';
            case '9':
                return 'outra';
        }
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return string
     */
    private function tab7($string)
    {
        switch ($string) {
            case 'AD':
                return 'Adiant Conta';
            case 'AG':
                return 'Emprestimos';
            case 'AR':
                return 'Leasing';
            case 'AV':
                return 'Ch Vista';
            case 'C1':
                return 'Cons Imóveis';
            case 'C2':
                return 'Cons Vei Pes';
            case 'C3':
                return 'Cons Veiculo';
            case 'C4':
                return 'Cons Motos';
            case 'C5':
                return 'Cons Bens';
            case 'C6':
                return 'Cons Aereo';
            case 'CA':
                return 'Oper Cambio';
            case 'CB':
                return 'Cartao Debit';
            case 'CD':
                return 'Crediario';
            case 'CH':
                return 'Cheque';
            case 'CO':
                return 'Consorcio';
            case 'CP':
                return 'Cred Pessoal';
            case 'CQ':
                return 'Ch Predatado';
            case 'CR':
                return 'Impedido Bc';
            case 'CS':
                return 'Cred Seguro';
            case 'CT':
                return 'Cred Cartao';
            case 'CV':
                return 'Cred Veiculo';
            case 'DB':
                return 'Debito em Co';
            case 'DC':
                return 'Dividas Cheq';
            case 'DE':
                return 'Cheque Elet';
            case 'DP':
                return 'Duplicata';
            case 'EC':
                return 'Empres Conta';
            case 'EF':
                return 'Empres Folha';
            case 'FI':
                return 'Financiamento';
            case 'IM':
                return 'Oper Imobili';
            case 'OJ':
                return 'Oper Ajuizad';
            case 'OO':
                return 'Outras Oper';
            case 'OU':
                return 'Outros Cred';
            case 'RC':
                return 'Recuper Cred';
            case 'RE':
                return 'Repasse';
            case 'SA':
                return 'Seguro Auto';
            case 'SE':
                return 'Seg Elementa';
            case 'SF':
                return 'Seguro Fianc';
            case 'SO':
                return 'Seguro Outro';
            case 'SR':
                return 'Seguro Risco';
            case 'SS':
                return 'Seguro Saude';
            case 'ST':
                return 'ServTelecom';
            case 'SV':
                return 'Seguro Vida';
            case 'TD':
                return 'Tit Desconta';
            case 'TF':
                return 'Telefonia Fixa';
        }
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return string
     */
    private function tab11($string)
    {
        switch ($string) {
            case '1':
                return 'Casa';
            case '2':
                return 'Apartamento';
            case '3':
                return 'Terreno';
            case '4':
                return 'Sítio';
            case '5':
                return 'Fazenda';
            case '6':
                return 'Loja';
            case '9':
                return 'Outro';
        }
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return string
     */
    private function tab12($string)
    {
        switch ($string) {
            case '1':
                return 'Amex';
            case '2':
                return 'Credicard';
            case '3':
                return 'Diners';
            case '4':
                return 'Visa';
            case '5':
                return 'Mastercard';
            case '9':
                return 'Outro';
        }
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return string
     */
    private function tab13($string)
    {
        switch ($string) {
            case '1':
                return 'Automóvel';
            case '2':
                return 'Saúde';
            case '3':
                return 'Vida';
            case '4':
                return 'Fiança Locatícia';
            case '5':
                return 'Ramos Elementares';
            case '9':
                return 'Outro';
        }
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return string
     */
    private function tab14($string)
    {
        switch ($string) {
            case '1':
                return 'Filho';
            case '2':
                return 'Pais';
            case '3':
                return 'Irmão';
            case '9':
                return 'Outro parente';
        }
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return string
     */
    private function tab16($string)
    {
        switch ($string) {
            case '1':
                return 'Residencial';
            case '2':
                return 'Comercial';
            case '3':
                return 'Outra Atividade';
        }
    }

    /**
     * Função de parseamento da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param string $string
     *
     * @return string
     */
    private function tab26($string)
    {
        switch ($string) {
            case '1':
                return 'RG';
            case '2':
                return 'Carteira Estrangeiro';
            case '3':
                return 'Carteira de Trabalho';
            case '4':
                return 'Certificado Militar';
            case '5':
                return 'Carteira Profissional';
            case '6':
                return 'Passaporte';
        }
    }

    /**
     * Gerar uma resposta falsa da Serasa
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Favoreto Makhoul - 25/06/2018 - Correção da PSR
     *
     * @param void
     *
     * @return string
     */
    private function getResponseSerasaFake()
    {
        return file_get_contents(__DIR__ . '/SerasaFakeResponse.txt');
    }
}
