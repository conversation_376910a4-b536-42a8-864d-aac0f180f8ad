<?php

namespace App\Crawler\MunicipiosFronteiricos;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Factory\MongoDB;
use App\Manager\DynamoManager;
use Exception;

class MunicipiosFronteiricos extends Spider
{
    private $cnpj;
    private $dynamoManager;
    public function start()
    {
        $this->dynamoManager = new DynamoManager();
        $result = $this->getResults();
        if (empty($result)) {
            throw new Exception("Nenhum Resultado Encontrado", 2);
        }
        return $result;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->cnpj = Document::removeMask($this->param['cnpj']);
    }

    private function getResults()
    {
        $spine = $this->dynamoManager->getItem('spine_pj', [
            'cnpj' => $this->cnpj
        ]);

        $cidade = trim($spine['cidade']);
        $uf = trim($spine['uf']);

        $manager = (new MongoDB())
            ->connectSources()
            ->setDb('common')
            ->setCollection('municipios_fronteiricos');

        $result = $manager->textSearch("\"{$cidade}\" \"{$uf}\"", null, [], false);
        $result = json_decode(json_encode($result->toArray(), true), true);

        return $result;
    }
}
