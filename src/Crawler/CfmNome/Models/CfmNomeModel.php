<?php

namespace App\Crawler\CfmNome\Models;

use Exception;

class CfmNomeModel
{
    public $nome;
    public $crm;
    public $uf;
    public $data_de_inscricao;
    public $tipo_inscricao;
    public $situacao;
    public $inscricao;
    public $area_atuacao;
    public $endereco;
    public $telefone;
    public $imagem;
    public $sexo;
    public $existe_endereco = false;

    public function __set($name, $value)
    {
        if (property_exists($this, $name)) {
            return $this->$name = $value;
        }

        throw new Exception("Propriedade set '{$name}' não existe", 3);
    }

    public function __get($name)
    {
        if (property_exists($this, $name)) {
            return $this->$name;
        }

        throw new Exception("Propriedade get '{$name}'  não existe", 3);
    }
}
