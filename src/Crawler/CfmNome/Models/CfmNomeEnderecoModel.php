<?php

namespace App\Crawler\CfmNome\Models;

use Exception;

class CfmNomeEnderecoModel
{
    public $logradouro;
    public $bairro;
    public $cep;
    public $cidade;

    public function __set($name, $value)
    {
        if (property_exists($this, $name)) {
            return $this->$name = $value;
        }

        throw new Exception("Propriedade set '{$name}' não existe", 3);
    }

    public function __get($name)
    {
        if (property_exists($this, $name)) {
            return $this->$name;
        }

        throw new Exception("Propriedade get '{$name}'  não existe", 3);
    }
}
