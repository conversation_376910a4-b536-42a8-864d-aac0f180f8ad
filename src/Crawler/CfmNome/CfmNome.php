<?php

namespace App\Crawler\CfmNome;

use App\Crawler\CfmNome\Models\CfmNomeEnderecoModel;
use App\Crawler\CfmNome\Models\CfmNomeModel;
use App\Crawler\Spider;
use Exception;
use App\Helper\Document;

/**
 * Captura do Conselho Federal de Medicina
 */
class CfmNome extends Spider
{
    private const BASE_URL    = 'http://portal.cfm.org.br';
    private const API_INFO_URL = 'https://portal.cfm.org.br/api_rest_php/api/v1/medicos/buscar_medicos';
    private const API_DETAILS_URL = 'https://portal.cfm.org.br/api_rest_php/api/v1/medicos/buscar_foto/';
    private const RETRY = 10;

    private $doctors = [];
    private $registros = 0;
    private $existeMaisResultados = 0;

    /**
     * Metodo principal.
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Mel<PERSON> - data 05/02/2020
     */
    protected function start()
    {
        $this->setProxy();

        $this->getResult();

        if (empty($this->doctors)) {
            throw new Exception('Nenhum registro encontrado', 2);
        }

        //verifica se existe mais resultados no site do que na saida da fonte, se a saida for 1 existe.
        if (count($this->doctors) < $this->registros) {
            $this->existeMaisResultados = 1;
        }
        $this->doctors[0]['existeMaisResultados'] = $this->existeMaisResultados;

        return $this->doctors;
    }

    /**
     * Validar parâmetros.
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     *
     *@throws Exception
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (!isset($this->param['nome']) || empty($this->param['nome'])) {
            throw new Exception('Parâmetro Nome inválido', $this->PARAM_ERROR);
        }

        if (strlen(trim($this->param['nome'])) > 200) {
            throw new Exception("Parâmetro Nome não pode ter mais que 200 caracteres", $this->PARAM_ERROR);
        }

        if (Document::validarCpfOuCnpj($this->param['nome'])) {
            throw new Exception("A consulta deve ser feita apenas por nome", 6);
        }
    }

    /**
     * Quebra o captcha.
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Vidal - 19/02/2021 - Fonte passou a utilizar reCaptcha v3
     *
     * @param $html string
     *
     * @return string
     */
    private function resolveReCaptcha($html)
    {
        preg_match(
            '/grecaptcha\.execute\(\s*\'(.*?)\'.*action:\s*\'(.*?)\'\s*}\s*/isu',
            $html,
            $captchaMatch
        );

        if (empty($captchaMatch[1]) || empty($captchaMatch[2])) {
            throw new Exception("Erro ao localizar dados do captcha na página.", 3);
        }

        $captcha = $this->solveReCaptchaV3($captchaMatch[1], $captchaMatch[2], self::BASE_URL);

        return $captcha;
    }

    /**
     * Monta o array de parâmetros e faz a busca.
     * Informando o parâmetro page, será retornado o resultado da página informada.
     * Só passar parâmetro page caso exista paginação.
     *
     * @version 1.0.0
     *
     * <AUTHOR> Melo - data 05/02/2020
     * <AUTHOR> Vidal 19/02/2021 - Parâmetros alterados para fazer a requisição direto para a API.
     *
     * @param int
     *
     * @return array
     */
    private function makeResquest($page = 1)
    {
        $params = [
            [
                "captcha" => $this->captcha,
                "medico" => [
                    'nome' => $this->param['nome'],
                    'ufMedico' => '',
                    'crmMedico' => '',
                    'municipioMedico' => '',
                    'tipoInscricaoMedico' => '',
                    'situacaoMedico' => '',
                    'detalheSituacaoMedico' => '',
                    'especialidadeMedico' => '',
                    'areaAtuacaoMedico' => ''
                ],
                "page" => $page,
                "pageNumber" => $page,
                "pageSize" => 10
            ],
        ];

        $response = $this->getResponse(self::API_INFO_URL, 'POST', json_encode($params));
        return json_decode($response, true);
    }

    /**
     * Checar se há possíveis mensagens de erro na pesquisa.
     *
     * @version 1.0.0
     *
     * <AUTHOR>
     * <AUTHOR> Melo - data 05/02/2020
     *
     * @param string
     *
     *@throws Exception
     */
    private function checkResponse($response)
    {
        if ($response['status'] != 'sucesso') {
            throw new Exception('Não foi possível processar a requisição', 3);
        }
    }

    /**
     * Pega o resultado de todas as paginas da busca.
     *
     * @version 1.0.0
     *
     * <AUTHOR> Melo - data 05/02/2020
     * <AUTHOR> Vidal - 19/02/2021 - Método alterado para trabalhar com a paginação da API.
     */
    public function getResult()
    {
        $i = 0;
        $retries = 1;

        $html = $this->getResponse(self::BASE_URL);
        $response = '';

        while (self::RETRY > $retries) {
            echo PHP_EOL . "Tentativa nº: {$retries}/" . self::RETRY . PHP_EOL;
            $this->setAlternativeProxy();
            $this->captcha = $this->resolveReCaptcha($html);
            $response = $this->makeResquest();

            if (isset($response['dados'])) {
                break;
            }

            $retries++;
        }

        $this->checkResponse($response);

        $this->registros = isset($response['dados'][0]['COUNT']) ? $response['dados'][0]['COUNT'] : 0;

        if ($this->registros == 0) {
            throw new Exception('Nada encontrado!', 2);
        }

        $this->stepListDoctors($response);
        if (count($this->doctors) > $this->param['limite'] || count($this->doctors) > $this->param['limite']) {
            $unset = count($this->doctors) - $this->param['limite'];
            while ($i < $unset) {
                array_pop($this->doctors);
                $i++;
            }
        }
    }

    /**
     * Faz a iteração em cada médico encontrado na resposta da API.
     *
     * @version 1.0.0
     *
     * <AUTHOR> Vidal - 19/02/2021
     *
     * @param array $pageResult
     */
    private function stepListDoctors($pageResult)
    {
        foreach ($pageResult['dados'] as $result) {
            $details = json_decode(
                $this->getResponseByFileGet(self::API_DETAILS_URL . $result['NU_CRM'] . '/' . $result['SG_UF'], []),
                true
            );
            $this->doctors[] = $this->stepDoctorsInfo($result, $details['dados'][0]);
        }
    }

    /**
     * Faz o parse dos dados obtidos
     *
     * @version 1.0.0
     *
     * <AUTHOR> Vidal - 19/02/2021
     *
     * @param array $info
     * @param array $details
     *
     * @return array
     */
    private function stepDoctorsInfo($info, $details)
    {
        $cfmDoctor = new CfmNomeModel();
        $cfmEndereco = new CfmNomeEnderecoModel();

        $cfmDoctor->nome = $info['NM_MEDICO'];
        $cfmDoctor->crm = $info['NU_CRM'];
        $cfmDoctor->uf = $info['SG_UF'];
        $cfmDoctor->data_de_inscricao = $info['DT_INSCRICAO'];
        $cfmDoctor->tipo_inscricao = $info['TIPO_INSCRICAO'];
        $cfmDoctor->situacao = $info['SITUACAO'];
        $cfmDoctor->inscricao = $info['SG_UF'] . '/' . $info['NU_CRM'];
        $cfmDoctor->area_atuacao = preg_replace('/&/isU', '', $info['ESPECIALIDADE']);
        $cfmDoctor->imagem = '';
        $cfmDoctor->sexo = '';
        $cfmDoctor->telefone = $details['TELEFONE'];

        // if (!empty($details['IMAGEM'])) {
        //     $cfmDoctor->imagem = self::IMAGE_DEFAULT . $details['IMAGEM'] ;
        // }

        $cfmEndereco->logradouro = '';
        $cfmEndereco->bairro  = '';
        $cfmEndereco->cep  = '';
        $cfmEndereco->cidade  = '';

        $endereco = $details['ENDERECO'];

        // Se entrar no if abaixo, o médico tem endereço cadastrado
        if (!preg_match('/\s*\-\s*\-\s*-/u', $endereco)) {
            list(
                $cfmEndereco->logradouro,
                $cfmEndereco->bairro,
                $cfmEndereco->cep,
                $cfmEndereco->cidade
            ) = explode('-', $endereco);

            $cfmDoctor->existe_endereco = true;
        }

        //Transformar objeto em array
        $result = $this->objectToArray($cfmDoctor);
        $result['endereco'] = $this->objectToArray($cfmEndereco);

        return $result;
    }

    /**
     * Método responsável por transformar um objeto em array
     * <AUTHOR> Vidal - 19/02/2021
     *
     * @return array
     */
    private function objectToArray($data)
    {
        if (is_array($data) || is_object($data)) {
            $result = array();
            foreach ($data as $key => $value) {
                $result[$key] = $this->objectToArray(html_entity_decode($value));
            }
            return $result;
        }
        return $data;
    }
}
