<?php

namespace App\Crawler\Offshore;

use App\Factory\PostgresDB;

class OffshoreDatabase
{
    /**
     * @Inject
     * @var PostgresDB
     */
    private $db;

    // Constantes de tabelas
    private const TABLE_ADDRESSES = "offshore_addresses";
    private const TABLE_ENTITIES = "offshore_entities";
    private const TABLE_INTERMEDIARIES = "offshore_intermediaries";
    private const TABLE_OFFICERS = "offshore_officers";
    private const TABLE_CONNECTIONS = "offshore_all_edges";
    private const TABLE_COUNTRIES = "offshore_countries";
    private const TABLE_JURISDICTIONS = "offshore_jurisdictions";

    // Constantes de resultado de nós
    private const DATA_NODE = 0;
    private const INFORMATION_NODE = 1;

    // Default
    private $default_match_exact = false;

    private $default_fields = [
        'officers' => [
            'id',
            'name',
            'icij_id',
            'valid_until',
            'country_codes',
            'countries',
            'node_id',
            'sourceid'
        ],
        'entities' => [
            'id',
            'name',
            'original_name',
            'former_name',
            'jurisdiction',
            'jurisdiction_description',
            'company_type',
            'address',
            'internal_id',
            'incorporation_date',
            'inactivation_date',
            'struck_off_date',
            'dorm_date',
            'status',
            'service_provider',
            'ibcRUC',
            'country_codes',
            'countries',
            'note',
            'valid_until',
            'node_id',
            'sourceid'
        ],
        'intermediaries' => [
            'id',
            'name',
            'internal_id',
            'address',
            'valid_until',
            'country_codes',
            'countries',
            'status',
            'node_id',
            'sourceid'
        ],
        'addresses' => [
            'id',
            'address',
            'icij_id',
            'valid_until',
            'country_codes',
            'countries',
            'node_id',
            'sourceid'
        ]
    ];

    // Tradução dos tipos de relação
    private $t_relation_types = [
        "alternate director of"         => "Conselheiro Suplemente",
        "appointor of"                  => "Nomeado",
        "assistant secretary of"        => "Assistente Secretário",
        "auditor of"                    => "Auditor",
        "authorised person / signatory of" => "Pessoa / Signatário Autorizado",
        "authorized signatory of"       => "Signatário Autorizado",
        "auth. representative of"       => "Representante Autorizado",
        "bank signatory of"             => "Banco Signatário",
        "beneficial owner of"           => "Proprietário Beneficiário",
        "beneficiary of"                => "Proprieario",
        "beneficiary, shareholder and director of" => "beneficiário, Acionista e Diretor",
        "board representative of"       => "Conselheiro Representante",
        "chairman of"                   => "Presidente",
        "connected of"                  => "Conectado",
        "correspondent addr. of"        => "Endereço Correspondente",
        "co-trustee of trust of"        => "Co-administrador de Confiança",
        "custodian of"                  => "Zelador",
        "director and shareholder of"   => "Diretor e Acionista",
        "director / beneficial owner of" => "Diretor / Beneficiário Proprietário",
        "director of"                    => "Diretor",
        "director (rami makhlouf) of"    => "Diretor (rami makhlouf)",
        "director / shareholder / beneficial owner of" => "Diretor / Acionista / Beneficiário",
        "director / shareholder of"     => "Diretor / Beneficiário",
        "first beneficiary of"          => "Primeiro Beneficiário",
        "general accountant of"         => "Contador Geral",
        "grantee of a mortgage of"      => "Beneficiário da Hipoteca",
        "intermediary of"               => "Intermediary",
        "investment advisor of"         => "Consultor de Investimentos",
        "joint settlor of"              => "Instituidor Conjunto",
        "legal advisor of"              => "Assessor Jurídico",
        "member of foundation council of" => "Membro do Conselho da Fundação",
        "member / shareholder of"       => "Membro / Acionista",
        "nominated person of"           => "Pessoa nomeada",
        "nominee beneficial owner of"   => "Beneficiário Proprietário Representante",
        "nominee beneficiary of"        => "Beneficiário Representante",
        "nominee director of"           => "Diretor Representante",
        "nominee investment advisor of" => "Conselheiro de Investimentos Representante",
        "nominee name of"               => "Nome Representante",
        "nominee protector of"          => "Protetor Representante",
        "nominee secretary of"          => "Secretário Representante",
        "nominee shareholder of"        => "Acionista Representante",
        "nominee trust settlor of"      => "Instituidor de Confiança Representante",
        "officer of"                    => "Administrador",
        "owner, director and shareholder of" => "Proprietário, Diretor e Acionista",
        "owner of"                      => "Proprietário",
        "partner of"                    => "Parceiro",
        "personal directorship of"      => "Diretor Pessoal",
        "power of attorney of"          => "Procurador",
        "power of attorney / shareholder of" => "Procurador / Acionista",
        "president and director of"     => "Presidente e Diretor",
        "president - director of"       => "Presidente - Diretor",
        "president of"                  => "Presidente",
        "principal beneficiary of"      => "Principal Beneficiário",
        "protector of"                  => "Protetor",
        "records & registers of"        => "Registros",
        "registered address"            => "Endereço Registrado",
        "register of director of"       => "Registro de Endereço",
        "register of shareholder of"    => "Registro de Acionista",
        "related entity"                => "Entidade Relacionada",
        "reserve director of"           => "Diretor Reserva",
        "resident director of"          => "Diretor Residente",
        "safekeeping of"                => "Protetor",
        "same address as"               => "Mesmo Endereço",
        "same name and registration date as" => "Mesmo Nome e Data de Registro",
        "secretary of"                  => "Secretário",
        "shareholder of"                => "Acionista",
        "shareholder (through julex foundation) of" => "Acionista",
        "signatory of"                  => "Signatário",
        "similar name and address as"   => "Nome e Endereço Similar",
        "sole shareholder of"           => "Único Acionista",
        "sole signatory / beneficial owner of" => "Único Signatário / Portador Beneficiado",
        "sole signatory of"             => "Único Signatário",
        "stockbroker of"                => "Corretor de Ações",
        "successor protector of"        => "Protetor Sucessor",
        "tax advisor of"                => "Consultor Fiscal",
        "treasurer of"                  => "Tesoureiro",
        "trustee of trust of"           => "Instituidor de Confiança",
        "trust settlor of"              => "Instituidor de Confiança",
        "unit trust register of"        => "Unidade de Confiança Registrado",
        "vice president of"             => "Vice Presidente",
    ];

    public function __construct(PostgresDB $db)
    {
        $this->db = $db;
    }

    public function getResultsByName($table, $name, $field = 'name')
    {
        $where = $this->matchExactString($name, $field);
        $fields = $this->getFields($table);

        return $this->getDatabaseConnection()
            ->select($fields)
            ->from('common.offshore_' . $table)
            ->where($where)
            ->setParameter($field, $name)
            ->setMaxResults(20)
            ->execute()
            ->fetchAll();
    }

    /**
     * Setar match exato
     *
     * @param bool $match_exact
     */
    public function setMatchExact($match_exact)
    {
        $this->default_match_exact = $match_exact;
    }

    /**
     * Setar campos de retorno
     *
     * @param array $fields
     */
    public function setFields($fields)
    {
        if (! is_array($fields)) {
            return;
        }

        foreach ($fields as $table => $the_fields) {
            if (! is_array($the_fields)) {
                $the_fields = explode(",", $the_fields);
            }

            if (in_array($table, array_keys($this->default_fields))) {
                $this->default_fields[$table] = $the_fields;
            }
        }
    }

    /**
     * Retorna a lista de países cadastrados
     *
     * @return array
     */
    public function getAllCountries()
    {
        return $this->getDatabaseConnection()
            ->select('country_code, country_name')
            ->from('common.offshore_countries')
            ->execute()
            ->fetchAll();
    }

    /**
     * Busca por um país cadastrado
     *
     * @param string $country_search
     * @return array
     */
    public function getCountry($country_search)
    {
        return $this->getDatabaseConnection()
            ->select('country_code, country_name')
            ->from('common.offshore_countries')
            ->where('lower(country_code) = :country_search')
            ->orWhere('lower(country_name) = :country_search')
            ->setParameter(':country_search', strtolower($country_search))
            ->execute()
            ->fetch();
    }

    /**
     * Chamada direta para retornar os nós de uma pessoa/empresa
     *
     * @param int node_id
     * @param int type_result
     */
    public function getRelations($node_id)
    {
        return $this->getNodes($node_id, self::DATA_NODE);
    }

    /**
     * Retorna as conexões de uma pessoa/empresa
     * O type_result define se retorna os nós ou o resultado dos nós
     *
     * @param int node_id
     * @param int type_result
     */
    public function getNodes($node_id, $type_result = 0)
    {
        $nodes = $this->getDatabaseConnection()
            ->select('node_1, rel_type, node_2')
            ->from('common.offshore_all_edges')
            ->where('node_1 = :node_id')
            ->orWhere('node_2 = :node_id')
            ->setParameter('node_id', $node_id)
            ->setMaxResults(5)
            ->execute()
            ->fetchAll();

        $information_nodes = array();
        $nodes_result = array();

        // Retornar informações do nó
        if ($type_result == self::INFORMATION_NODE) {
            foreach ($nodes as $node) {
                $mode        = ($node['node_1'] == $node_id) ? "PARENT" : "CHILD";

                $this_node  = ($node['node_1'] == $node_id) ? $node['node_1'] : $node['node_2'];
                $other_node = ($node['node_1'] == $node_id) ? $node['node_2'] : $node['node_1'];
                $this_relation = $node['rel_type'];

                $information_nodes[] = array(
                    'relation_mode' => $mode,
                    'this_node'  => $this_node,
                    'other_node' => $other_node,
                    'relaction'  => $this_relation
                );
            }

            return $information_nodes;
        } else // Retornar consulta do nó
        {
            foreach ($nodes as $node) {
                $mode       = ($node['node_1'] == $node_id) ? "CHILD" : "PARENT";
                $other_node = ($node['node_1'] == $node_id) ? $node['node_2'] : $node['node_1'];
                $this_relation = $node['rel_type'];

                $bool = (in_array(strtolower($this_relation), array_keys($this->t_relation_types)));
                $relation_desc = $bool ? $this->t_relation_types[$this_relation] : $this_relation;
                //$relation_desc = $this->t_relation_types[$this_relation];

                $nodes_result[] = array_merge(
                    $this->getNodeResult($other_node),
                    array('relation_mode' => $mode,
                                                    'relation_type' => $this_relation,
                    'relation_desc' => $relation_desc )
                );
            }

            return $nodes_result;
        }

        return array();
    }

    /**
     * Retorna os dados de uma conexão em nó buscando em todas tabelas
     *
     * @param int $node_id
     */
    private function getNodeResult($node_id)
    {
        $result = array();
        $type = null;

        $nodes_search = array(
            'Addresses',
            'Entities',
            'Intermediaries',
            'Officers'
        );

        foreach ($nodes_search as $node_relation) {
            $search_function = "get{$node_relation}ByNode";
            $node_result = $this->$search_function($node_id);

            if (! empty($node_result)) {
                return array(
                    'relation_for'  => $node_relation,
                    'result'        => $node_result
                );
            }
        }

        /*
        if ($direction == "LEFT")
        {
            switch(strtolower($rel_type))
            {
                // Endereços
                case "registered address":
                case "same address as":
                    $type = "address";
                    $result = $this->getAddressByNode($node_id);
                break;
                case "intermediary of":
                case "similar name and address as":
                    $type = "intermediary";
                    $result = $this->getIntermediariesByNode($node_id);
                break;

                case "related entity":
                case "same name and registration date as":
                default:
                    $type = "entity";
                    $result = $this->getEntitiesByNode($node_id);
                break;
            }
        } */

        return array();
    }

    /**
     * Retorna os dados de uma conexão em nó usando LEFT JOIN
     *
     * @param int $node_id
     */
    public function getUndefinedNode($node_id)
    {

            $nodes_tables = array(
                'o' => 'officers',
                'e' => 'entities',
                'i' => 'intermediaries',
                'a' => 'addresses'
            );

            $fields = [];

            foreach ($nodes_tables as $alias => $table) {
                $f = $this->getFields($table, array("_prefix_" => "{$table}_"), true, false);

                // adicionar o alias na frente dos selects
                $fields[] = implode(',', array_map(function ($item) use ($alias) {
                    return $alias . '.' . $item;
                }, explode(',', $f)));
            }

            $node_result = $this->getDatabaseConnection()
                ->select(implode(', ', $fields))
                ->from('common.offshore_all_edges', 'all_edges')
                ->leftJoin('all_edges', 'common.offshore_intermediaries', 'i', 'i.node_id = :node_id')
                ->leftJoin('all_edges', 'common.offshore_entities', 'e', 'e.node_id = :node_id')
                ->leftJoin('all_edges', 'common.offshore_officers', 'o', 'o.node_id = :node_id')
                ->leftJoin('all_edges', 'common.offshore_addresses', 'a', 'a.node_id = :node_id')
                ->where('all_edges.node_1 = :node_id')
                ->orWhere('all_edges.node_2 = :node_id')
                ->setParameter('node_id', $node_id)
                ->execute()
                ->fetch();

            $result_table = '';
            foreach ($nodes_tables as $table) {
                if (! empty($node_result["{$table}_id"])) {
                    $result_table = $table;
                    break;
                }
            }

            $this_fields = $this->getFields($result_table, array(), false, false);
            $result = array();

            foreach ($this_fields as $the_field) {
                $result[$the_field] = $node_result["{$result_table}_{$the_field}"];
            }

            return array(
                'relation_for' => $result_table,
                'result' => $result
            );
    }

    /**
     * Retornar um nó relacionado a um address
     *
     * @param int $node_id
     */
    private function getAddressesByNode($node_id)
    {
        return $this->sqlNode(self::TABLE_ADDRESSES, $node_id);
    }

    /**
     * Retornar um nó relacionado a uma entity
     *
     * @param int $node_id
     */
    private function getEntitiesByNode($node_id)
    {
        return $this->sqlNode(self::TABLE_ENTITIES, $node_id);
    }

    /**
     * Retornar um nó relacionado a um intermediarie
     *
     * @param int $node_id
     */
    private function getIntermediariesByNode($node_id)
    {
        return $this->sqlNode(self::TABLE_INTERMEDIARIES, $node_id);
    }


    /**
     * Retornar um nó relacionado a um officer
     *
     * @param int $node_id
     */
    private function getOfficersByNode($node_id)
    {
        return $this->sqlNode(self::TABLE_OFFICERS, $node_id);
    }

    /**
     * Função genérica que retorna uma conexão buscando pelo id de um nó em uma tabela
     *
     * @param string $table   --> Tabela de resultado da conexão
     * @param int $node_id    --> Id do nó
     */
    private function sqlNode($table, $node_id)
    {
        $table = strtolower($table);
        if (strpos($table, 'offshore') === false) {
            $table = "offshore_" . $table;
        }

        $fields = $this->getFields($table);

        return $this->getDatabaseConnection()
            ->select($fields)
            ->from('common.' . $table)
            ->where('node_id = :node_id')
            ->setParameter('node_id', $node_id)
            ->execute()
            ->fetch();
    }

    /**
     * Retorna a lista de jurisdições cadastradas
     *
     * @return array
     */
    public function getAllJurisdictions()
    {
        return $this->getDatabaseConnection()
            ->select('jurisdiction_code, jurisdiction_name')
            ->from('common.offshore_jurisdictions')
            ->execute()
            ->fetchAll();
    }

    /**
     * Busca por uma jurisdição cadastrada
     *
     * @param string $jurisdiction_search
     * @return array
     */
    public function getJurisdiction($jurisdiction_search)
    {
        return $this->getDatabaseConnection()
            ->select('jurisdiction_code, jurisdiction_name')
            ->from('common.offshore_jurisdictions')
            ->where('lower(jurisdiction_code) = :jurisdiction_search')
            ->orWhere('lower(jurisdiction_name) = :jurisdiction_search')
            ->setParameter('jurisdiction_search', strtolower($jurisdiction_search))
            ->execute()
            ->fetch();
    }

    /**
     * Retorna os campos da consulta
     *
     * @param string $table
     * @return string
     */
    private function getFields($table, $rename = array(), $implode = true, $put_table_name = true)
    {
        $table = str_replace(array('offshore', '_', '-'), '', $table);

        $fields = $this->default_fields[strtolower($table)];

        if (! is_array($rename)) {
            $rename = array($rename);
        }
        if (! is_array($fields)) {
            $fields = explode(',', addslashes($fields));
        }

        foreach ($fields as &$field) {
            if (! empty($rename)) {
                if (in_array($field, array_keys($rename))) {
                    $field = $field . " AS " . $rename[$field];
                } elseif (in_array("_prefix_", array_keys($rename))) {
                    $field = $field . " AS " . $rename["_prefix_"] . $field;
                }
            }

            if ($put_table_name) {
                $field = "offshore_{$table}.{$field}";
            }
        }

        if ($implode) {
            $fields = addslashes(implode(',', $fields));
        }

        return $fields;
    }

    /**
     * Trata uma string de consulta e retorna ela formatada pronta para buscar busca exata ou não no banco
     *
     * @param string $str
     * @param string $field
     * @param bool $match_exact
     * @return string
     */
    private function matchExactString($str, $field)
    {
        $match_exact = $this->default_match_exact;

        $str = strtolower($str);
        $str = pg_escape_string(str_replace("''", "'", strtolower($str)));

        if (! $match_exact) {
            $str = "lower(\"{$field}\") like '%{$str}%'";
        } else {
            $str = "lower(\"{$field}\") = '{$str}'";
        }

        return $str;
    }

    ////////////////////////////////////////////////////////////////////////////////////////////

    /**
     * Retorna uma instância de conexão com o banco captura3
     *
     * @return Object
     */
    public function getDatabaseConnection()
    {
        return $this->db->connectCaptura();
    }

    public function disconnect()
    {
        $this->db->disconnect();
    }
}
