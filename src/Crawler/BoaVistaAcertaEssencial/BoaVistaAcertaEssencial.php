<?php

namespace App\Crawler\BoaVistaAcertaEssencial;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\BoaVista\BoaVistaManager;
use Exception;

/**
 * Fonte Acerta Completa do fornecedor BoaVista
 *
 * @version 1.0.0
 *
 * <AUTHOR> Mesquita 14/04/2021
 */
class BoaVistaAcertaEssencial extends Spider
{
    private $cpf;

    /**
     * Valida os parametros de busca da fonte
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 14/04/2021
     *
     * @return void
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['cpf'])) {
            throw new Exception("Parâmetro CPF é obrigatório", 1);
        }

        if (!Document::validarCpf($this->param['cpf'])) {
            throw new Exception('Parâmetro Inválido', 1);
        }

        $this->cpf = Document::removeMask($this->param['cpf']);
    }

    /**
     * Inicia o processamento da fonte
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 14/04/2021
     *
     * @return array
     */
    protected function start()
    {
        $sources = [
            'resumo_ocorrencias_de_debitos',
            'resumoConsultas_anteriores_90_dias',
            'informacoes_complementares',
            'debitos',
            'consultas_anteriores',
            'titulos_protestados',
            'resumo_titulos_protestados',
            'cheque_talao_sustado',
            'resumo_devolucoes_informadas_pelo_ccf',
            'resumo_consultas_anteriores_cheque',
            'confirmacao_telefone',
            'endereco_telefones_agencia_bancaria',
            'confirmacao_cep',
            'nome_documentos',
            'relacao_devolucoes_informadas_pelo_ccf',
            'informacoes_complementares_cheque',
            'devolucoes_informadas_pelo_usuario',
            'cheques_sustados_pelo_motivo_21',
            'historico_cheque_informado',
            'historico_conta_corrente_informada',
            'consultas_anteriores_cheque',
            'resumo_devolucoes_informada_pelo_usuario',
            'identificacao',
            'localizacao',
            'score_classificacao_varios_modelos' => [
                'score',
                'renda_presumida'
            ],
            'decisao',
            'mensagem_registro'
        ];

        return (new BoaVistaManager())->searchAcertaEssencial($this->cpf, $sources);
    }
}
