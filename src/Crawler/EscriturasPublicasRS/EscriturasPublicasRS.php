<?php

namespace App\Crawler\EscriturasPublicasRS;

use App\Crawler\EscriturasPublicasRS\Models\EscrituraPublicaRSModel;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Util;
use Exception;

class EscriturasPublicasRS extends Spider
{
    private const BASE_URL = "https://www.tjrs.jus.br/servicos/escrituras_publicas/";
    private $cpfName;
    private $limit;
    private $option = 2;
    private $results = [];

    public function start()
    {
        $this->searchDeed();
        return $this->results;
    }

    /**
     * Método que faz a busca da requisição do critério
     *
     * <AUTHOR>
     * <AUTHOR>
     */
    private function searchDeed()
    {
        $results = $this->makeRequest();
        $this->parseResults($results);
    }

    /**
     * Método para fazer a requisição e retornar o HTML
     *
     * <AUTHOR>
     * <AUTHOR>
     */
    private function makeRequest()
    {
        $params = [
            'opcoes' => $this->option,
            'cpf_nome' => $this->cpfName,
            'datainicial' => "",
            'datafinal' => "",
            'ufoab' => "RS",
            'numoab' => "",
            'numlinhas' => $this->limit,
        ];
        $this->setAlternativeProxy();
        $this->setCurlOpt([
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $retry = 10;
        while ($retry >= 0) {
            try {
                $results = $this->getResponse(self::BASE_URL, 'POST', $params);

                if (preg_match('/N&atilde;o\sforam\sencontrados/is', $results)) {
                    throw new Exception("Nenhum registro encontrado", 2);
                }

                break;
            } catch (Exception $e) {
                if ($retry == 0) {
                    throw new Exception($e->getMessage(), 2);
                }
                $retry--;
            }
        }

        return $results;
    }

    /**
     * Método para retornar os links dos dados dos detalhes
     *
     * <AUTHOR> Pereira
     * <AUTHOR> Guilherme
     */
    private function parseResults($result)
    {
        preg_match_all('/<div.*><a.*href="([a-zA-Z_.?=\d]+)/', $result, $matches);

        $matches = $matches[1];

        if (!empty($this->limit)) {
            $matches = array_slice($matches, 0, $this->limit);
        }

        foreach ($matches as $match) {
            $retry = 10;
            while ($retry >= 0) {
                try {
                    $link = $this->getResponse(self::BASE_URL . $match);

                    if (!preg_match('/Detalhe\sda\sescritura/is', $link)) {
                        throw new Exception("Link sem resultado", 2);
                    }

                    $this->parseDeedDetails($link);

                    break;
                } catch (Exception $e) {
                    if ($retry == 0) {
                        print PHP_EOL . $e->getMessage() .  ' - ' . $match . PHP_EOL;
                    }
                    $retry--;
                }
            }
        }
    }

    /**
     * Método para retornar das informações dos detalhes
     *
     * <AUTHOR> Pereira
     * <AUTHOR> Guilherme
     */
    private function parseDeedDetails($result)
    {
        $patterns = [
            'escritura' => ['@<strong>Escritura:<\/strong>(.*)<\/p>@'],
            'numeroLivro' => ['@<td.*class="texto_capa"><b>.*livro:<\/b>(.*)<\/td>@'],
            'numeroFolha' => ['@<td.*class="texto_capa"><b>.*folha:<\/b>(.*)<\/td>@'],
            'numeroAto' => ['@<td.*class="texto_capa"><b>.*ato:<\/b>(.*)<\/td>@'],
            'data' => ['@<td.*class="texto_capa"><b>.*Data:<\/b>(.*)<\/td>@'],
            'tipo' => ['@<span.*class="texto_capa"><b>Tipo:<\/b>(.*)<br>@'],
            'observacao' => ['@<b>Observa&ccedil;&atilde;o:<\/b>(.*)<\/span>@'],
            'serventia' => ['@<td.*width="12%">Serventia:.*\s+<td>(.*)<\/td>@'],
            'cidade' => ['@<td>Cidade:.*\s+<td>(.*)<\/td>@'],
        ];

        $data = Util::parseDados($patterns, $result);
        $data['partes'] = $this->parseParts($result);
        $data['advogados'] = $this->parseAdvogados($result);
        $this->finalParse($data);
    }

    /**
     * Método para retornar as informações finais
     *
     * <AUTHOR> Pereira
     * <AUTHOR> Guilherme
     */
    private function finalParse($data)
    {
        $escrituraModel = new EscrituraPublicaRSModel();

        foreach ($data as $key => $value) {
            $escrituraModel->$key = !is_array($value)
                ? trim(html_entity_decode(utf8_decode($value)))
                : $value;
        }

        $this->results[] = $escrituraModel;
    }

    /**
     * Método para retornar as partes
     *
     * <AUTHOR> Pereira
     * <AUTHOR> Guilherme
     */
    private function parseParts($result)
    {
        $parts = [];

        if (preg_match('/Partes:(.*)<\/table>/isU', $result, $match)) {
            preg_match_all('/<tr\sclass="texto_capa">.*\s+.*<td>(.*)<\/td>/', $match[0], $matches);

            foreach ($matches[1] as $match) {
                $parts[] = trim(html_entity_decode($match));
            }
        }

        return $parts;
    }

    /**
     * Método para retornar os advogados
     *
     * <AUTHOR> Borges
     */
    private function parseAdvogados($result)
    {
        $advogados = [];

        if (preg_match('/Advogados:(.*)<\/table>/isU', $result, $match)) {
            $pattern = '/<tr\sclass="texto_capa">.*?<td>(.*?)<\/td>.*?<td>(.*?) - \s+(.*?)<\/td>.*?<\/tr>/s';
            preg_match_all($pattern, $match[0], $matches);

            foreach ($matches[1] as $match) {
                $advogados[] = trim(html_entity_decode($match . ' - ' . $matches[2][0] . '-' . $matches[3][0]));
            }
        }

        if (empty($advogados)) {
            $advogados = ['Não há advogados cadastrados.'];
        }

        return $advogados;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->limit = !isset($this->param['limit']) ? 30 : $this->param['limit'];

        if (empty(trim($this->param['cpf_name']))) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        $this->cpfName = trim($this->param['cpf_name']);

        if (is_numeric(Document::removeMask($this->cpfName))) {
            $this->cpfName = Document::removeMask($this->cpfName);
            $this->option = 1;
        }
    }
}
