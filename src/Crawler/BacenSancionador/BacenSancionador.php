<?php

namespace App\Crawler\BacenSancionador;

use App\Crawler\BacenSancionador\Models\BacenSancionadorModel;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Util;
use Exception;

class BacenSancionador extends Spider
{
    private const URL = 'https://www.bcb.gov.br/api/search/sitebcb/buscaconteudo/';
    private $results = [];


    public function start()
    {
        if (Document::validarCpfOuCnpj($this->param['criterio'])) {
            $this->param['criterio'] = Document::formatCpfOrCnpj($this->param['criterio']);
        }

        $res = $this->makeRequest();
        $this->parseResults($res);
        return $this->results;
    }


    /**
     * Método para fazer a requisição e retornar o HTML
     * <AUTHOR>
     */
    public function makeRequest()
    {
        $param = [
            'querytext' => "(\"" . $this->param['criterio'] . "\")",
            'rowlimit' => 10,
            'startrow' => 0,
            'sourceid' => '58f69d7f-03f4-48da-9a08-e6b9baaeb8df',
        ];

        $term = http_build_query($param);

        $url = self::URL . "?" . $term;
        $response = json_decode($this->getResponse($url));

        return $response;
    }

    /**
     * Trata os dados do resultado da requisição colocando no array final
     *
     * <AUTHOR> Meneghini
     */
    private function parseResults($result)
    {

        if ($result->TotalRows == 0) {
            throw new Exception("Nenhum resultado encontrado", 2);
        }

        $patterns = [
            "process" => [
                '/<[\w]+>PROCESSO&#58;.*?>([\w|0-9 \s)]+)</m',
                null
            ],
            "accused" => [
                '/ACUSA.*?S?.(.*?)<\/p>/',
                null
            ],
            "decision" => [
                '/<[\w]+>DECISÃO[[<>\/\w0-9&#;\s]+<\/[\w]+>([\d\w\W\s\/\-,)]+)<[\w]+ style="text-align&#58;center;/m',
                null
            ],
            "foundation" => ['/<strong>FUNDAMENTO.*?>(.*?)<\/p>/', null],
            "result" => ['/<strong>RESULTADO.*?>(.*?)<\/p>/', null],
            "body" => ['/<.*?>(PROCESSO&#58;<\/.*?>[\n\s\w\W]+)>/m'],
        ];

        foreach ($result->Rows as $row) {
            if (!isset($row->CorpodaPaginaOWSHTML)) {
                continue;
            }
            $data = Util::parseDados($patterns, $row->CorpodaPaginaOWSHTML, false, false);
            $data["date"] = $row->RefinableDate01;
            $data["category"] = $row->RefinableString01;
            $data["title"] = $this->parseText(
                '/(<span .*?gepadTitulo.*?>.*?<\/span>|<p.*?center;"><[strong|b]+>.*?<\/[strong|b]+>)/m',
                $row->CorpodaPaginaOWSHTML
            );

            if ($data["title"] == "") {
                $this->parseText(
                    '/text-align.*?center;">(.*)/m',
                    $row->CorpodaPaginaOWSHTML
                );
            }

            if ($data["accused"] == "") {
                $data["accused"] = $this->parseText(
                    '/ACUSADOS&#58;.*?>(.*?)</m',
                    $row->CorpodaPaginaOWSHTML
                );
            }

            if ($data["title"] == "") {
                $decisao = $this->parseText('/<[\w]+>DECISÃO(.*?)</', $row->CorpodaPaginaOWSHTML);
                $decisao = ($decisao ? 'Decisão ' . $decisao : '');
                $intimacao = $this->parseText('/<[\w]+>INTIMAÇÃO(.*?)</', $row->CorpodaPaginaOWSHTML);
                $intimacao = ($intimacao ? 'Intimação ' . $intimacao : '');
                $data["title"] = $decisao . $intimacao;
            }

            $this->results[] = $this->finalParse($data);
        }
    }

    private function parseText($pattern, $search)
    {
        $result = "";
        preg_match_all($pattern, $search, $matches);
        foreach ($matches[1] as $matche) {
            $result .= $matche . " \n";
        }

        return $result;
    }

    /**
     * Vincula ao modelo da model
     *
     * <AUTHOR> Meneghini
     */
    private function finalParse($data): BacenSancionadorModel
    {
        $bacenModel = new BacenSancionadorModel();

        foreach ($data as $key => $value) {
            $value = str_replace('&#160;', " \n", $value);
            $value = str_replace('&#58;', ": ", $value);

            $value = strip_tags($value);

            $bacenModel->$key = $value;
        }

        return $bacenModel;
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty(trim($this->param["criterio"]))) {
            throw new Exception("Criterio invalido", 1);
        }
    }
}
