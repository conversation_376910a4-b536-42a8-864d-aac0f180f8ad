<?php

namespace App\Crawler\BaseOffEscavadorV2;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\Escavador\EscavadorManager;
use Exception;

class BaseOffEscavadorV2 extends Spider
{
    private $manager;
    private $documento;
    private $limit = 100;
    private $courts = [];
    private $uf = [];

    public function start()
    {
        $bilhetadorSource = !empty($this->param['bilhetadorSource']) ? $this->param['bilhetadorSource'] : '';

        $this->manager = new EscavadorManager($this->documento, $bilhetadorSource);

        $data = $this->manager->getProcessByCpfCnpj($this->documento, $this->limit, $this->uf, $this->courts);

        if (empty($data['items'])) {
            throw new Exception("Nenhum Resultado Encontrado", 2);
        }

        return $data['items'];
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['documento'])) {
            throw new Exception("Parâmetro nome é obrigatório", 1);
        }

        if (!Document::validarCpfOuCnpj($this->param['documento'])) {
            throw new Exception("Parâmetro CPF ou CNPJ é obrigatório", 1);
        }

        if (!empty($this->param['limit'])) {
            $this->limit = $this->param['limit'];
        }

        if (!empty($this->param['tribunais'])) {
            $this->courts = $this->param['tribunais'];
        }

        if (!empty($this->param['uf'])) {
            $this->courts = [];
            $this->uf = $this->param['uf'];
        }

        $this->documento = $this->param['documento'];
    }
}
