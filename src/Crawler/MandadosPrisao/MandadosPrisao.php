<?php

namespace App\Crawler\MandadosPrisao;

use App\Crawler\Spider;
use App\Helper\Document;
use Exception;

/**
 *  Classe da fonte (171) Banco Nacional de Mandado de Prisão - BNMP (CNJ)
 *
 *  <AUTHOR> - 07/10/2019
 */
class MandadosPrisao extends Spider
{
    private const BASE_URL = 'https://portalbnmp.cnj.jus.br/';
    private const CAPTCH_URL = self::BASE_URL . 'api/recaptcha';
    private const SITE_KEY_URL = self::BASE_URL . 'api/recaptcha/sitekey';
    private const POST_URL = self::BASE_URL . 'bnmpportal/api/pesquisa-pecas/filter?page=<page>&size=<size>&sort=';
    private const DETAIL_URL = self::BASE_URL . 'bnmpportal/api/certidaos/<numero>/<tipopeca>';

    private const TYPE_CPF = 1;
    private const TYPE_NOME = 2;
    private const KEY_LIMIT_RETRY = 5;
    private const TOKEN_LIMIT_RETRY = 5;
    private const DETAIL_LIMIT_RETRY = 5;
    private const DEFAULT_LIMIT = 40;

    private $resultSize = 30;
    private $postToken;
    private $paramType;
    private $limit;
    private $currentPage = 0;
    private $currentLimit = 0;
    private $result = [];

    /**
     *  Valida e retorna os parametros
     *
     *  <AUTHOR> Mesquita - 07/10/2019
     *
     *  @version 1.0.0
     *
     *  @return array
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (!isset($this->param['criterio']) || empty($this->param['criterio'])) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }

        if (Document::validarCnpj($this->param['criterio'])) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }

        $this->limit = $this->param['limit'];

        if (empty($this->limit)) {
            $this->limit = self::DEFAULT_LIMIT;
        }

        if (Document::validarCpf($this->param['criterio'])) {
            $this->paramType = self::TYPE_CPF;
            return true;
        }

        $this->paramType = self::TYPE_NOME;
    }

    /**
     * Inicia a pesquisa da fonte
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita - 07/10/2019
     *
     * @return array
     */
    protected function start()
    {
        $this->getToken();
        $this->getSearch();

        return $this->result;
    }

    /**
     * Executa a busca no site e retorna os dados
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 07/10/2019
     *
     */
    private function getSearch()
    {
        $jsonList = $this->getList();

        $this->validateResult($jsonList);
        foreach ($jsonList['content'] as $key => $item) {
            if ($this->currentLimit >= $this->limit) {
                break;
            }

            $result[] = $this->parseWarrants($item);
        }

        if ($this->currentLimit >= $this->resultSize * ($this->currentPage + 1)) {
            $this->pagingWarrants();
        }
    }

    /**
     * Pagina os resultados até atingir o limit
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 07/10/2019
     *
     */
    private function pagingWarrants()
    {
        if ($this->currentLimit >= $this->limit) {
            return;
        }

        $this->currentPage++;
        $this->getSearch();
    }

    /**
     * Pega a lista com os resultados
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 07/10/2019
     *
     * @return array
     *
     */
    private function getList()
    {
        $this->setCurlOpt(
            array(
                CURLOPT_COOKIE  => 'COOKIE_PORTALBNMP_0=BKOENAAK; portalbnmp=' . $this->postToken,
            )
        );

        $url = str_replace('<size>', $this->resultSize, self::POST_URL);
        $url = str_replace('<page>', $this->currentPage, $url);

        $response = $this->getResponse(
            $url,
            'POST',
            $this->getParams()
        );

        return json_decode($response, true);
    }

    /**
     * Valida o retorno da api
     *
     * @param array $response
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 07/10/2019
     *
     */
    private function validateResult($response)
    {
        if (isset($response['status'])) {
            throw new Exception("Erro ao pesquisar critério: {$response['status']} {$response['detail']}", 3);
        }

        if (!isset($response['content'])) {
            throw new Exception("Não foi possível pegar o conteúdo", 3);
        }

        if (empty($response['content'])) {
            throw new Exception("Nada encontrado", 2);
        }
    }

    /**
     * Pega o array com os detalhes do mandado
     *
     * @param int $numero
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 07/10/2019
     *
     * @return array
     */
    private function getDetail($item)
    {
        $url = str_replace('<numero>', $item['id'], self::DETAIL_URL);
        $url = str_replace('<tipopeca>', $item['idTipoPeca'], $url);

        return json_decode($this->getResponse(
            $url
        ), true);
    }

    /**
     * Parse dos dados
     *
     * @param array $item
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 07/10/2019
     *
     */
    private function parseWarrants($item)
    {
        for ($i = 0; $i < self::DETAIL_LIMIT_RETRY; $i++) {
            try {
                $detailItem = $this->getDetail($item);

                if (!isset($detailItem['id'])) {
                    throw new Exception("Erro ao pegar os dados do detalhe", 3);
                }

                break;
            } catch (Exception $ex) {
                if ($i >= self::DETAIL_LIMIT_RETRY) {
                    throw new Exception("Erro ao pegar os detalhes: " . $ex->getMessage(), 3);
                }
            }
        }

        $this->currentLimit++;

        $this->result[] = [
            'id' => $item['id'],
            'situacao' => $item['descricaoStatus'],
            'numero' => $item['numeroPecaFormatado'],
            'data' => $this->formatDate($item['dataExpedicao']),
            'validade' => $this->formatDate($detailItem['dataValidade']),
            'processo' => $item['numeroProcesso'],
            'classe' => $item['descricaoPeca'],
            'assuntos' => 'Não informado',
            'procedimentos' => 'Não informado',
            'magistrado' => $detailItem['magistrado'],
            'orgao' => $item['nomeOrgao'],
            'municipio' => $detailItem['orgaoUsuarioCriador']['municipio']['nome'],
            'estado' =>  $detailItem['orgaoUsuarioCriador']['municipio']['uf']['nome'],
            'estadoSigla' =>  $detailItem['orgaoUsuarioCriador']['municipio']['uf']['sigla'],
            'nomesMae' => array_column($detailItem['pessoa']['nomeMae'], 'nome'),
            'nomesPai' => array_column($detailItem['pessoa']['nomePai'], 'nome'),
            'nomes' => array_column($detailItem['pessoa']['outrosNomes'], 'nome'),
            'alcunhas' => array_column($detailItem['pessoa']['outrasAlcunhas'], 'nome'),
            'sexos' => [$item['descricaoSexo']],
            'documentos' => [],
            'genitores' => [],
            'genitoras' => [],
            'nacionalidades' => [$detailItem['pessoa']['dadosGeraisPessoa']['paisNascimento']['nome']],
            'naturalidades' => [$detailItem['pessoa']['dadosGeraisPessoa']['naturalidade']['nome']],
            'datasNascimentos' => array_column($detailItem['pessoa']['dataNascimento'], 'dataNascimento'),
            'aspectosFisicos' => [],
            'profissoes' => [$item['descricaoProfissao']],
            'enderecos' =>  $this->getAddress($detailItem['pessoa']['enderecos']),
            'dataDelito' => '',
            'assuntoDelito' => '',
            'motivo' => '',
            'prazo' => '',
            'recaptura' => $detailItem['recaptura'],
            'sintese' => $detailItem['sinteseDecisao'],
            'pena' => $detailItem['tempoPena'],
            'regime' => $detailItem['regimePrisional'],
            'codigoCertidao' => '',
            'tipificacaoPenal' => $detailItem['tipificacaoPenal'],
            'especiePrisao' => $detailItem['especiePrisao']
        ];
    }

    /**
     * Formata Data
     *
     * @param string $date
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 08/10/2019
     *
     * @return string
     */
    private function formatDate($date)
    {
        if (empty($date)) {
            return null;
        }

        $date = explode("-", $date);
        return "{$date[2]}/{$date[1]}/{$date[0]}";
    }

    /**
     * Monta o endereço completo
     *
     * @param array $enderecos
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 08/10/2019
     *
     * @return array
     */
    private function getAddress($enderecos)
    {
        $result = [];
        $resultEndereco = '';

        foreach ($enderecos as $endereco) {
            $resultEndereco = "{$endereco['logradouro']}, {$endereco['numero']} ({$endereco['complemento']}) - ";
            $resultEndereco .= "CEP: {$endereco['cep']} - {$endereco['bairro']} {$endereco['municipio']['nome']} - ";
            $resultEndereco .= "{$endereco['estado']['sigla']} - {$endereco['pais']['nome']}";
            $result[] = $resultEndereco;
        }

        return $result;
    }

    /**
     * Retorna os parametros utilizados para o post
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 07/10/2019
     *
     * @return string
     */
    private function getParams()
    {
        $params = '';

        if ($this->paramType == self::TYPE_NOME) {
            $params = '"nomePessoa":"' . $this->param['criterio'] . '"';
        }

        if ($this->paramType == self::TYPE_CPF) {
            $params = '"idTipoDocumento":6,"numeroDocumento":"' . $this->param['criterio'] . '"';
        }

        return '{"buscaOrgaoRecursivo":false,"orgaoExpeditor":{},' . $params . '}';
    }

    /**
     * Pega o token da quebra do captcha usado no post
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita - 07/10/2019
     *
     * @return string
     *
     */
    private function getToken()
    {
        $exception = null;
        $token = '';

        for ($i = 0; $i < self::TOKEN_LIMIT_RETRY; $i++) {
            try {
                $captcha = $this->resolveRecaptcha();

                if (preg_match('/O site da fonte retornou um erro/is', $captcha)) {
                    $exception = $captcha;
                    break;
                }

                $param = json_encode(['idToken' => $captcha]);

                $this->setCurlOpt(array(
                    CURLOPT_TIMEOUT => 300
                ));
                $token = $this->getResponse(self::CAPTCH_URL, 'POST', $param);
                $exception = null;
                break;
            } catch (Exception $ex) {
                $exception = $ex->getMessage();
            }
        }

        if ($exception) {
            if (preg_match('/O site da fonte retornou um erro/is', $exception)) {
                throw new Exception($exception, 6);
            } else {
                throw new Exception($exception, 3);
            }
        }

        $token = json_decode($token, true);

        if (empty($token) || empty($token['idToken'])) {
            throw new Exception("Erro ao pegar o token", 3);
        }

        $this->postToken = $token['idToken'];
    }

    /**
     * Pega a chave do captcha no site
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita - 07/10/2019
     *
     * @return string
     *
     */
    private function getSiteKey()
    {
        $exception = null;
        $token = '';

        for ($i = 0; $i < self::KEY_LIMIT_RETRY; $i++) {
            try {
                $token = $this->getResponse(self::SITE_KEY_URL);
                if (
                    preg_match("/title>([\d]{3}\s*[\w\s\-]*)<\/title/is", $token, $out)
                ) {
                    if ($i == (self::KEY_LIMIT_RETRY - 1)) {
                        $exception = "O site da fonte retornou um erro: " . $out[1];
                        return $exception;
                    }
                }
                $exception = null;
            } catch (Exception $ex) {
                $exception = $ex->getMessage();
            }
        }

        if ($exception) {
            throw new Exception($exception, 3);
        }

        $token = json_decode($token, true);
        $token = $token['siteKey'];

        if (empty($token)) {
            throw new Exception("Erro ao pegar a chave do captcha.", 3);
        }

        return $token;
    }

    /**
     * Resolve e retorna a token captcha
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita - 07/10/2019
     *
     * @return string
     *
     */
    private function resolveRecaptcha()
    {
        $key = $this->getSiteKey();

        if (preg_match('/O site da fonte retornou um erro/is', $key)) {
            return $key;
        }

        $captcha = $this->solveReCaptcha($key, self::CAPTCH_URL, 2);

        if ($captcha == 'CAPCHA_NOT_READY' || empty($captcha)) {
            throw new Exception("Erro ao recuperar o captcha.", 3);
        }

        return $captcha;
    }
}
