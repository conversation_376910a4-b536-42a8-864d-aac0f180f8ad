<?php

namespace App\Crawler\Info4cPep;

use App\Crawler\Spider;
use App\Factory\MongoDB;
use Exception;

class Info4cPep extends Spider
{
    private const INDEX_MONGODB = 'name';
    private const LIMIT = 100;

    private $name;
    private $limit;
    private $manager;

    public function start()
    {
        $this->manager = (new MongoDB())
            ->connectSources()
            ->setDb('info4c');

        $result = $this->getDataByName();

        if (empty($result)) {
            throw new Exception("Nenhum Resultado Encontrado", 2);
        }

        $relatives = $this->getRelatives($result);

        return $this->parseResult($result, $relatives);
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->name = trim($this->param['name']);
        $this->limit = $this->param['limit'] ?? self::LIMIT;

        if (empty($this->name)) {
            throw new Exception("Critério de busca inválido.", 6);
        }
    }

    /** Busca registros na base
     * @return mixed
     */
    private function getDataByName()
    {
        $names = $this->breakName($this->name);

        $fields = ['search_field'];
        $query[] = [
            '$search' => [
                'index' => self::INDEX_MONGODB,
                'compound' => [
                    'must' => [
                        [
                            "phrase" => [
                                "query" => $names,
                                "path" => $fields
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $query[] = [
            '$lookup' => [
                'localField' => 'id',
                'from' => 'info4c_pep_detalhes',
                'foreignField' => 'pep_id',
                'as' => 'details'
            ]
        ];

        $query[] = ['$sort' => ['dataDetail.record_type' => 1]];

        $query[] = ['$limit' => $this->limit];

        return json_decode(
            json_encode(
                $this->manager->setCollection('info4c_pep')
                ->query(
                    'aggregate',
                    $query,
                    null,
                    null,
                    true
                )->toArray(),
                true
            ),
            true
        );
    }

    private function getDetails($results)
    {
        foreach ($results as $keyResult => $result) {
            foreach ($result['details'] as $keyDetail => $detail) {
                $id = json_decode(json_encode($detail['_id'], true), true);
                $results[$keyResult]['details'][$keyDetail] = [
                    'id' => $id['$oid'],
                    'pep_id' => $detail['pep_id'] ?? '',
                    'record_type' => $detail['record_type'] ?? '',
                    'function' => $detail['function'] ?? '',
                    'specificfunction' => $detail['specificfunction'] ?? '',
                    'category' => $detail['category'] ?? '',
                    'expeps' => $detail['expeps'] ?? '',
                    'date_not_in_charge_since' => $detail['date_not_in_charge_since']
                        ?? '',
                    'birth_date' => $detail['birth_date'] ?? '',
                    'birth_place' => $detail['birth_place'] ?? '',
                    'country' => $detail['country'] ?? '',
                    'additional_information' => $detail['additional_information'] ?? '',
                    'country_origin' => $detail['country_origin'] ?? '',
                    'country_activity' => $detail['country_activity'] ?? '',
                    'dt_date_not_in_charge' => $this->parseDate($detail['date_not_in_charge_since'])
                ];
            }
        }

        return $results;
    }

    private function getRelatives($result)
    {
        foreach ($result as $keyResult => $valueResult) {
            $id = $valueResult['id'];

            $pep_id = $id;
            if ($valueResult['relative_id'] != '') {
                $id = $valueResult['relative_id'];
            }

            $subqueries = $this->findDataSubQuery($pep_id);

            $pipeline = [
                [
                    '$project' => [
                        '_id' => 0,
                        'info4c_pep' => '$$ROOT'
                    ]
                ],
                [
                    '$lookup' => [
                        'localField' => 'info4c_pep.id',
                        'from' => 'info4c_pep_detalhes',
                        'foreignField' => 'pep_id',
                        'as' => 'detalhes'
                    ]
                ],
                [
                    '$unwind' => [
                        'path' => '$detalhes',
                        'preserveNullAndEmptyArrays' => true
                    ]
                ],
                [
                    '$match' => [
                        'info4c_pep.relative_id' => $id
                    ]
                ],
                [
                    '$group' => [
                        '_id' => [
                            'info4c_pep᎐first_name' => '$info4c_pep.first_name',
                            'info4c_pep᎐full_name' => '$info4c_pep.full_name',
                            'info4c_pep᎐record_type' => '$info4c_pep.record_type',
                            'info4c_pep᎐last_name' => '$info4c_pep.last_name',
                            'info4c_pep᎐id' => '$info4c_pep.id',
                            'info4c_pep᎐title' => '$info4c_pep.title',
                            'info4c_pep᎐search_field' => '$info4c_pep.search_field',
                            'info4c_pep᎐gender' => '$info4c_pep.gender',
                            'info4c_pep᎐relative_id' => '$info4c_pep.relative_id',
                            'info4c_pep᎐original_name' => '$info4c_pep.original_name',
                            'info4c_pep᎐other_names' => '$info4c_pep.other_names'
                        ],
                        'MAX(detalhes᎐birth_date)' => [
                            '$max' => '$detalhes.birth_date'
                        ],
                        'MAX(detalhes᎐birth_place)' => [
                            '$max' => '$detalhes.birth_place'
                        ]
                    ]
                ],
                [
                    '$project' => [
                        'id' => '$_id.info4c_pep᎐id',
                        'relative_id' => '$_id.info4c_pep᎐relative_id',
                        'record_type' => '$_id.info4c_pep᎐record_type',
                        'title' => '$_id.info4c_pep᎐title',
                        'gender' => '$_id.info4c_pep᎐gender',
                        'first_name' => '$_id.info4c_pep᎐first_name',
                        'last_name' => '$_id.info4c_pep᎐last_name',
                        'full_name' => '$_id.info4c_pep᎐full_name',
                        'other_names' => '$_id.info4c_pep᎐other_names',
                        'search_field' => '$_id.info4c_pep᎐search_field',
                        'original_name' => '$_id.info4c_pep᎐original_name',
                        'birth_date' => '$MAX(detalhes᎐birth_date)',
                        'birth_place' => '$MAX(detalhes᎐birth_place)',
                        '_id' => 0,

                    ],

                ],
                [
                    '$addFields' => [
                        "country" => $subqueries['country'] ?? '',
                        "country_activity" => $subqueries['country_activity'] ?? ''
                    ]
                ],
                [
                    '$sort' => [
                        'last_name' => 1,
                        'first_name' => 1
                    ]
                ]
            ];

            $dataRelatives[$keyResult] = $this->manager->setCollection('info4c_pep')
                ->query('aggregate', [$pipeline, ['allowDiskUse' => true]])->toArray();
        }
        $dataRelativesParse = [];
        foreach ($dataRelatives as $key => $valueData) {
            foreach ($valueData as $keyValue => $value) {
                if ($result[$key]['id'] != $value['id']) {
                    $dataRelativesParse[$key][] = [
                        'id' => $value['id'] ?? '',
                        'relative_id' => $value['relative_id'] ?? '',
                        'record_type' => $value['record_type'] ?? '',
                        'title' => $value['title'] ?? '',
                        'gender' => $value['gender'] ?? '',
                        'first_name' => $value['first_name'] ?? '',
                        'last_name' => $value['last_name'] ?? '',
                        'full_name' => $value['full_name'] ?? '',
                        'other_names' => $value['other_names'] ?? '',
                        'search_field' => $value['search_field'] ?? '',
                        'original_name' => $value['original_name'] ?? '',
                        'birth_date' => $value['birth_date'] ?? '',
                        'birth_place' => $value['birth_place'] ?? '',
                        'country' => $value['country'] ?? '',
                        'country_activity' => $value['country_activity'] ?? '',
                        'country_origin' => $value['country_origin'] ?? ''
                    ];
                }
            }
        }

        return $dataRelativesParse;
    }

    /** Esses finds são subqueries do método, porém não é possivel fazer em uma unica query no mongodb
     * @param $id
     * @return array
     */
    private function findDataSubQuery($id)
    {
        $country = $this->manager->setCollection('info4c_pep_detalhes')
            ->query(
                'find',
                [
                    [
                        'pep_id' => $id,
                        'country' => [
                            '$ne' => 'International'
                        ]
                    ],
                    [
                        'projection' => [
                            'country' => '$country',
                            '_id' => 0
                        ],
                        'limit' => 1
                    ]
                ]
            )->toArray();

        $countryActivity = $this->manager->setCollection('info4c_pep_detalhes')
            ->query(
                'find',
                [
                    [
                        'pep_id' => $id,
                        'country_activity' => [
                            '$ne' => ''
                        ]
                    ],
                    [
                        'projection' => [
                            'country_activity' => '$country_activity',
                            '_id' => 0
                        ]
                    ]
                ]
            )->toArray();

        return [
            'country' => $country[0]['country'] ?? '',
            '$countryActivity' => $countryActivity[0]['country_activity'] ?? ''
        ];
    }

    private function findDataSubQueryDetail($id)
    {
        $country = $this->manager->setCollection('info4c_pep_detalhes')
            ->query(
                'find',
                [
                    [
                        '$and' => [
                            [
                                'pep_id' => $id
                            ],
                            [
                                'country' => [
                                    '$ne' => null
                                ]
                            ],
                            [
                                'country' => [
                                    '$ne' => 'International'
                                ]
                            ]
                        ]
                    ],
                    [
                        'projection' => [
                            'country' => '$country',
                            '_id' => 0
                        ],
                        'limit' => 1
                    ]
                ]
            )->toArray();

        $countryOrigin = $this->manager->setCollection('info4c_pep_detalhes')
            ->query(
                'find',
                [
                    [
                        'pep_id' => $id,
                        'country_origin' => [
                            '$ne' => null
                        ]
                    ],
                    [
                        'projection' => [
                            'country_origin' => '$country_origin',
                            '_id' => 0
                        ],
                        'limit' => 1
                    ]
                ]
            )->toArray();

        $countryActivity = $this->manager->setCollection('info4c_pep_detalhes')
            ->query(
                'find',
                [
                    [
                        'pep_id' => $id,
                        'country_origin' => [
                            '$ne' => null
                        ]
                    ],
                    [
                        'projection' => [
                            'country_origin' => '$country_origin',
                            '_id' => 0
                        ],
                        'limit' => 1
                    ]
                ]
            )->toArray();


        return [
            'country' => $country[0]['country'] ?? '',
            'country_origin' => $countryOrigin[0]['country_origin'] ?? '',
            'country_activity' => $countryActivity[0]['country_activity'] ?? ''
        ];
    }

    private function parseResult($result, $relatives)
    {
        foreach ($result as $keyResult => $valueResult) {
            $arrDetails = $this->getPepDetails($valueResult);
            $dataResult[] = [
                'id' => $valueResult['id'] ?? '',
                'relative_id' => $valueResult['relative_id'] ?? '',
                'record_type' => $valueResult['record_type'] ?? '',
                'title' => $valueResult['title'] ?? '',
                'gender' => $valueResult['gender'] ?? '',
                'first_name' => $valueResult['first_name'] ?? '',
                'last_name' => $valueResult['last_name'] ?? '',
                'full_name' => $valueResult['full_name'] ?? '',
                'other_names' => $valueResult['other_names'] ?? '',
                'original_name' => $valueResult['original_name'] ?? '',
                'birth_date' => $arrDetails[0]['birth_date'] ?? '',
                'birth_place' => $arrDetails[0]['birth_place'] ?? '',
                'country' => $arrDetails[0]['country'] ?? '',
                'country_origin' => $arrDetails[0]['country_origin'] ?? '',
                'country_activity' => $arrDetails[0]['country_activity'] ?? '',
                'details' => $result[$keyResult]['details'] ?? '',
                'relatives' => $relatives[$keyResult] ?? '',
            ];
        }

        return $dataResult;
    }

    private function getPepDetails($result)
    {
        $id = $result['id'];

        $subqueries = $this->findDataSubQueryDetail($id);

        $order = ['Main', 'Unique', 'Sec'];

        $pipeline = [
            [
                '$match' => [
                    'pep_id' => (int)$id
                ]
            ],
            [
                '$project' => [
                    'birth_date' => '$birth_date',
                    'birth_place' => '$birth_place',
                    '_id' => 0
                ],

            ],
            ['$limit' => 1],
            [
                '$addFields' => [
                    'country' => $subqueries['country'],
                    'country_origin' => $subqueries['country_origin'],
                    'country_activity' => $subqueries['country_activity'],
                    '_order' => ['$indexOfArray' => [$order, '$record_type']]
                ]
            ],
            ['$sort' => ['_order' => 1]],

        ];

        return $this->manager->setCollection('info4c_pep_detalhes')
            ->query('aggregate', [$pipeline])->toArray();
    }

    private function breakName($name)
    {
        $nameArr = explode(" ", $name);

        $middleName = $nameArr[1] ?? '';
        $lastName = $nameArr[2] ?? '';
        $firstName = $nameArr[0];
        $newName = trim($middleName . ' ' . $lastName . ' ' . $firstName);

        return [$newName, $name];
    }


    /** Formata data de acordo com o formato que vem da base
     * @param $date
     * @return string|null
     */
    private function parseDate($date)
    {
        $length = strlen($date);
        switch ($length) {
            case 10:
                $date = date_create_from_format('d.m.Y', $date);
                return $date->format('Y-m-d');
            case 7:
                $date = date_create_from_format('m.Y', $date);
                return $date->format('Y-m');
            case 4:
                $date = date_create_from_format('Y', $date);
                return $date->format('Y');
            default:
                return null;
        }
    }
}
