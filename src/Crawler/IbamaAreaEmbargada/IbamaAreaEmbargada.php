<?php

namespace App\Crawler\IbamaAreaEmbargada;

use Exception;
use App\Helper\Pdf;
use App\Helper\Str;
use App\Manager\S3\S3;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\S3\Bucket\StaticUplexisBucket;

class IbamaAreaEmbargada extends Spider
{
    private const BASE_URL = 'https://servicos.ibama.gov.br/';
    private const URL_CONSULT = self::BASE_URL . 'ctf/publico/areasembargadas/ConsultaPublicaAreasEmbargadas.php';
    private const URL_STATIC = S3_STATIC_URL;
    private const AWS_FOLDER = 'captura/ibama_area_embargada/';
    private const URL_CAPTCHA = self::BASE_URL . 'ctf/publico/areasembargadas/';
    private $pdfPath = "/tmp/IbamaAreaEmbargada";
    private $type = 0;
    private $doc = '';
    private const ESTADO = 1;
    private const NOME = 2;
    private const IMOVEL = 3;
    private const DOCUMENTO = 4;

    protected function validateAndSetCrawlerAttributes()
    {
        if (empty(Str::removeSpecialCharacters($this->param['nome_cpf_cnpj']))) {
            throw new Exception("Nenhum Parâmetro informado!", 3);
        }

        if (Document::validarCpfOuCnpj($this->param['nome_cpf_cnpj'])) {
            if (Document::validarCpf($this->param['nome_cpf_cnpj'])) {
                $this->type = self::DOCUMENTO;
            }

            if (Document::validarCnpj($this->param['nome_cpf_cnpj'])) {
                $this->type = self::DOCUMENTO;
            }

            $this->criterion = Document::formatCpfOrCnpj($this->param['nome_cpf_cnpj']);
        } else {
            $this->type = self::NOME;
            $this->criterion = Str::removerAcentos($this->param['nome_cpf_cnpj']);

            $criteria = Str::removerAcentos($this->param['nome_cpf_cnpj']);
        }
    }

    protected function start()
    {
        switch ($this->type) {
            case self::ESTADO:
                $result = $this->search($this->criterion, self::ESTADO);
                break;
            case self::NOME:
                $result = $this->search($this->criterion, self::NOME);
                break;
            case self::IMOVEL:
                $result = $this->search($this->criterion, self::IMOVEL);
                break;
            case self::DOCUMENTO:
                $result = $this->search($this->criterion, self::DOCUMENTO);
                break;
            default:
                throw new Exception('Não existe consulta para este tipo de critério!', 6);
                break;
        }

        return [
            'dados' => $result
        ];
    }

    private function search($criteria, $type)
    {
        $this->setCurlOpt([
            CURLOPT_SSL_VERIFYPEER => 0,
            CURLOPT_SSL_VERIFYHOST => 0,
            CURLOPT_FOLLOWLOCATION => 1,
            CURLOPT_RETURNTRANSFER => 1,

            CURLOPT_HEADER => 1
        ]);

        $headers = [
            "User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0",
            "Host: servicos.ibama.gov.br",
        ];

        $html = $this->getResponse(self::URL_CAPTCHA, 'GET', [], $headers);

        preg_match_all('/^Set-Cookie:\s*([^;]*)/mi', $html, $matches);
        $cookies = array();
        foreach ($matches[1] as $item) {
            parse_str($item, $cookie);
            $cookies = array_merge($cookies, $cookie);
        }
        $this->setCurlOpt([
            CURLOPT_HTTPHEADER => array("Cookie: PHPSESSID=" . $cookie['PHPSESSID']),
            CURLOPT_HEADER => 0
        ]);

        $captcha = $this->resolveHcaptcha($html);

        $headers = [
            "User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0",
            "Host: servicos.ibama.gov.br",
            "Origin: https://servicos.ibama.gov.br",
            "Referer: " . self::URL_CAPTCHA,
        ];

        $params = array(
            'formDinAcao' => 'Entrar',
            'formDinPosVScroll' => '30',
            'formDinPosHScroll' => '0',
            'h-captcha-response' => $captcha
        );

        $result = $this->getResponse(self::URL_CAPTCHA, 'POST', $params, $headers);

        if (preg_match('/Erro de valida..o do CAPTCHA/is', $html, $matches)) {
            throw new Exception('Não foi possível validar o captcha', 3);
        }

        $params = array(
            'ajax' => 1,
            'formDinAcao' => 'atualizar_grid_Areas_Embargadas_ajax',
            'cod_uf' => '',
            'cod_municipio' => '',
            'cod_tipo_bioma' => '',
            'nome_razao' => '',
            'nom_propriedade' => '',
            'cpf_cnpj' => '',
            'modulo' => 'ConsultaPublicaAreasEmbargadas.php',
            'sit_desmatamento' => 'T'
        );

        switch ($type) {
            case self::ESTADO:
                $params['codigo_uf'] = $criteria;
                break;
            case self::NOME:
                $params['nome_razao'] = $criteria;
                break;
            case self::IMOVEL:
                $params['nom_propriedade'] = $criteria;
                break;
            case self::DOCUMENTO:
                $params['cpf_cnpj'] = $this->doc = $criteria;
                $this->doc = Document::removeMask($this->doc);
                break;
            default:
                throw new Exception("Tipo de dado não mapeado. Entre em contato com o suporte", FLOW_ERROR);
                break;
        }

        $headers = [
            "User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0",
            "Host: servicos.ibama.gov.br",
            "Origin: https://servicos.ibama.gov.br",
            "Referer: " . self::URL_CONSULT
        ];

        $result = $this->getResponse(self::URL_CONSULT, 'POST', $params, $headers);

        $body = '';
        $result = utf8_encode($result);
        if ($this->haveResult($result)) {
            preg_match_all(
                '/<tr id="grid_areas_embargadas_tr_(\d)"[\s\t\n\w!-~\/à-úÀ-Ú]*line_number=\"\1"/',
                $result,
                $body
            );
            return $this->getFilledObjects($body[0]);
        }
        // throw new Exception("Não há resultados para essa consulta.", NOTFOUND_ERROR);
        switch ($type) {
            case self::ESTADO:
                return $this->returnNadaConsta($criteria, $type);
                break;

            case self::NOME:
                return $this->returnNadaConsta($criteria, $type);
                break;

            case self::IMOVEL:
                return $this->returnNadaConsta($criteria, $type);
                break;

            case self::DOCUMENTO:
                return $this->pdfNadaConsta($this->doc);
                break;

            default:
                throw new Exception("Tipo de dado não mapeado. Entre em contato com o suporte", FLOW_ERROR);
                break;
        }
    }

    private function returnNadaConsta($criteria, $type)
    {
        $object = array(
            'ntad' => '',
            'area' => '',
            'nai' => '',
            'nomeRazao' => '',
            'documento' => '',
            'localizacao' => '',
            'uf' => '',
            'municipio' => '',
            'julgamento' => '',
            'infracao' => 'NADA CONSTA',
            'dataInsercao' => '',
            'certidao_pdf' => '',
        );

        switch ($type) {
            case self::ESTADO:
                $object['uf'] = $criteria;
                break;

            case self::NOME:
                $object['nomeRazao'] = $criteria;
                break;

            case self::IMOVEL:
                break;

            default:
                break;
        }

        $aAreas = array($object);
        $aAreas = $aAreas;
        return $aAreas;
    }

    private function getPDF($html, $retry = 3)
    {
        $response = null;
        if (!preg_match('#<iframe[^>]*?src="(.*?)">#is', $html, $link)) {
            throw new Exception("Erro ao recuperar url captcha", 3);
        }
        $link = str_replace(' ', '%20', $link[1]);

        $headers = [
            'Accept: */*',
            'Accept-Encoding: gzip, deflate, br',
            'Accept-Language: pt-BR,pt;q=0.9v',
            'Connection: keep-alive',
            'Host: servicos.ibama.gov.br',
            'Referer: ' . self::URL_CONSULT,
            'Upgrade-Insecure-Requests: 1',
            'User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
        ];
        try {
            $response = $this->getResponse($link, 'GET', [], $headers);
        } catch (Exception $e) {
            if ($retry > 0) {
                $retry--;
                print "\nERROR: {$e->getMessage()}\n";
                print "\nTentando novamente...\n";
                print "\nTentativas restantes: {$retry}\n";
                sleep(5);
                $this->getPDF($html, $retry);
            }
        }
        return $response;
    }

    private function searchPDF($documento)
    {
        $this->doc = $documento;
        $params = array(
            'sit_isencao_lic_transporte' => 'E',
            'sit_desmatamento' => 'T',
            'cod_uf' => '',
            'cod_municipio' => '',
            'cod_tipo_bioma' => '',
            'nome_razao' => '',
            'nom_propriedade' => '',
            'cpf_cnpj' => $this->doc,
            'num_cpf_cnpj' => $this->doc,
            'cpf_cnpj_autuado' => '',
            'nom_pessoa_autuado' => '',
            'cod_receita' => '',
            'cod_tipo_bioma_auto' => '',
            'cod_uf_auto' => '',
            'dat_inicial' => '',
            'dat_final' => '',
            'cod_municipio_auto_temp' => '',
            'total_auto_infracao' => '',
            'total_valor_multa' => '',
            'fw_back_to' => '',
            'primeiro_acesso' => 'N',
            'num_latitude_tad' => '',
            'num_longitude_tad' => '',
            'numero_tad' => '',
            'moduloId' => '',
            'modulo' => '',
            'formDinAcao' => 'EmitirCertidao'
        );
        $headers = [
            "User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0",
            "Host: servicos.ibama.gov.br",
            "Origin: https://servicos.ibama.gov.br",
            "Referer: " . self::URL_CONSULT
        ];
        $result = $this->getResponse(self::URL_CONSULT, 'POST', $params, $headers);
        $pdf = $this->getPDF($result);

        return $this->saveAnexo($pdf);
    }

    private function getFilledObjects($html)
    {
        $arr = [];
        foreach ($html as $key => $item) {
            $item = html_entity_decode($item);
            preg_match_all(
                '/<td id="grid_areas_embargadas_.*?>([\D\W\S]*?)<\/td>/',
                $item,
                $output_array
            );
            $aux = $output_array[0];
            $documento = trim(strip_tags($aux[3]));
            $caminho_s3 = '';
            if ($this->type == self::DOCUMENTO) {
                $caminho_s3 = $this->searchPDF($this->criterion);
                $documento = Document::formatCpfOrCnpj($this->criterion);
            } elseif (Document::validarCpfOuCnpj($documento)) {
                $caminho_s3 = $this->searchPDF($documento);
            }
            //$content = strip_tags($item);
            $object = array(
                'ntad' => trim(strip_tags($aux[0])),
                'area' => '',
                'nai' => trim(strip_tags($aux[8])),
                'nomeRazao' => trim(strip_tags($aux[2])),
                'documento' => $documento,
                'localizacao' => trim(strip_tags($aux[4])),
                'uf' => trim(strip_tags($aux[5])),
                'municipio' => trim(strip_tags($aux[6])),
                'julgamento' => '',
                'infracao' => trim(strip_tags($aux[9])),
                'dataInsercao' => trim(strip_tags($aux[7])),
                'certidao_pdf' => $caminho_s3,
            );

            $arr[$key] =  $object;
        }
        $data = $arr;
        return $data;
    }

    public function pdfNadaConsta($documento)
    {
        $result = $this->searchPDF($documento);
        // a partir do documento realiza uma consulta para retornar o certificado nada consta
        $object = array(
            'ntad' => '',
            'area' => '',
            'nai' => '',
            'nomeRazao' => '',
            'documento' => $documento,
            'localizacao' => '',
            'uf' => '',
            'municipio' => '',
            'julgamento' => '',
            'infracao' => 'NADA CONSTA',
            'dataInsercao' => '',
            'certidao_pdf' => $result,
        );
        $aAreas = array($object);
        $aAreas = $aAreas;
        return $aAreas;
    }

    public function saveAnexo($pdf, $cliente = 'default')
    {
        $caminho = $this->pdfPath . uniqid() . '.pdf';
        file_put_contents($caminho, $pdf);
        $text = (new Pdf())->getTextFromPdf($caminho, [
            'nopgbrk',
            'layout',
            'fixed 4'
        ]);
        preg_match('/CNPJ\/CPF:(.*?)ENDEREÇO:/is', $text, $documento);
        $documento = trim($documento[1]);
        $documento = Document::removeMask($documento);
        $this->doc = Document::removeMask($this->doc);

        if ($documento == $this->doc || preg_match('/' . $documento . '/is', $this->doc)) {
            /*
            Gera o caminho para o S3 com o hash unico
            Observação, $cliente sempre será default, ja que o cliente não é passado nos parametros dessa fonte
            */
            $caminho_s3 = self::AWS_FOLDER . "{$cliente}/" . sha1(uniqid(rand(), true)) . '.pdf';

            //envia para o S3
            try {
                (new S3(new StaticUplexisBucket()))->save($caminho_s3, $caminho);
            } catch (Exception $e) {
                throw new Exception("Erro ao salvar arquivo no S3", 1);
            }

            file_exists($caminho) && unlink($caminho);
            //retorna a url estatica
            return self::URL_STATIC . $caminho_s3;
        }
        throw new Exception("Erro ao pesquisar, tente reprocessar.", 1);
    }

    private function haveResult($html)
    {
        preg_match('/((id=\"erro\")|(erro))/', $html, $output_array);
        return (count($output_array) > 0) ? false : true;
    }

    private function resolveHcaptcha($html)
    {
        $retry = 3;

        do {
            if (!preg_match('/data-sitekey="(.*?)"/is', $html, $matches)) {
                throw new Exception('Não foi possível recuperar o sitekey', 3);
            }

            return $this->solveHCaptchaWithCapMonster($matches[1], self::URL_CAPTCHA);

            $retry--;
        } while ($retry > 0);
    }
}
