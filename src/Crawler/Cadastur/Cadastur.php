<?php

namespace App\Crawler\Cadastur;

use Amfphp_Core_Amf_Deserializer;
use App\Crawler\Spider;
use App\Helper\Document;
use Exception;

class Cadastur extends Spider
{
    private const MAIN_URL = 'http://www.cadastur.turismo.gov.br/cadastur/messagebroker/amf';
    private const PDF_URL = 'http://www.cadastur.turismo.gov.br/cadastur/DocumentoAction.mtur?';

    private $activities = array(
        '96'  =>  'certificadoGuiaTurismo',
        '95'  =>  'certificadoBacharel',
        '20'  =>  'certificadoOficialPessoaJuridica',
        '10'  =>  'certificadoOficialPessoaJuridica'
    );

    protected function start()
    {
        $this->setProxy();
        $links = $this->getLinksByDocumento($this->cpfCnpj);
        return $links;
    }

    protected function validateAndSetCrawlerAttributes()
    {
        if (
            isset($this->param['cpf_cnpj'])
            && !empty($this->param['cpf_cnpj'])
            && Document::validarCpfOuCnpj($this->param['cpf_cnpj'])
        ) {
            $cpfCnpj = preg_replace("/\\D+/isu", "", $this->param['cpf_cnpj']);
        } else {
            throw new Exception('Parâmetro de criterio inválido', 1);
        }
        $this->cpfCnpj = $cpfCnpj;
    }

    private function getLinksByDocumento($documento)
    {
        // Get content of x-amf file (must read in binary mode)
        $postData = Document::validarCpf($documento)
            ? file_get_contents(__DIR__ . '/postData/cpf.txt')
            : file_get_contents(__DIR__ . '/postData/cnpj.txt');

        $header = array(
            'Content-Type:application/x-amf',
        );

        $response2 = $this->getResponse(self::MAIN_URL, 'POST', $postData, $header);

        $deserializer = new Amfphp_Core_Amf_Deserializer();
        $amfObject = $deserializer->deserialize([], [], $response2);
        try {
            $data = $amfObject->messages[0]->data->body;
        } catch (Exception $e) {
            throw new Exception('Falha ao recuperar dados do objeto AMF', 3);
        }
        $links = [];

        if (!isset($data->colDTO) || empty($data->colDTO)) {
            throw new Exception('Nenhum resultado encontrado', 2);
        }
        try {
            foreach ($data->colDTO as $colDto) {
                $data = [];
                if (Document::validarCpf($documento)) {
                    $data = array(
                        'ativpfdto.nuPessoaFisica=' . $colDto->nuPessoaFisica,
                        'ativpfdto.nuTipoAtividade=' . $colDto->nuTipoAtividade,

                    );
                } elseif (Document::validarCnpj($documento)) {
                    $data = array(
                        'ativpjdto.nuPessoaJuridica=' . $colDto->nuPessoaJuridica,
                        'ativpjdto.nuTipoAtividade=' . $colDto->nuTipoAtividade,

                    );
                }
                $data[] =   'method%3A' . $this->activities[$colDto->nuTipoAtividade];
                $links[] = array(
                    'url'   =>  self::PDF_URL . implode('&', $data)
                );
            }
        } catch (Exception $e) {
            throw new Exception('Falha ao parsear os dados do objeto colDTO', 3);
        }
        return $links;
    }
}
