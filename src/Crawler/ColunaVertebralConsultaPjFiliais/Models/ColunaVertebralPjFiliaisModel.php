<?php

namespace App\Crawler\ColunaVertebralConsultaPjFiliais\Models;

use App\Crawler\AbstractModel;
use App\Crawler\ColunaVertebralConsultaPjFiliais\Models\ColunaVertebralPjFiliaisCnjModel;
use App\Crawler\ColunaVertebralConsultaPjFiliais\Models\ColunaVertebralPjFiliaisCnaeModel;
use App\Crawler\ColunaVertebralConsultaPjFiliais\Models\ColunaVertebralPjFiliaisAtividadeSecundariaModel;

class ColunaVertebralPjFiliaisModel extends AbstractModel
{
    public $cnpj;
    public $matriz;
    public $razao_social;
    public $nome_fantasia;
    public $logradouro;
    public $numero;
    public $complemento;
    public $bairro;
    public $municipio;
    public $uf;
    public $cep;
    public $data_abertura;
    public $situacao_cadastral;
    public $data_situacao;
    public $motivo_situacao;
    public $situacao_especial;
    public $data_especial;
    public $motivo_especial;
    public $hora_consulta;
    public $data_consulta;
    public $numero_filiais;
    public $oCnj;
    public $oCnae;
    public $aAtividadeSecundaria;
    public $arrayOriginal;

    public function setCnj(ColunaVertebralPjFiliaisCnjModel $cnj)
    {
        $this->oCnj = $cnj;
    }

    public function setCnae(ColunaVertebralPjFiliaisCnaeModel $cnae)
    {
        $this->oCnae = $cnae;
    }

    public function setSecondaryActivity(ColunaVertebralPjFiliaisAtividadeSecundariaModel $secondaryActivity)
    {
        $this->aAtividadeSecundaria[] = $secondaryActivity;
    }
}
