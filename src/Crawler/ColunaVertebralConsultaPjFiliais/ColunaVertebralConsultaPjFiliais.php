<?php

namespace App\Crawler\ColunaVertebralConsultaPjFiliais;

use App\Crawler\Spider;
use App\Manager\ElasticsearchManager;
use App\Helper\Document;
use App\Crawler\ColunaVertebralConsultaPjFiliais\Models\ColunaVertebralPjFiliaisModel;
use App\Crawler\ColunaVertebralConsultaPjFiliais\Models\ColunaVertebralPjFiliaisCnjModel;
use App\Crawler\ColunaVertebralConsultaPjFiliais\Models\ColunaVertebralPjFiliaisCnaeModel;
use App\Manager\ColunaVertebralManager;
use Exception;

/**
 * Classe da fonte Coluna Vertebral PJ Filiais
 *
 * @version 1.0.0
 *
 * <AUTHOR> Mesquita 01/10/2020
 */
class ColunaVertebralConsultaPjFiliais extends Spider
{
    private $colunaVertebralManager;
    private $QuerySize;
    private $QueryOffset;
    private $OnlyCount;

    /**
     * Busca os dados de filiais no spine PJ
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 01/10/2020
     *
     * @return array
     */
    public function start(): array
    {
        $this->colunaVertebralManager = new ColunaVertebralManager();

        if ($this->OnlyCount) {
            return $this->countFiliais();
        }

        return $this->searchFiliais();
    }

    /**
     * Valida os parametros da fonte
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 01/10/2020
     *
     * @return void
     */
    public function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCnpj($this->param['cnpj'])) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }

        $this->param['cnpj'] = Document::removeMask($this->param['cnpj']);
        $this->QuerySize = $this->param['size'] ?? 10000;
        $this->QueryOffset = $this->param['offset'] ?? 0;
        $this->OnlyCount = $this->param['only_count'] ?? false;

        if (!isset($this->param['cnae'])) {
            $this->param['cnae'] = true;
        }
    }

    /**
     * Retorna as filiais do critério informado
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 01/10/2020
     *
     * @return array
     */
    private function searchFiliais(): array
    {
        $results = (new ElasticsearchManager())->search($this->getQueryParams());

        $data = [];

        foreach ($results['hits']['hits'] as $hit) {
            $spinePj = $hit['_source'];

            if ($this->param['cnpj'] == $spinePj['cnpj']) {
                continue;
            }

            $consultaCnpj = new ColunaVertebralPjFiliaisModel();
            $consultaCnpjCnae = new ColunaVertebralPjFiliaisCnaeModel();
            $consultaCnpjCnj = new ColunaVertebralPjFiliaisCnjModel();

            $consultaCnpj->cnpj = $spinePj['cnpj'];
            $consultaCnpj->matriz = $spinePj['matriz'] == 'True' ? 'sim' : 'nao';
            $consultaCnpj->razao_social = $spinePj['razao_social'];
            $consultaCnpj->nome_fantasia = $spinePj['nome_fantasia'];
            $consultaCnpj->logradouro = "{$spinePj['logr_tipo']} {$spinePj['logr_nome']}";
            $consultaCnpj->numero = $spinePj['logr_numero'];
            $consultaCnpj->complemento = $spinePj['logr_complemento'];
            $consultaCnpj->bairro = $spinePj['bairro'];
            $consultaCnpj->municipio = $spinePj['cidade'];
            $consultaCnpj->uf = $spinePj['uf'];
            $consultaCnpj->cep = $spinePj['cep'];
            $consultaCnpj->data_abertura = $this->colunaVertebralManager->formatDate($spinePj['data_abertura']);
            $consultaCnpj->situacao_cadastral = $spinePj['situacao_cadastral'];
            $consultaCnpj->data_situacao = $this->colunaVertebralManager->formatDate($spinePj['data_situacao']);
            $consultaCnpj->motivo_situacao = $spinePj['motivo_situacao'];
            $consultaCnpj->situacao_especial = $spinePj['situacao_especial'];
            $consultaCnpj->numero_filiais = $spinePj['numero_filiais'];

            if ($this->param['cnae']) {
                $spinePjCnae = $this->colunaVertebralManager->getSpinePjCnae((string)intval($spinePj['cnae']));

                if (!empty($spinePjCnae) && $spinePjCnae[0]) {
                    $consultaCnpjCnae->codigo = (string)intval($spinePjCnae[0]['id']);
                    $consultaCnpjCnae->descricao = $spinePjCnae[0]['descricao'];
                }
            }

            $consultaCnpj->setCnae($consultaCnpjCnae);

            $consultaCnpjCnj->codigo = $spinePj['natureza_juridica'];
            $consultaCnpjCnj->descricao = $this->colunaVertebralManager->getCnpj($spinePj['natureza_juridica']);
            $consultaCnpj->setCnj($consultaCnpjCnj);

            $data[] = $consultaCnpj;
        }

        return $data;
    }

    private function countFiliais(): array
    {
        $results = (new ElasticsearchManager())->search($this->getQueryParams());

        $results = array_filter($results['hits']['hits'], function ($key) {
            return $key['_source']['cnpj'] != $this->param['cnpj'];
        });

        return ['quantidade_filiais' => count($results)];
    }

    /**
     * Retorna query do elastic search para busca de filiais
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 01/10/2020
     *
     * @return array
     */
    private function getQueryParams(): array
    {
        return [
            'index' => 'spine_pj',
            'type' => 'pj',
            'body' => [
                'query' => [
                    'bool' => [
                        'filter' => [
                            'regexp' => [
                                'cnpj' => [
                                    'value' => substr($this->param['cnpj'], 0, 8) . '[0-9]{6}',
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            'size' => $this->QuerySize,
            'from' => $this->QueryOffset
        ];
    }
}
