<?php

namespace App\Crawler\CertidaoCeatTrtMG;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Helper\Util;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

class CertidaoCeatTrtMG extends Spider
{
    private $cpfCnpj = '';
    private $cpf = '';
    private $cnpj = '';
    private $pdf = '';
    private $tipoPessoa = '';
    protected $sitekey = '';
    protected $viewState = null;
    private const BASE_URL = 'https://certidao.trt3.jus.br/certidao/feitosTrabalhistas/aba1.emissao.htm';
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const CERTIDAO_S3_PATH = 'captura/certidao_ceat_trt3_mg/';
    private const BASE_URL_SITE = 'https://sistemas.trt3.jus.br';

    public function start()
    {
        $this->setAlternativeProxy();

        $this->setCurlOpt([
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::CERTIDAO_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;

        $retry = 5;
        while ($retry >= 0) {
            try {
                $html = $this->getResponse(self::BASE_URL, 'GET');

                preg_match('/State"\svalue="([\s\S]*?)"/is', $html, $this->viewState);
                if (!isset($this->viewState[1])) {
                    throw new Exception("Erro ao obter viewState", 6);
                }

//                preg_match('/data-sitekey="(.*?)"/is', $html, $this->sitekey);
//
//                if (!isset($this->sitekey[1])) {
//                     throw new Exception("Erro ao obter sitekey", 6);
//                }

                if (!empty($this->cpf)) {
                    $this->makeRequestCPF();
                    $htmlName = $this->makeRequestNameCPF();
                    $pdf = $this->makeRequestPdfCpf($htmlName);
                } else {
                    $this->makeRequestCNPJ();
                    $htmlName = $this->makeRequestNameCNPJ();
                    $pdf = $this->makeRequestPdfCNPJ($htmlName);
                }

                $text = $this->savePdfAndReturnText($pdf);
                $info = $this->parseData($text);

                $data = [
                    'info' => $info,
                    'pdf' => $this->pdf
                ];

                return $data;
            } catch (Exception $e) {
                if ($retry == 0) {
                    throw new Exception("Erro ao capturar as informações da página", 6);
                }

                $retry--;
            }
        }
    }

    private function makeRequestCNPJ()
    {
        $params = [
            'javax.faces.source' => 'form:tipoPessoa',
            'javax.faces.partial.execute' => 'form:tipoPessoa',
            'javax.faces.partial.render' => 'form:tableDocumentos form:nomeConsulta 
                form:painelNomeInformado form:textoVariacao',
            'javax.faces.behavior.event' => 'change',
            'javax.faces.partial.event' => 'change',
            'form' => 'form',
            'form:tipoPessoa' => $this->tipoPessoa,
            'form:nomeConsulta' => '',
            // 'g-recaptcha-response' => '',
            'form:verifyCaptcha_' => '',
            'javax.faces.ViewState' => $this->viewState[1],
        ];

        $html = $this->getResponse(self::BASE_URL, 'POST', $params);

        preg_match('/1.\sCNPJ/', $html, $is_cnpj);

        preg_match('/State"\svalue="([\s\S]*?)"/', $html, $this->viewState);
        if (!isset($this->viewState[1])) {
            throw new Exception("Erro ao obter viewState", 6);
        }

        if (empty($is_cnpj)) {
            throw new Exception('Resposta inválida!');
        }
    }

    /**
     * Captura o nome da pessoa jurídica quando for CNPJ.
     * <AUTHOR> Pereira
     * @param string $viewState
     * @return string $htmlName
     */
    private function makeRequestNameCNPJ()
    {
        $params = [
            'javax.faces.source' => 'form:inputCNPJ',
            'javax.faces.partial.execute' => 'form:inputCNPJ',
            'javax.faces.partial.render' => 'form:nomeConsulta form:nomeReceitaCPF 
                form:nomeReceitaCNPJ form:botaoConsultar messages',
            'javax.faces.behavior.event' => 'change',
            'javax.faces.partial.event' => 'change',
            'form' => 'form',
            'form:tipoPessoa' => $this->tipoPessoa,
            'form:inputCNPJ' => $this->cnpj,
            'form:nomeReceitaCNPJ' => '',
            'form:nomeConsulta' => '',
            // 'g-recaptcha-response' => '',
            'form:verifyCaptcha_' => '',
            'javax.faces.ViewState' => $this->viewState[1]
        ];

        $htmlName = $this->getResponse(self::BASE_URL, 'POST', $params);
        preg_match('/name="form:nomeReceitaCNPJ"\stype="text"\svalue="([\s\S]*?)"/', $htmlName, $name);

        if (empty($name[1])) {
            throw new Exception('Nome não retornado');
        }

        return $name[1];
    }

    /**
     * Captura a certidão da requisição em pdf quando for CNPJ.
     * <AUTHOR> Pereira
     * @param string $viewState
     * @param string $html
     * @return $pdf
     */
    private function makeRequestPdfCNPJ($name)
    {
        $captcha = $this->validateCaptcha();

        $params = [
            'form' => 'form',
            'form:tipoPessoa' => $this->tipoPessoa,
            'form:inputCNPJ' => $this->cnpj,
            'form:nomeReceitaCNPJ' => $name,
            'form:nomeConsulta' => '',
            'form:botaoConsultar' =>  '',
            'javax.faces.ViewState' => $this->viewState[1],
//             'g-recaptcha-response' => $captcha
            'form:verifyCaptcha_' => $captcha,
        ];

        $pdf = $this->getResponse(self::BASE_URL, 'POST', $params);
        return $pdf;
    }

    private function makeRequestCPF()
    {
        $params = [
            'javax.faces.source' => 'form:tipoPessoa',
            'javax.faces.partial.execute' => 'form:tipoPessoa',
            'javax.faces.partial.render' => 'form:tableDocumentos form:nomeConsulta 
                form:painelNomeInformado form:textoVariacao',
            'javax.faces.behavior.event' => 'change',
            'javax.faces.partial.event' => 'change',
            'form' => 'form',
            'form:tipoPessoa' => $this->tipoPessoa,
            'form:nomeConsulta' => '',
            // 'g-recaptcha-response' => '',
            'form:verifyCaptcha_' => '',
            'javax.faces.ViewState' => $this->viewState[1]
        ];

        $html = $this->getResponse(self::BASE_URL, 'POST', $params);
        preg_match('/1.\sCPF/', $html, $is_cpf);

        preg_match('/State"\svalue="([\s\S]*?)"/', $html, $this->viewState);
        if (!isset($this->viewState[1])) {
            throw new Exception("Erro ao obter viewState", 6);
        }

        if (empty($is_cpf)) {
            throw new Exception('Resposta inválida!');
        }
    }

    /**
     * Captura o nome da pessoa física quando for CPF.
     * <AUTHOR> Pereira
     * @param string $viewState
     * @return string $htmlName
     */
    private function makeRequestNameCPF()
    {
        $params = [
            'javax.faces.source' => 'form:inputCPF',
            'javax.faces.partial.execute' => 'form:inputCPF',
            'javax.faces.partial.render' => 'form:nomeConsulta form:nomeReceitaCPF 
                form:nomeReceitaCNPJ form:botaoConsultar',
            'javax.faces.behavior.event' => 'change',
            'javax.faces.partial.event' => 'change',
            'form' => 'form',
            'form:tipoPessoa' => $this->tipoPessoa,
            'form:inputCPF' => $this->cpf,
            'form:nomeReceitaCPF' => '',
            'form:nomeConsulta' => '',
            // 'g-recaptcha-response' => '',
            'form:verifyCaptcha_' => '',
            'javax.faces.ViewState' => $this->viewState[1],
        ];

        $htmlName = $this->getResponse(self::BASE_URL, 'POST', $params);
        preg_match('/name="form:nomeReceitaCPF"\stype="text"\svalue="([\s\S]*?)"/', $htmlName, $name);

        if (empty($name[1])) {
            throw new Exception('Nome não retornado');
        }

        return $name[1];
    }

    /**
     * Captura a certidão da requisição em pdf quando for CPF.
     * <AUTHOR> Pereira
     * @param string $viewState
     * @param string $html
     * @return $pdf
     */
    private function makeRequestPdfCpf($name)
    {
        $captcha = $this->validateCaptcha();
        $params = [
            'form' => 'form',
            'form:tipoPessoa' => $this->tipoPessoa,
            'form:inputCPF' => $this->cpf,
            'form:nomeReceitaCPF' => $name,
            'form:nomeConsulta' => '',
            'form:botaoConsultar' =>  '',
            'javax.faces.ViewState' => $this->viewState[1],
//             'g-recaptcha-response' => $captcha
            'form:verifyCaptcha_' => $captcha,
        ];

        $pdf = $this->getResponse(self::BASE_URL, 'POST', $params);
        return $pdf;
    }

    /**
     * Valida o captcha da fonte
     * <AUTHOR> Pereira
     * @return array
     */
    private function validateCaptcha()
    {
//         $this->captcha = $this->solveReCaptcha($this->sitekey[1], self::BASE_URL);

        $urlCaptcha = 'https://certidao.trt3.jus.br/certidao/seam/resource/captcha?f=16987531' . mt_rand(1, 5);
        file_put_contents($this->captcha_path, $this->getResponse($urlCaptcha));
        $this->breakCaptcha();

        return $this->captcha;
    }

    /**
     * Retorna o texto do PDF
     * <AUTHOR> Pereira
     * @return string
     */
    private function savePdfAndReturnText($pdf)
    {
        file_put_contents($this->certificateLocalPath, $pdf);

        $text = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'layout',
            'nopgbrk'
        ]);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        $this->pdf = $this->certificateUrl;

        return $text;
    }

    private function parseData($text)
    {
        $patterns = [
            'statusCertidao' => ['@CERTIDÃO\s(NEGATIVA|POSITIVA)@'],
            'numCertidao' => ['@o\sn\.\s([\s\S]*?)\n@'],
            'expedicao' => ['@Expedição:\s([\s\S]*?)\n@'],
            'codAutenticidade' => ['@autenticidade:\s([\s\S]*?)\n@'],
            'validade' => ['@Válida\saté\s([\s\S]*?)\n@'],
            'descricao' => ['@(Certifica-se[\s\S]*?)\s+OBSERVA@'],
            'observacoes' => ['@(OBSERVAÇÕES[\s\S]*?)\s+v\.1\.3@']
        ];

        $data = Util::parseDados($patterns, $text);
        $data = array_map("utf8_decode", $data);
        $data['observacoes'] = preg_replace('/\s\sPágina[\s\S]*?TRABALHISTAS/', '', $data['observacoes']);
        $data['descricao'] = preg_replace('/\s\sPágina[\s\S]*?TRABALHISTAS/', '', $data['descricao']);
        $data['descricao'] = preg_replace('/\(PJe\)/', "(PJe)\n", $data['descricao']);
        return $data;
    }


    public function validateAndSetCrawlerAttributes()
    {
        $this->cpfCnpj = trim($this->param['cpf_cnpj']);

        if (!Document::validarCpfOuCnpj($this->cpfCnpj)) {
            throw new Exception('Parâmetro Inválido', 6);
        }

        if (Document::validarCnpj($this->cpfCnpj)) {
            $this->cnpj = Document::formatCnpj($this->cpfCnpj);
            $this->tipoPessoa = 'J';
        } elseif (Document::validarCpf($this->cpfCnpj)) {
            $this->cpf = Document::formatCpf($this->cpfCnpj);
            $this->tipoPessoa = 'F';
        }
    }
}
