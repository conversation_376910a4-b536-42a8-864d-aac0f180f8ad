<?php

namespace App\Crawler\EnriquecimentoDadosCadastrais;

use App\Crawler\Spider;
use App\Manager\BigDataCorpManager;
use App\Manager\TargetDataManager;
use App\Helper\Document;
use App\Helper\Date;
use App\Helper\Phone;

class EnriquecimentoDadosCadastrais extends Spider
{
    public function start()
    {
        $targetInfo = $this->getDataFromTargetData($this->param['cpf']);
        $dataCorp = $this->getDataFromBigDataCorp($this->param['cpf']);

        return $this->getSummaryDatas($targetInfo, $dataCorp);
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['cpf']) || !Document::validarCpf($this->param['cpf'])) {
            throw new Exception('CPF informado é inválido');
        }
    }

    private function getSummaryDatas($targetData, $bigDataCorp)
    {
        $targetDataInfos = $this->getInfoDataTarget($targetData['result'][0]['pessoa']);
        $bigDataCorpInfos = $this->getInfoBigDataCorp($bigDataCorp['Result'][0]);

        return array_merge($targetDataInfos, $bigDataCorpInfos);
    }

    private function getDataFromBigDataCorp($cpf)
    {
        $bigDataCorp = new BigDataCorpManager($this->idUser);

        return json_decode($bigDataCorp->getDataPersonApi(
            'emails_extended,related_people_addresses,phones_extended',
            $cpf,
            'EnriquecimentoDadosCadastrais'
        ), true);
    }

    private function getDataFromTargetData($cpf)
    {
        $targetData = new TargetDataManager();
        return $targetData->searchByCpf($cpf);
    }

    private function getInfoDataTarget(array $targetData)
    {
        $telefones = array_map(function ($telefone) {
            $telefone['TelefoneComDDD'] = $telefone['ddd'] . $telefone['numero'];
            return $telefone;
        }, $targetData['contato']['telefone']);

        $enderecos = array_map(function ($endereco) {
            return [
                'LogradouroComplemento' => $endereco['complemento'] ?? null,
                'LogradouroCompleto' => $endereco['tipoLogradouro'] . ' ' . $endereco['logradouro'],
                'Logradouro' => $endereco['tipoLogradouro'] ?? null,
                'LogradouroNumero' => $endereco['numero'] ?? null,
                'Bairro' => $endereco['bairro'] ?? null,
                'Cidade' => $endereco['cidade'] ?? null,
                'UF' => $endereco['uf'] ?? null,
                'CEP' => $endereco['cep'] ?? null,
                'LATITUDE' => $endereco['latitude'] ?? null,
                'LOGINTUDE' => $endereco['longitude'] ?? null,
            ];
        }, $targetData['contato']['endereco']);

        $emails = array_map(function ($email) {
            return[
                'EnderecoEmail' => $email['email']
            ];
        }, $targetData['contato']['email']);

        return [
            'cpf' => $targetData['cadastral']['CPF'],
            'dataNascimento' => $targetData['cadastral']['dataNascimento'],
            'rg' => $targetData['cadastral']['rgNumero'],
            'nome' =>
                $targetData['cadastral']['nomePrimeiro'] . ' ' .
                $targetData['cadastral']['nomeMeio'] . ' ' .
                $targetData['cadastral']['nomeUltimo'],
            'nomeMae' =>
                $targetData['cadastral']['maeNomePrimeiro'] . ' ' .
                $targetData['cadastral']['maeNomeMeio'] . ' ' .
                $targetData['cadastral']['maeNomeUltimo'],
            'sexo' => $targetData['cadastral']['sexo'],
            'idade' => Date::getAge($targetData['cadastral']['dataNascimento']),
            'situacaoReceitaBancoDados' => $targetData['cadastral']['statusReceitaFederal'],
            'profissao' => $targetData['socioDemografico']['profissao'],
            'rendaEstimada' => $targetData['socioDemografico']['rendaPresumida'],
            'patrimonio' => $targetData['patrimonio'],
            'emails' => $emails,
            'telefones' => $telefones,
            'enderecos' => $enderecos
        ];
    }

    private function getInfoBigDataCorp($targetData)
    {
        $emails = $emails = array_map(function ($email) {
            return[
                'EnderecoEmail' => $email['EmailAddress']
            ];
        }, $targetData['ExtendedEmails']['Emails']);

        $telefones = array_map(function ($telefone) {
            return [
                'TelefoneComDDD' => $telefone['AreaCode'] . $telefone['Number'],
                'TipoTelefone' => Phone::kindOfPhone($telefone['Number'])
            ];
        }, $targetData['ExtendedPhones']['Phones']);

        $enderecos = array_map(function ($endereco) {
            return [
                'tipoLogradouro' => $endereco['Typology'],
                'Logradouro' => $endereco['AddressMain'],
                'LogradouroNumero' => $endereco['Number'],
                'LogradouroComplemento' => $endereco['Complement'],
                'Bairro' => $endereco['Neighborhood'],
                'Cidade' => $endereco['City'],
                'UF' => $endereco['State'],
                'CEP' => $endereco['ZipCode'],
                'LogradouroCompleto' => $endereco['Typology'] . ' '  . $endereco['AddressMain'],
                'LATITUDE' => $endereco['Latitude'],
                'LONGITUDE' => $endereco['Longitude']
            ];
        }, $targetData['RelatedPeopleAddresses']);

        return [
            'emails' => $emails,
            'telefones' => $telefones,
            'enderecos' => $enderecos
        ];
    }
}
