<?php

namespace App\Crawler\ReceitaFederalComprovanteSituacaoCpf;

use App\Crawler\Spider;
use App\Helper\Pdf;
use App\Helper\Client;
use Symfony\Component\DomCrawler\Crawler;
use App\Manager\S3\S3;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use Exception;

class ReceitaFederalComprovanteSituacaoCpf extends Spider
{
    private const BASE_URL = 'https://servicos.receita.fazenda.gov.br/';

    private $mainURL = self::BASE_URL . 'servicos/cpf/consultasituacao/consultapublica.asp';

    private $request = self::BASE_URL . 'servicos/cpf/consultasituacao/ConsultaPublicaExibir.asp';
    private $static = S3_STATIC_URL . 'receita_federal_comprovante_situacao_cpf/';

    /**
     * @var Goutte\Client
     */
    private $client;

    private $pdf = null;

    /**
     * Processa a fonte
     *
     * @return array
     * <AUTHOR> <<EMAIL>>
     */
    protected function start()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->tempPath = '/tmp/';
        $this->certificatePath = $this->tempPath . "{$this->certificateName}";
        $this->certificatePathS3 = S3_STATIC_URL . "captura/receita_federal_comprovante_situacao_cpf/" .
            "{$this->certificateName}";
        $this->configAlternativeClient();

        $check = false;

        $form = [
            'Enviar' => 'Consultar',
            'h-captcha-response' => '',
            'idCheckedReCaptcha' => false,
            'txtCPF' => \App\Helper\Document::formatCpf($this->param['cpf']), // CPF Estilizado
            'txtDataNascimento' => $this->param['data_nascimento'], // Data formatada d/m/Y
        ];

        $checkErroCaptcha = '/A validação anti-robô não foi realizada corretamente. Por favor, tente novamente./iu';
        $retry = 0;

        while ($check === false) {
            if ($retry >= 4) {
                throw new Exception('Erro na validação do Captcha!', 3);
            }

            $token = $this->resolveCaptcha();
            $form['h-captcha-response'] = $token;

            $result = $this->client->request('POST', $this->request, $form);

            if (preg_match($checkErroCaptcha, $result->html())) {
                // Se der erro no captcha pela Receita pede o refund
                $this->refundReCaptchaAntigate();

                $retry++;
                continue;
            }

            $check = true;
        }

        $hasRegistered = '/CPF não encontrado na base de dados da Receita Federal/iu';

        if (preg_match($hasRegistered, $result->html())) {
            $this->pdf = $this->certificatePathS3;

            $this->simulatePrintContent($result, true);

            throw new Exception('CPF não encontrado na base de dados da Receita Federal', 6);
        }

        file_put_contents($this->tempPath . '/teste-receita-comprovante.html', $result->html());
        (new S3(new StaticUplexisBucket()))->save(
            'captura/receita_federal_comprovante_situacao_cpf/teste-receita-comprovante.html',
            $this->tempPath . '/teste-receita-comprovante.html'
        );

        $this->simulatePrintContent($result);

        $data = $this->parseResult($result);

        return $data;
    }

    protected function addExtraDataToResponse($response)
    {
        $response['pdf'] = $this->pdf;

        return $response;
    }

    /**
     * Parseia as informações do Resultado
     *
     * @param Crawler $result
     * @return array
     *
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function parseResult(Crawler $result)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $data = [];
        $html = $result->filter('#conteudo')->html();

        $rules = [
            'cpf' => "#CPF:\\s*<b>(.*?)</b>#i",
            'nome' => "#Nome:\\s*<b>(.*?)<\\/b>#i",
            'nascimento' => "#Data\\s*de\\s*Nascimento:\\s*<b>(.*?)</b>#i",
            'situacao' => "#Situa.*?o\\s*Cadastral:\\s*<b>(.*?)</b>#i",
            'inscricao' => "#Data\\s*da\\s*Inscri.*?o:\\s*<b>(.*?)</b>#i",
            'digito_verificador' => "#Digito\\s*Verificador:\\s*<b>(.*?)</b>#i",
            'obito' => "#Ano\\s*de\\s*.*?bito:\\s*<b>(.*?)</b>#i",
            'hora' => "#Comprovante\\s*emitido\\s*.*s:\\s*<b>(.*?)</b>#i",
            'data' => "#do\\s*dia\\s*<b>(.*?)</b>#i",
            'chave' => "#controle\\s*do\\s*comprovante:\\s*<b>(.*?)</b>#i",
        ];

        foreach ($rules as $k => $p) {
            if (!preg_match($p, $html, $rs)) {
                $data[$k] = '';
                continue;
            }
            $data[$k] = trim($rs[1]);
        }

        if ($this->param['completo']) {
            return $data;
        }

        return [
            'cpf' => $data['cpf'],
            'nome' => $data['nome'],
            'dataNascimento' => $data['nascimento'],
            'situacaoCadastral' => $data['situacao'],
            'dataInscrição' => $data['inscricao'],
            'digitoVerificador' => $data['digito_verificador'],
            'codigoComprovante' => $data['chave'],
            'pdf' => $this->certificatePathS3,
        ];
    }

    /**
     * Simula a funcionalidade de imprimir da Receita Federal
     *
     * @param Crawler $result
     * @param bool $errorMode
     * @return void
     *
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function simulatePrintContent(Crawler $result, bool $errorMode = false)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (!$errorMode) {
            $conteudo = $result->filter('#conteudo');
            if ($conteudo->count() > 0) {
                $content = $conteudo->html();
            } else {
                throw new Exception("Falha ao buscar o conteúdo do PDF", 3);
            }
        }

        if ($errorMode) {
            $content = $result->html();
        }

        $html = '<html>
        <head>
            <meta http-equiv="x-ua-compatible" content="IE=9">
            <meta http-equiv="Content-Language" content="pt-br">
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
            <title>Document</title>
            <link href="' . $this->static . 'assets/ComprovanteCPF01.css" rel="stylesheet" type="text/css">
            <link href="' . $this->static . 'assets/ComprovanteCPF_Impressao01.css" rel="stylesheet" type="text/css">
            <script src="' . $this->static . 'assets/qrcode.js"></script>
        </head>
        <body>' . $content . '</body></html>';

        $html = $this->sanitizeResponse($html);

        $this->savePDF($html);
    }

    /**
     * Gera o PDF a partir de um HTML
     *
     * @param string $html
     * @param string $pdf
     * @return boolean
     *
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function html4pdf(string $html, string $pdf)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        (new Pdf())->saveHtmlToPdf($html, $this->certificatePath);
        $res = false;
        if (file_exists($this->certificatePath)) {
            // Remove o arquivo temporário
            $res = true;
        }
        return $res;
    }

    /**
     * Ajusta os erros de decodificação
     *
     * @param string $html
     * @return string
     *
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function sanitizeResponse(string $html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $search = array(
            '/images\/armasbra\.gif/iu',
            '/images\/printer\.gif/iu',
            '/Minist..rio/iu',
            '/Situa....o/iu',
            '/Inscri....o/iu',
            '/\Ã\ss/iu',
            '/Bras..lia/iu',
            '/C\Ã.digo/iu',
            '/n..o/iu',
            '/\Â\/iu',
            '/.amp.nbsp/iu',
            '/Bras..o/iu',
            '/Rep..blica/iu',
            '/P..gina/iu',
            '/Impress..o/iu',
            '/CPF\Â\/iu',
            '/conãole/iu',
        );

        $replace = array(
            "{$this->static}assets/armasbra.gif",
            "{$this->static}assets/printer.gif",
            'Ministério',
            'Situação',
            'Inscrição',
            'às',
            'Brasília',
            'Código',
            'não',
            '"',
            '',
            'Brasão',
            'República',
            'Página',
            'Impressão',
            'CPFÂ',
            'controle',
        );

        foreach ($search as $key => $pattern) {
            $html = preg_replace($pattern, $replace[$key], $html);
        }

        return $html;
    }

    /**
     * Validação do Recaptcha
     *
     * @return string
     *
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function resolveCaptcha()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }



        $retry = 3;

        do {
            $html = $this->client->request('GET', $this->mainURL);

            $element = $html->filter('div#hcaptcha');

            if (!empty($element)) {
                return $this->solveHCaptcha($element->attr('data-sitekey'), $this->request);
            }

            $retry--;
        } while ($retry > 0);

        throw new Exception("Erro ao localizar dados do captcha na página.", 3);
    }

    /**
     * Valida os parametros
     *
     * @return void
     *
     * <AUTHOR> Hugo <<EMAIL>>
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (empty($this->param['cpf'])) {
            throw new Exception('É necessário CPF!', 1);
        }

        if (empty($this->param['data_nascimento'])) {
            throw new Exception('É necessário Data de Nascimento!', 1);
        }

        if (empty($this->param['completo'])) {
            $this->param['completo'] = false;
        }

        $this->param['cpf'] = preg_replace('/[^0-9]/', '', $this->param['cpf']);
    }

    /**
     * Define as configurações do Client secundário
     *
     * @return void
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function configAlternativeClient()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $this->client = (new Client())->createGoutteClient((new Client())->createHttpClient([], 'native'));
    }

    /**
     * Gera e salva no S3 o PDF a partir do HTML
     *
     * @param string $html
     * @return void
     *
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function savePDF($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (!$this->html4pdf($html, $this->certificateName)) {
            throw new Exception("Erro ao gravar PDF!", 3);
        }

        (new S3(new StaticUplexisBucket()))->save(
            'captura/receita_federal_comprovante_situacao_cpf/' . $this->certificateName,
            $this->certificatePath
        );
    }
}
