<?php

namespace App\Crawler\AcspScpcCpf;

use App\Crawler\AcspScpcCpf\Models\AcspScpcCpfModel;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Util;
use Exception;

class AcspScpcCpf extends Spider
{
    private const BASE_URL = 'https://www.bvsnet.com.br/cgi-bin/db2www/';
    private $user;
    private $password;
    private $cpf;
    private $results = [];

    public function start()
    {
        return $this->searchByCpf();
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (!Document::validarCpf($this->param['cpf'])) {
            throw new Exception('Parâmetro Inválido', 1);
        }

        $this->user = $this->auth['usuario'];
        $this->password = $this->auth['senha'];
        $this->cpf = Document::removeMask($this->param['cpf']);
    }

    private function searchByCpf()
    {
        $this->doLogin();
        return $this->results;
    }

    private function doLogin()
    {
        $params = [
            "lk_codig"  => $this->user,
            "lk_senha" => $this->password,
            "lk_width" => '123',
            "lk_consu" => 'SENHA'
        ];

        $response = $this->getResponse(self::BASE_URL . 'netpo001.mbr/senha?', 'POST', $params, [
            'Referer' => self::BASE_URL . 'NETPO101.mbr/loginSI'
        ]);
        $this->whereAmI($response);
    }

    private function whereAmI($result)
    {
        $pattern = '@IDENTIFICACAO.*?DO.*?ASSOCIADO.*?INVALIDA@is';
        if (preg_match($pattern, $result)) {
            throw new Exception("Identificação do Associado Invalida", 1);
        }

        # Caso não ache nenhuma informação.
        $pattern = '@OPERADOR.*?NAO.*?CADASTRADO@is';
        if (preg_match($pattern, $result, $matches)) {
            throw new Exception("Não foi possível autenticar no sistema!", 1);
        }

        $pattern = '@IDENTIFICACAO.*?DO.*?ASSOCIADO.*?INVALIDA@is';
        if (preg_match($pattern, $result)) {
            throw new Exception("ACESSO NEGADO - CODIGO DE SERVICO NAO PERMITIDO PARA ACESSO VIA INTERNET", 1);
        }

        $pattern = '@ACESSO.*?NEGADO.*?OPERADOR.*?NAO.*?DISPONIVEL@is';
        if (preg_match($pattern, $result)) {
            throw new Exception("Acesso Negado. Operador Não Disponivel", 1);
        }

        $pattern = '@CODIGO.*?DE.*?SERVICO.*?}NAO.*?PERMITIDO.*?PARA.*?ACESSO.*?VIA.*?INTERNET@is';
        if (preg_match($pattern, $result)) {
            throw new Exception("CODIGO DE SERVICO NAO PERMITIDO PARA ACESSO VIA INTERNET", 1);
        }

        if (preg_match('#<frame[^>]*?name="bot"[^>]*?SRC=".*?erro\?lk_mensa=(.*?)"[^>]*?>#is', $result, $matches)) {
            throw new Exception(urldecode($matches[1]), 1);
        }

        if (preg_match('#ws_contr"\s*value="(.*?)".*?lk_acess"\s*value="(.*?)".*?</form>#is', $result, $matches)) {
            $lk_access = trim($matches[2]);
            $ws_control = trim($matches[1]);
            $this->getResults($this->cpf, $lk_access, $ws_control);

            return;
        }
    }

    private function getResults($cpf, $access, $control)
    {
        $refererParams = [
            "lk_acess" => $access,
            "lk_codig" => $this->user,
            "ws_contr" => $control,
            "lk_conor" => "COMPLETO",
            "lk_click" => "N"
        ];

        $referer = "NETPO044.mbr/infocada?  " . http_build_query($refererParams);

        $params = [
            "lk_codig" => $this->user,
            "ws_contr" => $control,
            "lk_vslay" => '01',
            "lk_consu" => 'COMPN',
            "lk_vscon" => '01',
            "lk_acess" => $access,
            "ws_menu" => 'infocada',
            "lk_tdoc1" => 'CPF',
            "lk_tcons" => 'N',
            "lk_tresp" => '1',
            "lk_ndoc1" => $cpf,
            "lk_nuddd" => '',
            "lk_nutel" => '',
            "lk_tcred" => 'XX',
            "B1" => ' Aguarde '
        ];

        $response = $this->getResponse(
            self::BASE_URL .
            'NETPO044.mbr/Consulta1?',
            'POST',
            $params,
            ['Referer' => self::BASE_URL . $referer]
        );

        $this->parseResults($response);
    }

    private function parseResults($results)
    {
        $pattern = '@AUTORIZACAO\s*DE\s*ACESSO\s*INIBIDA\s*TEMPORARIAMENTE@is';
        if (preg_match($pattern, $results, $matches)) {
            throw new Exception("AUTORIZACAO DE ACESSO INIBIDA TEMPORARIAMENTE!", 2);
        }

        # Servico fora do ar.
        $pattern = '@SERVICO\s*NAO\s*DISPONIVEL\s*TEMPORARIAMENTE@is';
        if (preg_match($pattern, $results, $matches)) {
            throw new Exception("SERVICO NAO DISPONIVEL TEMPORARIAMENTE, TENTE MAIS TARDE!", 2);
        }

        # Sem permissão
        $pattern = '@CODIGO\s*DE\s*SERVICO\s*NAO\s*AUTORIZADO\s*A\s*ESTA\s*CONSULTA@is';
        if (preg_match($pattern, $results, $matches)) {
            throw new Exception("CODIGO DE SERVICO NAO AUTORIZADO A ESTA CONSULTA!", 2);
        }

        $patterns = [
            'consulta' => ['#CONSULTA:(.*?)NUMERO#mis', null],
            'solicitante' => ['#SOLICITANTE:(.*?)\\n#mis', null],
            'nome' => ['#Nome:(.*?)\\n#mis', null],
            'documento' => ['#DOCUMENTOS:(.*?)\\n#mis', null],
        ];

        $data = Util::parseDados($patterns, $results);
        $acspScpcModel = new AcspScpcCpfModel();

        if (!empty($data)) {
            foreach ($data as $key => $value) {
                $acspScpcModel->$key = $value;
            }
            $acspScpcModel->html = base64_encode(gzcompress($results, 9));
        }

        $this->results = $acspScpcModel;
    }
}
