<?php

namespace App\Crawler\Info4cFullSearch;

use App\Crawler\Spider;
use Exception;

class Info4cFullSearch extends Spider
{
    public function start()
    {
        $result =  $this->searchAll();
        if (empty($result)) {
            throw new Exception("Nenhum Resultado Encontrado", 2);
        };
        return $result;
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['name'])) {
            throw new Exception("Critério de busca inválido.", 6);
        }
    }

    private function searchAll()
    {

        $sources = [
            'pep' => 'Info4cPep',
            'sanction' => 'Info4cSanctionList',
            'watch' => 'Info4cWatchList'
        ];

        foreach ($sources as $key => $value) {
            $params = [
                'retry' => 1,
                'source' => $value,
                'param' => [
                    'name' => $this->param['name'],
                    'limit' => $this->param['limit']
                ]
            ];

            $data = index($params);

            $searchs = $data['data'];
            $sourceArr = ['source' => $value];

            foreach ($searchs as $keySearch => $valueSearch) {
                $result[] = array_merge($valueSearch, $sourceArr);
            }
        }
        return $result;
    }
}
