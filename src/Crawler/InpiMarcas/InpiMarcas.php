<?php

namespace App\Crawler\InpiMarcas;

use App\Crawler\Spider;
use App\Helper\Str;
use App\Helper\Document;
use App\Helper\Util;
use Exception;

/**
 * Classe utilizada no processo de captura de informações de Marcas cadastradas no Inpi
 *
 * @version 2.0
 * <AUTHOR> - 23/07/2018 - Adicionada a busca por nome como critério.
 *
 */
class InpiMarcas extends Spider
{
    // Constantes - Regex

    /**
     * Constante utilizada para verificar se existe algum erro na requisição.
     * Ocorre raramente em casos onde a base de dados do Inpi está instável.
     */
    private const JAVA_SQL_EXCEPTION_RGX = "#Erro:\\s*java.sql.SQLException:\\s*No\\s*connect\\s*permission#is";

    /**
     * Constante utilizada para verificar se existe algum erro na requisição. Raramente acontece.
     */
    private const ORACLE_SQL_EXCEPTION_RGX = "#org\\.apache\\.commons\\.dbcp\\.SQLNestedException#is";

    /**
     * Utilizada para encontrar, na tabela de resultados, os links para a página de processo de uma marca.
     */
    private const BRAND_ENTRY_RGX = '#(?<=<font\sclass="normal">)\s*<a\s*href="(.*?)"\s*class="normal">#is';

    /**
     * Utilizada para encontrar, na tabela de processos de uma marca, os links para a página de
     * informações de uma marca.
     */
    private const BRAND_REGISTRY_RGX = '#<a\s*href=\'(.*?)\'\s*class="visitado">#is';
    // Atributos - URLs

    /**
     * URL utilizada para montar as requisições para as páginas que contém as informações das marcas.
     *
     * @var string
     */
    private $baseURL = 'https://busca.inpi.gov.br';

    /**
     * URL utilizada para obter os cookies da página de login. O login não é necessariamente realizado,
     * porém, é uma requisição obrigatória, sem ela, qualquer requisição dentro do sistema
     * de busca é retornada para esta página.
     *
     * @var string
     */
    private $menuURL = 'https://busca.inpi.gov.br/pePI/servlet/LoginController?action=login';

    /**
     * URL para a página de consulta por titular. Necessita da requisição para a página de
     * login($menuURL) da requisição principal
     *
     * @var string
     */
    private $formURL = 'https://busca.inpi.gov.br/pePI/jsp/marcas/Pesquisa_titular.jsp';

    /**
     * URL que será utilizada para realizar as principais buscas de informações sobre a marca.
     * Necessita que sejam feitas requisições para $menuURL e $formURL para montar os cookies da requisição,
     * caso contrário qualquer outra requisição será redirecionada para a página $menuURL
     *
     * @var string
     */
    private $formPostURL = 'https://busca.inpi.gov.br/pePI/servlet/MarcasServletController';
    /**
     * Atributo principal para ativar a impressão de mensagens no console ao testar a captura
     *
     * @var boolean
     */

    private $type;
    private $criterion;
    private $limit = 1;

    /**
     * Valida e retorna os parâmetros informados pelo usuário que serão utilizados como critérios de busca.
     * - Verifica se o critério de busca é um nome ou documento;
     * - Se for um documento, remove qualquer caractere que não seja numérico;
     *
     * @version 1.1.1
     * <AUTHOR> Andrade - 26/02/2017 - Método para verificar e retornar os
     *                                       critérios de busca passados para a captura;
     * <AUTHOR> Andrade - 23/07/2018 - Adicionada a validação por tipo de critério.
     * <AUTHOR> Andrade - 24/07/2018 - Passado método para o padrão de codificação.
     *
     * @return array
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['nome_cpf_cnpj'])) {
            throw new Exception("Nenhum critério de busca informado!");
        }

        $cpfCnpj = $this->param['nome_cpf_cnpj'];
        $this->type = !Document::validarCpfOuCnpj($cpfCnpj) ? 'name' : 'document';
        if ($this->type == 'document') {
            $cpfCnpj = preg_replace('/[^0-9]/', '', $cpfCnpj);
        }

        $this->criterion = $cpfCnpj;

        if (!empty($this->param['limit'])) {
            $this->limit = $this->param['limit'];
        }
    }

    /**
     * Função que realiza o processo de captura. Ela realiza:
     * - As requisições iniciais;
     * - Valida e retorna os parâmetros informados;
     * - Obtém as marcas; e
     * - Através do resultado das marcas, são obtidas as informações de cada marca e retornadas como resultado;
     *
     * @version 1.2.1
     * <AUTHOR> Andrade - 26/02/2017 - Função ajustada para o padrão de codificação;
     * <AUTHOR> Andrade - 08/08/2018 - Parâmetros obtidos por lista e passados para outros métodos.
     * <AUTHOR> Andrade - 14/08/2018 - Criado os métodos initialRequests e parseAndGetResponseResult
     *                                     para melhorar a visibilidade do método.
     *
     * @return array
     */
    protected function start()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $this->initialRequests();

        $parameters = $this->getRequestParameters();
        $brands = $this->checkResponse($this->getResponse($this->formPostURL, 'POST', $parameters));

        return $this->parseAndGetResponseResult($brands);
    }

    /**
     * Função que realiza o processo de captura de informações das marcas encontradas.
     *
     * @version 1.0.2
     * <AUTHOR> Andrade - 13/08/2018 - Função ajustada para a PSR-2 e removido o parâmetro limitDetail.
     *
     * @param array $brands - Lista de marcas obtidas;
     * @param int $limit - Define a quantidade máxima de marcas que serão obtidas.
     *
     * @return array
     */
    private function parseAndGetResponseResult($brands)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }
        $responses = [];
        foreach ($brands as $brandKey => $brand) {
            try {
                $entries = $this->checkRegistryResponse($this->getResponse($this->baseURL . $brand));
            } catch (Exception $e) {
                //Para tudo caso o site esteja fora do ar
                if ($e->getCode() === 3) {
                    break;
                }

                if ($this->debug) {
                    print PHP_EOL . 'Nada encontrado! Tentando proxima marca.' . PHP_EOL;
                }

                continue;
            }

            foreach ($entries as $entryKey => $entry) {
                $rs = $this->getResponse($this->baseURL . $entry);
                if (preg_match('#Forbidden#is', $rs)) {
                    continue;
                }
                $responses[] = $this->finalParse($rs);
            }

            if ($brandKey >= $this->limit) {
                break;
            }
        }

        return $responses;
    }

    /**
     * Função que executa as primeiras requisições para o Inpi.
     *
     * @version 1.0.0
     * <AUTHOR> Andrade - 02/08/2018 - Função para verificar se as requisições
     *                                     iniciais foram realizadas com sucesso.
     *
     * @return void
     */
    private function initialRequests()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }
        $this->setAlternativeProxy();
        if (!($this->getResponse($this->menuURL) && $this->getResponse($this->formURL))) {
            throw new Exception("Não foi possível comunicar-se com a base de dados do Inpi!");
        }
    }

    /**
     * Valida se existem ocorrências na resposta da requisição.
     *
     * @version 1.0.1
     * <AUTHOR> Andrade - 26/02/2017 - Método para verificar se existem resultados e retorná-los caso exista;
     * <AUTHOR> Andrade - 13/08/2018 - Adicionada a constante BRAND_ENTRY_RGX
     *                                     para melhorar a legibilidade do código.
     *
     * @param string $html - página contendo os resultados da captura.
     *
     * @return array
     */
    public function checkResponse($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (
            preg_match('#Nenhum\s*resultado\s*foi\s*encontrado | ' .
                'Nenhum\s*Nome\s*de\s*Titular\s*foi\s*encontrado#is', $html)
        ) {
            throw new Exception('Nada Encontrado', 2);
        }

        return $this->getLinkDetails(self::BRAND_ENTRY_RGX, $html);
    }

    /**
     * Valida se existem processos relacionados a uma marca.
     *
     * @version 1.0.1
     * <AUTHOR> Andrade - 26/02/2017 - Método para verificar a resposta de um processo de uma marca.
     * <AUTHOR> Andrade - 14/08/2018 - Adicionada constante BRAND_REGISTRY_INDEX
     *                                     para melhorar a legibilidade da função.
     *
     * @param string $html
     *
     * @return array
     */
    public function checkRegistryResponse($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if (preg_match('#Nenhum\s*resultado\s*foi\s*encontrado#is', $html)) {
            throw new Exception('Nada Encontrado', 2);
        }

        return $this->getLinkDetails(self::BRAND_REGISTRY_RGX, $html);
    }

    /**
     * Verifica, utilizando Regex, se o site está fora do ar.
     *
     * @version 1.0.0
     * <AUTHOR> Andrade - 24/07/2018 - Função verifica se o site está fora do ar.
     *
     * @param string $errorPattern - Regex utilizado para validar se o site está funcionando.
     * @param string $html - página contendo os processos de uma marca
     *
     * @return void
     */
    public function checkSiteAvailability($errorPattern, $html = "")
    {
        if (preg_match($errorPattern, $html)) {
            throw new Exception('O site esta fora do ar.', 3);
        }
    }

    /**
     * Utiliza o padrão passado como parâmetro para obter os resultados de uma string passada.
     * Também são feitas validações para verificar se o site não está fora do ar.
     *
     * @version 1.0.2
     * <AUTHOR> Andrade - 26/02/2017 - Função verifica se existem resultados para o critério informado;
     * <AUTHOR> Andrade - 24/07/2018 - Adicionadas as constantes
     *                                     JAVA_SQL_EXCEPTION_RGX e ORACLE_SQL_EXCEPTION_RGX
     *                                     para verificar se houve algum erro interno do Inpi.
     *
     * @param string $errorPattern - Regex utilizado para validar se o site está funcionando.
     * @param string $html - página contendo os processos de uma marca
     *
     * @return array
     */
    public function getLinkDetails($pattern, $html = "")
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }
        if (!preg_match_all($pattern, $html, $links)) {
            $this->checkSiteAvailability(self::JAVA_SQL_EXCEPTION_RGX, $html);

            $this->checkSiteAvailability(self::ORACLE_SQL_EXCEPTION_RGX, $html);

            throw new Exception('Erro ao obter links {' . __METHOD__ . '}', 3);
        }

        return $links[1];
    }

    /**
     * Monta e retorna os dados de uma marca.
     * Também monta os dados das petições e publicações das marcas.
     * No final, obtem a data de atualização e o número da revista.
     *
     * @version 1.0.1
     * <AUTHOR> Andrade 23/07/2018 - Método para obter informações de uma marca;
     * <AUTHOR> Andrade 24/07/2018 - Método passado para o padrão de codificação.
     *
     * @param string $html - página contendo os dados que serão obtidos de uma marca
     *
     * @return array
     */
    private function finalParse($html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $brand = $this->parseBrand($html);

        $divs = Util::queryXPath($html, '//div[@class="accordions"]', true);

        foreach ($divs as $key => $div) {
            if (preg_match('#<font\s*class="titulo">peti.{1,16}<\/font>#is', $div)) {
                $petitions = $this->parsePetitions($div);
            }

            if (preg_match('#<font\s*class="titulo">Publica.{1,16}es<\/font>#is', $div)) {
                $publications = $this->parsePublications($div);
            }
        }

        $patterns = [
            'data_atualizacao' => ['#<font[^>]*?>.*dados\s*atualizados\s*at.{1,8}\s*(.*?)<\/b>#is', null],
            'num_revista' => ['#Revista:\s*<b>\s*(.*?)\s*<\/b>#is', null],
        ];

        $content = self::stripTagsRecursive($this->parseData($patterns, $html));

        $content['Marca'] = $brand;
        $content['Peticoes'] = $petitions;
        $content['Publicacoes'] = $publications;

        return Str::encoding($content);
    }

    /**
     * Monta e retorna os dados das publicações de uma marca.
     * Ao montar a lista de parâmetros, ela executa a função getDispatchDescription
     * para obter a descrição do despacho.
     *
     * @version 1.0.1
     * <AUTHOR> Andrade - 23/07/2018 - Método para obter informações de uma publicação;
     * <AUTHOR> Andrade - 24/07/2018 - Passado o método para de codificação.
     *
     * @param string $html - página contendo os dados da publicação
     *
     * @return array
     */
    private function parsePublications($html)
    {
        $trs = Util::queryXPath($html, '//div[@class="accordion-content"]/table/tbody/tr');

        if (count($trs) == 0) {
            return [];
        }

        $response = [];

        foreach ($trs as $key => $tr) {
            list(
                $rpi,
                $rpiDate,
                $dispatch,
                $dispatchComplement
            ) = Util::queryXPath($tr, '//td[@align][@width]', true);


            $response[] = [
                'rpi' => strip_tags($rpi),
                'data_rpi' => strip_tags($rpiDate),
                'despacho' => $this->getDispatchDescription($dispatch),
                'complemento_do_despacho' => strip_tags($dispatchComplement)
            ];
        }

        return $response;
    }

    /**
     * Monta e retorna os dados do despacho de uma publicação.
     *
     * @version 1.0.1
     * <AUTHOR> Andrade - 26/02/2017 - Método para obter o id e descrição de um despacho;
     * <AUTHOR> Andrade - 24/07/2018 - Passado o método para o padrão de codificação.
     *
     * @param string $html - página contendo os dados do despacho
     *
     * @return array
     */
    private function getDispatchDescription($html)
    {
        $text = Util::queryXPath($html, '//table/tr[2]/td/font');

        list($id, $desc) = preg_split("#<\\/b*>#is", $text[0]);

        return [
            'id' => strip_tags($id),
            'desc' => strip_tags($desc),
        ];
    }

    /**
     * Monta e retorna os dados de pagamento de uma petição.
     * Na página, os dados são mostrados ao clicar no check da coluna pgo da tabela de petições.
     *
     * @version 1.0.1
     * <AUTHOR> Andrade - 26/02/2017 - Método para obter os dados de um pagamento de uma petição;
     * <AUTHOR> Andrade - 24/07/2018 - Passado o método para PSR-2.
     *
     * @param string $html - página contendo os dados do pagamento
     *
     * @return array
     */
    private function getBankInformation($html)
    {
        $text = Util::queryXPath($html, '//table/tr[2]/td/font');

        list($bank, $date, $value) = preg_split("#<br\\/*>#is", $text[0]);

        return [
            'banco' => $bank,
            'data' => $date,
            'valor' => $value
        ];
    }

    /**
     * Monta e retorna os dados de um serviço pago em uma petição.
     * Na página, na tabela de petições, para ver a descrição do serviço
     * basta clicar no id dele na coluna de mesmo nome.
     *
     * @version 1.0.1
     * <AUTHOR> Andrade - 26/02/2017 - Método para obter os dados de um serviço;
     * <AUTHOR> Andrade - 24/07/2018 - Passado método para PSR-2.
     *
     * @param string $html - página contendo a descrição e id do serviço
     *
     * @return array
     */
    private function getServiceDescription($html)
    {
        $text = Util::queryXPath($html, '//table/tr[2]/td/font');

        list($id, $desc) = preg_split("#<\\/b>#is", $text[0]);

        return [
            'id' => strip_tags($id),
            'desc' => strip_tags($desc),
        ];
    }

    /**
     * Monta e retorna os dados de uma petição.
     * A função também obtem os dados do serviço e do
     * pagamento da petição antes de retornar os dados.
     *
     * @version 1.0.1
     * <AUTHOR> Andrade - 26/02/2017 - Método para obter os dados das petições;
     * <AUTHOR> Andrade - 24/07/2018 - Passado método para PSR-2.
     *
     * @param string $html - página contendo as informações da petição
     *
     * @return array
     */
    private function parsePetitions($html)
    {
        $trs = Util::queryXPath($html, '//div[@class="accordion-content"]/table/tbody/tr');

        if (count($trs) == 0) {
            return [];
        }

        $response = [];

        foreach ($trs as $key => $tr) {
            list(
                $pgo,
                $protocol,
                $depositDate,
                $images,
                $service,
                $client,
                $delivery,
                $date
            ) = Util::queryXPath($tr, '//td[@align="center"][@width]', true);


            $response[] = [
                'pgo' => $this->getBankInformation($pgo),
                'protocolo' => strip_tags($protocol),
                'data_deposito' => strip_tags($depositDate),
                'imagens1' => strip_tags($images),
                'servico' => $this->getServiceDescription($service),
                'cliente' => strip_tags($client),
                'delivery' => strip_tags($delivery),
                'data' => strip_tags($date)
            ];
        }

        return $response;
    }

    /**
     * Monta e retorna os dados de uma marca.
     *
     * @version 1.0.1
     * <AUTHOR> Andrade - 26/02/2017 - Método para obter os dados principais da marca;
     * <AUTHOR> Andrade - 24/07/2018 - Passado método para PSR-2.
     *
     * @param string $html - página contendo as informações da petição
     *
     * @return array
     */
    private function parseBrand($html)
    {
        $trs2 = Util::queryXPath($html, '//*[@id="principal"]/table[2]/tr', true);
        $trs3 = Util::queryXPath($html, '//*[@id="principal"]/table[3]/tr', true);
        $trs = array_filter(array_merge($trs2, $trs3));

        $response = [];
        foreach ($trs as $key => $tr) {
            $tr = trim($tr);
            if (!empty($tr)) {
                $tds[$key] = Util::queryXPath($tr, '//td');

                list($desc) = Util::queryXPath($tds[$key][0], '//font');
                unset($tds[$key][0]);

                $response[] = array(
                    'desc' => strip_tags($desc),
                    'conteudo ' => strip_tags(implode(' ', $tds[$key])),
                );
            }
        }

        return $response;
    }

    /**
     * Prepara o array para a requisição de acordo com os parâmetros passados.
     * Caso o tipo seja document a pesquisa será feita com cpf ou cnpj;
     * Caso contrário, a busca será realizada pelo nome.
     *
     * @version 1.1.1
     * <AUTHOR> Andrade - 26/02/2017 - Método que obtém os parâmetros da requisição de busca;
     * <AUTHOR> Andrade - 23/07/2018 - Adicionado a verificação de busca por tipo de critério;
     * <AUTHOR> Andrade - 24/07/2018 - Passado método para o padrão de codificação.
     * <AUTHOR> Andrade - 25/03/2020 - Removido os parâmetros e passado a usar atributos
     *
     * @return array
     */
    private function getRequestParameters()
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        if ($this->type === 'document') {
            $parameters['cpf_cgc_numINPI'] = $this->criterion;
            $parameters['nomeTitular'] = '';
        } else {
            $parameters['nomeTitular'] = $this->criterion;
            $parameters['cpf_cgc_numINPI'] = '';
        }
        $parameters['registerPerPage'] = '20';
        $parameters['botao'] = '+pesquisar+%BB+';
        $parameters['Action'] = 'searchNome';
        $parameters['precisao'] = 'aproximacao';
        $parameters['tipoPesquisa'] = 'BY_CNPJ_NOME';

        return $parameters;
    }

    /**
     * Verifica se o valor passado é uma string:
     * Se for, remove as tags de uma string e a retorna;
     * Caso seja um outro tipo, a função retorna uma chamada a si mesma recursivamente.
     * Os parâmetros de entrada e de retorno podem ser tanto um array como também string.
     *
     * @version 1.0.0
     * <AUTHOR> Andrade - 26/02/2017 - Método recursivo para remover tags de um array de strings.
     *
     * @param mixed $str - Valor de onde serão retiradas as tags.
     *
     * @return mixed
     */
    public static function stripTagsRecursive($str)
    {
        if (!is_array($str) && !is_object($str)) {
            return strip_tags($str);
        }

        return array_map('self::stripTagsRecursive', $str);
    }

    /**
     * Utiliza os padrões informados para obter, utilizando regex, na página passada no segundo parâmetro.
     *
     * @version 1.0.1
     * <AUTHOR> Andrade - 26/02/2017 - Método para obter os dados de um html passados um ou mais padrões;
     * <AUTHOR> Andrade - 24/07/2018 - Passado método para o padrão de codificação.
     *
     * @param array $patterns - Valor de onde serão retiradas as tags.
     * @param string $html - Página de onde serão capturados os dados.
     *
     * @return mixed
     */
    private function parseData($patterns, $html)
    {
        if ($this->debug) {
            print(__METHOD__ . "\n");
        }

        $data = [];
        $aError = [];

        foreach ($patterns as $desc => $aRegExp) {
            foreach ($aRegExp as $sRegExp) {
                if ($sRegExp == null) {
                    $data[$desc] = null;
                    unset($aError[$desc]);
                } elseif (preg_match($sRegExp, $html, $matches)) {
                    $data[$desc] = trim(html_entity_decode($matches[1]));
                    unset($aError[$desc]);
                } else {
                    $aError[$desc] = $desc;
                }
            }
        }

        return $data;
    }
}
