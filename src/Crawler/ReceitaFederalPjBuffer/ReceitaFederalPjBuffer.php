<?php

namespace App\Crawler\ReceitaFederalPjBuffer;

use App\Crawler\ReceitaFederalPjGeneric\Models\ReceitaFederalPjGenericModel;
use App\Crawler\ReceitaFederalPjGeneric\Models\ReceitaFederalPjGenericCnaeSecundarioModel;
use App\Crawler\ReceitaFederalPjGeneric\Models\ReceitaFederalPjGenericQsaModel;
use App\Crawler\GenericInterface;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Util;
use App\Crawler\ReceitaFederalPjGeneric\ReceitaFederalPjTrait;
use Exception;

class ReceitaFederalPjBuffer extends Spider implements GenericInterface
{
    use ReceitaFederalPjTrait;

    private $bufferData;
    private $originalDocument;

    protected function validateAndSetCrawlerAttributes()
    {
        $this->originalDocument = Util::formatAsCNPJ($this->param['documento']);
        $this->param['documento'] = preg_replace('/[^0-9]/isu', '', $this->param['documento']);

        if (!Document::validarCnpj($this->param['documento'])) {
            throw new Exception("Critério de busca inválido.", 6);
        }
    }

    protected function start()
    {
        $this->verifyBuffer();
        if (!empty($this->bufferData) && isset($this->bufferData['cnpj'])) {
            return $this->bufferData;
        }

        throw new Exception("Acionando contingência.", 404);
    }

    public function parseToGeneric(array $data, string $sourceName): ReceitaFederalPjGenericModel
    {
        $model = new ReceitaFederalPjGenericModel();

        $model->source = $sourceName;
        $model->cnpj = $data['cnpj'] ?? '';
        $model->tipo = $data['tipo'] ?? '';
        $model->data_abertura = $data['data_abertura'] ?? '';
        $model->nome_empresarial = '';

        if (isset($data['nome_empresarial'])) {
            $model->nome_empresarial = $data['nome_empresarial'];
        } elseif (isset($data['razao_social'])) {
            $model->nome_empresarial = $data['razao_social'];
        }

        $model->nome_fantasia = $data['nome_fantasia'] ?? '';
        $model->atividade_economica_principal = $data['atividade_economica_principal'] ?? '';
        $model->natureza_juridica = $data['natureza_juridica'] ?? '';
        $model->logradouro = $data['logradouro'] ?? '';
        $model->numero = $data['numero'] ?? '';
        $model->complemento = $data['complemento'] ?? '';
        $model->cep = $data['cep'] ?? '';
        $model->bairro = $data['bairro'] ?? '';
        $model->municipio = $data['municipio'] ?? '';
        $model->uf = $data['uf'] ?? '';
        $model->ente_federativo_responsavel = $data['ente_federativo_responsavel'] ?? '';
        $model->endereco_eletronico = $data['endereco_eletronico'] ?? '';
        $model->telefone = $data['telefone'] ?? '';
        $model->situacao_cadastral = $data['situacao_cadastral'] ?? '';
        $model->data_situacao = $data['data_situacao'] ?? '';
        $model->motivo_situacao = $data['motivo_situacao'] ?? '';
        $model->situacao_especial = $data['situacao_especial'] ?? '';
        $model->data_especial = $data['data_especial'] ?? '';
        $model->data_consulta = $data['data_consulta'] ?? '';
        $model->hora_consulta = $data['hora_consulta'] ?? '';
        $model->cod_atividade = $data['cod_atividade'] ?? '';
        $model->nome_atividade = $data['nome_atividade'] ?? '';
        $model->cod_natureza = $data['cod_natureza'] ?? '';
        $model->nome_natureza = $data['nome_natureza'] ?? '';
        $model->cnpj_uf = $data['cnpj_uf'] ?? '';
        $model->html = $data['html'] ?? '';
        $model->initial_capital = $data['initial_capital'] ?? '';

        if (isset($data['aAtividadeSecundaria']) && is_array($data['aAtividadeSecundaria'])) {
            foreach ($data['aAtividadeSecundaria'] as $cnae) {
                $modelCnae = new ReceitaFederalPjGenericCnaeSecundarioModel();

                $modelCnae->codigo = $cnae['codigo'] ?? '';
                $modelCnae->descricao = $cnae['descricao'] ?? '';
                $modelCnae->atividade_economica_secundaria = $cnae['atividade_economica_secundaria'] ?? '';

                $model->aAtividadeSecundaria[] = $modelCnae;
            }
        }

        if (isset($data['aQsa']) && is_array($data['aQsa'])) {
            foreach ($data['aQsa'] as $qsa) {
                $modelQsa = new ReceitaFederalPjGenericQsaModel();

                $modelQsa->name = $qsa['name'] ?? '';
                $modelQsa->qualification = $qsa['qualification'] ?? '';
                $modelQsa->representante_qualificacao = $qsa['representante_qualificacao'] ?? '';
                $modelQsa->representante_legal = $qsa['representante_legal'] ?? '';
                $modelQsa->pais_origem = $qsa['pais_origem'] ?? '';
                $modelQsa->qsa = $qsa['qsa'] ?? '';

                $model->aQsa[] = $modelQsa;
            }
        }

        return $model;
    }
}
