<?php

namespace App\Crawler\CadastroPf;

class PFResponse
{
    public static function basicData($results)
    {
        if (isset($results->Minor)) {
            $errorFeedBeck = $results->Minor;
        }
        $results = $results->Result[0]->BasicData;

        preg_match('/(.*?)T/', $results->BirthDate, $date);

        $data['dados_pessoais'] = [
            "rg" => null,
            "cpf" => $results->TaxIdNumber,
            "nome" => $results->Name ?? $errorFeedBeck,
            "data_nascimento" => date('d/m/Y', strtotime($date[1])),
            "sexo" => $results->Gender,
            "estado_civil" => self::parseMaritalStatus(
                $results->MaritalStatusData->MaritalStatus
            ),
            "data_obito" => $results->HasObitIndication ? $results->ObitIndicationYear : '',
            "nome_mae" => $results->MotherName,
            "signo" => $results->ZodiacSign,
            "idade" => $results->Age
        ];

        $data['situacao_cadastral'] = [
            "descricao" => $results->TaxIdStatus,
            "data" => date('d/m/Y', strtotime($results->TaxIdStatusDate)),
            "hora" => date('Hmi', strtotime($results->TaxIdStatusDate))
        ];

        $data['hash_receita_federal'] = null;

        return $data;
    }

    public static function demographicData($results)
    {
        $results = $results->Result[0]->DemographicData[0];
        $data['classe_social'] = $results->SocialClass;
        $data['escolaridade'] = $results->EstimatedInstructionLevel;
        return $data;
    }

    public static function phones($results)
    {
        $results = $results->Result[0]->ExtendedPhones;
        if (empty($results->Phones)) {
            $data['telefones'] = [];
            return $data;
        }

        $data['telefones'] = array_map(function ($result) {
            return [
                "tipo" => self::parsePhoneType($result->Type),
                "ddd" => $result->AreaCode,
                "telefone" => $result->Number
            ];
        }, $results->Phones);

        return $data;
    }

    public static function emails($results)
    {
        $results = $results->Result[0]->ExtendedEmails;
        if (empty($results->Emails)) {
            $data['emails'] = [];
            return $data;
        }

        $data['emails'] = array_map(function ($result) {
            return [
                "email" => $result->EmailAddress
            ];
        }, $results->Emails);

        return $data;
    }

    public static function addresses($results)
    {
        $results = $results->Result[0]->ExtendedAddresses;
        if (empty($results->Addresses)) {
            $data['enderecos'] = [];
            return $data;
        }

        $data['enderecos'] = array_map(function ($result) {
            return [
                "numero" => $result->Number,
                "complemento" => $result->Complement,
                "cidade" => $result->City,
                "cep" => $result->ZipCode,
                "bairro" => $result->Neighborhood,
                "uf" => $result->State,
                "logradouro" => $result->AddressMain,
                "longitude" => $result->Longitude,
                "ibge" => null,
                "latitude" => $result->Latitude
            ];
        }, $results->Addresses);

        return $data;
    }

    public static function commonQsa($results)
    {
        $results = $results->data;
        if (empty($results->aSocio)) {
            $data['sociedades'] = [];
            return $data;
        }

        $data['sociedades'] = array_map(function ($result) use ($results) {
            $participacao = ($result->participacao) ? $result->participacao : '0';
            return [
                "ano" => date('Y', strtotime($results->data_consulta)),
                "percentual_participacao" => $participacao . '.00',
                "cnpj" => $result->cpf_cnpj,
                "tipo" => "PF",
                "qualificacao" => $result->qualificacao,
                "razao_social" => $result->nome,
                "data_entrada" => $result->entrada
            ];
        }, $results->aSocio);

        return $data;
    }

    public static function targetData($results)
    {
        $results = $results->result[0]->pessoa;
        $data['dados_pessoais']['rg'] = $results->cadastral->rgNumero;
        $data['profissao'] = $results->socioDemografico->profissao;
        $data['renda_estimada'] = $results->socioDemografico->rendaPresumida;

        return $data;
    }

    private static function parseMaritalStatus($status)
    {
        switch ($status) {
            case 'DIVORCED':
                return "DIVORCIADO(A)";
            break;
            default:
                return $status;
            break;
        }
    }

    private static function parsePhoneType($type)
    {
        switch ($type) {
            case 'MOBILE':
                return "TELEFONE MÓVEL";
            break;
            case 'HOME':
                return "TELEFONE RESIDENCIAL";
            break;
            case 'WORK':
                return "TELEFONE COMERCIAL";
            break;
            default:
                return $type;
            break;
        }
    }
}
