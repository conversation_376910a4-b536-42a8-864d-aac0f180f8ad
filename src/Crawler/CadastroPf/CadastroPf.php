<?php

namespace App\Crawler\CadastroPf;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Util;
use App\Manager\BigDataCorpManager;
use App\Factory\SourceFactory;
use App\Manager\TargetDataManager;
use Exception;

class CadastroPf extends Spider
{
    private $document;
    private $bigDataManager;
    private $targetManager;
    private $data = [];
    private const RETRY_SOURCES = 2;
    private const SOURCES = [
        ['dataset' => 'basic_data', 'parse' => 'basicData', 'type' => 'bigdata'],
        ['dataset' => 'demographic_data', 'parse' => 'demographicData', 'type' => 'bigdata'],
        ['dataset' => 'phones_extended', 'parse' => 'phones', 'type' => 'bigdata'],
        ['dataset' => 'emails_extended', 'parse' => 'emails', 'type' => 'bigdata'],
        ['dataset' => 'addresses_extended', 'parse' => 'addresses', 'type' => 'bigdata'],
        ['dataset' => 'CommonQsa', 'parse' => 'commonQsa', 'type' => 'lambda'],
        ['dataset' => 'cpf', 'parse' => 'targetData', 'type' => 'targetdata']
    ];

    /**
     * @return array
     */
    protected function start()
    {
        $this->bigDataManager = new BigDataCorpManager($this->idUser);
        $this->targetManager = new TargetDataManager();
        $this->runSources();
        return $this->data;
    }

    /**
     * Metodo responsável por percorrer o array de sources, enviar o response
     * para ser parseado de acordo com o app
     *
     * <AUTHOR> Guilherme Sório
     * @return array
     */
    private function runSources()
    {
        foreach (self::SOURCES as $source) {
            try {
                $exception = null;
                $results = $this->getDados($source['dataset'], $source['type']);
                $functionName = $source['parse'];
                $data = PFResponse::{$functionName}($results);

                $this->parseFinalInfo($data);
            } catch (Exception $e) {
                if ($e->getCode() == 6) {
                    $exception = $e;
                    break;
                }
                $exception = $e;
            }
        }

        if (empty($this->data) && $exception !== null) {
            throw new Exception($exception->getMessage(), $exception->getCode());
        }
    }

    /**
     * Efetua o parse para o array final, verifica se a chave existe senão cria
     * caso exista, subscreve com o valor corrente, pois há divirgencia de informacoes
     * nos datasets dos fornecedores
     *
     * <AUTHOR> Guilherme Sório
     * @param array $data
     */
    private function parseFinalInfo($data)
    {
        foreach ($data as $key => $value) {
            if (array_key_exists($key, $this->data)) {
                $this->completeInfos($value, $key);
                continue;
            }

            $this->data[$key] = $value;
        }
    }

    /**
     * Completa as informacoes que não estão presentes em outros datasets
     *
     * <AUTHOR> Guilherme Sório
     * @param array $data
     * @param string $dataKey
     */
    private function completeInfos($data, $dataKey)
    {
        foreach ($data as $key => $value) {
            if (empty($this->data[$dataKey][$key])) {
                $this->data[$dataKey][$key] = $value;
            }
        }
    }

    /**
     * Método responsável por chamar individualmente cada dataset e fornecedor
     *
     * <AUTHOR> Guilherme Sório
     * @param string $dataset
     * @param string $type
     * @return array
     */
    private function getDados($dataset, $type)
    {
        if (empty($type)) {
            throw new Exception('Nenhum tipo de fornecedor setado', 1);
        }

        if ($type == 'bigdata') {
            $class = 'DadosCadastraisPf';
            $result = json_decode($this->bigDataManager->getDataPersonApi($dataset, $this->document, $class));

            if (is_object($this->bigDataManager->checkCriteriaMajority($result))) {
                throw new Exception(
                    $this->bigDataManager->checkCriteriaMajority($result)->Minor,
                    6
                );
            }

            return $result;
        }

        if ($type == 'lambda') {
            $configs = [
                'source' => $dataset,
                'params' => ['cpf_cnpj' => $this->document]
            ];

            return json_decode(json_encode($this->parseSourceToRun($configs)));
        }

        if ($type == 'targetdata') {
            return json_decode(json_encode($this->targetManager->searchByCpf($this->document)));
        }
    }

    /**
     * Cria a classe com base no nome da fonte
     *
     * <AUTHOR> Guilherme Sório - 10/05/2021
     * @param array $sourceConfigs
     * @return array
     */
    private function parseSourceToRun(array $sourceConfigs)
    {
        try {
            return Util::runSource(
                SourceFactory::make($sourceConfigs),
                self::RETRY_SOURCES
            );
        } catch (Exception $e) {
            if ($e->getCode() == 2) {
                return [];
            }
        }
    }

    /**
     * @return object
     * @throws Exception
     */
    protected function validateAndSetCrawlerAttributes()
    {
        $this->document = Document::removeMask($this->param['documento']);

        // Valida o parâmetro documento para verificar se é um CPF
        if (!Document::validarCpf($this->document)) {
            throw new Exception("O documento informado não é um CPF válido.", 1);
        }
    }
}
