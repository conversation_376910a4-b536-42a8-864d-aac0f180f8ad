<?php

namespace App\Crawler\DeclaracaoDeAptidaoPronaf;

use App\Crawler\Spider;
use App\Helper\Document;
use Exception;

/**
 * Classe responsável por capturar os dados da Declaração de aptidão do pronaf
 *
 * <AUTHOR> - 29/10/2019
 */
class DeclaracaoDeAptidaoPronaf extends Spider
{
    private const POST_URL = 'https://smap14.mda.gov.br/extratodap/PesquisarDAP/CarregarExtratoDAP';
    private const POST_URL_CNPJ = 'https://smap14.mda.gov.br/extratodap/PesquisarDAP/ExportarExtrato';
    private const PDF_URL = 'https://smap14.mda.gov.br/extratodap/PesquisarDAP/Visualizar?Token=';

    private $document = '';
    private $type = '';

    protected function validateAndSetCrawlerAttributes()
    {
        $this->document = $this->param['documento'];
        $this->document = Document::removeMask($this->document);

        if (!Document::validarCpfOuCnpj($this->document)) {
            throw new Exception('Documento inválido para pesquisa!', 3);
        }

        if (Document::validarCpf($this->document)) {
            $this->type = 'cpf';
            return false;
        }

        if (Document::validarCnpj($this->document)) {
            $this->type = 'cnpj';
            return false;
        }
    }

    protected function start()
    {
        $this->setAlternativeProxy();

        if ($this->type == 'cpf') {
            return $this->getDadosWithCPF($this->document);
        }

        if ($this->type == 'cnpj') {
            return $this->getDadoswithCnpj($this->document);
        }
    }

    /**
     * Função que captura os dados de acordo com o resultado,
     *  realiza o POST e formata as variaveis de acordo parse (CNPJ)
     * <AUTHOR> Alves
     * Data: 29/10/2019
     * @param $documento
     *
     */
    private function getDadosWithCnpj($cnpj)
    {
        $params = [
            'chave' => '',
            'cpf' => $cnpj,
            'numeroDAP' => '',
            'TipoConsulta' => 'Juridica'
        ];

        $response = $this->getResponse(self::POST_URL_CNPJ, 'POST', $params);
        $response = json_decode($response);
        $this->checkResponse($response);
        $html = $response->{'HTML'};

        preg_match('/font-size:11px;"> <strong>DAP: <\/strong>(.*?)<\/td>/m', $html, $numeroDAP);
        preg_match('/<strong>Razão Social: <\/strong>(.*?)<\/td>/m', $html, $razaoSocial);
        preg_match('/<strong>Emissão: <\/strong>(.*?)<\/td>/m', $html, $dataEmissao);
        preg_match('/<strong>Validade.*?: <\/strong>(.*?)<\/td>/m', $html, $dataValidade);
        preg_match('/<strong>Emissor: <\/strong>(.*?)<\/td>/m', $html, $emissorDap);
        preg_match('/<strong>CNPJ: <\/strong>(.*?)<\/td>/m', $html, $cnpjEmissor);
        preg_match('/Agente Emissor: <\/strong>(.*?)<\/td>/m', $html, $agenteEmissor);
        preg_match('/<strong>CPF: <\/strong>(.*?)<\/td>/m', $html, $cpf);

        $pdfLink = "cpf={$cnpj}&numeroDAP=&usuario=&chave=&tipo=Juridica";
        $pdfLink = base64_encode($pdfLink);
        $pdf = self::PDF_URL . $pdfLink;

        $data = [
            'numeroDAP' => $numeroDAP[1],
            'dataEmissao' => $dataEmissao[1],
            'validade' => $dataValidade[1],
            'emissorDap' => [
                'emissor' => $emissorDap[1],
                'cnpj' => $cnpjEmissor[1],
                'nome' => $agenteEmissor[1],
                'cpf' => $cpf[1]
            ],
            'pdf' => $pdf

        ];
        return $data;
    }

    /**
     * Função que captura os dados de acordo com o resultado,
     * realiza o POST e formata as variaveis de acordo parse (CPF)
     * <AUTHOR> Alves
     * Data: 29/10/2019
     * @param $documento
     *
     */
    private function getDadosWithCPF($documento)
    {
        $params = [
            'cpf' => $documento,
            'numeroControleExterno' => ''
        ];

        $response = $this->getResponse(self::POST_URL, 'POST', $params);
        preg_match('/{"Chave":"(.*?)","DAP"/m', $response, $chave);
        $response = json_decode($response);
        $pdfLink = $this->getPdfLink($chave[1]);
        $this->checkResponse($response);
        $response = $response->{'DAP'};
        $dataEmissao = $this->formatAndReturndateJson($response[0]->{'dataEmissao'});
        $dataValidade = $this->formatAndReturndateJson($response[0]->{'validade'});
        $ultimaVersao = $this->checkTrueOrFalse($response[0]->{'FLGUltimaVersao'});
        $dapValida = $this->checkTrueOrFalse($response[0]->{'FLGDAValida'});
        $dapExpirada = $this->checkTrueOrFalse($response[0]->{'FLGDAExpirada'});
        $categoriaDap = $response[0]->{'CategoriaDAP'};
        $usoDaTerra = $response[0]->{'UsoDaTerraDAP'};

        $data = [
            'tipoBusca' => 'cpf',
            'numeroDAP' => $response[0]->{'numeroControleExterno'},
            'dataEmissao' => $dataEmissao,
            'validade' => $dataValidade,
            'MunicipioUF' => $response[0]->{'MunicipioUF'},
            'enquadramento' => $response[0]->{'enquadramento'},
            'versao' => $response[0]->{'versao'},
            'ultimaVersao' => $ultimaVersao,
            'dapValida' => $dapValida,
            'dapExpirada' => $dapExpirada,
            'titulares' => [
                'titular1' => [
                    'nome' => (trim($response[0]->{'Titular1DAP'}->{'Nome'})),
                    'cpf' => $response[0]->{'Titular1DAP'}->{'Cpf'}
                ],
                'titular2' => [
                    'nome' => (trim($response[0]->{'Titular2DAP'}->{'Nome'})),
                    'cpf' => $response[0]->{'Titular2DAP'}->{'Cpf'}
                ],
            ],
            'categoria' => $categoriaDap[0]->{'DescricaoDAPCategoria'},
            'usoDaTerra' => trim($usoDaTerra[0]->{'UsoDaTerra'}),
            'emissorDap' => [
                'emissor' => trim($response[0]->{'EmissorDAP'}->{'RazaoSocial'}),
                'cnpj' => $response[0]->{'EmissorDAP'}->{'CNPJ'},
                'nome' => trim($response[0]->{'EmissorDAP'}->{'NomeRepresentante'}),
                'cpf' => $response[0]->{'EmissorDAP'}->{'CPFRepresentante'}
            ],
            'pdf' => $pdfLink,
        ];

        return $data;
    }

    /**
     * Função que captura o link do pdf
     * <AUTHOR> Alves
     * Data: 29/10/2019
     * @param $$data
     */
    private function getPdfLink($chave)
    {
        $linkDecode = "cpf=null&numeroDAP=null&usuario&chave=${chave}&tipo=Fisica";
        $linkEncode = base64_encode($linkDecode);

        return self::PDF_URL . $linkEncode;
    }

    /**
     * Método que verifica se uma váriavel é 1 ou 0, assim trnsforma true para str SIM e false para str NÃO
     * <AUTHOR> Alves
     * Data: 29/10/2019
     * @param $data
     */
    private function checkTrueOrFalse($data)
    {
        return ($data == 1) ? 'SIM' : "NÃO";
    }

    /**
     * A data que retorna do Json é do parâmetro objeto, esse método transforma a data para uma maneira legível
     * <AUTHOR> Alves
     * Data: 29/10/2019
     * @param $jsonDate
     *
     */
    private function formatAndReturndateJson($jsonDate)
    {
        preg_match('/\d{10}/m', $jsonDate, $date);

        return date('d/m/Y', $date[0]);
    }


    /**
     * Verifica se a resposta foi uma mensagem de nada encontrado.
     * <AUTHOR> Alves
     * Data: 29/10/2019
     *
     * <AUTHOR> Medeiros - 26/05/2021 - Adicionando um preg para caso não haja resultado na busca com CNPJ
     * @param $response
     */
    private function checkResponse($response)
    {
        if (preg_match('/Nenhum dado encontrado para a pesquisa/m', $response->{'DescMensagem'})) {
            throw new Exception('Nenhum registro encontrado', 2);
        }

        if (preg_match('/O CNPJ \d+ n\W*o possui DAP REGISTRADA no sistema/mi', $response->{'Mensagem'})) {
            throw new Exception('Nenhum registro encontrado', 2);
        }
    }
}
