<?php

namespace App\Crawler\DiariosOficiais;

use App\Crawler\Spider;
use App\Manager\ElasticsearchManager;
use Exception;

class DiariosOficiais extends Spider
{
    private const SEARCH_ON_ESCAVADOR = true;
    private $pageEscavador = 1;

    private $es_index   = "upjuris_diarios";
    private $es_type    = "diario";

    private $limite_default = 60;

    protected function validateAndSetCrawlerAttributes()
    {
        if (!isset($this->param['termo']) || empty($this->param['termo'])) {
            throw new Exception("Termo de busca é obrigatorio", 1);
        }

        if (!isset($this->param['pdf'])) {
            $this->param['pdf'] = true;
        }

        if (empty($this->param['fromApp'])) {
            $this->param['fromApp'] = 'nao-informado';
        }

        if (empty($this->param['keywords'])) {
            $this->param['keywords'] = '';
        }

        if (empty($this->param['page'])) {
            $this->param['page'] = 1;
        }

        if (empty($this->param['limite']) || $this->param['limite'] > $this->limite_default) {
            $this->param['limite'] = $this->limite_default;
        }
    }

    /**
     *  Faz a busca no Elastic Search
     */
    protected function start()
    {
        if (self::SEARCH_ON_ESCAVADOR) {
            return $this->searchOnEscavador();
        }

        $query = $this->createQuery();

        return $this->parseResult(
            (new ElasticsearchManager())->search($query)
        );
    }

    /**
     * Cria a query do elasticsearch
     */
    public function createQuery()
    {
        $params = [];
        $params['index'] = $this->es_index;
        $params['type'] = $this->es_type;

        $params['body']['track_scores'] = true;

        // Configura limite
        $params['body']['size'] = $this->param['limite'];

        // Configura o termo de busca
        $params['body']['query']['bool']['must'][] = array(
            'query_string'  =>  array(
                'query' =>  '"' . $this->param['termo'] . '"',
                'default_field' => 'texto',
            )
        );

        // Configura a paginação
        if (isset($this->param['pagina']) && !empty($this->param['pagina'])) {
            $params['body']['from'] = (($this->param['pagina'] - 1) * $this->param['limite']) + 1;
        }

        // Configura filtro por processo
        if (isset($this->param['processo']) && !empty($this->param['processo'])) {
            $processo = $this->param['processo'];

            $params['body']['query']['bool']['must'][] = array(
                'query_string'  =>  array(
                    'query' =>  '"' . $this->formatarTermo($processo) . '"',
                    'default_field' => 'processo',
                    'phrase_slop' => 0,
                    'minimum_should_match' => '100%'
                ),

            );
        }

        // Configura filtro por datas
        $data = [];
        if (isset($this->param['data_inicio']) && !empty($this->param['data_inicio'])) {
            $data_inicio = $this->param['data_inicio'];
            if (!is_integer($data_inicio)) {
                $data_inicio = date('d/m/Y', strtotime($data_inicio));
                $data['gte'] = $data_inicio;
            }
        }
        if (isset($this->param['data_fim']) && !empty($this->param['data_fim'])) {
            $data_fim = $this->param['data_fim'];
            if (!is_integer($data_fim)) {
                $data_fim = date('d/m/Y', strtotime($data_fim));
                $data['lte'] = $data_fim;
            }
        }

        if (count($data) > 0) {
            $params['body']['query']['bool']['filter'][] = array(
                'range' =>  array(
                    'data'  =>  $data,
                )
            );
        }

        // Confgiura filtro por caderno e regiao
        $regiao = '';
        $caderno = '';
        if (isset($this->param['regiao']) && !empty($this->param['regiao'])) {
            $regiao = $this->param['regiao'];
        }
        if (isset($this->param['caderno']) && !empty($this->param['caderno'])) {
            $caderno = $this->param['caderno'];
        }
        if (!empty($regiao) || !empty($caderno)) {
            $regiaoCaderno = '';
            $regiaoCaderno .= !empty($regiao) ? $regiao . '' : '*';
            $regiaoCaderno .= $caderno ??  '*';

            $params['body']['query']['bool']['must'][] = array(
                'query_string' => array(
                    'query' => $regiaoCaderno,
                    'default_field' => 'caderno',
                    'default_operator' => 'AND',
                    'minimum_should_match' => '100%'
                )
            );
        }

        // Configura o highlight (corte do texto onde há ocorrencia)
        $params['body']['highlight']['fields'][] = ['texto' => [
            'fragment_size'         => 500,
            'number_of_fragments'   => 1
        ]];

        // Ordenação
        if (isset($this->param['ordenacao']) && !empty($this->param['ordenacao'])) {
            $ordem = (substr($this->param['ordenacao'], 0, 1) == '-') ? 'desc' : 'asc';
            $campo = trim(str_replace(['-', '+'], '', $this->param['ordenacao']));
            $params['body']['sort'][$campo]['order'] = $ordem;
        }

        return $params;
    }

    public function parseResult($result)
    {
        $response = [];

        $response['search_time']    = $result['took'];
        $response['total_docs']     = $result['hits']['total'];
        $response['total_pagina']   = count($result['hits']['hits']);
        $response['max_result']     = $this->param['limite'] * $this->param['pagina'];
        $response['resultados']     = [];

        foreach ($result['hits']['hits'] as $k => $v) {
            $response['resultados'][] = [
                'pagina'        => $v['_source']['pagina'],
                'posicao'       => ($this->param['limite'] * $this->param['pagina']) + ($k + 1),
                'relevancia'    => $v['_score'],
                'caderno'       => $v['_source']['caderno'],
                'data'          => $v['_source']['data'],
                'filename'      => $v['_source']['filename'],
                'texto'         => $v['highlight']['texto'][0],
            ];
        }

        return $response;
    }

    /**
     * @param string $termo
     * @return string
     */
    private function formatarTermo($termo)
    {
        $termo = trim($termo);

        $termo = preg_replace('!\s+!', ' ', $termo);

        $termo = $this->escapeElasticSearchTermo($termo);

        return $termo;
    }

    /**
     * @param string $termo
     * @return string
     */
    private function escapeElasticSearchTermo($termo)
    {
        $reservedChars = array(
            '\\', '+', '-', '=', '&&', '||', '!', '(', ')', '{', '}', '[', ']', '^',
            '"', '~', '*', '?', ':',  '/'
        );
        $prohibitedChars = array('>', '<');

        foreach ($prohibitedChars as $char) {
            $termo = str_replace($char, '', $termo);
        }
        foreach ($reservedChars as $char) {
            $termo = str_replace($char, '\\' . $char, $termo);
        }
        return $termo;
    }

    /**
     * Realiza a busca no fornecedor (escavador)
     *
     * @param array $items
     * @return void
     */
    private function searchOnEscavador($items = [])
    {
        $books = $this->checkBooks();
        $params = [
            'query' => $this->param['termo'],
            'pages' => [
                0 => $this->param['page']
            ],
            'book' => empty($books) && $this->param['cadernos'] ? explode(',', $this->param['cadernos']) : [],
            'names' => !empty($books) ? $books : [],
            'keywords' => $this->param['keywords'] ? explode(',', $this->param['keywords']) : [],
            'fromApp' => $this->param['fromApp'],
            'returnContent' => true
        ];

        if (empty($books)) {
            unset($params['names']);
        }

        $params = json_encode([
            'retry' => 1,
            'source' => 'UpSearchBooksAsync',
            'param' => $params
        ]);

        $curl = curl_init(LAMBDA_LUMEN . LAMBDA_UPSEARCH_SOURCES);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($curl, CURLOPT_POSTFIELDS, $params);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'Content-Length: ' . strlen($params),
        ));
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 0);
        curl_setopt($curl, CURLOPT_TIMEOUT, 800);
        $data = null;
        $retry = 3;

        do {
            //echo "Tentando pegar dados do escavador, tentativa: $retry \n";
            $curlResponse = curl_exec($curl);
            $curlResponse = json_decode($curlResponse, true);
            $data = $curlResponse['body'] ?? null;
            $retry--;
        } while ($retry != 0 && empty($data));

        if (empty($data)) {
            throw new Exception("Erro ao buscar os dados: " . $curlResponse['message']);
        }

        if (!empty($data['items'])) {
            $items = array_merge($items, $data['items']);
        }

        if (!empty($data['paginator'])) {
            if (count($items) < $this->param['limite'] && $data['paginator']['total'] > count($items)) {
                $this->pageEscavador++;
                return $this->searchOnEscavador($items);
            }

            $items = array_slice($items, 0, $this->param['limite']);
        }

        $formatedResponse = [];

        foreach ($items as $item) {
            $escavadorObj = new EscavadorObj($item, $this->param);
            $formatedResponse[] = (new DiarioOficialObj())->fromEscavador($escavadorObj);
        }

        return [
            'search_time' => time() - $this->initialTime,
            'total_docs' => count($formatedResponse),
            'total_pagina' => $data['paginator']['total_pages'],
            'pagina_atual' => $this->param['page'],
            'max_result' => $data['paginator']['total'],
            'resultados' => $formatedResponse
        ];
    }

    public function checkBooks()
    {
        $books = explode(',', $this->param['cadernos']);

        if (is_numeric($books[0])) {
            return [];
        }

        $books_file = json_decode(file_get_contents(__DIR__ . '/books.json'), true);

        $filters = [];
        foreach ($books as $book) {
            $book = str_replace("_", "", $book);
            $filters[] = $books_file[$book][0];
        }

        return $filters;
    }
}
