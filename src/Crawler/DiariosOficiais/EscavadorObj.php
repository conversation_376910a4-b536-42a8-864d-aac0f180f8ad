<?php

namespace App\Crawler\DiariosOficiais;

use App\Crawler\Spider;
use App\Helper\Pdf;
use App\Manager\RequestManager;
use App\Manager\S3\S3;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use Exception;
use GuzzleHttp\Client;

class EscavadorObj extends Spider
{
    public const GET_PDF_LINK = LAMBDA_LUMEN . 'upsearch-sources-dev-search_pdf_escavador';
    public const UPSEARCH_PDF_LINK = UPSEARCH . '/pagina-pdf/{token}';
    public const RETRY = 10;
    private const UPSEARCH_S3_PATH = 'upsearch/';
    private const UPSEARCH_S3_URL = S3_STATIC_URL . self::UPSEARCH_S3_PATH;
    public $diario_id;
    public $numero_pagina;
    public $diario_sigla;
    public $diario_nome;
    public $diario_data;
    public $texto;
    public $diario_edicao;
    public $link_pdf;
    public $link_api;
    public $estado;
    public $link_escavador;
    public $fromApp;

    protected function start()
    {
    }
    protected function validateAndSetCrawlerAttributes()
    {
    }

    public function __construct(array $array, array $params)
    {
        parent::__construct([], []);
        ini_set("pcre.backtrack_limit", "-1");
        $this->debug = false;
        $mustHave = [
            'diario_id',
            'numero_pagina',
            'diario_data',
            'texto'
        ];
        foreach ($mustHave as $has) {
            if (empty($array[$has])) {
                throw new Exception('Variável obrigatória vazia');
            }
        }
        $this->diario_id = $array['diario_id'];
        $this->numero_pagina = $array['numero_pagina'];
        $this->diario_data = date('Y-m-d', strtotime($array['diario_data']));
        $this->texto = $array['texto'];
        $this->diario_sigla = $array['diario_sigla'] ?? null;
        $this->diario_nome = $array['diario_nome'] ?? null;
        $this->diario_edicao = $array['diario_edicao'] ?? null;
        $this->estado = $array['estado'] ?? null;
        $this->fromApp = $params['fromApp'];
        $parametros = (object) [
            'id' => $this->diario_id,
            'page' => $this->numero_pagina,
            'fromApp' => $this->fromApp,
        ];
        $this->link_pdf = $params['pdf'] ? $this->getS3Url($parametros) : null;
        $this->link_api = $array['link_api'];
        $termo = $params['termo'];

        $treatedText = strtoupper($this->prepareString($termo));

        $this->texto = preg_replace(
            "@($termo|$treatedText)@i",
            "<strong>$termo</strong>",
            $this->texto
        );

        if (isset($params['paga']) && $params['paga'] == false) {
            if (empty($array['link'])) {
                throw new Exception('Variável obrigatória vazia (link)');
            }
            $this->link_escavador = $array['link'];

            // Estou mudando o metodo pois o html do site
            // está vindo com o processo mascarado (ex 002XXXX-18.2018)
            // O html que vem da API está completo
            $this->link_pdf = $this->generatePdfFromApi($termo);

            // $this->link_pdf = $this->generatePdfFromLink($termo);
        }
    }


    private function getPDFsPage($params)
    {
        $url = LAMBDA_LUMEN . LAMBDA_UPSEARCH_SOURCES_PDF;
        $headers = [
            'Content-Type: application/json',
            'Access-Control-Allow-Origin: *',
        ];

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        if ($httpCode == 200) {
            $responseData = json_decode($response, true);
            return empty($responseData['data']['url']) ? null : $this->removeBaseUrlS3($responseData['data']['url']);
        } else {
            // Handle the error case, e.g., log the error or return null
            return null;
        }
    }

    private function removeBaseUrlS3($url)
    {
        $baseUrlS3 = 's3' . AWS_S3_REGION . 'amazonaws.com/';
        return str_replace($baseUrlS3, '', $url);
    }

    private function getParams($params): array
    {
        $pdfParams = [
            'id' => $params->id,
            'page' => null,
            'fromApp' => $params->fromApp === 'upsearch' ? 'upSearch' : $params->fromApp,
        ];

        return [
            'retry' => 1,
            'source' => 'UpSearchPdf',
            'param' => $pdfParams,
        ];
    }


    private function getS3Url($params)
    {

        stream_context_set_default([
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
            ],
        ]);

        $pdfParams = $this->getParams($params);
        $pdfParams['param']['page'] = $params->page;
        $fileName = "upSearch-{$params->id}-{$params->page}.pdf";

        $mergedPDFUrl = self::UPSEARCH_S3_URL . $fileName;
        // verifica se o PDF já foi gerado antes.
        $MainPDFMerged = (new S3(new StaticUplexisBucket()))->get(self::UPSEARCH_S3_PATH . $fileName);
        if ($MainPDFMerged) {
            return $mergedPDFUrl;
        }

        // recupera a pagina do S3 ou faz download dela.
        $mainPageInfo = $this->getPDFsPage($pdfParams);

        if (empty($mainPageInfo)) {
            //caso a pagina nao tem opcao de dowload
            return false;
        }
        $mainPageName = '/tmp/page-' . $params->page . '.pdf';
        file_put_contents($mainPageName, file_get_contents($mainPageInfo));

        $previousPageName = [];
        $nextPageName = [];

        // pegando pagina anterior da consulta atual
        // e validando se a pagina anterior existe
        $pdfParams['param']['page'] = $params->page - 1;
        $prevPageInfo = $this->getPDFsPage($pdfParams);
        if (!empty($prevPageInfo)) {
            $previousPageName = '/tmp/page-' . $pdfParams['param']['page'] . '.pdf';
            file_put_contents($previousPageName, file_get_contents($prevPageInfo));
            unset($pdfParams['param']['page']);
        }

        // pegando proxima pagina da consulta atual
        // e validando se a pagina seguinte existe
        $pdfParams['param']['page'] = $params->page + 1;
        $nextPageInfo = $this->getPDFsPage($pdfParams);
        if (!empty($nextPageInfo)) {
            $nextPageName = '/tmp/page-' . $pdfParams['param']['page'] . '.pdf';
            file_put_contents($nextPageName, file_get_contents($nextPageInfo));
        }

        $allPDFs = array_filter([
            'filePreviousPath' => $previousPageName,
            'fileAtualPath' => $mainPageName,
            'fileNextPath' => $nextPageName,
        ]);
        $tmpPdf =  (new Pdf())->mergePdfs($allPDFs, '/tmp/' . $fileName);
        (new S3(new StaticUplexisBucket()))->saveWithFileName(self::UPSEARCH_S3_PATH . $fileName, $tmpPdf, $fileName);
        unlink($tmpPdf);

        return $mergedPDFUrl;
    }


    public function generatePdfFromApi($termo)
    {
        $dirtyContent = $this->getContentFromApi();
        $content = preg_replace('/\n/', '', $dirtyContent);

        $patternTerm = $this->prepareString($termo, false);
        preg_match("@$patternTerm@isu", $content, $termMatch, PREG_OFFSET_CAPTURE);
        if (!isset($termMatch[0][1]) || !is_numeric($termMatch[0][1])) {
            $termMatch[0] = [1 => 0];
        }

        $content = preg_replace("@$patternTerm@isu", '<strong>' . strtoupper($termo) . '</strong>', $content);

        $pdfPath = '/tmp/pdfdo_' . uniqid() . '.pdf';
        (new Pdf())->saveHtmlToPdf(utf8_decode($content), $pdfPath);

        $prefix = "upSearch-{$this->diario_id}-{$this->numero_pagina}";
        $retry = 3;
        $url = '';
        $responseS3 = [];
        $pdfContent = file_get_contents($pdfPath);
        do {
            $responseS3 = $this->downloadAndGetFilePath($pdfContent, $prefix, 'pdf', 'diarioOficial');
            if (!empty($responseS3['pdf'])) {
                $url = 'https://' .  $responseS3['pdf'];
            }
            $retry--;
        } while ($url == '' && $retry != 0);
        if (empty($url)) {
            $msgErr = !empty($responseS3['msg']) ? $responseS3['msg'] : 'Não foi possível salvar o arquivo no s3';
            throw new Exception($msgErr);
        }
        return $url;
    }

    private function getContentFromApi()
    {

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => $this->link_api,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImp0aSI6Im' .
                    'VjOTU5MzYyZTMyMWE5YTMyZDc4YzA2MjU1OTFkN2M4MWVkOTE5ZjdjMzk0NGZjN2M1ZT' .
                    'Y0ZGJkZWExZWJkNTRlYmFhMGFhODIwOTJiYjczIn0.eyJhdWQiOiIzIiwianRpIjoiZW' .
                    'M5NTkzNjJlMzIxYTlhMzJkNzhjMDYyNTU5MWQ3YzgxZWQ5MTlmN2MzOTQ0ZmM3YzVlNj' .
                    'RkYmRlYTFlYmQ1NGViYWEwYWE4MjA5MmJiNzMiLCJpYXQiOjE2MDU1Mjg2NzUsIm5iZi' .
                    'I6MTYwNTUyODY3NSwiZXhwIjoxOTIxMDYxNDc1LCJzdWIiOiI3NTUzODYiLCJzY29wZX' .
                    'MiOlsidXNlcl9hcGkiXX0.QEDVV3Z5vI4Hpm--K4QHDIaY1XcjNg0EsVqnNwJv1JmmSh' .
                    'b5oz19X7Ce0bH2cd2UU6dd7oFcHd59Xt52EGVichRCbklUmNJf1bF5MyfYKuVu0OeHKY' .
                    'iCzPW5-8QcPC5srmG6jj6Ep3JtC4247zakrL_YMKNitZ1KNjDDGdd5dPGZrVtgbK2m8Z' .
                    '0E9dhMWL2bH1Y5tQ3U2LEqi19cHdNeXWlyEm7LyC4BEHXmgG8YowVqTyem6dacRq0mTt' .
                    'IxXOrCLsfJ0-JaAFptJZvm5FyzJ3pL1kOaKd8NfnYKDiK8EkVOTFFbWeaTCmXik3o9PQ' .
                    '9GKkRu0GXyPa420vZOGpFZeA2W-zit3AgUb9wZRG6RaCPGUOCQYkj7NMXeJvCZNoX46B' .
                    'NB4HsguJ-85ydQM2EpMZ8GycgWTNHID1CflgEHDZfz4mqrp8C-dHguFqxjYD9IzI61ij' .
                    'm6w4UZwtwFOqmDw7HE06Prpek1ycxKms9GBIz5fUMUv_-N14W7DYp68zWry7ZUeymdk1' .
                    'PXx87mja9OBjp6AWvJNB24RDgWGd1_ncqDWtyhxcOwEu24njnie7PzJ0_VKOkGvooDXZ' .
                    'baNpYoZ4wnf7_WuuMHZfHNgYfoPC-_jv9Dt1769qqUyzwPBNxlO4HulIioMwm3ZB-08l' .
                    'RUw0lE92zmWg0lC2b3tli58zA',
                'Cookie: escavador_session=LdSBQpbF1v95p2P3MBPH7D0heCv918MxxpRt8qkl;' .
                    ' escavador_session=LdSBQpbF1v95p2P3MBPH7D0heCv918MxxpRt8qkl'
            ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response, true);

        curl_close($curl);
        return strip_tags($response["paginas"]["items"]["conteudo"]);
    }


    public function prepareString($str, $full = true)
    {
        $str = preg_replace('/[aáàãâä]/ui', '(a|á|à|ã|â|ä)', $str);
        $str = preg_replace('/[eéèêë]/ui', '(e|é|è|ê|ë)', $str);
        $str = preg_replace('/[iíìîï]/ui', '(i|í|ì|î|ï)', $str);
        $str = preg_replace('/[oóòõôö]/ui', '(o|ó|ò|õ|ô|ö)', $str);
        $str = preg_replace('/[uúùûü]/ui', '(u|ú|ù|û|ü)', $str);
        $str = preg_replace('/[cç]/ui', '(c|ç)', $str);
        // $str = preg_replace('/[,(),;:|!"#$%&/=?~^><ªº-]/', '_', $str);
        if ($full) {
            $str = preg_replace('/[^a-z0-9]/i', '_', $str);
            $str = preg_replace('/_+/', '_', $str);
        }
        return $str;
    }

    private function getPdfLink()
    {
        $token = "upSearch-{$this->diario_id}-{$this->numero_pagina}-{$this->fromApp}";
        return str_replace('{token}', $token, self::UPSEARCH_PDF_LINK);
    }

    private function generatePdfFromLink($termo)
    {
        // Pega a página atráves do link
        $content = '';
        $patt = '/\<div\s?class=\"conteudoMonitoravel.*>(.*)<\/div>\n<\/section>\n<div>/isU';
        $match = [];
        $retries = 1;
        sleep(1);
        do {
            try {
                $this->setAlternativeProxy();

                $exception = null;
                $content = $this->getResponse($this->link_escavador);

                preg_match($patt, $content, $match);
                if (empty($match[1])) {
                    if ($this->checkIfPageHasNotBeenFound($content)) {
                        return '';
                    }

                    throw new Exception('Não foi possível recuperar o PDF da página', 3);
                }
                break;
            } catch (Exception $e) {
                $exception = $e;

                // $retries > 1 ? $this->setAlternativeProxy() : $this->setProxy();

                // // Se continuar a dar erro, vai tentar sem proxy
                // $retries == 4 ? $this->removeProxy() : '';

                echo "Refazendo a requisição... $retries de " . self::RETRY . PHP_EOL;
                $retries++;
            }
        } while ($retries <= self::RETRY);

        if ($exception) {
            return [];
        }

        // Captura apenas o html do conteúdo da página
        if (empty($match[1])) {
            throw new Exception('Não foi possível recuperar o PDF da página - match');
        }
        $spaceString = str_replace('<', ' <', $match[1]);
        $doubleSpace = strip_tags($spaceString);
        $content = str_replace('  ', ' ', $doubleSpace);

        // Identifica onde tá o termo
        $patternTerm = $this->prepareString($termo, false);
        preg_match("@$patternTerm@isu", $content, $termMatch, PREG_OFFSET_CAPTURE);
        if (!isset($termMatch[0][1]) || !is_numeric($termMatch[0][1])) {
            $termMatch[0] = [1 => 0];
            // throw new Exception('Não foi possível recuperar o PDF da página - posição');
        }

        // Faz um recorte com 1500 caracteres para trás e pra frente a partir do primeiro caracter do termo
        // $pos = $termMatch[0][1];
        // $start = ($pos - 1500) <= 0 ? 0 : $pos - 1500;
        // $content = substr($content, $start, 3000);
        $content = preg_replace("@$patternTerm@isu", '<strong>' . strtoupper($termo) . '</strong>', $content);

        // Gera o pdf
        $pdfPath = '/tmp/pdfdo_' . uniqid() . '.pdf';
        (new Pdf())->saveHtmlToPdf(utf8_decode($content), $pdfPath);

        // Salva e retorna o link do s3
        $prefix = "upSearch-{$this->diario_id}-{$this->numero_pagina}";
        $retry = 3;
        $url = '';
        $responseS3 = [];
        $pdfContent = file_get_contents($pdfPath);
        do {
            //echo "\nTentando salvar no s3, tentativa: $retry\n";
            $responseS3 = $this->downloadAndGetFilePath($pdfContent, $prefix, 'pdf', 'diarioOficial');
            if (!empty($responseS3['pdf'])) {
                $url = 'https://' .  $responseS3['pdf'];
            }
            $retry--;
        } while ($url == '' && $retry != 0);
        if (empty($url)) {
            $msgErr = !empty($responseS3['msg']) ? $responseS3['msg'] : 'Não foi possível salvar o arquivo no s3';
            throw new Exception($msgErr);
        }
        return $url;
    }

    private function login()
    {
        $this->getResponse('https://www.escavador.com/login', 'POST', [
            'email' => '<EMAIL>',
            'senha' => 'Uplexis@123'
        ]);
    }

    private function checkIfPageHasNotBeenFound($html)
    {
        $pattern = '/<p\sclass=\"errorTitle\">Página não encontrada<\/p>/isU';
        return (bool) preg_match($pattern, $html);
    }
}
