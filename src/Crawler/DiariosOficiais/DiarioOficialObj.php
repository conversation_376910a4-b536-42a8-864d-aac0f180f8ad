<?php

namespace App\Crawler\DiariosOficiais;

use DateTime;

class DiarioOficialObj
{
    public $pagina;
    public $posicao;
    public $relevancia;
    public $caderno;
    public $data;
    public $filename;
    public $texto;
    public $estado;
    public $link_pdf;

    public function fromEscavador(EscavadorObj $escavadorObj)
    {
        $escavadorObj->diario_data = (!empty($escavadorObj->diario_data))
            ? (new DateTime($escavadorObj->diario_data))->format('d/m/Y')
            : null;

        $this->pagina = $escavadorObj->numero_pagina;
        $this->caderno = $escavadorObj->diario_nome;
        $this->data = $escavadorObj->diario_data;
        $this->texto = $escavadorObj->texto;
        $this->link_pdf = $escavadorObj->link_pdf;
        $this->estado = $escavadorObj->estado;
        return $this;
    }
}
