<?php

namespace App\Crawler\BoaVistaPessoaGoldAnalitico;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\BoaVista\BoaVistaManager;
use Exception;

/**
 * Fonte Acerta Completa do fornecedor BoaVista
 *
 * @version 1.0.0
 *
 * <AUTHOR> Mesquita 14/04/2021
 */
class BoaVistaPessoaGoldAnalitico extends Spider
{
    private $cpf;

    /**
     * Valida os parametros de busca da fonte
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 14/04/2021
     *
     * @return void
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['cpf'])) {
            throw new Exception("Parâmetro CPF é obrigatório", 1);
        }

        if (!Document::validarCpf($this->param['cpf'])) {
            throw new Exception('Parâmetro Inválido', 1);
        }

        $this->cpf = Document::removeMask($this->param['cpf']);
    }

    /**
     * Inicia o processamento da fonte
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 14/04/2021
     *
     * @return array
     */
    protected function start()
    {
        $sources = [
            'identificacao',
            'informacoes_complementares',
            'nome_documentos',
            'localizacao',
            'participacoes_do_documento_consultado',
            'resumo_ocorrencias_de_debitos',
            'debitos',
            'resumo_titulos_protestados',
            'titulos_protestados',
            'resumo_de_acoes_civeis',
            'relacao_de_acoes_civeis',
            'relacao_falencia_recuperacao_judicial',
            'resumo_consultas_anteriores',
            'outras_grafias',
            'relacao_consultas_anteriores_credito_segmento',
            'score_classificacao_varios_modelos' => [
                'score',
                'limite_parcela',
                'renda_presumida'
            ],
            'resumo_devolucoes_informadas_pelo_ccf',
            'relacao_devolucoes_informadas_pelo_ccf',
            'historico_conta_corrente_informada',
            'mensagem_registro'
        ];

        return (new BoaVistaManager())->searchAcertaCompleta($this->cpf, $sources);
    }
}
