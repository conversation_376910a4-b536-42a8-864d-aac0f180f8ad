<?php

namespace App\Crawler\Info4cWatchList;

use App\Crawler\Spider;
use App\Factory\MongoDB;
use Exception;

class Info4cWatchList extends Spider
{
    private const INDEX_MONGODB = 'name';
    private const LIMIT = 100;

    private $name;
    private $limit;
    private $manager;

    public function start()
    {
        $this->manager = (new MongoDB())
            ->connectSources()
            ->setDb('info4c')
            ->setCollection('info4c_watchlist');

        $result = $this->getDataByname();

        if (empty($result)) {
            throw new Exception("Nenhum Resultado Encontrado", 2);
        }
        return $this->getRelative($result);
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->name = trim($this->param['name']);
        $this->limit = $this->param['limit'] ?? self::LIMIT;

        if (empty($this->name)) {
            throw new Exception("Critério de busca inválido.", 6);
        }
    }

    /** Realiza busca na base por nome
     * @return array
     */
    private function getDataByname()
    {
        $names = $this->breakName($this->name);

        $fields = ['search_field'];
        $query[] = [
            '$search' => [
                'index' => self::INDEX_MONGODB,
                'compound' => [
                    'must' => [
                        [
                            "phrase" => [
                                "query" => $names,
                                "path" => $fields
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $query[] = ['$limit' => $this->limit];

        $results =  json_decode(
            json_encode(
                $this->manager
                ->query(
                    'aggregate',
                    $query,
                    null,
                    null,
                    true
                )->toArray(),
                true
            ),
            true
        );

        return $this->parseData($results);
    }

    private function parseData($results)
    {
        $data = [];
        foreach ($results as $keyResult => $valueResult) {
            $data[] = [
                'id' => $valueResult['id'] ?? '',
                'relative_id' => $valueResult['relative_id'] ?? '',
                'title' => $valueResult['title'] ?? '',
                'first_name' => $valueResult['first_name'] ?? '',
                'last_name' => $valueResult['last_name'] ?? '',
                'full_name' => $valueResult['full_name'] ?? '',
                'other_names' => $valueResult['other_names'] ?? '',
                'original_name' => $valueResult['original_name'] ?? '',
                'case' => $valueResult['case'] ?? '',
                'entity_type' => $valueResult['entity_type'] ?? '',
                'publication_date' => $valueResult['publication_date'] ?? '',
                'no_longer_on_the_list' => $valueResult['no_longer_on_the_list'] ?? '',
                'birth_date' => $valueResult['birth_date'] ?? '',
                'birth_place' => $valueResult['birth_place'] ?? '',
                'additional_information' => $valueResult['additional_information'] ?? '',
                'country' => $valueResult['country'] ?? '',
                'category' => $valueResult['category'] ?? '',
                'address' => $valueResult['address'] ?? '',
                'address_country' => $valueResult['address_country'] ?? '',
                'passport_nr' => $valueResult['passport_nr'] ?? '',
                'name_of_the_list' => $valueResult['name_of_the_list'] ?? '',
                'information_date' => $valueResult['information_date'] ?? '',
                'authority' => $valueResult['authority'] ?? '',
            ];
        }

        return $data;
    }

    private function getRelative($result)
    {
        foreach ($result as $key => $value) {
            $id = $value['id'];
            if ($value['relative_id'] != '') {
                $id = $value['relative_id'];
            }

            $result[$key]['relatives'] = $this->manager->query(
                'find',
                [
                    [
                        'relative_id' => $id,
                        '$or' => [
                            ['id' => $id],
                            ['id' => ['$ne' => $id]]
                        ]
                    ],
                    [
                        'projection' => [
                            "id" => '$id',
                            "full_name" => '$full_name',
                            "name_of_the_list" => '$name_of_the_list',
                            "authority" => '$authority',
                            "_id" => 0
                        ]
                    ]
                ]
            )->toArray();
        }
        return $result;
    }


    private function breakName($name)
    {

        $nameArr = explode(" ", $name);

        $middleName = $nameArr[1] ?? '';
        $lastName = $nameArr[2] ?? '';
        $firstName = $nameArr[0];
        $newName = trim($middleName . ' ' . $lastName . ' ' . $firstName);

        return [$newName, $name];
    }
}
