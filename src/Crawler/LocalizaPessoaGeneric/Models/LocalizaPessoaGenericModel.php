<?php

namespace App\Crawler\LocalizaPessoaGeneric\Models;

use Exception;

class LocalizaPessoaGenericModel
{
    public $dados_cadastrais;
    public $aTelefones;
    public $aEmails;
    public $aEnderecos;
    public $aParticipacao_empresa;
    public $cadastro_unit;

    public function __set($name, $value)
    {
        if (property_exists($this, $name)) {
            return $this->$name = $value;
        }

        throw new Exception("Propriedade set '{$name}' não existe", 3);
    }

    public function __get($name)
    {
        if (property_exists($this, $name)) {
            return $this->$name;
        }

        throw new Exception("Propriedade get '{$name}'  não existe", 3);
    }
}
