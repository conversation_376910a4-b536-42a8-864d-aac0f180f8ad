<?php

namespace App\Crawler\LocalizaPessoaGeneric;

use App\Crawler\SpiderGeneric;
use App\Helper\Document;
use Exception;

class LocalizaPessoaGeneric extends SpiderGeneric
{
    public function start()
    {
        return $this->runGenericSources($this->param, $this->auth ?? []);
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (isset($this->param['documento']) && Document::validarCpf($this->param['documento'])) {
            $this->param['documento'] = Document::removeMask($this->param['documento']);
            return;
        }

        throw new Exception('Parâmetro inválido', 1);
    }

    protected function getSourcesConfig(): array
    {
        return [
            [
                'source' => 'BigDataCorpLocalizaPessoa',
                'params' => ['documento' => 'documento'],
                'auths' => []
            ],
            [
                'source' => 'TargetDataLocalizaPessoa',
                'params' => ['cpf' => 'documento'],
                'auths' => []
            ]
        ];
    }
}
