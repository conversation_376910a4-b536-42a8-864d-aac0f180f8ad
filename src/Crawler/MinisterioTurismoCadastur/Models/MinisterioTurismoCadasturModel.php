<?php

namespace App\Crawler\MinisterioTurismoCadastur\Models;

class MinisterioTurismoCadasturModel
{
    public $registroRf;
    public $dtFimVigencia;
    public $dtInicioVigencia;
    public $nomePrestador;
    public $numeroCadastro;
    public $atividade;
    public $complemento;
    public $localidade;
    public $localidadeNuUf;
    public $municipio;
    public $noBairro;
    public $noLogradouro;
    public $noWebSite;
    public $nuCep;
    public $nuTelefone;
    public $nuUf;
    public $sguf;
    public $situacao;
    public $tipoPessoa;

    public function __set($name, $value)
    {
        $this->$name = $value;
    }

    public function __get($name)
    {
        return $this->$name;
    }
}
