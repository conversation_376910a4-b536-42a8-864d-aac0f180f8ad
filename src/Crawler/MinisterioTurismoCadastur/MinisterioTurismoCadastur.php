<?php

namespace App\Crawler\MinisterioTurismoCadastur;

use App\Crawler\MinisterioTurismoCadastur\Models\MinisterioTurismoCadasturModel;
use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Util;
use Exception;

class MinisterioTurismoCadastur extends Spider
{
    private const BASE_URL = 'https://cadastur.turismo.gov.br/';
    private $uf;
    private $name;

    public function start()
    {
        $providers = $this->getDataProviders();
        return $this->parseResults($providers);
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['uf'])) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }

        if (empty(trim($this->param['name']))) {
            throw new Exception('Parâmetro ou critério invalido', 1);
        }

        $this->name = strtoupper($this->param['name']);
        $this->uf = $this->param['uf'];
    }

    /**
     * Método responsável por efetuar a busca no site utilizando os parametros
     * passados na fonte
     *
     * <AUTHOR> Sório | Geanne Santos - 14/04/2021
     * @return array
     */
    private function getDataProviders()
    {
        $headers = [
            'accept: application/json, text/plain, */*',
        ];

        $params = json_encode([
            'currentPage' => 1,
            'filtros' => [
                'flPossuiVeiculo' => '',
                'localidade' => '',
                'localidadeNuUf' => $this->uf,
                'noPrestador' => $this->name,
                'nuAtividadeTuristica' => '',
                'souPrestador' => true,
                'souTurista' => false
            ],
            'pageSize' => 10,
            'sortDirections' => 'ASC',
            'sortFields' => 'nomePrestador'
        ]);

        $response = json_decode($this->getResponse(
            self::BASE_URL . 'cadastur-backend/rest/portal/obterDadosPrestadores',
            'POST',
            $params,
            $headers
        ));

        if (empty($response->list)) {
            throw new Exception('Nenhum resultado encontrado', 2);
        }

        return $response->list;
    }

    /**
     * Metodo responsável por efetuar o parse dos resultados
     * seguindo o model
     *
     * <AUTHOR> Guilherme Sório | Geanne Santos - 14/04/2021
     * @param array $providers
     * @return array
     */
    private function parseResults($providers)
    {
        $results = [];
        foreach ($providers as $provider) {
            $providerModel = new MinisterioTurismoCadasturModel();
            $provider = $this->formatProviderValues($provider);
            foreach ($provider as $key => $value) {
                $providerModel->$key = $value;
            }
            $results[] = $providerModel;
        }

        return $results;
    }

    /**
     * Formatar valores nos padroes
     *
     * <AUTHOR> Guilherme Sório - 15/04/2021
     * @param object $provider
     * @return array
     */
    private function formatProviderValues(object $provider)
    {
        foreach ($provider as $key => $value) {
            // Tratamento da data, pois não vem como timestamp
            if ($key == 'dtFimVigencia' || $key == 'dtInicioVigencia') {
                $value = date('d/m/Y', substr($value, 0, 10));
            }
            //Formatar CNPJ
            if ($key == 'numeroCadastro') {
                $value = Document::formatCnpj($value);
            }
            //Formatar Cep
            if ($key == 'nuCep') {
                $value = Util::cepFormat($value);
            }
            //Formatar o Telefone
            if ($key == 'nuTelefone') {
                $value = '(' . substr($value, 0, 2) . ')' . substr($value, 2);
            }
            $provider->$key = $value;
        }

        return $provider;
    }
}
