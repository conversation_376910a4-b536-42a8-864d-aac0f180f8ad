<?php

namespace App\Crawler\BoaVistaParticipacaoEmpresasScore;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Manager\BoaVista\BoaVistaManager;
use Exception;

/**
 * Fonte Pariticapação Empresas Score do fornecedor BoaVista
 *
 * @version 1.0.0
 *
 * <AUTHOR> Mesquita 14/04/2021
 */
class BoaVistaParticipacaoEmpresasScore extends Spider
{
    private $cpf;

    /**
     * Valida os parametros de busca da fonte
     *
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 14/04/2021
     *
     * @return void
     */
    protected function validateAndSetCrawlerAttributes()
    {
        if (empty($this->param['cpf'])) {
            throw new Exception("Parâmetro CPF é obrigatório", 1);
        }

        if (!Document::validarCpf($this->param['cpf'])) {
            throw new Exception('Parâmetro Inválido', 1);
        }

        $this->cpf = Document::removeMask($this->param['cpf']);
    }

    /**
     * Inicia o processamento da fonte
     *
     * @version 1.0.0
     *
     * <AUTHOR> <PERSON>qui<PERSON> 14/04/2021
     *
     * @return array
     */
    protected function start()
    {

        $sources = [
            'participacoes_do_documento_consultado',
            'resumo_ocorrencias_de_debitos',
            'debitos',
            'relacao_falencia_recuperacao_judicial',
            'score_classificacao_varios_modelos' => [
                'score',
            ]
        ];

        $response = (new BoaVistaManager())->searchAcertaCompleta($this->cpf, $sources);

        $this->updateSpine('BoaVistaParticipacaoEmpresasScore', array_merge($response, ['cpf' => $this->cpf]));
        return $response;
    }
}
