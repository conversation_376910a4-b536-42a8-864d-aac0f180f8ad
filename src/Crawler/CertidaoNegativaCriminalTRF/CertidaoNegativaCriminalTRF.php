<?php

namespace App\Crawler\CertidaoNegativaCriminalTRF;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

class CertidaoNegativaCriminalTRF extends Spider
{
    private const BASE_URL = "https://certidoes.trf5.jus.br/certidoes/";
    private const REQUEST_URL = "paginas/certidaocriminal.faces";
    private const PATH_STATIC_S3 = S3_STATIC_URL;
    private const PATH_CERTIDAO_S3 = 'captura/certidao_negativa_criminal_trf5/';
    private $name;
    private $document;


    public function start()
    {
        $uniqid = md5(uniqid(rand(), true));
        $certificateName = "{$uniqid}.pdf";
        $this->certificateLocalPath = "/tmp/{$certificateName}";
        $this->certificateS3Path = self::PATH_CERTIDAO_S3 . $certificateName;
        $this->certificateUrl = self::PATH_STATIC_S3 . $this->certificateS3Path;

        $html = $this->getResponse(self::BASE_URL . self::REQUEST_URL);
        preg_match('/ViewState"\svalue="([\s\S]*?)"/', $html, $viewState);

        $result = $this->makeRequest($viewState[1]);

        $pdf = $this->validateResult($result, $viewState[1]);

        $this->savePdf($pdf);

        return [
            'pdf' => $this->pdf
        ];
    }

    /** Faz requisição no site da fonte
     * @param $viewState
     * @return mixed
     * @throws Exception
     * <AUTHOR> Santos - 22 fev. 22
     */
    private function makeRequest($viewState)
    {
        $params = [
            "form" => "form",
            "form:targ" => 'false',
            "form:nome" => $this->name,
            "form:cpfCnpj" => $this->document,
            "form:jcaptcha" => $this->validateCaptcha(),
            "javax.faces.ViewState" => $viewState,
            "javax.faces.partial.ajax" => 'true',
            "javax.faces.source" => "form:validar",
            "javax.faces.partial.execute" => "@all",
            "javax.faces.partial.render" => "form:growl form:labelTipoRetorno form:panelBotoes",
            "form:validar" => "form:validar"
        ];

        return $this->getResponse(self::BASE_URL . self::REQUEST_URL, 'POST', $params);
    }

    /** Gera o pdf da certidão negativa
     * @param $form
     * @param $viewState
     * @return mixed
     * @throws Exception
     * <AUTHOR> Santos 22 fev 22
     */
    private function generatePdf($form, $viewState)
    {
        $params = [
            "form" => "form",
            "form:targ" => false,
            "form:nome" => $this->name,
            "form:cpfCnpj" => $this->document,
            "form:jcaptcha" => $this->validateCaptcha(),
            "javax.faces.ViewState" => $viewState,
            (string)$form => $form
        ];

        return $this->getResponse(self::BASE_URL . self::REQUEST_URL, 'POST', $params);
    }


    /**
     * @param $file
     * @throws Exception
     */
    private function savePdf($file)
    {
        file_put_contents($this->certificateLocalPath, $file);

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);

        $this->pdf = $this->certificateUrl;
    }

    private function validateCaptcha()
    {
        $this->getImageAndBreakCaptcha(self::BASE_URL . 'jcaptcha.jpg');
        return strtolower($this->captcha);
    }


    /** Valida o resultado da certidão.
     *  O pdf só é gerado em caso de certidão negativa
     * @param $html
     * @param $viewState
     * @return mixed
     * @throws Exception
     * <AUTHOR> Santos - 22 fev 22
     */
    private function validateResult($html, $viewState)
    {
        preg_match('/N.o\sencontramos\sprocessos\spara\sos\sdados\sinformados/isu', $html, $matches);
        if (!empty($matches)) {
            preg_match("/\\\'(form:j_idt\d*)\\\'/mi", $html, $form);
            return $this->generatePdf($form[1], $viewState);
        }
        throw new Exception(
            'Constam processo(s) com o Nome e/ou CPF informados na consulta.
             É necessário acessar o site e enviar os dados da pessoa pesquisada para análise dessa Certidão',
            1
        );
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->name = trim($this->param['name']);
        $this->document = trim($this->param['document']);

        if (empty($this->name)) {
            throw new Exception('Parâmetro Nome/Razão Social Inválido', 1);
        }

        if (!Document::validarCpfOuCnpj($this->document)) {
            throw new Exception('Parâmetro CPF/CNPJ Inválido', 1);
        }

        $this->document = Document::formatCpfOrCnpj($this->document);
    }
}
