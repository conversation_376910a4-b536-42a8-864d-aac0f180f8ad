<?php

// Campos para filtrar
define('FIELD_PARTES_ATIVAS', 'PARTES_ATIVAS');
define('FIELD_PARTES_PASSIVAS', 'PARTES_PASSIVAS');
define('FIELD_PARTES_OUTRAS', 'OUTRAS_PARTES');
define('FIELD_PARTES_PASSIVAS_ADVOGADOS', 'ADVOGADOS_PARTE_ATIVA');
define('FIELD_PARTES_ATIVAS_ADVOGADOS', 'ADVOGADOS_PARTE_PASSIVA');
define('FIELD_PARTES_OUTRAS_ADVOGADOS', 'OUTROS_ADVOGADOS');
define('FIELD_DIARIOS_RECORTES', 'DIARIOS_RECORTES');
define('FIELD_ASSUNTOS', 'ASSUNTOS');
define('FIELD_ANO', 'ANO');
define('FIELD_CLASSES', 'CLASSES');
define('FIELD_COMARCA', 'COMARCA');
define('FIELD_VARA', 'VARA');
define('FIELD_STATUS', 'STATUS');
define('FIELD_UF_NOME', 'UNIDADES_FEDERAIS_NOME');
define('FIELD_UF', 'UNIDADES_FEDERAIS_ID');
define('FIELD_NUMERO_PROCESSO', 'NUMERO_PROCESSO');
define('FIELD_VLR_UNIDADE_MONETARIA', 'VALOR_UNIDADE_MONETARIA');
define('FIELD_JUIZES', 'JUIZES');
define('FIELD_TRIBUNAL_NOME', 'TRIBUNAL_NOME');
define('FIELD_PRIMEIRA_DATA', 'PRIMEIRA_DATA');
define('FIELD_ULTIMA_DATA', 'ULTIMA_DATA');
define('FIELD_ULTIMO_ANDAMENTO', 'ULTIMO_ANDAMENTO_DATA');
define('FIELD_VALOR', 'VALOR');
define('FIELD_PRIMEIRO_DIARIO_DATA', 'PRIMEIRO_DIARIO_DATA');
define('FIELD_ULTIMO_DIARIO_DATA', 'ULTIMO_DIARIO_DATA');
define('FIELD_DATA_CAPTURA', 'DATA_CAPTURA');
define('FIELD_INSTANCIA_ID', 'INSTANCIA_ID');

// Constantes de filtros
// Filtro que busca um texto dentro dos campos informados
define('FILTER_SEARCH_INPUTS', serialize(array('searchInputs' => 'fieldsToQuery', 'name_search' => 'text')));

// Filtro que busca nos campos exatamente pelo valor definido na busca
define('FILTER_TERM_FILTERS', serialize(array('termFilters' => 'acceptValues', 'name_search' => 'field')));

// Filtro que busca em um range de valores dentro do campo informado
define('FILTER_RANGE_FILTERS', serialize(array(
    'rangeFilters' => array('fromValue', 'toValue'),
    'name_search' => 'field'
)));

// Constantes da classe Spider
// validação no primeiro método do spider, verificando por exemplo
// se o nome de entrada é um nome composto ou por exemplo se o número de cnpj é válido
define("PARAM_ERROR", 1);
// quando o site dá aquela mensagem que nenhum processo foi encontrado para os parâmetros informados
define("NONE_ERROR", 2);
// quando o site dá aquela mensagem que nenhum processo foi encontrado para os parâmetros informados
define("NOTFOUND_ERROR", 2);
// erros do método request, o próprio método já vai retornar
// a exception com o código 3, vale apenas se assegurar que isso será passado pro serviço
define("REQUEST_ERROR", 3);
define("PARSE_ERROR", 4); // qqer parse feito na página
define("FLOW_ERROR", 5); // apareceu uma pagina desconhecida na navegacao
define("CAPTCHA_ERROR", 100); // as tentativas esgotaram e não foi possível quebrar
define("FONT_OFFLINE", 7); // fonte indisponível
define("FONT_BROKEN", 8); // Fonte em manutenção pela upLexis
define("UNKNOWN_ERROR", 9); // Unknown error, USADA APENAS PELO PROCESSAMENTO DO UPMINER - haha conta outra
/**
 * validação no primeiro método do spider, verificando por exemplo se o nome de entrada é um nome composto;
 * mas em situações em que não há o que ser feito...o nome da empresa é simples, mas o site requer um nome composto
 */
define("PARAM_EXCEPTION", 6);
define("FLOW_EXCEPTION", 6);

define("LIMIT_RESULTS", 25);

if (!defined('JSON_PRETTY_PRINT')) {
    define('JSON_PRETTY_PRINT', 128);
}
