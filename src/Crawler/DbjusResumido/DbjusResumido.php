<?php

namespace App\Crawler\DbjusResumido;

use App\Crawler\Spider;
use App\Crawler\Tj\tjOld\helpers\Polisher;
use App\Helper\Str;
use App\Manager\DbjusManager;
use Exception;

class DbjusResumido extends Spider
{
    private $name;
    private $searchParams = [];
    private $similarity = 80;
    private $offset = 0;
    private $itensInPage = 80;
    private $totalProcessos = 0;
    private $limit = 100;
    private $sections = [];
    private $manager;
    private $total_processos = 0;

    private $search = array(
        "base" => "https://api.dbjus.com/v7",
        "listar_processos" => array(
            "url" => "/process/search",
            "method" => "POST",
            "description" => "Realiza uma consulta avançada em todos os processos da base DBJus."
        ),
        "detalhes_processo" => array(
            "url" => "/process/details?nup={process_id}",
            "method" => "GET",
            "description" => "Retorna detalhes completos sobre um processo capturado."
        ),
        "total_processos" => array(
            "url" => "/process/search/count",
            "method" => "POST",
            "description" => "Retorna o total de processos"
        ),
    );
    public function start()
    {
        ini_set('memory_limit', '-1');
        $this->similarity = $this->param['similarity'];
        $results = $this->searchByNome();
        return $results;
    }

    private function searchByNome()
    {
        $this->validateSections();

        if ($this->similarity == 100) {
            // Forçar buscar por nome exato
            if (strpos($this->name, '"') === false) {
                $this->name = '"' . $this->name . '"';
            }
        } else {
            $this->name = str_replace('"', "", $this->name);
        }

        // Adicionar nome no filtro
        $this->addSearchFilter($this->name, $this->sections);

        // Buscar pelos processos mais recentes
        $this->setOrder("ULTIMO_DIARIO_DATA", 'DESC');

        // Inicializar variaveis
        $processos['total_processos'] = 0;
        $processos['total_pesquisados'] = 0;
        $processos['processos'] = array();

        $this->setPage(0);

        // Buscar processos
        $continue = false;
        do {
            // Recuperar processo
            $_processos = $this->processListaProcessos(
                $this->request(
                    $this->search['listar_processos'],
                    json_encode(
                        $this->getSearchParams(),
                        JSON_PRETTY_PRINT
                    )
                )
            );

            // Total de processos
            if ($processos['total_processos'] == 0) {
                $processos['total_processos'] = $this->getLastTotalProcessos();
            }

            // Adicionar processos ao array
            $processos['processos'] = array_merge($processos['processos'], $_processos);

            // Contar total de processos pesquisados até agora
            $processos['total_pesquisados'] = (count($processos['processos'])  >= 0)
                ? count($processos['processos'])
                : 0;

            // Verficar se retornou algum processo
            if (empty($_processos)) {
                break;
            }

            if (ENV == 'qa' || ENV == 'dev') {
                if (count($_processos) < 10) {
                    break;
                }
            } else {
                if (count($_processos) < 80) {
                    break;
                }
            }

            if ($processos['total_pesquisados'] >= $this->limit) {
                break;
            }

            // Setar próxima página
            $this->setPage((int) $processos['total_pesquisados']);

            // Verificar se trouxe mais processos
            if (empty($_processos)) {
                break;
            }

            if (empty($processos['total_pesquisados'])) {
                break;
            }

            // Verificar se ultrapassou o limite
            $continue = ($this->getLimit() > $processos['total_pesquisados']);
        } while ($continue === true);

        if ($processos['total_pesquisados'] > $this->limit) {
            array_splice($processos['processos'], $this->limit);
            $processos['total_pesquisados'] = $this->limit;
        }

        return $this->parseProcessos($processos);
    }

    private function parseProcessos($processos)
    {
        // Adiciona a filtragem de similaridade antes da filtragem por limitação
        foreach ($processos['processos'] as $key => $processo) {
            $this->name = preg_replace("/\"/", "", $this->name);
            $check = Polisher::bootstrap($processo, $this->name, $this->similarity);

            if ($check['del'] === true) {
                unset($processos['processos'][$key]);
            } else {
                $simi = $check['similarity'] == 'N/A' ? $this->similarity : $check['similarity'];
                $processos['processos'][$key]['similarity'] = $simi;
            }
        }

        if ($processos['total_pesquisados'] == 0) {
            throw new Exception("Nenhum registro encontrado", 2);
        }

        array_walk_recursive(
            $processos['processos'],
            function (&$value) {
                if (gettype($value) == 'string') {
                    $value = Str::removerAcentos($value);
                }
            }
        );

        unset($value);

        return $processos['processos'];
    }

    /**Salva o log na billing
     *
     *<AUTHOR>
     **/

    private function saveBilhetador($results)
    {
        $fonte = 'Dbjus - Resumido';
        $criterio = $this->param['nome'];
        $tipo = 'Resumo';

        $manager = new DbjusManager();
        $manager->insertBilhetador($fonte, $results, $criterio, $tipo);
    }

    public function validateAndSetCrawlerAttributes()
    {
        if (empty(trim($this->param['nome']))) {
            throw new Exception("Parâmetro nome é obrigatório", 1);
        }

        $this->validateAndDefineFields();

        $this->name = Str::removerAcentos(strtoupper($this->param['nome'])) ?? [];

        $this->shares = $this->param['partes'] ?? [];
    }

    public function __construct($param, $auth)
    {
        require_once __DIR__ . '/configDbjus.php';
        parent::__construct($param, $auth);
    }

    /**
     * Valida e define os campos de busca
     * Validates and defines the search fields
     *
     * @return array
     * <AUTHOR> Machado - 2018-04-26
     * Revisão: Jonathan Machado - 2018-04-26
     * @version 1.0.0
     */
    private function validateAndDefineFields()
    {
        $this->manager = new DbjusManager();
        if (empty($this->param['params'])) {
            return;
        }

        foreach ($this->param['params'] as $param => $value) {
            if (is_array($value)) {
                foreach ($value as $k => $v) {
                    if (empty($v)) {
                        unset($this->param['params'][$param][$k]);
                    }
                }
            }
        }

        foreach ($this->param['params'] as $param => $value) {
            if (empty($value)) {
                continue;
            }
            switch ($param) {
                case 'uf':
                    $arrValue = (array) $value;
                    foreach ($arrValue as $k_uf => $v_uf) {
                        $arrValue[$k_uf] = $this->manager->getIdUF($v_uf);
                    }
                    if (!empty($arrValue)) {
                        $this->addTermFilter(FIELD_UF, $arrValue);
                    }
                    break;
                case 'assuntos':
                    $arrValue = (array) $value;
                    $arrValue = array_map(function ($str) {
                        return '"' . strtoupper($str) . '"';
                    }, $arrValue);
                    $arrValue = implode(" OR ", $arrValue);
                    if (!empty($arrValue)) {
                        $this->addSearchFilter($arrValue, FIELD_ASSUNTOS);
                    }
                    break;
                case 'instancias':
                    $arrValue = $value;
                    if (!is_array($value)) {
                        $arrValue = explode(',', $value);
                    }
                    if (isset($arrValue[0]) && strtoupper($arrValue[0]) != 'TODOS') {
                        $strValue = implode(" OR ", $arrValue);
                        $this->addSearchFilter($strValue, FIELD_INSTANCIA_ID);
                    }
                    break;
                case 'limite':
                    if (!empty($value)) {
                        $this->limit = (int) $value;
                    }
                    break;
            }
        }
    }

    private function addSearchFilter($text, $fieldsToQuery = [])
    {
        return $this->addFilter($text, FILTER_SEARCH_INPUTS, $fieldsToQuery);
    }

    private function addTermFilter($field, $acceptValues)
    {
        return $this->addFilter(strtoupper($field), FILTER_TERM_FILTERS, $acceptValues);
    }

    private function addRangeFilter($field, $fromValue, $toValue)
    {
        return $this->addFilter(strtoupper($field), FILTER_RANGE_FILTERS, array($fromValue, $toValue));
    }

    private function addFilter($text, $theFilter, $filters)
    {
        // Tratar parâmetros
        if (!is_array($filters)) {
            $filters = array($filters);
        }

        if (!is_array($theFilter)) {
            $theFilter = $this->getTheFilter($theFilter);
        }

        // Pegar tipo do filtro e campos
        $filter_type = $theFilter['filter_type'];
        $filter_fields = $theFilter['filter_fields'];
        $name_search = $theFilter['name_search'];

        // Criar array de filtro
        $final_filter = [];
        $final_filter[$name_search] = $text;

        // Adicionar campos ao filtro
        if (!is_array($filter_fields)) {
            $final_filter[$filter_fields] = $filters;
        } else {
            foreach ($filter_fields as $k_field => $filter_field) {
                if (!empty($filters[$k_field])) {
                    $final_filter[$filter_field] = $filters[$k_field];
                }
            }
        }

        $this->searchParams[$filter_type][] = $final_filter;

        return $this;
    }

    private function setOrder($field, $sort = 'ASC')
    {
        // Tratar parametros
        $sort = strtoupper($sort);
        if ($sort != 'ASC' && $sort != 'DESC') {
            $sort = 'ASC';
        }

        // Converter para boolean
        if ($sort == 'DESC') {
            $sort = true;
        }

        // Criar array de ordem
        $theSort = array();
        $theSort['desc'] = $sort;
        $theSort['field'] = $field;

        // Adicionar a ordenação
        $this->search_params['sorts'][] = $theSort;

        return $this;
    }

    /**
     * Retorna o nome e campos do filtro
     * Return filter's names and fields
     *
     * @param string $filter_type
     * @return void
     * <AUTHOR> Revisão:
     * @version
     */
    private function getTheFilter($filter_type)
    {
        $filter_type = unserialize($filter_type);

        $filter_keys = array_keys($filter_type);
        $filter_fields = $filter_type[$filter_keys[0]];

        $final['filter_type'] = $filter_keys[0];
        $final['filter_fields'] = $filter_fields;
        $final['name_search'] = $filter_type['name_search'];

        return $final;
    }

    /**
     * Valida as partes passadas por parâmetro
     * Validates all sections passed by parameter
     *
     * @param array $partes
     * @return array $partes
     * <AUTHOR> Revisão:
     * @version 1.0.0
     */
    private function validateSections()
    {
        // Validar partes
        if (empty($this->sections)) {
            $this->sections = $this->param['partes'];
        } elseif (!$this->isValidParte($this->sections)) {
            throw new Exception('Tipo de parte para consulta não existe !', PARAM_ERROR);
        }

        if (!is_array($this->sections)) {
            $this->sections = array($this->sections);
        }

        // Forçar buscar por outras partes, outros advogados e recortes
        if (!in_array(FIELD_PARTES_OUTRAS, $this->sections)) {
            if (in_array(FIELD_PARTES_ATIVAS, $this->sections) || in_array(FIELD_PARTES_PASSIVAS, $this->sections)) {
                $this->sections[] = FIELD_PARTES_OUTRAS;
            }
        }

        if (!in_array(FIELD_PARTES_OUTRAS_ADVOGADOS, $this->sections)) {
            if (
                in_array(FIELD_PARTES_ATIVAS_ADVOGADOS, $this->sections)
                || in_array(FIELD_PARTES_PASSIVAS_ADVOGADOS, $this->sections)
            ) {
                $this->sections[] = FIELD_PARTES_OUTRAS_ADVOGADOS;
            }
        }

        if (!in_array(FIELD_DIARIOS_RECORTES, $this->sections)) {
            $this->sections[] = FIELD_DIARIOS_RECORTES;
        }
    }

    private function isValidParte($partes)
    {
        if (!is_array($partes)) {
            $partes = array($partes);
        }

        foreach ($partes as $parte) {
            if (!in_array($parte, $this->getPartes())) {
                return false;
            }
        }

        return true;
    }

    /*
     * Retorna em um array todas as partes disponíveis para consulta
     *
     * @return Array
     */
    public function getPartes()
    {
        return array(
            FIELD_PARTES_ATIVAS,
            FIELD_PARTES_PASSIVAS,
            FIELD_PARTES_OUTRAS,
            FIELD_PARTES_PASSIVAS_ADVOGADOS,
            FIELD_PARTES_ATIVAS_ADVOGADOS,
            FIELD_PARTES_OUTRAS_ADVOGADOS,
            FIELD_DIARIOS_RECORTES
        );
    }

    private function setPage($page)
    {
        if ($page < 0) {
            $page = 0;
        }

        $this->setOffset($page);
    }

    /*
     * Setar offset
     *
     * $offset : String
     */
    private function setOffset($offset)
    {
        if ($offset < 0) {
            $offset = 0;
        }

        $this->offset = $offset;
    }

    private function processListaProcessos($data)
    {
        if (is_object($data)) {
            if (!empty($data->total) && $this->total_processos == 0) {
                $this->total_processos = $data->total;
            } // Total de processsos

            $data = $data->hits; // Processos
        }

        if (!is_array($data) || (count($data) < 1)) {
            return array();
        }

        $final = array();

        foreach ($data as $processo) {
            $arr_processo = array();
            $uf = "";
            //$arr_processo['andamentos'] = array();

            // Dados de identificação do processo
            $arr_processo['id_processo'] = $processo->processId;
            $arr_processo['numero_processo'] = $processo->nup;

            // Unidades Federais
            foreach ($processo->unidadesFederais as $processo_UF) {
                $uf .= $processo_UF->name . " / ";
            }

            $uf = substr($uf, 0, strlen($uf) - 3);

            $arr_processo['uf'] = $uf;

            // Instancias
            foreach ($processo->instancesSummaries as $processo_instancia) {
                $arr_processo_instancia = array();
                $arr_processo_instancia['nome'] = $processo_instancia->instanceName;
                $arr_processo_instancia['assuntos'] = (!empty($processo_instancia->assuntos))
                    ? implode(" / ", $processo_instancia->assuntos)
                    : array();
                $arr_processo_instancia['classes'] = (!empty($processo_instancia->classes))
                    ? implode(" / ", $processo_instancia->classes)
                    : array();

                $arr_processo['instancias'][] = $arr_processo_instancia;

                // Partes ativas
                if (!empty($processo_instancia->partesAtivas)) {
                    foreach ($processo_instancia->partesAtivas as $partesAtivas) {
                        $arr_processo['partes'][] = array('tipo' => 'ATIVA', 'nome' => $partesAtivas);
                    }
                }

                // Partes passivas
                if (!empty($processo_instancia->partesPassivas)) {
                    foreach ($processo_instancia->partesPassivas as $partesPassivas) {
                        $arr_processo['partes'][] = array('tipo' => 'PASSIVA', 'nome' => $partesPassivas);
                    }
                }

                // Outras partes
                if (!empty($processo_instancia->outrasPartes)) {
                    foreach ($processo_instancia->outrasPartes as $partesOutras) {
                        $arr_processo['partes'][] = array('tipo' => 'OUTRO', 'nome' => $partesOutras);
                    }
                }
            }

            $arr_processo['ultimo_diario'] = $processo->ultimoDiario;

            // Último processo
            $arr_processo['ultimo_diario'] = '';

            if ($processo->ultimoDiario != null) {
                $arr_processo['ultimo_diario'] = "[" .
                    date(
                        "d/m/Y",
                        substr(
                            $processo->ultimoDiario->date,
                            0,
                            strlen(
                                $processo->ultimoDiario->date
                            )
                                - 3
                        )
                    ) . "] " . $processo->ultimoDiario->bookName .
                    " - " .
                    $processo->ultimoDiario->siteName;
            }
            // Último andamento
            $arr_processo['ultimo_andamento'] = '';
            if ($processo->ultimoAndamento != null) {
                $arr_processo['ultimo_andamento'] = "[" .
                    date(
                        "d/m/Y",
                        substr(
                            $processo->ultimoAndamento->date,
                            0,
                            strlen(
                                $processo->ultimoAndamento->date
                            )
                                - 3
                        )
                    ) . "] " .
                    $processo->ultimoAndamento->title;
            }

            $final[] = $arr_processo;
        }

        return $final;
    }

    /**
     * Faz uma requisição recebendo os parametros como array
     * Makes a request receiving parameters as an array
     *
     * @param array $pageRequest
     * @return array
     * <AUTHOR> Revisão: Jonathan Machado - 2018-04-26 - Trocado o client por RequestManager
     * @version 2.0.0
     */
    public function request($pageRequest)
    {
        $pageRequest['params'] = $this->getSearchParams();
        $pageRequest['url'] = $this->search['base'] . $pageRequest['url'];
        $pageRequest['headers'] = [];

        try {
            // Setar parâmetros
            if (strtolower($pageRequest['method']) == 'get') {
                foreach ($pageRequest['params'] as $field => $value) {
                    if (is_array($value)) {
                        continue;
                    }
                    $strField = "{" . $field . "}";
                    $strValue = urlencode($value);
                    $url = $pageRequest['url'];
                    $pageRequest['url'] = str_replace($strField, $strValue, $url);
                }
            }

            if ($this->offset > 0) {
                if (!strpos($pageRequest['url'], '?')) {
                    $pageRequest['url'] .= '?';
                }
                $pageRequest['url'] .= 'offset=' . $this->offset;
            }

            $loginArr = [
                CURLOPT_USERPWD => DBJUS_USER . ":" . DBJUS_PASS,
            ];

            $this->setCurlOpt($loginArr);

            try {
                $response = $this->getResponse(
                    $pageRequest['url'],
                    $pageRequest['method'],
                    json_encode($pageRequest['params']),
                    $pageRequest['headers']
                );

                $result = json_decode($response);

                if (isset($result->hits)) {
                    $this->saveBilhetador($response);
                } else {
                    throw new Exception('Erro ao acessar o servidor', 3);
                }

                return $result;
            } catch (Exception $e) {
                // Algumas vezes o servidor bloqueia o request por muitas requisições feitas em pouco tempo
                sleep(1);

                try {
                    $response = $this->getResponse(
                        $pageRequest['url'],
                        $pageRequest['method'],
                        json_encode($pageRequest['params']),
                        $pageRequest['headers']
                    );

                    $result = json_decode($response);

                    if (isset($result->hits)) {
                        $this->saveBilhetador($response);
                    } else {
                        throw new Exception('Erro ao acessar o servidor', 3);
                    }
                } catch (Exception $e) {
                    $msg = "Ocorreu um erro: " . $e->getMessage();
                    $msg .= "\n URL: {$pageRequest['url']} ";
                    $msg .= "\n Parametros: \n" . json_encode($pageRequest['params']);
                    throw new Exception($msg);
                }
            }

            // Verificar se ocorreram erros
            if ($response->status == 'error') {
                $json = '';
                $json = "Json de request: " . substr($pageRequest['params'], 0, 1000);
                $json .= "\nUrl do request: " . substr($pageRequest['url'], 0, 1000);

                throw new Exception("Response error {$response->getStatus()} - {$json}", REQUEST_ERROR);
            }

            // Retornar resultado
            return $result;
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * Retorna os parametros de busca
     * Returns the search params
     *
     * @return void
     * <AUTHOR> Revisão:
     * @version 1.0.0
     */
    private function getSearchParams()
    {
        return $this->searchParams;
    }

    /**
     * Retorna a quantidade de ultimos processos
     *
     * @return void
     * <AUTHOR> Revisão:
     * @version 1.0.0
     */
    private function getLastTotalProcessos()
    {
        return $this->totalProcessos;
    }

    /**
     * Recuperar página
     *
     * @return int
     */
    private function getPage()
    {
        $pagina = ceil($this->getOffset() / $this->itensInPage);

        return $pagina;
    }

    /**
     * Recuperar offset
     *
     * @return int
     */
    private function getOffset()
    {
        $offset = $this->offset;

        if ($offset < 0 || empty($offset)) {
            $offset = 0;
        }

        return $offset;
    }

    /**
     * Recuperar limite de resultados
     *
     * @return int
     */
    public function getLimit()
    {
        return $this->limit;
    }
}
