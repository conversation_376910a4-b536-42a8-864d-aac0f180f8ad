<?php

namespace App\Crawler\CertidaoCadastroNacionalDeCondenacoesCiveis;

use App\Crawler\Spider;
use App\Helper\Document;
use App\Helper\Pdf;
use App\Manager\S3\Bucket\StaticUplexisBucket;
use App\Manager\S3\S3;
use Exception;

class CertidaoCadastroNacionalDeCondenacoesCiveis extends Spider
{
    private $cpfCnpj = '';
    private $pdf = '';
    private const BASE_URL = "https://www.cnj.jus.br/improbidade_adm/certidao.php?";
    private const S3_STATIC_PATH = S3_STATIC_URL;
    private const CERTIDAO_S3_PATH = 'captura/certidao_cadastro_nacional_condenacao_civeis/';

    public function start()
    {
        $uniqd = md5(uniqid(rand(), true));
        $this->certificateName = "{$uniqd}.pdf";
        $this->certificateLocalPath = "/tmp/{$this->certificateName}";
        $this->certificateS3Path = self::CERTIDAO_S3_PATH . $this->certificateName;
        $this->certificateUrl = self::S3_STATIC_PATH . $this->certificateS3Path;

        $pdf = $this->makeRequest();
        $text = $this->savePdfAndReturnText($pdf);
        $data = [
            'text' => $text,
            'pdf' => $this->pdf
        ];

        return $data;
    }

    /**
     * Captura a certidão da requisição em pdf.
     * <AUTHOR> Pereira
     * @return $pdf
     */
    private function makeRequest()
    {
        $params = [
            'tipoCertidao' => 'P',
            'cpfCnpj' => $this->cpfCnpj,
            'tipoPessoa' => '',
        ];
        $params = http_build_query($params);

        $pdf = $this->getResponse(self::BASE_URL . $params);

        return $pdf;
    }

    /**
     * Salva o pdf capturado na amazom temporariamente.
     * <AUTHOR> Pereira
     * @param string $data
     * @return $text
     */
    private function savePdfAndReturnText($pdf)
    {
        file_put_contents($this->certificateLocalPath, $pdf);

        $text = (new Pdf())->getTextFromPdf($this->certificateLocalPath, [
            'layout',
            'nopgbrk'
        ]);

        if (!preg_match('/Certidão\sPositiva/', $text) && !preg_match('/Certidão\sNegativa/', $text)) {
            throw new Exception('Sem resultados encontrados.', 2);
        }

        (new S3(new StaticUplexisBucket()))->save($this->certificateS3Path, $this->certificateLocalPath);
        $this->pdf = $this->certificateUrl;
        return $text;
    }

    public function validateAndSetCrawlerAttributes()
    {
        $this->cpfCnpj = Document::removeMask(trim($this->param['cpf_cnpj']));

        if (empty($this->cpfCnpj) || !is_numeric($this->cpfCnpj)) {
            throw new Exception('Parâmetro Inválido', 6);
        }
    }
}
