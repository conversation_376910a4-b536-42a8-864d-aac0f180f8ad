# Changelog

Todas as alterações notáveis neste projeto serão documentadas neste arquivo.

## [1.1.1] - 2024-09-19

### Adicionado
- Comando para processar os documentos a partir do terminal;
- Factory das fontes para reutilizar o instanciamento das fontes em outros contextos;
- Método na classe Source que verifica se o documento existe no dynamo ou elastic e caso não, consultamos ele na receita para fazer a retroalimentação das bases.

### Alteração
- Removido código de processamento dos sócios que alteravam a tabela spine_pj_qsa no dynamo de todas as fontes relacionadas ao fornecedor Boa Vista;
- O processamento de sócios dessas fontes agora só irá retroalimentar as tabelas spine_pf e pj do elastic ou dynamo utilizando o novo método de checagem e retroalimentação da receita federal;

## [1.1.0] - 2024-08-19

### Adicionado
- Parse da nova fonte NewQuod (QSA Enriquecido - 412).

### Alteração
- Adicionado data e source como atributos da classe abstrata Source.

## [1.0.0] - 2024-08-19

### Adicionado
- Criação do arquivo de CHANGELOG e versionamento.
- Primeira versão do projeto lançada.
