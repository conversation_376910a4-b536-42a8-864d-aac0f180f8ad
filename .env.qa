APP_ENV=qa
APP_DEBUG=false
APP_KEY=base64:hpQ9Y+6iy6WNXbJlqmESH5W3D3VVVzWYf3MwKPvqF/I=
APP_URL=https://spine-update.qa.uplexis.com.br

AWS_KEY=${AWS_KEY}
AWS_SECRET=${AWS_SECRET}
AWS_REGION='us-east-1'

QUEUE_SPINE=spine
QUEUE_SPINE_URL=https://sqs.us-east-1.amazonaws.com/857717895630/

ES_HOST=https://vpc-upminerv5-kgwobtef5z5e32pwvvmsptikpq.us-east-1.es.amazonaws.com:443

DYNAMO_PREFIX_TABLE=

URL_SPINE=https://d3ddok0cn6.execute-api.us-east-1.amazonaws.com/qa/search
URL_LAMBDA=https://lambda.qa.uplexis.com.br/captura-qa

SOURCE_RECEITA_PF=https://5un3wv2xo5.execute-api.us-east-1.amazonaws.com/captura/qa
SOURCE_RECEITA_PJ=http://teste-captura.qa.uplexis.com.br/receita_federal_comprovante_cnpj/service/ComprovanteCnpjService.php?wsdl

CAPTURA_SPINE_HOST=db-qa.cu7goubbl3mv.us-east-1.rds.amazonaws.com
DB_PORT=5432
CAPTURA_DATABASE=spine_enriquecimento_captura3
DB_USERNAME=${DB_USERNAME}
DB_PASSWORD=${DB_PASSWORD}
HERE_API_KEY='thZAvoxDCPWYEj40kgeltndHw9UCwpZNNhMUq7_9xqo'

### AIRBREAK ###
AIRBREAK_PROJECT_ID=426848
AIRBREAK_PROJECT_KEY=fbc99687bc37dca7c2451fe9010d68f1
