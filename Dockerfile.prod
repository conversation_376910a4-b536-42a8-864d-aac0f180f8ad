FROM 342743391564.dkr.ecr.us-east-1.amazonaws.com/php-8-1:2.0

# Maintainer
MAINTAINER "Ya<PERSON><PERSON> <PERSON> <<EMAIL>>"

# Set workdir
WORKDIR /var/www
RUN rm  -rf /var/www/html

# Nginx configuration
COPY ./.deploy/000-default-prod.conf /etc/nginx/http.d/default.conf
COPY ./.deploy/ssl.2017 /etc/nginx/ssl/

# Add files of project to image

ARG DB_USERNAME
ARG DB_PASSWORD

ENV DB_USERNAME=$DB_USERNAME
ENV DB_PASSWORD=$DB_PASSWORD

ARG AWS_KEY
ARG AWS_SECRET

ENV AWS_KEY=$AWS_KEY
ENV AWS_SECRET=$AWS_SECRET

COPY . .

RUN composer self-update --snapshot
RUN composer install --optimize-autoloader --no-dev

# Deploy project
RUN cp .env.prod .env

RUN chown -R www-data:www-data .
RUN chmod 777 -R ./storage/

RUN php artisan config:clear

RUN composer dumpautoload
