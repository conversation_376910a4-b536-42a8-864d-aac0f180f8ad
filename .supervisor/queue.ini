[program:queue-work]
command=php artisan queue:work sqs --tries=5 --daemon --queue spine
process_name=%(program_name)s_%(process_num)02d
numprocs=20                 ; number of processes copies to start (def 1i)
directory=/var/www
autostart=true                ; start at supervisord start (default: true)
startsecs=1                   ; # of secs prog must stay up to be running (def. 1)
startretries=3                ; max # of serial start failures when starting (default 3)
autorestart=true
redirect_stderr=true
stdout_logfile=/tmp/queue.log
