FROM 342743391564.dkr.ecr.us-east-1.amazonaws.com/php-8-1-supervisor:latest

# Set workdir
WORKDIR /var/www
RUN rm  -rf /var/www/html

# Copying Supervisor config file
COPY ./.supervisor/queue.ini /etc/supervisor.d/queue.ini
COPY ./.supervisor/consultas.ini /etc/supervisor.d/consultas.ini

# Add files of project to image

ARG DB_USERNAME
ARG DB_PASSWORD

ENV DB_USERNAME=$DB_USERNAME
ENV DB_PASSWORD=$DB_PASSWORD

ARG AWS_KEY
ARG AWS_SECRET

ENV AWS_KEY=$AWS_KEY
ENV AWS_SECRET=$AWS_SECRET

COPY . .

RUN rm -rf composer.lock
RUN composer self-update --snapshot
RUN composer install --optimize-autoloader --no-dev

# Deploy project
RUN cp .env.prod .env

RUN chmod 777 -R ./storage/

RUN php artisan optimize

RUN composer dumpautoload

CMD ["/usr/bin/supervisord", "-n"]
