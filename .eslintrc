{"root": true, "env": {"es2020": true, "node": true, "jest": true}, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "ignorePatterns": ["node_modules", ".esbuild", "dist/", "coverage/", "test/"], "parserOptions": {"ecmaVersion": 2018, "sourceType": "module", "ecmaFeatures": {"impliedStrict": true}}, "rules": {"quotes": ["error", "single", {"allowTemplateLiterals": true}], "default-case": "warn", "no-param-reassign": "warn", "no-await-in-loop": "warn", "@typescript-eslint/no-unused-vars": ["error", {"vars": "all", "args": "none"}], "semi": [2, "always"]}, "settings": {"import/resolver": {"node": {"extensions": [".js", ".jsx", ".ts", ".tsx"]}}}}