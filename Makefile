docker-install: docker-build docker-up
	cp .env.dev .env
	make docker-composer-install
	
docker-up:
	docker-compose up -d
	
docker-build:
	docker-compose build

docker-composer-install: docker-up
	docker-compose exec spine_update sh -c "chown -R www-data:www-data storage"
	docker-compose exec spine_update sh -c "composer self-update --snapshot"
	docker-compose exec spine_update sh -c "composer install --optimize-autoloader --no-dev"
	docker-compose exec spine_update sh -c "php artisan optimize"
	docker-compose exec spine_update sh -c "php artisan cache:clear"
	docker-compose exec spine_update sh -c "composer dumpautoload"

docker-bash:
	docker-compose exec spine_update sh

docker-down:
	docker-compose down
