{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": "^8.0.2", "laravel/framework": "^9.0", "baopham/dynamodb": "^6.1", "elasticsearch/elasticsearch": "^7.11", "kouz/laravel-airbrake": "^0.8.0", "laravel/tinker": "^2.8"}, "require-dev": {"fakerphp/faker": "^1.9.1", "mockery/mockery": "0.9.*", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.3", "filp/whoops": "~2.0", "symfony/css-selector": "6.0", "symfony/dom-crawler": "6.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-root-package-install": ["php -r \"copy('.env.example', '.env');\""], "post-create-project-cmd": ["php artisan key:generate"], "post-install-cmd": ["Illuminate\\Foundation\\ComposerScripts::postInstall"], "post-update-cmd": ["Illuminate\\Foundation\\ComposerScripts::postUpdate"]}, "config": {"preferred-install": "dist", "allow-plugins": {"kylekatarnls/update-helper": true, "php-http/discovery": true}}}