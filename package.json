{"name": "aws-serverless-typescript-api", "version": "1.0.0", "description": "Serverless aws-nodejs-typescript template", "main": "serverless.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint . --ext .ts", "lint-fix": "eslint . --ext .ts --fix"}, "engines": {"node": "^18.18.2"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.58.0", "@aws-sdk/lib-dynamodb": "^3.58.0", "@aws-sdk/util-dynamodb": "^3.58.0", "@middy/core": "^2.5.3", "@middy/http-json-body-parser": "^2.5.3", "aws-sdk": "^2.1106.0", "axios": "^1.6.2"}, "devDependencies": {"@serverless/typescript": "^3.38.0", "@types/aws-lambda": "^8.10.71", "@types/aws-sdk": "^2.7.0", "@types/node": "^14.14.25", "@typescript-eslint/eslint-plugin": "^5.19.0", "@typescript-eslint/parser": "^5.19.0", "esbuild": "^0.19.8", "eslint": "^8.13.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "json-schema-to-ts": "^1.5.0", "serverless": "^3.34.0", "serverless-esbuild": "^1.49.0", "serverless-offline": "^13.3.0", "serverless-plugin-optimize": "^4.2.1-rc.1", "serverless-plugin-typescript": "^2.1.5", "ts-node": "^10.4.0", "tsconfig-paths": "^3.9.0", "typescript": "^4.1.3"}, "author": "The serverless webpack authors (https://github.com/elastic-coders/serverless-webpack)", "license": "MIT"}