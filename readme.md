## Spine Update

Projeto responsável por atualizar um dos elasticsearch e algumas tabelas no dynamodb através de resultados de fontes

## Executando Projeto Local Via Docker

**Obs: caso já tenha rodado o projeto de outra forma, delete a pasta vendor e o arquivo composer.lock**

1. Configurar AWS CLI 
    <br>(usar a Access Key ID e Secret Access Key do seu usuário, na conta da aws de qa, e a região é us-east-1):
```
aws configure
```
2. Realizar autenticação:
```
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 857717895630.dkr.ecr.us-east-1.amazonaws.com
```
3. Executar comando:

```shell
## Este comando vai executar todas as etapas necessárias para configurar o ambiente e atribuir a porta 8085 sua execução

make docker-install
```

Den<PERSON> do arquivo `Makefile` você encontra outros comandos úteis para o dia a dia, como para acessar o Container em execução do Docker `make docker-bash`.