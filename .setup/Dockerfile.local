FROM bref/php-80

RUN yum -y install telnet
RUN yum update -y
RUN yum install -y ca-certificates
RUN update-ca-trust

COPY --from=bref/extra-pgsql-php-80 /opt /opt
COPY --from=bref/extra-mongodb-php-80 /opt /opt
COPY --from=bref/extra-xdebug-php-80 /opt /opt
COPY --from=bref/extra-gd-php-80 /opt /opt

COPY ./layer /opt

ADD ./src $LAMBDA_TASK_ROOT

RUN echo "xdebug.mode=debug,develop" >> /opt/bref/etc/php/conf.d/ext-xdebug.ini \
    && echo "xdebug.start_with_request=yes" >> /opt/bref/etc/php/conf.d/ext-xdebug.ini \
    && echo "xdebug.discover_client_host=1" >> /opt/bref/etc/php/conf.d/ext-xdebug.ini \
    && echo "xdebug.client_host=**********" >> /opt/bref/etc/php/conf.d/ext-xdebug.ini    

ENV SSL_CERT_FILE="/etc/pki/tls/certs/ca-bundle.crt"
ENV PATH="/opt/bin:/usr/local/bin:/usr/bin/:/bin"
ENV LD_LIBRARY_PATH="/opt/lib:/lib64:/usr/lib64:$LAMBDA_RUNTIME_DIR:$LAMBDA_RUNTIME_DIR/lib:$LAMBDA_TASK_ROOT:$LAMBDA_TASK_ROOT/lib"
ENV FONTCONFIG_PATH="/opt/fonts"

CMD ["index.php"]
