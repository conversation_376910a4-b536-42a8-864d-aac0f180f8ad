# libcurl.la - a libtool library file
# Generated by libtool (GNU libtool) 2.4.2
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# The name that we can dlopen(3).
dlname='libcurl.so.4'

# Names of this library.
library_names='libcurl.so.4.7.0 libcurl.so.4 libcurl.so'

# The name of the static archive.
old_library='libcurl.a'

# Linker flags that can not go in dependency_libs.
inherited_linker_flags=' -pthread'

# Libraries that this one depends upon.
dependency_libs=' -lssl -lcrypto -lz'

# Names of additional weak libraries provided by this library
weak_library_names=''

# Version information for libcurl.
current=11
age=7
revision=0

# Is this an already installed library?
installed=yes

# Should we warn about portability when linking against -modules?
shouldnotlink=no

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/curl/build/lib'
